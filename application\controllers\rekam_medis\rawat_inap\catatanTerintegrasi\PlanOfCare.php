<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PlanOfCare extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }
        $this->load->model(array('masterModel', 'pengkajianAwalModel', 'rekam_medis/rawat_inap/catatanTerintegrasi/PlanOfCareModel', 'rekam_medis/rawat_inap/igd/SurKetEmModel'));
    }

    public function index()
    {
        $norm = $this->uri->segment(6);
        $nopen = $this->uri->segment(7);
        $nokun = $this->uri->segment(8);
        $data = array(
            'norm' => $norm,
            'nopen' => $nopen,
            'nokun' => $nokun,
            'pasien' => $this->pengkajianAwalModel->getNomr($this->uri->segment(2)),
            'listDr' => $this->masterModel->listDr(),
            'listPr' => $this->masterModel->listPerawat(),
            'listTimja' => $this->masterModel->listTimja(),
            'historyPOC' => $this->PlanOfCareModel->historyPOC($norm),
            'jnsdiskusi' => $this->masterModel->referensi(1740),
            'datadokterupdate' => "",
            'datapegawaiupdate' => "",
            'dataperawatupdate' => "",
            'id' => "",
            'getDataPoc' => "",
        );
        $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/planOfCare', $data);
    }

    public function action($param)
    {
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            if ($param == 'tambah' || $param == 'ubah') {
                $rules = $this->PlanOfCareModel->rules;
                $this->form_validation->set_rules($rules);

                if ($this->form_validation->run() == TRUE) {
                    $idpoc = $this->input->post("id");
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $dataPlanOfcare = array(
                        'id' => $post['id'],
                        'nokun' => $post['nokun'],
                        'tanggal' => $post['tanggal'],
                        'jam' => $post['jam'],
                        'daftar_masalah' => $post['daftar_masalah'],
                        'planofcare' => $post['planofcare'],
                        'target_terukur' => $post['target_terukur'],
                        'tanggal_rencana' => $post['tanggal_rencana'],
                        'dpjp_utama' => isset($post['dpjputama']) ? json_encode($post['dpjputama']) : "",
                        'perawat' => isset($post['namaperawat']) ? json_encode($post['namaperawat']) : "",
                        'pegawai' => isset($post['namapegawai']) ? json_encode($post['namapegawai']) : "",
                        'timja' => $post['timja'],
                        'jenis_diskusi' => $post['jenis_diskusi'],
                        'jenis_lainnya' => $post['jenis_lainnya'],
                        'hasilperiksa' => $post['hasilperiksa'],
                        'lamarawat' => $post['lamarawat'],
                        'oleh' => $this->session->userdata("id")
                    );
                    // echo '<pre>';
                    // print_r($dataPlanOfcare);
                    // exit();

                    if (!empty($idpoc)) {
                        $this->db->where('db_layanan.tb_planofcare.id', $idpoc);
                        $this->db->update('db_layanan.tb_planofcare', $dataPlanOfcare);
                    } else {
                        $this->db->insert('db_layanan.tb_planofcare', $dataPlanOfcare);
                        $idPlanOfCare = $this->db->insert_id();
                    }
                    //$this->db->replace('db_layanan.tb_planofcare', $dataPlanOfcare);


                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
                } else {
                    $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
                }
                echo json_encode($result);
            } else if ($param == 'ambil') {
                $post = $this->input->post(NULL, TRUE);
                $dataPlanOfcare = $this->PlanOfCareModel->get($post['id'], true);

                echo json_encode(array(
                    'status' => 'success',
                    'data' => $dataPlanOfcare
                ));
            } else if ($param == 'count') {
                $result = $this->PlanOfCareModel->get_count();;
                echo json_encode($result);
            }
        }
    }

    public function viewEditPoc($norm, $nopen, $nokun, $id)
    {

        $norm = $norm;
        $nopen = $nopen;
        $nokun = $nokun;
        $getHistoryPoc = $this->PlanOfCareModel->historyPOC($norm);
        $getDataPoc = $this->PlanOfCareModel->getDataPoc($id);
        $listTimja = $this->masterModel->listTimja();


        $data = array(
            'norm' => $norm,
            'nopen' => $nopen,
            'nokun' => $nokun,
            'getDataPoc' => $getDataPoc,
            'datadokterupdate' => $getDataPoc['dpjp_utama'],
            'dataperawatupdate' => $getDataPoc['perawat'],
            'datapegawaiupdate' => $getDataPoc['pegawai'],
            'jnsdiskusi' => $this->masterModel->referensi(1740),
            'listTimja' => $listTimja,
            'historyPOC' => $getHistoryPoc,
            'id' => $id
        );

        $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/planOfCare', $data);
    }

    public function listDr()
    {
        $result = $this->PlanOfCareModel->listDr();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row->NAMA;
            $sub_array['text'] = $row->NAMA;
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

    public function listpr()
    {
        $result = $this->PlanOfCareModel->listpr();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row->NAMA;
            $sub_array['text'] = $row->NAMA;
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

    public function listPg()
    {
        $result = $this->PlanOfCareModel->listPg();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row->NAMA;
            $sub_array['text'] = $row->NAMA;
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

    public function planOfCareDashboard()
    {
        $result = $this->PlanOfCareModel->getDataPlanOfCare();

        echo json_encode(array(
            'status' => 'success',
            'data'   => $result
        ));
    }

    public function checkPocExists()
    {
        $nokun = $this->input->post('nokun');
        $exists = $this->PlanOfCareModel->checkPocExists($nokun);
        
        echo json_encode(array(
            'exists' => $exists
        ));
    }
}

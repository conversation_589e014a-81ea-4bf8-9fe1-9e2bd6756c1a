<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Konsultasi extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      [
        'masterModel',
        'pengkajianAwalModel',
        'konsultasi/KonsultasiModel'
      ]
    );
    $this->load->library('whatsapp');
  }

  public function index()
  {
    $nomr = $this->uri->segment(4);
    $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $data = [
      'drPengirim' => $this->masterModel->listDrUmum(null, null, null),
      'pilihProtokolKemo' => $this->masterModel->pilihProtokolKemo(),
      'drTujuan' => $this->masterModel->listDrUmum(null, null, null), // 230 = Final otomatis, 48 = Gigi Penyakit Mulut, 54, Gigi Bedah Mulut
      'drPA' => $this->masterModel->listDrUmum(null, null, [30]),
      'drPaliatif' => $this->masterModel->listDrPaliatif(),
      'listSMF' => $this->masterModel->listSMF(),
      'jenisKonsultasi' => $this->masterModel->referensi(235),
      'ruanganBedah' => $this->masterModel->ruanganBedah(),
      'ruanganRawatJalan' => $this->masterModel->ruanganRawatJalan(),
      'ruanganRawatInap' => $this->masterModel->ruanganRawatInap(),
      'penilaianKasusSaatIni' => $this->masterModel->referensi(236),
      'historyKonsul' => $this->KonsultasiModel->tampilHistoryKonsul($nomr),
      'jawabKonsul' => $this->KonsultasiModel->tampilJawabKonsul(),
      'tindakanKonsul' => $this->masterModel->referensi(52),
      'konsulYgDiminta' => $this->masterModel->referensi(53),
      'getNomr' => $getNomr,
      'nokun' => $nokun,
    ];
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/konsultasi/index', $data);
  }

  public function simpanFormKonsul()
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      $rules = $this->KonsultasiModel->rules;
      $this->form_validation->set_rules($rules);
      if ($this->form_validation->run() == true) {
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $backtime = $post['backtime'] ?? null;
        $tanggal = null;
        $dokterPengirim = $post['dokter_pengirim'] ?? null;
        $tujuan = $post['tujuan'] ?? null;
        $jam = null;
        $salam = null;
        $dokterTujuan = null;
        $namaDokterTujuan = null;
        $nomorTujuan = null;
        $smf = null;
        $namaDokterPengirim = null;
        $nomorPengirim = null;
        $sumberSediaan = $post['sumber_sediaan'] ?? null;
        $jenisRawat = $post['jenis_rawat'] ?? null;
        $diagnosisKerja = $post['diagnosis_kerja'] ?? null;
        $ikhtisarKlinis = $post['ikhtisar_klinis'] ?? null;
        $konsulYangDiminta = $post['konsul_yang_diminta'] ?? null;
        $nomr = $post['norm'] ?? null;
        $nokun = $post['nokun'] ?? null;
        $pengirim = $this->masterModel->nomorDokter($dokterPengirim);
        $namaPasien = $this->masterModel->getPasien($nomr);
        $dataTujuan = null;
        $teksTujuan = null;
        $chatPengirim = [];
        $chatTujuan = [];
        $oleh = $this->session->userdata('id');

        // Mulai periksa pilihan waktu
        if ($post['backtime'] == '1') {
          $tanggal = date('Y-m-d H:i:s', strtotime($post['tanggal']));
          $jam = date('H', strtotime($post['tanggal']));
        } else {
          $tanggal = date('Y-m-d H:i:s');
          $jam = date('H');
        }
        // Akhir periksa pilihan waktu

        // Mulai menentukan waktu
        if ($jam >= '05' && $jam < '10') {
          $salam = 'pagi';
        } elseif ($jam >= '10' && $jam < '15') {
          $salam = 'siang';
        } elseif ($jam >= '15' && $jam < '19') {
          $salam = 'sore';
        } else {
          $salam = 'malam';
        }
        // Akhir menentukan waktu

        // Mulai memilih jenis rawat
        if ($jenisRawat == '0') {
          $tujuanRuangan = $post['tujuan_ruangan_0'];
        } elseif ($jenisRawat == '1') {
          $tujuanRuangan = $post['tujuan_ruangan_1'];
        } elseif ($jenisRawat == '2') {
          $tujuanRuangan = $post['tujuan_ruangan_2'];
        }
        // Akhir memilih jenis rawat

        // Mulai ambil data pengirim
        if (!empty($pengirim)) {
          // echo '<pre>';print_r($pengirim);exit();
          $namaDokterPengirim = $pengirim->DOKTER;
          $nomorPengirim = '+62' . substr(trim($pengirim->NOMOR), 1);
          // echo '<pre>';print_r($nomorPengirim);exit();
        }
        // Akhir ambil data pengirim

        // Mulai memilih berdasarkan dokter atau spesialisasi
        if ($tujuan == 1) { // Dokter
          $i = 0;
          foreach ($post['dokter_tujuan'] as $dt) {
            list($dokterTujuan[$i], $smf) = array_pad(explode('-', $post['dokter_tujuan'][$i]), 2, '0');
            $dataTujuan[$i] = $this->masterModel->nomorDokter($dokterTujuan[$i]);
            // echo '<pre>';print_r($dataTujuan);exit();
            $namaDokterTujuan[$i] = $dataTujuan[$i]->DOKTER ?? 'NN';
            $nomorTujuan[$i] = '+62' . substr(trim($dataTujuan[$i]->NOMOR), 1);
            // echo '<pre>';print_r($nomorTujuan[$i]);exit();
            $teksTujuan[$i] = ' *' . $namaDokterTujuan[$i] . '* di *' . $post['ruang_awal'] . '*';
            $chatPengirim[$i] = [
              $salam . ', *' . $namaDokterPengirim . '*',
              'ini kami menyampaikan',
              'Bahwa pasien yang bernama *' . $namaPasien . '* dengan nomor rekam medis *[' . $nomr . ']*' . ' berhasil dikonsultasikan ke' . $teksTujuan[$i]
            ];
            // echo '<pre>';print_r($chatPengirim[$i]);exit();

            $chatTujuan[$i] = [
              $salam . $teksTujuan[$i],
              'ini kami menyampaikan',
              'Anda menerima konsultasi untuk pasien *[' . $nomr . ']* atas nama *' . $namaPasien . '* dari ruang *' . $post['ruang_awal'] . '* dengan diagnosis kerja *' . $diagnosisKerja . '* dan ikhtisar klinis *' . $ikhtisarKlinis . '*. Konsultasi yang diminta adalah *' . $konsulYangDiminta . '* pada tanggal *' . date('d-m-Y', strtotime($tanggal)) . '* dari *' . $namaDokterPengirim . "*. Mohon untuk membuka aplikasi EMR dan mengeklik 'Notifikasi Konsultasi' untuk mengetahui info lebih lanjut dan menjawab konsultasi."
            ];
            // echo '<pre>';print_r($chatTujuan[$i]);exit();

            // Menambahkan data tujuan ke data yang akan disimpan
            $data[$i] = [
              'kunjungan' => $nokun,
              'backtime' => $backtime,
              'tanggal' => $tanggal,
              'tanggal_update' => $tanggal,
              'dokter_pengirim' => $dokterPengirim,
              'tujuan' => $tujuan,
              'dokter_tujuan' => $dokterTujuan[$i],
              'diagnosis_kerja' => $diagnosisKerja,
              'ikhtisar_klinis' => $ikhtisarKlinis,
              'konsul_yang_diminta' => $konsulYangDiminta,
              'sumber_sediaan' => $sumberSediaan,
              'jenis_rawat' => $jenisRawat,
              'tujuan_ruangan' => $tujuanRuangan,
              'cito' => $post['jenis_konsultasi'],
              'penilaian_kasus' => $post['penilaian_kasus'],
              'oleh' => $this->session->userdata('id'),
            ];

            // Simpan
            // echo '<pre>';print_r($data[$i]);exit();
            $this->KonsultasiModel->insertFormKonsul($data[$i]);

            $i++;
          }
        } elseif ($tujuan == 2) { // SMF
          $j = 0;
          foreach ($post['smf'] as $s) {
            $smf[$j] = $post['smf'][$j];

            // Menambahkan data tujuan ke data yang akan disimpan
            $data[$j] = [
              'kunjungan' => $nokun,
              'backtime' => $backtime,
              'tanggal' => $tanggal,
              'tanggal_update' => $tanggal,
              'dokter_pengirim' => $dokterPengirim,
              'tujuan' => $tujuan,
              'smf' => $smf[$j],
              'diagnosis_kerja' => $diagnosisKerja,
              'ikhtisar_klinis' => $ikhtisarKlinis,
              'konsul_yang_diminta' => $konsulYangDiminta,
              'sumber_sediaan' => $sumberSediaan,
              'jenis_rawat' => $jenisRawat,
              'tujuan_ruangan' => $tujuanRuangan,
              'cito' => $post['jenis_konsultasi'],
              'penilaian_kasus' => $post['penilaian_kasus'],
              'oleh' => $oleh,
            ];

            // Simpan
            // echo '<pre>';print_r($data[$j]);exit();
            $this->KonsultasiModel->insertFormKonsul($data[$j]);

            $j++;
          }
        } elseif ($tujuan == '3') { // Konsul/review PA
          $kode = $this->pengkajianAwalModel->generateNoOrderLab($post['id_ruang_awal'], $tanggal);
          // echo '<pre>';print_r($kode);exit();
          $k = 0;
          foreach ($post['dokter_pa'] as $dt) {
            list($dokterTujuan[$k], $smf) = array_pad(explode('-', $post['dokter_pa'][$k]), 2, '0');
            $dataTujuan[$k] = $this->masterModel->nomorDokter($dokterTujuan[$k]);
            // echo '<pre>';print_r($dataTujuan[$k]);exit();
            $namaDokterTujuan[$k] = $dataTujuan[$k]->DOKTER;
            $nomorTujuan[$k] = '+62' . substr(trim($dataTujuan[$k]->NOMOR), 1);
            // echo '<pre>';print_r($nomorTujuan[$k]);exit();
            $teksTujuan[$k] = ', *' . $namaDokterTujuan[$k] . '*';
            $chatPengirim[$k] = [
              $salam . ', *' . $namaDokterPengirim . '*',
              'ini kami menyampaikan',
              'Bahwa pasien yang bernama *' . $namaPasien . '* dengan nomor rekam medis *[' . $nomr . ']*' . ' berhasil dikonsultasikan ke ' . $teksTujuan[$k]
            ];
            // echo '<pre>';print_r($chatPengirim[$k]);exit();

            $chatTujuan[$k] = [
              $salam . $teksTujuan[$k],
              'ini kami menyampaikan',
              'Anda menerima konsultasi untuk pasien *[' . $nomr . ']* atas nama *' . $namaPasien . '* dari ruang *' . $post['ruang_awal'] . '* dengan diagnosis kerja *' . $diagnosisKerja . '* dan ikhtisar klinis *' . $ikhtisarKlinis . '*. Konsultasi yang diminta adalah *' . $konsulYangDiminta . '* pada tanggal *' . date('d-m-Y', strtotime($tanggal)) . '* dari *' . $namaDokterPengirim . "*. Mohon untuk membuka aplikasi EMR dan mengeklik Notifikasi Konsultasi untuk mengetahui info lebih lanjut dan menjawab konsultasi."
            ];
            // echo '<pre>';print_r($chatTujuan[$k]);exit();

            // Menambahkan data tujuan ke data yang akan disimpan
            $data[$k] = [
              'kunjungan' => $nokun,
              'no_order' => $kode,
              'tanggal' => $tanggal,
              'tanggal_update' => $tanggal,
              'dokter_pengirim' => $dokterPengirim,
              'tujuan' => $tujuan,
              'dokter_tujuan' => $dokterTujuan[$k],
              'diagnosis_kerja' => $diagnosisKerja,
              'ikhtisar_klinis' => $ikhtisarKlinis,
              'konsul_yang_diminta' => $konsulYangDiminta,
              'sumber_sediaan' => $sumberSediaan,
              'jenis_rawat' => $jenisRawat,
              'tujuan_ruangan' => $tujuanRuangan,
              'cito' => $post['jenis_konsultasi'],
              'penilaian_kasus' => $post['penilaian_kasus'],
              'oleh' => $this->session->userdata('id'),
            ];

            // Simpan
            // echo '<pre>';print_r($data[$k]);exit();
            $this->KonsultasiModel->insertFormKonsul($data[$k]);

            $k++;
          }

          // Mulai order lab jika tujuan ke Lab PA
          $dataOrder = [
            'NOMOR' => $kode,
            'KUNJUNGAN' => $nokun,
            'TANGGAL' => $tanggal,
            'DOKTER_ASAL' => $dokterPengirim,
            'TUJUAN' => '105080101',
            'CITO' => $post['jenis_konsultasi'] == '800' ? 1 : 0,
            'OLEH' => $oleh,
          ];
          $this->db->insert('layanan.order_lab', $dataOrder);
          // Akhir order lab jika tujuan ke Lab PA
        } elseif ($tujuan == '4') { // Paliatif
          $l = 0;
          foreach ($post['dokter_paliatif'] as $dt) {
            list($dokterTujuan[$l], $smf) = array_pad(explode('-', $post['dokter_paliatif'][$l]), 2, '0');
            $dataTujuan[$l] = $this->masterModel->nomorDokter($dokterTujuan[$l]);
            // echo '<pre>';print_r($dataTujuan[$l]);exit();
            $namaDokterTujuan[$l] = $dataTujuan[$l]->DOKTER;
            $nomorTujuan[$l] = '+62' . substr(trim($dataTujuan[$l]->NOMOR), 1);
            // echo '<pre>';print_r($nomorTujuan[$l]);exit();
            $teksTujuan[$l] = ', *' . $namaDokterTujuan[$l] . '*';
            $chatPengirim[$l] = [
              $salam . ', *' . $namaDokterPengirim . '*',
              'ini kami menyampaikan',
              'Bahwa pasien yang bernama *' . $namaPasien . ' [' . $nomr . ']*' . ' berhasil dikonsultasikan ke ' . $teksTujuan[$l]
            ];
            // echo '<pre>';print_r($chatPengirim);exit();

            $chatTujuan[$l] = [
              $salam . $teksTujuan[$l],
              'ini kami menyampaikan',
              'Anda menerima konsultasi paliatif untuk pasien *[' . $nomr . ']* atas nama *' . $namaPasien . '* dari ruang *' . $post['ruang_awal'] . '* dengan diagnosis kerja *' . $diagnosisKerja . '* dan ikhtisar klinis *' . $ikhtisarKlinis . '*. Konsultasi yang diminta adalah *' . $konsulYangDiminta . '* pada tanggal *' . date('d-m-Y', strtotime($tanggal)) . '* dari *' . $namaDokterPengirim . '* Mohon untuk mengecek aplikasi EMR dan mengeklik Notifikasi Konsultasi untuk mengetahui info lebih lanjut dan menjawab konsultasi.'
            ];
            // echo '<pre>';print_r($chatTujuan[$l]);exit();

            // Menambahkan data tujuan ke data yang akan disimpan
            $data[$l] = [
              'kunjungan' => $nokun,
              'backtime' => $backtime,
              'tanggal' => $tanggal,
              'tanggal_update' => $tanggal,
              'dokter_pengirim' => $dokterPengirim,
              'tujuan' => $tujuan,
              'dokter_tujuan' => $dokterTujuan[$l],
              'diagnosis_kerja' => $diagnosisKerja,
              'ikhtisar_klinis' => $ikhtisarKlinis,
              'konsul_yang_diminta' => $konsulYangDiminta,
              'sumber_sediaan' => $sumberSediaan,
              'jenis_rawat' => $jenisRawat,
              'tujuan_ruangan' => $tujuanRuangan,
              'cito' => $post['jenis_konsultasi'],
              'penilaian_kasus' => $post['penilaian_kasus'],
              'oleh' => $this->session->userdata('id'),
            ];

            // Simpan
            // echo '<pre>';print_r($data[$l]);exit();
            $this->KonsultasiModel->insertFormKonsul($data[$l]);

            $l++;
          }
        }
        // Akhir memilih berdasarkan dokter atau spesialisasi

        // echo '<pre>';print_r($data);exit();

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = ['status' => 'failed'];
        } else {
          $this->db->trans_commit();
          if ($tujuan == 1 || $tujuan == 3 || $tujuan == 4) { // Individual
            try {
              $m = 0;
              foreach ($chatTujuan as $ct) {
                // Chat untuk pengirim konsul
                $this->whatsapp->send($nomorPengirim, $chatPengirim[$m]);
                // Chat untuk tujuan konsul
                $this->whatsapp->send($nomorTujuan[$m], $chatTujuan[$m]);
                $m++;
              }
            } catch (Exception $e) {
              // throw $e;
            }
          } elseif ($tujuan == 2) { // Kelompok
            // $n = 0;
            // foreach ($post['smf'] as $s) {
            //   try {
            //     $n = 0;
            //     // Chat untuk pengirim konsul
            //     $this->whatsapp->send($nomorPengirim, $chatPengirim);
            //     // Chat untuk tujuan konsul
            //     foreach ($chatTujuan as $ct) {
            //       $this->whatsapp->send($nomorTujuan[$n], $chatTujuan[$n]);
            //       $n++;
            //     }
            //   } catch (Exception $e) {
            //     //throw $th;
            //   }
            // }
            // $n++;
          }
          $result = ['status' => 'success'];
        }
      } else {
        $result = ['status' => 'failed', 'errors' => $this->form_validation->error_array()];
      }
      echo json_encode($result);
    }
  }

  public function lihatHistory()
  {
    $post = $this->input->post();
    $idKonsul = $this->input->post('ID_KONSUL');
    $isiModal = $this->ambilHistory($idKonsul);
    $ruangAwal = null;

    if (isset($post['RUANGAN_TUJUAN'])) {
      $ruangAwal = $post['RUANGAN_TUJUAN'];
    } else {
      $getNomr = $this->pengkajianAwalModel->getNomr($isiModal[0]['nokun']);
      if (isset($getNomr['RUANGAN_TUJUAN'])) {
        $ruangAwal = $getNomr['RUANGAN_TUJUAN'];
      } else {
        $ruangAwal = '-';
      }
    }

    $data = [
      'isiModal' => $isiModal,
      'idKonsul' => $idKonsul,
      'penilaianKasusSaatIni' => $this->masterModel->referensi(236),
      'ruangAwal' => $ruangAwal,
      'nomr' => $post['NORM'],
      'nopen' => $post['NOPEN'],
      'nokun' => $post['NOKUN'],
      'param' => 'modal',
    ];
    // echo '<pre>';print_r($isiModal);exit();
    $this->load->view('Pengkajian/konsultasi/historyKonsultasi/modalDetailHistory', $data);
  }

  public function ambilHistory($idKonsul)
  {
    $detailHistory = $this->KonsultasiModel->modalHistoryKonsul($idKonsul);
    $data = [];
    foreach ($detailHistory as $main) {
      $isi_array = [];
      $isi_array['id_konsul'] = $main['ID_KONSUL'];
      $isi_array['nokun'] = $main['kunjungan'];
      $isi_array['tanggal'] = $main['tanggal'];
      $isi_array['deskripsi'] = $main['DESKRIPSI'];
      $isi_array['ruang'] = $main['RUANGAN'];
      $isi_array['sumber_sediaan'] = $main['sumber_sediaan'];
      $isi_array['id_pengirim'] = $main['id_pengirim'];
      $isi_array['id_dokter_pengirim'] = $main['id_dokter_pengirim'];
      $isi_array['dokter_pengirim'] = $main['DOKTER_PENGIRIM'];
      $isi_array['id_dokter_tujuan'] = $main['id_dokter_tujuan'];
      $isi_array['smf'] = $main['smf'];
      $isi_array['cito'] = $main['CITO'];
      $isi_array['diagnosis_kerja'] = $main['diagnosis_kerja'];
      $isi_array['ikhtisar_klinis'] = $main['ikhtisar_klinis'];
      $isi_array['konsul_yang_diminta'] = $main['konsul_yang_diminta'];
      $isi_array['penilaian_kasus'] = $main['penilaian_kasus'];
      $isi_array['status'] = $main['status'];
      $isi_array['tanggal_jawab'] = $main['tanggal_jawab'];
      $isi_array['penemuan'] = $main['penemuan'];
      $isi_array['diagnosa'] = $main['diagnosa'];
      $isi_array['anjuran'] = $main['anjuran'];
      $isi_array['persetujuan_kasus'] = $main['persetujuan_kasus'];

      if ($main['id_dokter_tujuan'] != 0) {
        $isi_array['dokter_tujuan'] = $main['DOKTER_TUJUAN'];
      } else {
        if ($main['dokter_jawab'] != '0') {
          $isi_array['dokter_tujuan'] = $main['DOKTER_PENJAWAB'];
        } else {
          $isi_array['dokter_tujuan'] = '-';
        }
      }

      $data[] = $isi_array;
    }
    return $data;
  }

  public function ubahDetailHistory()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $idKonsul = $post['id_konsul'];
    $data = [
      'tanggal_update' => date('Y-m-d H:i:s'),
      'diagnosis_kerja' => $post['diagnosis_kerja'],
      'ikhtisar_klinis' => $post['ikhtisar_klinis'],
      'konsul_yang_diminta' => $post['konsul_yang_diminta'],
      'penilaian_kasus' => $post['penilaian_kasus'],
    ];
    $where = ['id' => $idKonsul];
    $this->KonsultasiModel->updateHistoryKonsul($where, $data, 'medis.tb_konsul');
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = ['status' => 'failed'];
    } else {
      $this->db->trans_commit();
      $result = ['status' => 'success'];
    }
    echo json_encode($result);
  }

  public function batalKonsultasi()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $jam = date('H');
    $salam = null;
    $namaDokterTujuan = null;
    $nomorTujuan = null;
    $namaDokterPengirim = null;
    $idKonsul = $post['id_konsul'];
    $isiKonsul = $this->ambilHistory($idKonsul);
    $nokun = $isiKonsul[0]['nokun'];
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $namaPasien = $getNomr['NAMA_PASIEN'];
    $nomr = $getNomr['NORM'];
    // echo '<pre>';print_r($isiKonsul);exit();

    if ($jam >= '05' && $jam < '10') {
      $salam = 'pagi';
    } elseif ($jam >= '10' && $jam < '15') {
      $salam = 'siang';
    } elseif ($jam >= '15' && $jam < '19') {
      $salam = 'sore';
    } else {
      $salam = 'malam';
    }

    // Mulai ambil data pengirim
    if (!empty($isiKonsul[0]['id_dokter_pengirim'])) {
      // echo '<pre>';print_r($pengirim);exit();
      $pengirim = $this->masterModel->nomorDokter($isiKonsul[0]['id_dokter_pengirim']);
      $namaDokterPengirim = $pengirim->DOKTER;
      $nomorPengirim = '+62' . substr(trim($pengirim->NOMOR), 1);
      // echo '<pre>';print_r($nomorPengirim);exit();
    }
    // Akhir ambil data pengirim

    // Mulai ambil data tujuan
    if (!empty($isiKonsul[0]['id_dokter_tujuan'])) {
      $dataTujuan = $this->masterModel->nomorDokter($isiKonsul[0]['id_dokter_tujuan']);
      // echo '<pre>';print_r($dataTujuan);exit();
      $namaDokterTujuan = $dataTujuan->DOKTER;
      $nomorTujuan = '+62' . substr(trim($dataTujuan->NOMOR), 1);
      // echo '<pre>';print_r($nomorPengirim .'-'. $nomorTujuan);exit();
      $teksTujuan = ', *' . $namaDokterTujuan . '*';
      $chatPengirim = [
        $salam . ', *' . $namaDokterPengirim . '*',
        'ini kami menyampaikan',
        'Bahwa konsultasi atas pasien yang bernama *' . $namaPasien . ' [' . $nomr . ']*' . ' yang dikonsultasikan kepada ' . $teksTujuan . ' telah berhasil *dibatalkan*'
      ];
      // echo '<pre>';print_r($chatPengirim);exit();

      $chatTujuan = [
        $salam . $teksTujuan,
        'ini kami menyampaikan',
        'Bahwa konsultasi yang ditujukan kepada Anda berikut ini telah *dibatalkan*, yaitu konsultasi untuk pasien *[' . $nomr . ']*, atas nama *' . $namaPasien . '* dengan diagnosis kerja *' . $isiKonsul[0]['diagnosis_kerja'] . '* dan ikhtisar klinis *' . $isiKonsul[0]['ikhtisar_klinis'] . '*. Konsultasi yang diminta adalah *' . $isiKonsul[0]['konsul_yang_diminta'] . '* pada tanggal *' . date('d-m-Y') . '* dari *' . $namaDokterPengirim . '*.'
      ];
      // echo '<pre>';print_r($chatTujuan);exit();
    }
    // Akhir ambil data tujuan

    $data = [
      'tanggal_update' => date('Y-m-d H:i:s'),
      'status' => 0,
    ];
    $where = ['id' => $idKonsul];
    $this->KonsultasiModel->updateHistoryKonsul($where, $data, 'medis.tb_konsul');
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = ['status' => 'failed'];
    } else {
      $this->db->trans_commit();
      try {
        // Chat untuk pengirim konsul
        $this->whatsapp->send($nomorPengirim, $chatPengirim);
        // Chat untuk tujuan konsul
        $this->whatsapp->send($nomorTujuan, $chatTujuan);
      } catch (Exception $e) {
      }
      $result = ['status' => 'success'];
    }
    echo json_encode($result);
  }

  public function lihatJawab()
  {
    $post = $this->input->post();
    $idKonsul = $post['ID_KONSUL'];
    $isiModal = $this->ambilJawab($idKonsul);
    $nokun = $isiModal[0]['nokun'];
    $idPengguna = $this->session->userdata('id');
    $ruangAwal = null;

    if (isset($post['RUANGAN_TUJUAN'])) {
      $ruangAwal = $post['RUANGAN_TUJUAN'];
    } else {
      $getNomr = $this->pengkajianAwalModel->getNomr($isiModal[0]['nokun']);
      if (isset($getNomr['RUANGAN_TUJUAN'])) {
        $ruangAwal = $getNomr['RUANGAN_TUJUAN'];
      } else {
        $ruangAwal = '-';
      }
    }

    $data = [
      'isiModal' => $isiModal,
      'idKonsul' => $idKonsul,
      'ruangAwal' => $ruangAwal,
      'kunjunganJawab' => $nokun,
      'penilaianKasusSaatIni' => $this->masterModel->referensi(236),
      'atasPersetujuan' => $this->masterModel->listDrUmum(),
      'dokterPengguna' => $this->KonsultasiModel->idDokter($idPengguna),
      'nomr' => $post['NORM'] ?? null,
      'nopen' => $post['NOPEN'] ?? null,
      'nokun' => $post['NOKUN'] ?? null,
    ];
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/konsultasi/jawabKonsultasi/modalJawabKonsul', $data);
  }

  public function ambilJawab($idKonsul)
  {
    $detailHistory = $this->KonsultasiModel->modalJawabKonsul($idKonsul);
    $data = [];
    foreach ($detailHistory as $main) {
      $isi_array = [];
      $isi_array['id_jawab'] = $main['ID_JAWAB'];
      $isi_array['id_konsul'] = $main['ID_KONSUL'];
      $isi_array['pasien'] = $main['PASIEN'];
      $isi_array['nokun'] = $main['nokun'];
      $isi_array['norm'] = $main['NORM'];
      $isi_array['backtime'] = $main['backtime'];
      $isi_array['tanggal'] = $main['tanggal'];
      $isi_array['deskripsi'] = $main['DESKRIPSI'];
      $isi_array['ruang'] = $main['RUANGAN'];
      $isi_array['sumber_sediaan'] = $main['sumber_sediaan'];
      $isi_array['dokter_pengirim'] = $main['DOKTER_PENGIRIM'];
      $isi_array['smf'] = $main['smf'];
      $isi_array['cito'] = $main['CITO'];
      $isi_array['diagnosis_kerja'] = $main['diagnosis_kerja'];
      $isi_array['ikhtisar_klinis'] = $main['ikhtisar_klinis'];
      $isi_array['konsul_yang_diminta'] = $main['konsul_yang_diminta'];
      $isi_array['penilaian_kasus_desc'] = $main['penilaian_kasus_desc'];
      $isi_array['status'] = $main['status'];
      $isi_array['tanggal_jawab'] = $main['tanggal_jawab'];
      $isi_array['penemuan'] = $main['penemuan'];
      $isi_array['diagnosa'] = $main['diagnosa'];
      $isi_array['anjuran'] = $main['anjuran'];
      $isi_array['persetujuan_kasus'] = $main['persetujuan_kasus'];
      $isi_array['atas_persetujuan'] = $main['atas_persetujuan'];

      if ($main['id_dokter_tujuan'] == 0 || isset($main['id_dokter_tujuan'])) {
        $isi_array['id_dokter_tujuan'] = null;
        $isi_array['dokter_tujuan'] = $main['DOKTER_PENJAWAB'] ?? '-';
        $isi_array['dokter_jawab'] = null;
      } else {
        $isi_array['id_dokter_tujuan'] = $main['id_dokter_tujuan'];
        $isi_array['dokter_tujuan'] = $main['DOKTER_TUJUAN'];
        $dokterJawab = $this->KonsultasiModel->idDokter($main['dokter_jawab']);
        $isi_array['dokter_jawab'] = $dokterJawab['ID'] ?? null;
      }

      $data[] = $isi_array;
    }
    return $data;
  }

  public function lihatJawabNotifikasi()
  {
    $idKonsul = $this->uri->segment(4);
    $isiModal = $this->ambilFormJawab($idKonsul);
    $nokun = $isiModal[0]['nokun'];
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $nomr = $getNomr['NORM'];
    $idPengguna = $this->session->userdata('id');
    $data = [
      'title' => 'Jawab Konsul',
      'isi' => 'Pengkajian/jawabKonsul/index',
      'isiModal' => $isiModal,
      'nokun' => $nokun,
      'idKonsul' => $idKonsul,
      'kunjunganJawab' => $nokun,
      'alertKeperawatan' => $this->pengkajianAwalModel->alertCppt($nokun, 2, $this->session->userdata('id')),
      'alertMedis' => $this->pengkajianAwalModel->alertCppt($nokun, 1, $this->session->userdata('id')),
      'VkeperwatanCppt' => $this->pengkajianAwalModel->VkeperwatanCppt($nokun),
      'statusPengguna' => $_SESSION['status'],
      'id_pengguna' => $this->session->userdata('id'),
      'getNomr' => $getNomr,
      'nomr' => $getNomr['NORM'],
      'nopen' => $getNomr['NOPEN'],
      'drPengirim' => $this->masterModel->listDrUmum(null, [29]), // 29 = Rehab Medik
      'pilihProtokolKemo' => $this->masterModel->pilihProtokolKemo(),
      'drTujuan' => $this->masterModel->listDrUmum([230], [48, 54]), // 230 = Final otomatis, 48  = Gigi Penyakit Mulut, 54, Gigi Bedah Mulut
      'listSMF' => $this->masterModel->listSMF(),
      'jenisKonsultasi' => $this->masterModel->referensi(235),
      'ruanganRawatJalan' => $this->masterModel->ruanganRawatJalan(),
      'ruanganRawatInap' => $this->masterModel->ruanganRawatInap(),
      'penilaianKasusSaatIni' => $this->masterModel->referensi(236),
      'atasPersetujuan' => $this->masterModel->listDrUmum(),
      'dokterPengguna' => $this->KonsultasiModel->idDokter($idPengguna),
      'historyKonsul' => $this->KonsultasiModel->tampilHistoryKonsul($nomr),
      'jawabKonsul' => $this->KonsultasiModel->tampilJawabKonsul(),
    ];
    // echo '<pre>';print_r($data);exit();
    $this->load->view('layout/wrapper', $data);
  }

  public function ambilFormJawab($idKonsul)
  {
    $detailHistory = $this->KonsultasiModel->formJawabKonsul($idKonsul);
    $data = [];
    foreach ($detailHistory as $main) {
      $isi_array = [];
      $isi_array['id_jawab'] = $main['ID_JAWAB'];
      $isi_array['id_konsul'] = $main['ID_KONSUL'];
      $isi_array['pasien'] = $main['PASIEN'];
      $isi_array['norm'] = $main['NORM'];
      $isi_array['nokun'] = $main['NOKUN_TERAKHIR'];
      $isi_array['ruangan_aktif'] = $main['RUANGAN_AKTIF'];
      $isi_array['backtime'] = $main['backtime'];
      $isi_array['tanggal'] = $main['tanggal'];
      $isi_array['deskripsi'] = $main['DESKRIPSI'];
      $isi_array['ruang'] = $main['RUANGAN'];
      $isi_array['sumber_sediaan'] = $main['sumber_sediaan'];
      $isi_array['dokter_pengirim'] = $main['DOKTER_PENGIRIM'];
      $isi_array['smf'] = $main['smf'];
      $isi_array['cito'] = $main['CITO'];
      $isi_array['diagnosis_kerja'] = $main['diagnosis_kerja'];
      $isi_array['ikhtisar_klinis'] = $main['ikhtisar_klinis'];
      $isi_array['konsul_yang_diminta'] = $main['konsul_yang_diminta'];
      $isi_array['penilaian_kasus_desc'] = $main['penilaian_kasus_desc'];
      $isi_array['status'] = $main['status'];
      $isi_array['tanggal_jawab'] = $main['tanggal_jawab'];
      $isi_array['penemuan'] = $main['penemuan'];
      $isi_array['diagnosa'] = $main['diagnosa'];
      $isi_array['anjuran'] = $main['anjuran'];
      $isi_array['persetujuan_kasus'] = $main['persetujuan_kasus'];
      $isi_array['verifikasi'] = $main['verifikasi'];

      if ($main['id_dokter_tujuan'] == 0) {
        $isi_array['id_dokter_tujuan'] = null;
        $isi_array['dokter_tujuan'] = '-';
        $isi_array['dokter_jawab'] = null;
      } else {
        $isi_array['id_dokter_tujuan'] = $main['id_dokter_tujuan'];
        $isi_array['dokter_tujuan'] = $main['DOKTER_TUJUAN'];
        $dokterJawab = $this->KonsultasiModel->idDokter($main['dokter_jawab']);
        $isi_array['dokter_jawab'] = $dokterJawab['ID'] ?? null;
      }

      $data[] = $isi_array;
    }
    return $data;
  }

  public function simpanJawabKonsul()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    // echo '<pre>';print_r($post);exit();
    $idKonsul = $post['id_konsul'] ?? null;
    $backtime = $post['backtime'] ?? null;
    $tanggal = null;
    $jam = null;
    $penemuan = $post['penemuan'] ?? null;
    $diagnosa = $post['diagnosa'] ?? null;
    $anjuran = $post['anjuran'] ?? null;
    $persetujuanKasus = $post['persetujuan_kasus'] ?? null;
    $atasPersetujuan = $post['atas_persetujuan'] ?? null;
    $isiPersetujuanKasus = null;
    $status = '2';
    $salam = null;
    $verifikasi = null;
    $chatPengirim = [];
    $chatPenjawab = [];

    // Mulai periksa pilihan waktu
    if ($post['backtime'] == '1') {
      $tanggal = date('Y-m-d H:i:s', strtotime($post['tanggal']));
      $jam = date('H', strtotime($post['tanggal']));
    } else {
      $tanggal = date('Y-m-d H:i:s');
      $jam = date('H');
    }
    // Akhir periksa pilihan waktu

    // Mulai ambil nomor MR
    $nokun = $post['kunjungan_jawab'] ?? null;
    $isiFormJawab = $this->KonsultasiModel->formJawabKonsul($idKonsul);
    $nomr = $isiFormJawab[0]['NORM'];
    // echo '<pre>';print_r($isiFormJawab);exit();
    $namaPasien = $isiFormJawab[0]['PASIEN'];
    // echo '<pre>';print_r($namaPasien);exit();
    // Akhir ambil nomor MR

    // Mulai status verifikasi
    $dokterJawab = $this->session->userdata('id');
    $idDokterJawab = $this->KonsultasiModel->idDokter($dokterJawab);
    if ($idDokterJawab == $isiFormJawab[0]['id_dokter_tujuan']) {
      $verifikasi = 1;
    } else {
      $verifikasi = 0;
    }
    // Akhir status verifikasi

    // Mulai mengambil data penjawab
    $penjawab = $this->masterModel->nomorDokter($idDokterJawab['ID']);
    // echo '<pre>';print_r($penjawab);exit();
    if (!empty($penjawab)) {
      $namaDokterPenjawab = $penjawab->DOKTER;
      $nomorPenjawab = '+62' . substr(trim($penjawab->NOMOR), 1);
    }
    // echo '<pre>';print_r($nomorPenjawab);exit();
    // Akhir mengambil data penjawab

    // Mulai mengambil data pengirim
    $pengirim = $this->masterModel->nomorDokter($isiFormJawab[0]['dokter_pengirim']);
    // echo '<pre>';print_r($pengirim);exit();
    // Akhir mengambil data pengirim

    // Mulai mengambil data pengirim
    if (!empty($pengirim)) {
      $namaDokterPengirim = $pengirim->DOKTER;
      $nomorPengirim = '+62' . substr(trim($pengirim->NOMOR), 1);
    }
    // echo '<pre>';print_r($namaDokterPengirim);exit();
    // Akhir mengambil data pengirim

    // Mulai jam
    if ($jam >= '05' && $jam < '10') {
      $salam = 'pagi';
    } elseif ($jam >= '10' && $jam < '15') {
      $salam = 'siang';
    } elseif ($jam >= '15' && $jam < '19') {
      $salam = 'sore';
    } else {
      $salam = 'malam';
    }
    // Akhir jam

    // Mulai isi persetujuan kasus
    switch ($persetujuanKasus) {
      case '801':
        $isiPersetujuanKasus = 'Penilaian kasus saat ini';
        break;
      case '802':
        $isiPersetujuanKasus = 'Alih rawat';
        break;
      case '803':
        $isiPersetujuanKasus = 'Rawat bersama';
        break;
      default:
        $isiPersetujuanKasus = '-';
        break;
    }
    // Akhir isi persetujuan kasus

    // Mulai membuat pesan ke dokter pengirim
    $chatPengirim = [
      $salam . ', *' . $namaDokterPengirim . '*',
      'ini kami menyampaikan',
      'Anda menerima jawaban untuk *konsultasi* atas nama pasien *' . $namaPasien . ' [' . $nomr . ']*' . ' dari *' . $namaDokterPenjawab . '* pada tanggal *' . date('d-m-Y') . '* dan pukul *' . date('H:i') . '*. Rincian jawabannya adalah: *penemuan:* ' . $penemuan . ', *diagnosis:* ' . $diagnosa . ', *anjuran:* ' . $anjuran . ', dan *setuju untuk:* ' . $isiPersetujuanKasus
    ];
    // echo '<pre>';print_r($chatPengirim);exit();
    // Akhir membuat pesan ke dokter pengirim

    // Mulai membuat pesan ke dokter penjawab
    $chatPenjawab = [
      $salam . ', *' . $namaDokterPenjawab . '*',
      'ini kami menyampaikan',
      'Bahwa *jawaban konsultasi* Anda untuk pasien bernama *' . $namaPasien . ' [' . $nomr . ']*' . ' telah diterima oleh *' . $namaDokterPengirim . '* pada tanggal *' . date('d-m-Y') . '* dan pukul *' . date('H:i') . '*.'
    ];
    // echo '<pre>';print_r($chatPenjawab);exit();
    // Akhir membuat pesan ke dokter penjawab

    // Mulai simpan jawaban
    $data = [
      'id_konsul' => $idKonsul,
      'kunjungan_jawab' => $nokun,
      'backtime' => $backtime,
      'tanggal' => $tanggal,
      'tanggal_update' => $tanggal,
      'dokter_jawab' => $dokterJawab,
      'penemuan' => $penemuan,
      'diagnosa' => $diagnosa,
      'anjuran' => $anjuran,
      'persetujuan_kasus' => $persetujuanKasus,
      'atas_persetujuan' => $atasPersetujuan,
      'verifikasi' => $verifikasi,
      'oleh' => $dokterJawab,
      'status' => $status,
    ];
    // echo '<pre>';print_r($data);exit();
    $this->KonsultasiModel->insertJawabKonsul($data);
    // Akhir simpan jawaban

    // Mulai memperbarui konsultasi
    $update_data = ['status' => $status];
    $where = ['id' => $idKonsul];
    $this->KonsultasiModel->updateHistoryKonsul($where, $update_data, 'medis.tb_konsul');
    // Akhir memperbarui konsultasi

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = ['status' => 'failed'];
    } else {
      $this->db->trans_commit();
      try {
        // Chat untuk pengirim konsul
        $this->whatsapp->send($nomorPengirim, $chatPengirim);
        // Chat untuk penjawab konsul
        $this->whatsapp->send($nomorPenjawab, $chatPenjawab);
      } catch (Exception $e) {
        //throw $th;
      }
      $result = ['status' => 'success'];
    }
    echo json_encode($result);
  }

  public function ubahJawabKonsul()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $idJawab = $post['id_jawab'];
    $backtime = $post['backtime'] ?? null;
    $tanggal = null;

    // Mulai periksa pilihan waktu
    if ($post['backtime'] == '1') {
      $tanggal = date('Y-m-d H:i:s', strtotime($post['tanggal']));
    } else {
      $tanggal = date('Y-m-d H:i:s');
    }
    // Akhir periksa pilihan waktu

    $data = [
      'backtime' => $backtime,
      'tanggal_update' => $tanggal,
      'penemuan' => $post['penemuan'] ?? null,
      'diagnosa' => $post['diagnosa'] ?? null,
      'anjuran' => $post['anjuran'] ?? null,
      'persetujuan_kasus' => $post['persetujuan_kasus'] ?? null,
      'atas_persetujuan' => $post['atas_persetujuan'] ?? null,
    ];

    $where = ['id' => $idJawab];
    $this->KonsultasiModel->updateJawabKonsul($where, $data, 'medis.tb_konsul_jawab');
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = ['status' => 'failed'];
    } else {
      $this->db->trans_commit();
      $result = ['status' => 'success'];
    }
    echo json_encode($result);
  }

  public function verifikasi()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $idJawab = $post['id_jawab'];
    $data = ['verifikasi' => 1];
    $where = ['id' => $idJawab];
    $this->KonsultasiModel->updateJawabKonsul($where, $data, 'medis.tb_konsul_jawab');
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = ['status' => 'failed'];
    } else {
      $this->db->trans_commit();
      $result = ['status' => 'success'];
    }
    echo json_encode($result);
  }

  public function historyKonsul()
  {
    $id_pengguna = $this->session->userdata('id');
    $data = [
      'title' => 'Konsul yang Sudah Dijawab',
      'isi' => 'Pengkajian/konsultasi/notifHistoryKonsul/index',
      'id_pengguna' => $id_pengguna,
      'konsulTerjawab' => $this->KonsultasiModel->konsulTerjawab($id_pengguna),
    ];
    $this->load->view('layout/wrapper', $data);
  }

  public function sidePane()
  {
    $data = [];
    $post = $this->input->post();
    // echo '<pre>';print_r($post);exit();
    $nomr = $post['nomr'];
    $ruangTujuan = $post['ruangTujuan'];

    if (!empty($this->KonsultasiModel->tampilHistoryKonsul($nomr))) {
      $historyKonsul = $this->KonsultasiModel->tampilHistoryKonsul($nomr);
      $data['historyKonsul'] = $historyKonsul;
      $data['isiModal'] = $this->ambilHistory($historyKonsul[0]['ID_KONSUL']);
      $data['ruangTujuan'] = $ruangTujuan;
    }

    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/konsultasi/sidePane/index', $data);
  }

  public function detailSidePane()
  {
    $post = $this->input->post();
    $idKonsul = $post['id_konsul'] ?? null;

    $data = [
      'isiModal' => $this->ambilHistory($idKonsul),
      'idKonsul' => $idKonsul,
      'penilaianKasusSaatIni' => $this->masterModel->referensi(236),
      'ruanganAwal' => $post['ruangan_awal'] ?? null,
      'param' => 'sidePane',
    ];
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/konsultasi/historyKonsultasi/modalDetailHistory', $data);
  }

  public function radioterapi()
  {
    $data = [
      'title' => 'Konsultasi Radioterapi',
      'isi' => 'Pengkajian/konsultasi/radioterapi/index',
    ];
    // echo '<pre>';print_r($data);exit();
    $this->load->view('layout/wrapper', $data);
  }

  public function tabelBelumDijawab()
  {
    $post = $this->input->post();
    $draw = intval($this->input->post('draw'));
    $jawab = $this->KonsultasiModel->belumDijawab($post['smf']);
    $data = [];
    $no = 1;
    // echo '<pre>';print_r($jawab);exit();

    foreach ($jawab as $j) {

      $data[] = [
        $no++,
        date('d/m/Y', strtotime($j['tanggal'])) . ', pukul ' . date('H.i', strtotime($j['tanggal'])),
        $j['DOKTER_PENGIRIM'],
        $j['RUANGAN'] ?: '-',
        $j['SMF_TUJUAN'] ?: '-',
        $j['DOKTER_TUJUAN'] ?: '-',
        "<a href='#modal-belum-konsultasi-" . $post['nama'] . "' class='btn btn-sm btn-block btn-custom waves-effect lihat-belum-konsultasi-" . $post['nama'] . "' data-toggle='modal' data-id='" . $j['ID_KONSUL'] . "-'>
          <i class='fa fa-check'></i> Lihat
        </a>
        <button type='button' data-name='emr.konsul.konsul' data-parameter='{\"ID\":\"" . $j['ID_KONSUL'] . "\"}' class='btn btn-warning btn-block btn-sm tombolCetakan' target='_blank'>
          <i class='fa fa-print'></i> Cetak
        </button>"
      ];
    }

    $output = [
      'draw' => $draw,
      'data' => $data
    ];
    echo json_encode($output);
  }

  public function tabelSudahDijawab()
  {
    $post = $this->input->post();
    $draw = intval($this->input->post('draw'));
    $history = $this->KonsultasiModel->sudahDijawab($post['smf']);
    $data = [];
    $no = 1;
    $dokterTujuan = null;
    // echo '<pre>';print_r($history);exit();

    foreach ($history as $h) {
      // Mulai dokter yang dituju
      if ($h['id_dokter_tujuan'] != 0) {
        $dokterTujuan = $h['DOKTER_TUJUAN'];
      } elseif ($h['dokter_jawab'] != '') {
        $dokterTujuan = $h['DOKTER_PENJAWAB'];
      } else {
        $dokterTujuan = '-';
      }
      // Akhir dokter yang dituju

      $data[] = [
        $no++,
        date('d/m/Y', strtotime($h['tanggal'])) . ', pukul ' . date('H.i', strtotime($h['tanggal'])),
        $h['DOKTER_TUJUAN'],
        $h['RUANGAN'] ?: '-',
        $h['SMF'] ?: '-',
        $dokterTujuan,
        "<a href='#modal-sudah-konsultasi-" . $post['nama'] . "' class='btn btn-sm btn-block btn-custom waves-effect lihat-sudah-konsultasi-" . $post['nama'] . "' data-toggle='modal' data-id='" . $h['ID_KONSUL'] . "-'>
          <i class='fa fa-check'></i> Lihat
        </a>
        <button type='button' data-name='emr.konsul.konsul' data-parameter='{\"ID\":\"" . $h['ID_KONSUL'] . "\"}' class='btn btn-warning btn-block btn-sm tombolCetakan' target='_blank'>
          <i class='fa fa-print'></i> Cetak
        </button>"
      ];
    }

    $output = [
      'draw' => $draw,
      'data' => $data
    ];
    echo json_encode($output);
  }
}

// End of file Konsultasi.php
// Location: ./application/controllers/konsultasi/Konsultasi.php
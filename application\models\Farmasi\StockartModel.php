<?php
defined('BASEPATH') or exit('No direct script access allowed');

class StockartModel extends CI_Model
{
    public function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    public function simpan($data)
    {
        return $this->db->insert('inventory.request_item', $data);
    }

    public function getByNomr($nomr)
    {
        $this->db->where('nokun', $nomr);
        $this->db->where('status !=', 0); // Exclude deleted records
        $this->db->where('category', 2); // Filter by category = 2
        return $this->db->get('inventory.request_item')->result();
    }

    public function getById($id)
    {
        $this->db->where('id', $id);
        $this->db->where('status !=', 0); // Exclude deleted records
        $this->db->where('category', 2); // Filter by category = 2
        return $this->db->get('inventory.request_item')->row();
    }

    public function getAll()
    {
        $this->db->where('category', 2); // Filter by category = 2
        return $this->db->get('inventory.request_item')->result();
    }

    public function update($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->where('category', 2); // Filter by category = 2
        return $this->db->update('inventory.request_item', $data);
    }

    public function hapus($id)
    {
        $this->db->where('id', $id);
        $this->db->where('category', 2); // Filter by category = 2
        return $this->db->delete('inventory.request_item');
    }
    
    public function softDelete($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->where('category', 2); // Filter by category = 2
        return $this->db->update('inventory.request_item', $data);
    }

    public function getByStatus($status)
    {
        $this->db->where('status', $status);
        $this->db->where('category', 2); // Filter by category = 2
        return $this->db->get('inventory.request_item')->result();
    }

    public function countByStatus($status)
    {
        $this->db->where('status', $status);
        $this->db->where('category', 2); // Filter by category = 2
        return $this->db->count_all_results('inventory.request_item');
    }

    public function getByDateRange($start_date, $end_date)
    {
        $this->db->where('created_at >=', $start_date);
        $this->db->where('created_at <=', $end_date);
        $this->db->where('category', 2); // Filter by category = 2
        return $this->db->get('inventory.request_item')->result();
    }

    public function getDataTable($nomr, $nokun = null)
    {
        try {
            $nokun_list = [];
            
            // If nomr is provided, find all nokun from pendaftaran tables
            if (!empty($nomr)) {
                // First get all NOMOR from pendaftaran.pendaftaran where NORM = $nomr
                $this->db->select('pp.NOMOR');
                $this->db->from('pendaftaran.pendaftaran pp');
                $this->db->where('pp.NORM', $nomr);
                $pendaftaran_query = $this->db->get();
                
                if ($pendaftaran_query->num_rows() > 0) {
                    $nomor_pendaftaran_list = [];
                    foreach ($pendaftaran_query->result() as $row) {
                        $nomor_pendaftaran_list[] = $row->NOMOR;
                    }
                    
                    // Then get all NOMOR from pendaftaran.kunjungan where NOPEN in nomor_pendaftaran_list
                    if (!empty($nomor_pendaftaran_list)) {
                        $this->db->select('pk.NOMOR');
                        $this->db->from('pendaftaran.kunjungan pk');
                        $this->db->where_in('pk.NOPEN', $nomor_pendaftaran_list);
                        $kunjungan_query = $this->db->get();
                        
                        if ($kunjungan_query->num_rows() > 0) {
                            foreach ($kunjungan_query->result() as $row) {
                                $nokun_list[] = $row->NOMOR;
                            }
                        }
                    }
                }
            } else if (!empty($nokun)) {
                // If specific nokun is provided, use it
                $nokun_list[] = $nokun;
            }
            
            // Group by id_pengajuan to show unique submissions with joins to pengguna table
            $this->db->select('ri.id_pengajuan, ri.nokun, ri.status, ri.created_at, ri.updated_at, COUNT(ri.id) as jumlah_item, 
                              cb.nama as created_by_name, ub.nama as updated_by_name');
            $this->db->from('inventory.request_item ri');
            $this->db->join('aplikasi.pengguna cb', 'cb.id = ri.created_by', 'left');
            $this->db->join('aplikasi.pengguna ub', 'ub.id = ri.updated_by', 'left');
            
            // Filter by nokun list if available
            if (!empty($nokun_list)) {
                $this->db->where_in('ri.nokun', $nokun_list);
            }
            
            // Exclude deleted records (status = 0) and filter by category = 2
            $this->db->where('ri.status !=', 0);
            $this->db->where('ri.category', 2);
            $this->db->group_by('ri.id_pengajuan, ri.nokun, ri.status, ri.created_at, ri.updated_at, cb.nama, ub.nama');
            $this->db->order_by('ri.created_at', 'DESC');
            $query = $this->db->get();
            
            if ($this->db->error()['code'] != 0) {
                log_message('error', 'Database error in getDataTable: ' . $this->db->error()['message']);
                return [];
            }
            
            return $query->result();
        } catch (Exception $e) {
            log_message('error', 'Exception in getDataTable: ' . $e->getMessage());
            return [];
        }
    }
    
    public function hapusByPengajuan($id_pengajuan)
    {
        $this->db->where('id_pengajuan', $id_pengajuan);
        $this->db->where('category', 2); // Filter by category = 2
        return $this->db->delete('inventory.request_item');
    }
    
    public function softDeleteByPengajuan($id_pengajuan, $data)
    {
        $this->db->where('id_pengajuan', $id_pengajuan);
        $this->db->where('category', 2); // Filter by category = 2
        return $this->db->update('inventory.request_item', $data);
    }
    
    public function getDetailPengajuan($id_pengajuan)
    {
        $this->db->select('ri.*, ib.NAMA as nama_barang, cb.nama as created_by_name, ub.nama as updated_by_name');
        $this->db->from('inventory.request_item ri');
        $this->db->join('inventory.barang ib', 'ib.ID = ri.id_item', 'left');
        $this->db->join('aplikasi.pengguna cb', 'cb.id = ri.created_by', 'left');
        $this->db->join('aplikasi.pengguna ub', 'ub.id = ri.updated_by', 'left');
        $this->db->where('ri.id_pengajuan', $id_pengajuan);
        // Exclude deleted records (status = 0) and filter by category = 2
        $this->db->where('ri.status !=', 0);
        $this->db->where('ri.category', 2);
        $this->db->order_by('ri.id', 'ASC');
        return $this->db->get()->result();
    }
    
    public function getByPengajuan($id_pengajuan)
    {
        $this->db->where('id_pengajuan', $id_pengajuan);
        $this->db->where('status !=', 0); // Exclude deleted records
        $this->db->where('category', 2); // Filter by category = 2
        return $this->db->get('inventory.request_item')->result();
    }
    
    public function getDataTableWithSearch($nomr, $start = 0, $length = 10, $search_value = '')
    {
        try {
            $nokun_list = [];
            
            // If nomr is provided, find all nokun from pendaftaran tables
            if (!empty($nomr)) {
                // First get all NOMOR from pendaftaran.pendaftaran where NORM = $nomr
                $this->db->select('pp.NOMOR');
                $this->db->from('pendaftaran.pendaftaran pp');
                $this->db->where('pp.NORM', $nomr);
                $pendaftaran_query = $this->db->get();
                
                if ($pendaftaran_query->num_rows() > 0) {
                    $nomor_pendaftaran_list = [];
                    foreach ($pendaftaran_query->result() as $row) {
                        $nomor_pendaftaran_list[] = $row->NOMOR;
                    }
                    
                    // Then get all NOMOR from pendaftaran.kunjungan where NOPEN in nomor_pendaftaran_list
                    if (!empty($nomor_pendaftaran_list)) {
                        $this->db->select('pk.NOMOR');
                        $this->db->from('pendaftaran.kunjungan pk');
                        $this->db->where_in('pk.NOPEN', $nomor_pendaftaran_list);
                        $kunjungan_query = $this->db->get();
                        
                        if ($kunjungan_query->num_rows() > 0) {
                            foreach ($kunjungan_query->result() as $row) {
                                $nokun_list[] = $row->NOMOR;
                            }
                        }
                    }
                }
            }
            
            // Base query for counting total records
            $this->db->select('ri.id_pengajuan');
            $this->db->from('inventory.request_item ri');
            $this->db->join('aplikasi.pengguna cb', 'cb.id = ri.created_by', 'left');
            $this->db->join('aplikasi.pengguna ub', 'ub.id = ri.updated_by', 'left');
            
            // Filter by nokun list if available
            if (!empty($nokun_list)) {
                $this->db->where_in('ri.nokun', $nokun_list);
            }
            
            // Exclude deleted records (status = 0) and filter by category = 2
            $this->db->where('ri.status !=', 0);
            $this->db->where('ri.category', 2);
            $this->db->group_by('ri.id_pengajuan');
            $total_records = $this->db->count_all_results();
            
            // Query for filtered records with search
            $this->db->select('ri.id_pengajuan, ri.nokun, ri.status, ri.created_at, ri.updated_at, COUNT(ri.id) as jumlah_item, 
                              cb.nama as created_by_name, ub.nama as updated_by_name');
            $this->db->from('inventory.request_item ri');
            $this->db->join('aplikasi.pengguna cb', 'cb.id = ri.created_by', 'left');
            $this->db->join('aplikasi.pengguna ub', 'ub.id = ri.updated_by', 'left');
            
            // Filter by nokun list if available
            if (!empty($nokun_list)) {
                $this->db->where_in('ri.nokun', $nokun_list);
            }
            
            // Exclude deleted records (status = 0) and filter by category = 2
            $this->db->where('ri.status !=', 0);
            $this->db->where('ri.category', 2);
            
            // Apply search filter if search value is provided
            if (!empty($search_value)) {
                $this->db->group_start();
                $this->db->like('ri.id_pengajuan', $search_value);
                $this->db->or_like('ri.nokun', $search_value);
                $this->db->or_like('cb.nama', $search_value);
                $this->db->or_like('ub.nama', $search_value);
                $this->db->group_end();
            }
            
            $this->db->group_by('ri.id_pengajuan, ri.nokun, ri.status, ri.created_at, ri.updated_at, cb.nama, ub.nama');
            
            // Count filtered records
            $filtered_query = clone $this->db;
            $filtered_records = $filtered_query->count_all_results();
            
            // Apply pagination and ordering
            $this->db->order_by('ri.created_at', 'DESC');
            if ($length != -1) {
                $this->db->limit($length, $start);
            }
            
            $query = $this->db->get();
            
            if ($this->db->error()['code'] != 0) {
                log_message('error', 'Database error in getDataTableWithSearch: ' . $this->db->error()['message']);
                return [
                    'data' => [],
                    'total' => 0,
                    'filtered' => 0
                ];
            }
            
            return [
                'data' => $query->result(),
                'total' => $total_records,
                'filtered' => $filtered_records
            ];
        } catch (Exception $e) {
            log_message('error', 'Exception in getDataTableWithSearch: ' . $e->getMessage());
            return [
                'data' => [],
                'total' => 0,
                'filtered' => 0
            ];
        }
    }

    public function getBarang($search = null)
    {
        $this->db->select('ib.ID, ib.NAMA');
        $this->db->from('inventory.barang ib');
        $this->db->join('inventory.barang_ruangan ibr', 'ibr.BARANG = ib.ID');
        $this->db->where('ibr.RUANGAN', '105050156');
        $this->db->where('ibr.STATUS', 1);
        $this->db->where('ib.STATUS', 1);
        if ($search) {
            $this->db->like('ib.NAMA', $search);
        }
        $this->db->limit(20);
        $query = $this->db->get();
        $result = $query->result();
        
        $data = [];
        foreach ($result as $row) {
            $data[] = [
                'id' => $row->ID,
                'text' => $row->NAMA
            ];
        }
        
        return $data;
    }

    public function isRoomRegistered($id_room)
    {
        $this->db->select('id');
        $this->db->from('inventory.stockart_room');
        $this->db->where('id_room', $id_room);
        $this->db->where('status', 1);
        $query = $this->db->get();
        return $query->num_rows() > 0;
    }
}
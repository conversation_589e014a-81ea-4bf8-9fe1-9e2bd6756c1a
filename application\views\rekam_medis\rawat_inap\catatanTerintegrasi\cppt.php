<?php if($cekPengkajianAwalRanap['pengkajianAwal'] == 0 && $pasien['ID_DPJP'] == $this->session->userdata('iddokter')): ?>
<div class="row" id="rowNotifPengkajianAwalRanap">
    <div class="col-md-12">
        <div class="alert alert-warning text-justify" style="margin-left:-10px"><center>
            <i class="fa fa-info-circle"></i> Silahkan melakukan pengisian Pengkaji<PERSON> Me<PERSON> terlebih dahulu.
            <a class="text-white isiPengkajian" id="isiPengkajian" href="#">Isi Pengkajian Medis</a>
            </center>
        </div>
    </div>
</div>
<?php endif; ?>
<div class="row">
    <div class="col-sm-12">
        <div class="btn-group pull-right m-t-20">
            <button type="button" class="btn btn-success btn-sm cppt waves-effect" data-toggle="tooltip" data-placement="left" data-original-title="Reset Form">
                <i class="fa fa-plus"></i>
            </button>
            <button type="button" class="btn btn-primary btn-sm waves-effect" id="history_cppt" data-toggle="tooltip" data-placement="left" data-original-title="History">
                <i class="fa fa-history"></i> History <span class="badge badge-danger badge-pill" id="count_history_cppt"></span>
            </button>
        </div>
        <h4 class="page-title">CATATAN PERKEMBANGAN PASIEN TERINTREGRASI</h4>
    </div>
</div>

<form method="post" action="tambah" id="formCppt">
    <input type="hidden" name="nokun" id="nokun_cppt" value="<?= $pasien['NOKUN'] ?>">
    <input type="hidden" name="nomr" id="nomr_cppt" value="<?= $pasien['NORM'] ?>">
    <input type="hidden" name="id" id="id_cppt">
    <input type="hidden" name="jenis_ruangan" id="jenis_ruangan" value="<?= $this->session->userdata('profesi') == 9 ? 3 : 2 ?>">
    <input type="hidden" name="ruangan" id="ruangan" value="<?= $pasien['ID_RUANGAN'] ?>">
    <input type="hidden" name="dpjp" id="dpjp" value="<?= $pasien['ID_DPJP'] ?>">

    <div class="form-group" id="info_verif"></div>
    <div class="form-group" id="info_24jam"></div>

    <div class="row form-group dokterAkses">
        <div class="col-sm-12 ">
            <div class="btn-group pull-right m-t-20" style="width: 100%;">
                <select name="cppt_template" id="cppt_template" class="form-control">
                    <option value="0" selected>Tanpa Template</option>
                </select>
                <div class="input-group-append">
                    <button type="button" class="btn btn-warning waves-effect" id="buat_template" data-toggle="tooltip" data-placement="left" aria-label="Buat Template">
                        <i class="fa fa-plus"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label for="hasilPemeriksaan">
            <span><strong>Hasil Pemeriksaan, Analisa, Rencana, Penatalaksanaan pasien</strong></span>
            <span class="text-white"><br>
                (<i>Ditulis dengan SOAP/ADIME, disertai dengan target yang terukur, evaluasi hasil tata laksana dituliskan dalam asesmen</i>)
            </span>
        </label>
    </div>
    <!-- START JENIS -->
    <div class="perawatAkses">
        <div class="row form-group">
            <div class="col-md-2">
                <span>Jenis</span>
            </div>
            <div class="col-md-7">
                <div class="form-group">
                    <div class="radio radio-primary form-check-inline">
                        <input type="radio" name="jenis" id="radio_jenis1" value="1" class="jenis" checked>
                        <label for="radio_jenis1">SOAP</label>
                    </div>
                    <div class="radio radio-primary form-check-inline">
                        <input type="radio" name="jenis" id="radio_jenis2" value="2" class="jenis">
                        <label for="radio_jenis2">ADIME</label>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END JENIS -->
    <div class="form-group">
        <div class="checkbox checkbox-primary">
            <input type="checkbox" name="backdate" id="backdate" value="1">
            <label for="backdate">
                Backdate
            </label>
        </div>
    </div>
    <!-- START TANGGAL PENGISIAN -->
    <div class="row form-group d-none" id="tanggal-backdate">
        <div class="col-md-3">
            <span>Tanggal</span>
        </div>
        <div class="col-md-8">
            <div class="input-group">
                <input type="text" name="tanggal" class="form-control" placeholder="yyyy/mm/dd" value="<?= date('Y-m-d H:i:s') ?>" id="tanggal_cppt">
                <div class="input-group-append">
                    <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                </div>
            </div>
        </div>
    </div>
    <!-- END TANGGAL PENGISIAN -->
    <div id="adime" style="display: none;">
        <div class="form-group">
            <label for="asesmen">A (Asesmen gizi)</label>
            <textarea name="asesmen" id="asesmen" rows="5" class="form-control" placeholder="[ Asesmen ]" required></textarea>
        </div>
        <div class="form-group">
            <label for="diagnosa">D (Diagnosa gizi)</label>
            <textarea name="diagnosa" id="diagnosa" rows="5" class="form-control" placeholder="[ Diagnosa ]" required></textarea>
        </div>
        <div class="row">
            <div class="col-lg-2">
                <label for="status-gizi">Status Gizi</label>
            </div>
            <div class="col-lg-10">
                <div class="form-group">
                    <select name="status_gizi" id="status_gizi" class="form-control select2 status_gizi">
                        <option value="">
                            Kosong
                        </option>
                        <?php foreach ($statusGizi as $sg): ?>
                            <option id="<?= $sg['id_variabel'] ?>" value="<?= $sg['id_variabel'] ?>">
                                <?= $sg['variabel'] ?>
                            </option>
                        <?php endforeach ?>
                    </select>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label for="intervensi">I (Intervensi)</label>
            <textarea name="intervensi" id="intervensi" rows="5" class="form-control" placeholder="[ Intervensi ]" required></textarea>
        </div>
        <div class="row form-group">
            <div class="col-md-1">
                <span>Diet</span>
            </div>
            <div class="col-md-2">
                <input type="text" name="diet" id="diet" class="form-control jarak3" placeholder="[ Diet ]" autocomplete="off">
            </div>
            <div class="col-md-1">
                <span>Bentuk</span>
            </div>
            <div class="col-md-2">
                <input type="text" name="bentuk" id="bentuk" class="form-control jarak3" placeholder="[ Bentuk ]" autocomplete="off">
            </div>
            <div class="col-md-1">
                <span>Route</span>
            </div>
            <div class="col-md-2">
                <input type="text" name="route" id="route" class="form-control jarak3" placeholder="[ route ]" autocomplete="off">
            </div>
        </div>
        <div class="form-row">
            <div class="form-group col-md-3">
                <label for="inputenergi" class="col-form-label">Energi (kalori)</label>
                <div class="input-group mb-2">
                    <input type="text" onkeypress="if ( isNaN(this.value + String.fromCharCode(event.keyCode) )) return false;" name="energi" id="energi" class="form-control" placeholder="[ Energi ]" autocomplete="off">
                    <div class="input-group-prepend">
                        <div class="input-group-text">kalori</div>
                    </div>
                </div>
            </div>

            <div class="form-group col-md-3">
                <label for="inputprotein" class="col-form-label">Protein (gram)</label>
                <div class="input-group mb-2">
                    <input type="text" onkeypress="if ( isNaN(this.value + String.fromCharCode(event.keyCode) )) return false;" name="protein" id="protein" class="form-control" placeholder="[ Protein ]" autocomplete="off">
                    <div class="input-group-prepend">
                        <div class="input-group-text">gram</div>
                    </div>
                </div>
            </div>

            <div class="form-group col-md-3">
                <label for="inputlemak" class="col-form-label">Lemak (gram)</label>
                <div class="input-group mb-2">
                    <input type="text" onkeypress="if ( isNaN(this.value + String.fromCharCode(event.keyCode) )) return false;" name="lemak" id="lemak" class="form-control" placeholder="[ Lemak ]" autocomplete="off">
                    <div class="input-group-prepend">
                        <div class="input-group-text">gram</div>
                    </div>
                </div>
            </div>

            <div class="form-group col-md-3">
                <label for="inputkh" class="col-form-label">KH (gram)</label>
                <div class="input-group mb-2">
                    <input type="text" onkeypress="if ( isNaN(this.value + String.fromCharCode(event.keyCode) )) return false;" name="kh" id="kh" class="form-control" placeholder="[ KH ]" autocomplete="off">
                    <div class="input-group-prepend">
                        <div class="input-group-text">gram</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row form-group">
            <div class="col-md-3">
                <input type="text" name="energi_desc" id="energi_desc" class="form-control jarak3" placeholder="[ Energi ]" autocomplete="off">
            </div>
            <div class="col-md-3">
                <input type="text" name="protein_desc" id="protein_desc" class="form-control jarak3" placeholder="[ Protein ]" autocomplete="off">
            </div>
            <div class="col-md-3">
                <input type="text" name="lemak_desc" id="lemak_desc" class="form-control jarak3" placeholder="[ Lemak ]" autocomplete="off">
            </div>
            <div class="col-md-3">
                <input type="text" name="kh_desc" id="kh_desc" class="form-control jarak3" placeholder="[ KH ]" autocomplete="off">
            </div>

        </div>
        <div class="form-group">
            <label for="monitoring">M (Monitoring)</label>
            <textarea name="monitoring" id="monitoring" rows="5" class="form-control" placeholder="[ Monitoring ]" required></textarea>
        </div>
        <div class="form-group">
            <label for="evaluasi">E (Evaluasi)</label>
            <textarea name="evaluasi" id="evaluasi" rows="5" class="form-control" placeholder="[ Evaluasi ]" required></textarea>
        </div>
    </div>
    <div id="soap">
        <div class="form-group">
            <label for="subyektif">S (Subyektif)</label>
            <main>
                <div style="position: relative;">
                    <textarea name="subyektif" id="subyektif" rows="5" class="form-control hasilRec" placeholder="[ Keluhan Pasien ]" required></textarea>
                    <img class="tombolVoiceRec"></img>
                    <div class="keteranganVoiceRec"></div>
                    <div class="error text-danger" id="error"></div>
                </div>
            </main>
        </div>
        <?php if ($pasien['ID_RUANGAN'] == 105120101 && $this->session->userdata('status') == 1) { ?>
            <input type="hidden" name="id_tokisistas" id="id_tokisistas">
            <h4>
                <a id="modalPanduanRad" class="btn btn-success" data-toggle="modal"><i class="fa fa-book"></i>Buku Panduan RTOG</a>
            </h4>
            <div class="form-group">
                <label for="rtog">RTOG</label>
                <select class="form-control" name="rtog" id="pilihRtog">
                    <option disabled selected> [ Pilih Toksitas Radiasi ]</option>
                    <option value="280">Toksisitas Radiasi Regio Kepala-Leher (RTOG)</option>
                    <option value="281">Toksisitas Radiasi Regio Thorax (RTOG)</option>
                    <option value="282">Toksisitas Radiasi Regio Abdomen (RTOG)</option>
                </select>
            </div>

            <div class="form-group">
                <div id="toksitasRadiasi"></div>
            </div>
        <?php } ?>
        <div class="form-group">
            <label for="obyektif">O (Obyektif)</label>
        </div>
        <!-- START KESADARAN -->
        <div class="perawatAkses">
            <div class="row form-group">
                <div class="col-md-3">
                    <span>Kesadaran</span>
                </div>
                <div class="col-md-9">
                    <input type="hidden" name="id_kesadaran" id="id_kesadaran">
                    <div class="checkbox checkbox-primary">
                        <input type="checkbox" name="kesadaranTerakhir" id="kesadaranTerakhir">
                        <label for="kesadaranTerakhir">
                            Mengambil Data Kesadaran Terakhir<span style="color:#ff6348" id="detailKesadaran"></span>
                        </label>
                    </div>
                    <?php foreach ($kesadaran as $kesadaran): ?>
                        <div class="radio radio-primary jarak2">
                            <input type="radio" name="kesadaran" id="radio<?= $kesadaran['id_variabel'] ?>" value="<?= $kesadaran['id_variabel'] ?>" class="kesadaran">
                            <label for="radio<?= $kesadaran['id_variabel'] ?>"><?= $kesadaran['variabel'] ?></label>
                        </div>
                    <?php endforeach ?>
                </div>
            </div>
        </div>
        <!-- END KESADARAN -->
        <!-- START TANDA VITAL -->
        <div class="row form-group">
            <div class="col-md-3">
                <span>Tanda-Tanda Vital</span>
            </div>
            <div class="col-md-4">
                <input type="hidden" name="id_tb_bb" id="id_tb_bb">
                <div class="checkbox checkbox-primary">
                    <input type="checkbox" name="tbBbTerakhir" id="tbBbTerakhir">
                    <label for="tbBbTerakhir">
                        Mengambil Data TB dan BB Terakhir<span style="color:#ff6348" id="detailTbBb"></span>
                    </label>
                </div>
            </div>
            <div class="col-md-4">
                <input type="hidden" name="id_tanda_vital" id="id_tanda_vital">
                <div class="checkbox checkbox-primary">
                    <input type="checkbox" name="tandaVitalTerakhir" id="tandaVitalTerakhir">
                    <label for="tandaVitalTerakhir">
                        Mengambil Data Tanda Vital Terakhir<span style="color:#ff6348" id="detailTandaVital"></span>
                    </label>
                </div>
            </div>
        </div>
        <div class="row form-group">
            <div class="col-md-3">
                <span>Tekanan Darah (mm/HG)</span>
            </div>
            <div class="col-md-3">
                <input type="number" name="sistolik" id="sistolik" class="form-control jarak3" placeholder="[ Sistolik ]" autocomplete="off" max="999">
            </div>
            <div class="col-md-3">
                <input type="number" name="diastolik" id="diastolik" class="form-control jarak3" placeholder="[ Diastolik ]" autocomplete="off" max="999">
            </div>
        </div>
        <div class="row form-group">
            <div class="col-md-3">
                <span>Pernapasan (x/menit)</span>
            </div>
            <div class="col-md-6">
                <input type="number" name="pernapasan" id="pernapasan" class="form-control jarak3" placeholder="[ Sebutkan Jumlah Pernapasan (x/menit) ]" autocomplete="off" max="99">
            </div>
        </div>
        <div class="row form-group">
            <div class="col-md-3">
                <span>Nadi (x/menit)</span>
            </div>
            <div class="col-md-6">
                <input type="number" name="nadi" id="nadi" class="form-control jarak3" placeholder="[ Sebutkan Jumlah Nadi (x/menit) ]" autocomplete="off" max="500">
            </div>
        </div>
        <div class="row form-group">
            <div class="col-md-3 ">
                <span>Suhu (dalam Celcius)</span>
            </div>
            <div class="col-md-6">
                <input type="number" name="suhu" id="suhu" class="form-control jarak3" placeholder="[ Sebutkan Jumlah Suhu dalam Celcius ]" autocomplete="off" max="99">
            </div>
        </div>
        <div class="row form-group">
            <div class="col-md-3">
                <span>Tinggi Badan/Berat Badan</span>
            </div>
            <div class="col-md-2">
                <input type="number" name="tb" id="tb" class="form-control jarak3" placeholder="[ TB ]" autocomplete="off" max="350">
            </div>
            <div class="col-md-2">
                <input type="number" name="bb" id="bb" class="form-control jarak3" placeholder="[ BB ]" autocomplete="off" max="999">
            </div>
        </div>
        <!-- END TANDA VITAL -->
        <!-- START SKRINING NYERI -->
        <div class="perawatAkses">
            <div class="row form-group">
                <div class="col-md-2">
                    <span>Skrining Nyeri</span>
                </div>
                <!-- START PILIH METODE -->
                <div class="col-md-10">
                    <div class="checkbox checkbox-primary">
                        <input type="hidden" name="id_nyeri" id="id_nyeri">
                        <input type="checkbox" name="nyeriTerakhir" id="nyeriTerakhir">
                        <label for="nyeriTerakhir">
                            Mengambil Data Skrining Nyeri Terakhir<span style="color:#ff6348" id="detailNyeri"></span>
                        </label>
                    </div>
                    <?php foreach ($skriningNyeri as $dataSkriningNyeri): ?>
                        <div class="radio radio-primary jarak2">
                            <input type="radio" name="skrining_nyeri" id="radio_nyeri<?= $dataSkriningNyeri['id_variabel'] ?>" value="<?= $dataSkriningNyeri['id_variabel'] ?>" class="skriningNyeri">
                            <label for="radio_nyeri<?= $dataSkriningNyeri['id_variabel'] ?>"><?= $dataSkriningNyeri['variabel'] ?></label>
                        </div>
                    <?php endforeach ?>
                </div>
                <!-- END PILIH METODE -->
            </div>
            <!-- START METODE -->
            <div class="SkriningNyeriMetode" style="display:none">
                <div class="row form-group">
                    <div class="offset-md-1 col-md-11">
                        <div class="card new-card-sub">
                            <div class="card-header new-card-header-sub">
                                <h6 class="m-0">
                                    <a href="#metode_pilihan" class="text-dark" data-toggle="collapse" aria-expanded="true" aria-controls="metode_pilihan">
                                        Form Metode
                                    </a>
                                </h6>
                            </div>
                            <div class="card-body">
                                <!-- START METODE NRS -->
                                <div id="pilihan_metode_1" style="display:none">
                                    <div class="row form-group">
                                        <div class="offset-md-4">
                                            <img src="<?= base_url('assets/admin/assets/images/metode/nrs.png') ?>" class="img-fluid rounded">
                                        </div>
                                    </div>
                                    <div class="row jarak2">
                                        <div class="offset-md-1 col-md-3">
                                            <span>A. Skala Nyeri</span>
                                        </div>
                                        <div class="col-md-8">
                                            <div class="form-group">
                                                <?php foreach ($skalaNyeriNRS as $dataSkalaNyeriNRS): ?>
                                                    <div class="radio radio-primary form-check-inline">
                                                        <input type="radio" name="skor_nyeri" id="radio_metode_skala_nyeri_nrs<?= $dataSkalaNyeriNRS['id_variabel'] ?>" value="<?= $dataSkalaNyeriNRS['id_variabel'] ?>" class="skorNyeri">
                                                        <label for="radio_metode_skala_nyeri_nrs<?= $dataSkalaNyeriNRS['id_variabel'] ?>"><?= $dataSkalaNyeriNRS['variabel'] ?></label>
                                                    </div>
                                                <?php endforeach ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- END METODE NRS -->
                                <!-- START METODE WONGBAKER -->
                                <div id="pilihan_metode_2" style="display:none">
                                    <div class="row form-group">
                                        <div class="offset-md-4">
                                            <img src="<?= base_url('assets/admin/assets/images/metode/wongbaker.png') ?>" class="img-fluid rounded">
                                        </div>
                                    </div>
                                    <div class="row jarak2">
                                        <div class="offset-md-1 col-md-3">
                                            <span>A. Skala Nyeri</span>
                                        </div>
                                        <div class="col-md-8">
                                            <div class="form-group">
                                                <?php foreach ($skalaNyeriWBR as $dataSkalaNyeriWBR): ?>
                                                    <div class="radio radio-primary form-check-inline">
                                                        <input type="radio" name="skor_nyeri" id="radio_metode_skala_nyeri_wbr<?= $dataSkalaNyeriWBR['id_variabel'] ?>" value="<?= $dataSkalaNyeriWBR['id_variabel'] ?>" class="skorNyeri">
                                                        <label for="radio_metode_skala_nyeri_wbr<?= $dataSkalaNyeriWBR['id_variabel'] ?>"><?= $dataSkalaNyeriWBR['variabel'] ?></label>
                                                    </div>
                                                <?php endforeach ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- END METODE WONGBAKER -->
                                <!-- START METODE FLACC -->
                                <div id="pilihan_metode_3" style="display:none">
                                    <div class="row jarak2">
                                        <div class="offset-md-1 col-md-3">
                                            <span>A. Skala Nyeri</span>
                                        </div>
                                        <div class="col-md-8">
                                            <div class="form-group">
                                                <?php foreach ($skalaNyeriFLACC as $dataSkalaNyeriFLACC): ?>
                                                    <div class="radio radio-primary form-check-inline">
                                                        <input type="radio" name="skor_nyeri" id="radio_metode_skala_nyeri_flacc<?= $dataSkalaNyeriFLACC['id_variabel'] ?>" value="<?= $dataSkalaNyeriFLACC['id_variabel'] ?>" class="skorNyeri">
                                                        <label for="radio_metode_skala_nyeri_flacc<?= $dataSkalaNyeriFLACC['id_variabel'] ?>"><?= $dataSkalaNyeriFLACC['variabel'] ?></label>
                                                    </div>
                                                <?php endforeach ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- END METODE FLACC -->
                                <!-- START METODE BPS -->
                                <div id="pilihan_metode_4" style="display:none">
                                    <div class="row jarak2">
                                        <div class="offset-md-1 col-md-3">
                                            <span>A. Skala Nyeri</span>
                                        </div>
                                        <div class="col-md-8">
                                            <div class="form-group">
                                                <?php foreach ($skalaNyeriBPS as $dataSkalaNyeriBPS): ?>
                                                    <div class="radio radio-primary form-check-inline">
                                                        <input type="radio" name="skor_nyeri" id="radio_metode_skala_nyeri_bps<?= $dataSkalaNyeriBPS['id_variabel'] ?>" value="<?= $dataSkalaNyeriBPS['id_variabel'] ?>" class="skorNyeri">
                                                        <label for="radio_metode_skala_nyeri_bps<?= $dataSkalaNyeriBPS['id_variabel'] ?>"><?= $dataSkalaNyeriBPS['variabel'] ?></label>
                                                    </div>
                                                <?php endforeach ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- END METODE BPS -->
                                <!-- START INTERVENSI NYERI -->
                                <!-- <div class="row jarak2">
                                                <div class="offset-md-1 col-md-3">
                                                    <span>B. Intervensi Nyeri</span>
                                                </div>
                                                <div class="col-md-8">
                                                    <div class="form-group">
                                                        <label for="">Farmakologi (metode pemberian)</label>
                                                        <input type="text" name="farmakologi" id="farmakologi" class="form-control" placeholder="[ Farmakologi (metode pemberian) ]">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row jarak2">
                                                <div class="offset-md-4 col-md-8">
                                                    <div class="form-group">
                                                        <label for="">Non Farmakologi (metode)</label>
                                                        <input type="text" name="non_farmakologi" id="non_farmakologi" class="form-control" placeholder="[ Non Farmakologi (metode) ]">
                                                    </div>
                                                </div>
                                            </div> -->
                                <!-- END INTERVENSI NYERI -->
                                <!-- START EFEK SAMPING  -->
                                <!-- <div class="row jarak2">
                                                <div class="offset-md-1 col-md-3">
                                                    <span>C. Efek Samping</span>
                                                </div>
                                                <div class="col-md-8">
                                                    <?php foreach ($efeksampingNRS as $dataEfekSamping): ?>
                                                    <div class="radio radio-primary form-check-inline jarak2">
                                                        <input type="radio" name="efek_samping" id="radio_metode_efek_samping<?= $dataEfekSamping['id_variabel'] ?>" value="<?= $dataEfekSamping['id_variabel'] ?>" class="efeksampingmetode">
                                                        <label for="radio_metode_efek_samping<?= $dataEfekSamping['id_variabel'] ?>"><?= $dataEfekSamping['variabel'] ?></label>
                                                    </div>
                                                    <?php endforeach ?>
                                                </div>
                                            </div>
                                            <div id="efeksampingmetode" style="display:none">
                                            <div class="row jarak2">
                                                <div class="offset-md-4 col-md-8">
                                                    <div class="form-group">
                                                        <input type="text" name="efek_samping_lain" id="efek_samping" class="form-control" placeholder="[ Sebutkan Efek Samping ]">
                                                    </div>
                                                </div>
                                            </div>
                                            </div> -->
                                <!-- END EFEK SAMPING -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- END METODE -->
            <!-- START NYERI LANJUTKAN PENGKAJIAN -->
            <div class="SkriningNyeriMetode" style="display:none">
                <!-- START PROVOKATIVE -->
                <!-- <div class="row form-group">
                            <div class="offset-md-1 col-md-5">
                            <span>Lanjutan pengkajian:</span>
                            </div>
                        </div>
                        <div class="row form-group">
                            <div class="offset-md-1 col-md-5">
                                <span>P = Provocative: Apa yang memprovokasi nyeri?</span>
                            </div>
                            <div class="col-md-6">
                                <?php foreach ($pengkajianNyeriProvocative as $dataNyeriProvocative): ?>
                                <div class="radio radio-primary form-check-inline jarak2">
                                    <input type="radio" name="provocative" id="radio_provokative<?= $dataNyeriProvocative['id_variabel'] ?>" value="<?= $dataNyeriProvocative['id_variabel'] ?>" class="nyeripropocative" >
                                    <label for="radio_provokative<?= $dataNyeriProvocative['id_variabel'] ?>"><?= $dataNyeriProvocative['variabel'] ?></label>
                                </div>
                                <?php endforeach ?>
                            </div>
                        </div> -->
                <!-- END PROVOKATIVE -->
                <!-- START QUALITY -->
                <!-- <div class="row form-group">
                            <div class="offset-md-1 col-md-5">
                                <span>Q = Quality: Seperti apa rasanya ?</span>
                            </div>
                            <div class="col-md-6">
                                <?php foreach ($pengkajianNyeriQuality as $dataNyeriQuality): ?>
                                <div class="radio radio-primary form-check-inline jarak2">
                                    <input type="radio" name="quality" id="radio_quality<?= $dataNyeriQuality['id_variabel'] ?>" value="<?= $dataNyeriQuality['id_variabel'] ?>" class="nyeriquality">
                                    <label for="radio_quality<?= $dataNyeriQuality['id_variabel'] ?>"><?= $dataNyeriQuality['variabel'] ?></label>
                                </div>
                                <?php endforeach ?>
                            </div>
                        </div>
                        <div id="nyeriquality" style="display:none">
                            <div class="row form-group">
                                <div class="offset-md-6 col-md-6">
                                    <div class="form-group">
                                        <input type="text" name="quality_lainnya" id="quality_nyeri_lainnya" class="form-control" placeholder="[ Sebutkan Lainnya ]">
                                    </div>
                                </div>
                            </div>
                        </div> -->
                <!-- END QUALITY -->
                <!-- START REGIO -->
                <!-- <div class="row form-group">
                            <div class="offset-md-1 col-md-5">
                                <span>R = Regio: Dimana daerah nyeri ?</span>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <input type="text" name="regio" id="nyeriregio" class="form-control jarak3" placeholder="[ Sebutkan Daerah Nyeri ]">
                                </div>
                            </div>
                        </div> -->
                <!-- END REGIO -->
                <!-- START SEVERITY -->
                <!-- <div class="row form-group">
                            <div class="offset-md-1 col-md-5">
                                <span>S = Severity: Berapa skala nyeri ?</span>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <input type="text" name="severity" id="nyeriseverity" class="form-control jarak3" placeholder="[ Sebutkan Skala Nyeri ]">
                                </div>
                            </div>
                        </div> -->
                <!-- END SEVERITY -->
                <!-- START TIME -->
                <!-- <div class="row form-group">
                            <div class="offset-md-1 col-md-5">
                            <span>T = Time: Kapan nyeri datang ?</span>
                            </div>
                            <div class="col-md-6">
                                <?php foreach ($pengkajianNyeriTime as $dataNyeriTime): ?>
                                <div class="radio radio-primary form-check-inline jarak2">
                                    <input type="radio" name="time" id="radio_time<?= $dataNyeriTime['id_variabel'] ?>" value="<?= $dataNyeriTime['id_variabel'] ?>" class="nyeritime">
                                    <label for="radio_time<?= $dataNyeriTime['id_variabel'] ?>"><?= $dataNyeriTime['variabel'] ?></label>
                                </div>
                                <?php endforeach ?>
                            </div>
                        </div>
                        <div id="nyeritime" style="display:none">
                            <div class="row form-group">
                            <div class="offset-md-6 col-md-6">
                                <div class="form-group">
                                <input type="text" name="durasi_nyeri" id="durasi_nyeri" class="form-control"
                                        placeholder="[ Sebutkan Durasi Nyeri ]">
                                </div>
                            </div>
                            </div>
                        </div> -->
                <!-- END TIME -->
            </div>
            <!-- END NYERI LANJUTKAN PENGKAJIAN -->
        </div>
        <!-- END SKRINING NYERI -->
        <div class="form-group">
            <main>
                <div style="position: relative;">
                    <textarea name="obyektif" id="obyektif" rows="5" class="form-control hasilRec" placeholder="[ Pemeriksaan dan Hasil penunjang lainya ]" required></textarea>
                    <img class="tombolVoiceRec"></img>
                    <div class="keteranganVoiceRec"></div>
                    <div class="error text-danger" id="error"></div>
                </div>
            </main>
        </div>
        <div class="form-group perawatAkses">
            <div class="checkbox checkbox-primary pl-0">
                <input type="checkbox" name="ewsBalanceTerakhir" id="ewsBalanceTerakhir">
                <label for="ewsBalanceTerakhir" class="form-check-label">
                    Mengambil Data EWS/PEWS, Balance dan Resiko Jatuh Terakhir<span style="color:#ff6348" id="detailEWSBalance"></span>
                </label>
            </div>
        </div>
        <div class="row dokterAkses">
            <div class="col-lg-12">
                <div class="form-group">
                    <b>Lokalis</b>
                </div>
            </div>
            <div class="col-lg-12">
                <div class="form-group">
                    <b>
                        <a href="<?= base_url('pengkajianAwal/statusSoap/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN']) ?>" class="btn btn-rounded btn-warning btn-block" target="_blank"><i class="fa fa-eye"></i> Lokalis
                        </a>
                    </b>
                </div>
            </div>
        </div>
        <div class="row perawatAkses">
            <div class="col-sm-12">
                <div class="btn-group pull-right m-t-20">
                    <button type="button" class="btn btn-primary btn-sm" id="history_observasi_keperawatan" data-toggle="tooltip" data-placement="left" data-original-title="History">
                        <i class="fa fa-history"></i> History Obeservasi
                    </button>
                </div>
                <h4 class="page-title">A (Analisis)</h4>
            </div>
        </div>
        <!-- START ASUHAN KEPERAWATAN-->
        <div class="perawatAkses">
            <div class="row form-group">
                <?php foreach ($formAsuhanKeperawatan as $formAsuhanKeperawatan): ?>
                    <div class="col-md-4">
                        <div class="checkbox checkbox-primary form-check-inline jarak2">
                            <input type="checkbox" name="form_asuhan_keperawatan[]" id="formasuhankeperawatan<?= $formAsuhanKeperawatan['id_variabel'] ?>" value="<?= $formAsuhanKeperawatan['id_variabel'] ?>" class="form_asuhan_keperawatan">
                            <label for="formasuhankeperawatan<?= $formAsuhanKeperawatan['id_variabel'] ?>"><?= $formAsuhanKeperawatan['variabel'] ?></label>
                        </div>
                    </div>
                <?php endforeach ?>
            </div>
        </div>
        <label class="perawatAkses" for="perencanaan">P (Perencanaan) & Instruksi, Paraf & Nama Pemberi Asuhan Termasuk Paska Bedah Prosedur<span class="text-white"> (<i>Instruksi ditulis dengan rinci dan jelas</i>)</span>
        </label>
        <div class="form-group dokterAkses">
            <label for="analisis">A (Analisis)</label>

            <main>
                <div style="position: relative;">
                    <textarea name="analisis" id="analisis" rows="5" class="form-control hasilRec" placeholder="[ Analisis ]"></textarea>
                    <img class="tombolVoiceRec"></img>
                    <div class="keteranganVoiceRec"></div>
                    <div class="error text-danger" id="error"></div>
                </div>
            </main>
            <div class="error text-danger" id="error"></div>
        </div>
        <div class="form-group dokterAkses" id="perencanaanPerawat">
            <label for="perencanaan">P (Perencanaan)</label>

            <main>
                <div style="position: relative;">
                    <textarea name="perencanaan" id="perencanaan" rows="5" class="form-control hasilRec" placeholder="[ Perencanaan ]"></textarea>
                    <img class="tombolVoiceRec"></img>
                    <div class="keteranganVoiceRec"></div>
                    <div class="error text-danger" id="error"></div>
                </div>
            </main>
            <div class="error text-danger" id="error"></div>
        </div>
        <div class="form_asuhan" style="display:none"></div>
        <div class="form-group">
            <label for="instruksi">I (Instruksi)</label>
            <button class="btn btn-sm btn-custom waves-effect" id="buat-eresep-cppt" status="aktif">Buat Resep</button>

            <main>
                <div style="position: relative;">
                    <textarea name="instruksi" id="instruksi" rows="5" class="form-control hasilRec" placeholder="[ Instruksi ]"></textarea>
                    <img class="tombolVoiceRec"></img>
                    <div class="keteranganVoiceRec"></div>
                    <div class="error text-danger" id="error"></div>
                </div>
            </main>
            <div class="error text-danger" id="error"></div>
            <div class="checkbox checkbox-primary">
                <input type="checkbox" name="resepTerakhir" id="resepTerakhir">
                <label for="resepTerakhir">
                    Mengambil Resep Terakhir
                </label>
            </div>
        </div>
        <hr>
        <div class="form-group">
            <label for="tbak">TBAK</label>
        </div>
        <div class="row">
            <table class="table col-sm-12">
                <thead>
                    <tr>
                        <th>Dokter TBAK</th>
                        <th>Pesan yang dilaporkan dan instruksi</th>
                        <th>#</th>
                    </tr>
                </thead>
                <tbody id="tbak"></tbody>
                <tfoot>
                    <tr>
                        <td colspan="3">
                            <div class="form-group">
                                <button class="btn btn-primary btn-block tambah-tbak"> <i class="fa fa-plus"></i></button>
                            </div>
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
        <!-- END ASUHAN KEPERAWATAN-->
    </div>
    <?php if (isset($pasien['ID_RUANGAN']) && (in_array($pasien['ID_RUANGAN'], ['105140101', '105140102']) || $pasien['JENIS_KUNJUNGAN'] == 3)) {  ?>
        <fieldset style="">
            <legend style="">Input Tindakan</legend>
            <?php if ($pasien['status_pasien'] == "2") { ?>
                <div class="alert alert-warning" role="alert">
                    Pengisian Tindakan tidak bisa dilakukan karena <strong>pasien sudah final.</strong>
                </div>
            <?php
            }
            if ($pasien['status_pasien'] == '1') {
            ?>
                <div class="row form-group">
                    <div class="col-md-3">
                        <span>Pilih Nama Tindakan</span>
                    </div>
                    <div class="col-md-7">
                        <select name="select_tindakan" id="select_tindakan" class="form-control select_tindakan"></select>
                    </div>
                    <div class="col-md-2">
                        <span type="button" class="btn btn-warning btn-block " onclick="tambah_tindakan()"><i class="fa fa-plus"></i></span>
                    </div>
                </div>
                <div class="row">
                    <table class="table table-striped table-bordered table_tindakan">
                        <thead>
                            <tr>
                                <th>No.</th>
                                <th>Nama Tindakan</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            <?php } ?>
        </fieldset>
        <br>
    <?php } ?>

    <!-- Mulai kontrol kembali -->
    <div class="form-group d-none">
        <div class="card new-card">
            <div class="card-header new-card-header"><em>Follow Up</em> Pasca <em>Treatment</em></div>
            <div class="card-body">
                <div class="form-group">
                    <div class="alert alert-info">
                        <i class="fa-solid fa-circle-info"></i> Diisi dengan jadwal rencana kunjungan kembali <em>(follow up)</em> berikutnya
                    </div>
                </div>
                <div class="row form-group">
                    <label for="kontrol-cppt" class="col-md-5 col-form-label"><em>Follow Up</em> Jadwal Rencana Kunjungan Berikutnya</label>
                    <div class="col-md">
                        <select name="kontrol" id="kontrol-cppt" class="form-control">
                            <?php foreach ($kontrolKembali as $kk): ?>
                                <option value="<?= $kk['id_variabel'] ?>" id="kontrol-cppt-<?= $kk['id_variabel'] ?>"><?= $kk['variabel'] ?></option>
                            <?php endforeach ?>
                        </select>
                    </div>
                </div>
                <div class="row form-group">
                    <label for="tanggal-kontrol-cppt" class="col-md-5 col-form-label">Tanggal Kontrol</label>
                    <div class="col-md">
                        <div class="input-group">
                            <input class="form-control" id="tanggal-kontrol-cppt" name="tanggal_kontrol" type="date" value="" readonly>
                            <div class="input-group-append">
                                <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Akhir kontrol kembali -->
    <!-- START BUTTON SIMPAN -->
    <div class="row">
        <div class="offset-10 col-lg-2">
            <div class="form-group">
                <button class="btn btn-sm btn-block btn-primary waves-effect simpan_cppt" id="simpan_cppt"><i class="fa fa-save"></i> Simpan</button>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md">
            <button class="btn btn-warning btn-rounded btn-block waves-effect" id="verif_cppt"><span style="color:black;">VERIFIKASI CPPT</span></button>
        </div>
    </div>
    <!-- END BUTTON SIMPAN -->
    <hr>
    <h3 class="header-title">Keterangan</h3>
    <ul>
        <li>S (Subyektif): Keluhan pasien</li>
        <li>O (Obyektif): Pemeriksaan dan hasil penunjang lainnya</li>
        <li>A (Analisis): Analisis yang didapat</li>
        <li>P (Perencanaan): Rencana tindakan/pengobatan dan target yang diharapkan</li>
    </ul>
    <p class="text-danger font-13">
        * Semua pemberi asuhan menulis tentang perkembangan pasien dan semua membaca seluruh rencana perawatan pasien
    </p>
</form>

<div id="viewInputDiagnosisCppt" class="modal fade" role="dialog">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div id="hasilInputDiagnosisCppt">
                <form class="form" id="formInputDiagnosisCppt" autocomplete="off">
                    <div class="modal-header d-flex flex-column">
                        <h3 class="modal-title mt-0 text-center w-100 fw-bold">Diagnosa Pasien</h3>
                        <p class="text-center w-100 mt-2">Bukti Layanan BPJS</p>
                        <div class="row">
                            <div class="md-6">
                                Tanggal Pendaftaran :
                            </div>
                            <div class="md-6">
                                <?= isset($pasien['TANGGAL_DAFTAR']) ? date('d-m-Y H:i:s', strtotime($pasien['TANGGAL_DAFTAR'])) : null ?>
                            </div>
                        </div>
                        <!-- Tombol Tambah Diagnosis -->
                        <button type="button" class="btn btn-info waves-effect mt-3" id="tombolTambahDiagnosisCppt">
                            <i class="fa fa-plus"></i> Tambah Diagnosis
                        </button>
                    </div>
            </div>
            <div class="modal-body text-white" style="max-height: calc(100vh - 200px); overflow-y: auto;">
                <input type="hidden" id="nomrformInputDiagnosisCppt" value="<?= $pasien['NORM'] ?>">
                <input type="hidden" id="nopenformInputDiagnosisCppt" value="<?= $pasien['NOPEN'] ?>">
                <input type="hidden" id="nokunformInputDiagnosisCppt" value="<?= $pasien['NOKUN'] ?>">
                <div class="table-responsive">
                    <table id="tabelDiagnosisCppt" class="table table-bordered table-hover table-custom" cellspacing="0" width="100%">
                        <thead>
                            <tr class="table-tr-custom">
                                <th>No.</th>
                                <th>Diagnosis</th>
                                <th>Pilih</th>
                            </tr>
                        </thead>
                        <tbody id="hasilTambahDiagnosisCppt"></tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary waves-effect" id="simpanModalCppt">Simpan</button>
            </div>
        </div>
    </div>
</div>
<script src="<?= base_url() ?>assets/admin/assets/speech/script.js"></script>
<script>
    $(document).ready(function() {

        var userakses = <?= $this->session->userdata('config')['cppt'] ?>;
        $('#tanggal_cppt').datetimepicker({
            format: 'Y-m-d H:i:s'
        });

        $('#count_history_cppt').html(getJSON("<?= base_url('rekam_medis/rawat_inap/catatanTerintegrasi/Cppt/action/count') ?>", {
            nomr: "<?= $pasien['NORM'] ?>"
        }));
        loadnyeri();
        aksesCppt();
        $('#nyeriTerakhir').click(function() {
            loadnyeri("<?= $pasien['NOKUN'] ?>");
        });

        $('.jenis').on('change', function() {
            var id = $(this).val();
            if (id == '1') {
                $('#soap').css("display", "block");
                $('#adime').css("display", "none");
            } else if (id == '2') {
                $('#soap').css("display", "none");
                $('#adime').css("display", "block");
            }
        });

        // Mulai kontrol kembali
        $('#kontrol-cppt').select2({
            placeholder: '[ Pilih jadwal kontrol kembali ]'
        }).change(function() {
            if ($(this).val() === '6248') {
                $('#tanggal-kontrol-cppt').attr('readonly', true).val(null);
            } else {
                if ($(this).val() !== '6247') {
                    if ($(this).val() === '6243') {
                        $('#tanggal-kontrol-cppt').attr('readonly', true).val(moment().add(1, 'month').format('YYYY-MM-DD'));
                    } else if ($(this).val() === '6244') {
                        $('#tanggal-kontrol-cppt').attr('readonly', true).val(moment().add(3, 'months').format('YYYY-MM-DD'));
                    } else if ($(this).val() === '6245') {
                        $('#tanggal-kontrol-cppt').attr('readonly', true).val(moment().add(6, 'months').format('YYYY-MM-DD'));
                    } else if ($(this).val() === '6246') {
                        $('#tanggal-kontrol-cppt').attr('readonly', true).val(moment().add(1, 'year').format('YYYY-MM-DD'));
                    }
                } else if ($(this).val() === '6247') {
                    $('#tanggal-kontrol-cppt').removeAttr('readonly', true);
                }
            }
        });
        // Akhir kontrol kembali

        function aksesCppt(jenis = 0, verif = 0) {
            var smf = "<?= $this->session->userdata('smf') ?>";
            var profesi = "<?= $this->session->userdata('profesi') ?>";
            if (jenis != 0) {
                if (jenis == 1) {
                    $('.perawatAkses').css('display', 'none');
                    // if(smf == 31 || profesi == 5){
                    //     $('.perawatAkses#tbakAkses').css('display','block');
                    // }
                    $('.dokterAkses').css('display', 'block');
                    $('.form_asuhan').css('display', 'none');
                    $('#verif_cppt').css('display', 'none');
                    $('#simpan_cppt').css('display', 'block');
                    if (userakses == 2) {
                        $('#simpan_cppt').css('display', 'none');
                        $('#formCppt :input').attr('readonly', true);
                    }
                } else if (jenis == 2 || jenis == 3) {
                    $('.perawatAkses').css('display', 'block');
                    $('.dokterAkses').css('display', 'none');
                    $('#verif_cppt').css('display', 'none');
                    $('#simpan_cppt').css('display', 'block');
                    if (userakses == 1) {
                        if (jenis == 2) {
                            $('#verif_cppt').css('display', 'block');
                        }
                        $('#simpan_cppt').css('display', 'none');
                        $('#formCppt :input').attr('readonly', true);
                    }
                    loadasuhan();

                    if (verif == 1) {
                        $('#verif_cppt').css('display', 'none');
                        $('#simpan_cppt').css('display', 'none');
                    }
                }
            } else {
                if (userakses == 1) {
                    $('.perawatAkses').css('display', 'none');
                    // if(smf == 31 || profesi == 5){
                    //     $('.perawatAkses#tbakAkses').css('display','block');
                    // }
                    tBbB("<?= $pasien['NOKUN'] ?>");
                    $("input[id=tbBbTerakhir]").attr('checked', true);
                    $('#verif_cppt').css('display', 'none');

                    tanda_vital("<?= $pasien['NOKUN'] ?>");
                    $("input[id=tandaVitalTerakhir]").attr('checked', true);
                } else if (userakses == 2) {
                    // $('.perawatAkses').css('display','block');
                    // if(smf == 31 || profesi == 5){
                    //     $('.perawatAkses#tbakAkses').css('display','block');
                    // }
                    loadasuhan();
                    $('.dokterAkses').css('display', 'none');
                    $('#verif_cppt').css('display', 'none');
                }
            }
        }

        function load(id = 0) {
            if (id != 0) {
                var detail = getJSON("<?= base_url('rekam_medis/rawat_inap/catatanTerintegrasi/Cppt/action/ambil') ?>", {
                    id: id
                });

                document.getElementById('formCppt').action = 'ubah';

                $("input[class=jenis]").attr('checked', false);
                var jenis = (detail.data['pemberi_cppt'] == 3) ? 2 : 1;
                $('#id_cppt').val(detail.data['IDCPPT']);
                $('#jenis_ruangan').val(detail.data['jenis']);
                $('#nokun_cppt').val(detail.data['nokun']);
                $("input[class=jenis][value=" + jenis + "]").attr('checked', true).trigger("change");
                if (detail.data['status_backdate'] == 1) {
                    $("input[id=backdate]").attr('checked', true);
                    $('#tanggal-backdate').removeClass('d-none');
                    $('#tanggal_cppt').val(detail.data['tanggal']);
                } else {
                    $("input[id=backdate]").attr('checked', false);
                    $('#tanggal-backdate').addClass('d-none');
                    $('#tanggal_cppt').val("");
                }

                $('#backdate').attr('disabled', true);
                $('#tanggal_cppt').attr('disabled', true);
                $('#subyektif').val(detail.data['subyektif']);
                $('#obyektif').val(detail.data['obyektif']);
                $('#analisis').val(detail.data['analisis']);
                $('#perencanaan').val(detail.data['perencanaan']);
                $('#instruksi').val(detail.data['instruksi']);

                $('#asesmen').val(detail.data['asesmen_gizi']);
                $('#diagnosa').val(detail.data['diagnosa_gizi']);
                $('#status_gizi').val(detail.data['status_gizi']);
                $('#status_gizi').trigger('change');
                $('#intervensi').val(detail.data['intervensi']);
                $('#diet').val(detail.data['diet']);
                $('#bentuk').val(detail.data['bentuk']);
                $('#route').val(detail.data['route']);
                $('#energi').val(detail.data['energi']);
                $('#protein').val(detail.data['protein']);
                $('#lemak').val(detail.data['lemak']);
                $('#kh').val(detail.data['kh']);
                $('#energi_desc').val(detail.data['energi_desc']);
                $('#protein_desc').val(detail.data['protein_desc']);
                $('#lemak_desc').val(detail.data['lemak_desc']);
                $('#kh_desc').val(detail.data['kh_desc']);
                $('#monitoring').val(detail.data['monitoring']);
                $('#evaluasi').val(detail.data['evaluasi']);
                $('#formCppt :input').attr('readonly', false);

                // Mulai ambil kontrol
                $('#kontrol-cppt').val(detail.kontrol['KONTROL']).trigger('change');
                $('#tanggal-kontrol-cppt').val(detail.kontrol['TANGGAL_KONTROL']);
                // Akhir ambil kontrol

                tBbB(detail.data['tb_bb'], 1);
                $("input[id=tbBbTerakhir]").attr('checked', true);

                tanda_vital(detail.data['tanda_vital'], 1);
                $("input[id=tandaVitalTerakhir]").attr('checked', true);

                aksesCppt(detail.data['pemberi_cppt'], detail.data['status_verif']);
                if (userakses == 2 || detail.data['pemberi_cppt'] == 2) {
                    kesadaran(detail.data['kesadaran'], 1);
                    $("input[id=kesadaranTerakhir]").attr('checked', true);

                    $('#nyeriTerakhir').attr('checked', true);
                    loadnyeri(detail.data['skrining_nyeri'], 1);

                    var asuhan = getJSON("<?= base_url('rekam_medis/medis/getAsuhanKeperawatanCppt') ?>", {
                        id: detail.data['IDCPPT']
                    });
                    $('.form_asuhan').html('');

                    $(":checkbox[class=form_asuhan_keperawatan]").attr('checked', false);

                    if (asuhan.data['id_asuhan'] != null) {
                        $.each(JSON.parse(asuhan.data['parent']), function(index, value) {
                            $(":checkbox[class=form_asuhan_keperawatan][value=" + value + "]").attr('checked', true);
                        });
                    } else {
                        $(":checkbox[class=form_asuhan_keperawatan][value=1471]").attr('checked', true);
                    }
                    $(".form_asuhan_keperawatan").trigger("change");

                    setTimeout(function() {
                        $(":checkbox[name='asuhankeperawatancppt[]']").attr('checked', false);
                        if (asuhan.data['id_asuhan'] != null) {
                            $.each(asuhan.data['id_asuhan'].split(','), function(index, value) {
                                $(":checkbox[name='asuhankeperawatancppt[]'][value=" + value + "]").attr('checked', true);
                            });
                        }
                        $('#modal').modal('hide');
                    }, 1000);

                    var detailTBAK = getJSON("<?= base_url('rekam_medis/rawat_inap/catatanTerintegrasi/Cppt/action/getTbak') ?>", {
                        id_cppt: id
                    });
                    $('#tbak').html('');
                    $.each(detailTBAK.data, function(index, element) {
                        var html = '<tr id="tbak-' + element.id + '">' +
                            '<td>' + element.dokter_tbak +
                            '</td>' +
                            '<td>' + element.instruksi_tbak +
                            '</td>' +
                            '<td>' +
                            '<div class="form-group">' +
                            '<button class="btn btn-danger konfirmasi-tbak" data-id="' + element.id + '" > <i class="fa fa-times"></i></button>' +
                            '</div>' +
                            '</td>' +
                            '</tr>';
                        $('#tbak').append(html);
                    });
                }

                var smf = "<?= $this->session->userdata('smf') ?>";
                var ruangan = <?= $pasien['ID_RUANGAN'] ?> || '';
                var profesi = "<?= $this->session->userdata('profesi') ?>";
                if (userakses == 1) {
                    var detailTBAK = getJSON("<?= base_url('rekam_medis/rawat_inap/catatanTerintegrasi/Cppt/action/getTbak') ?>", {
                        id_cppt: id
                    });
                    $('#tbak').html('');
                    $.each(detailTBAK.data, function(index, element) {
                        var html = '<tr id="tbak-' + element.id + '">' +
                            '<td>' + element.dokter_tbak +
                            '</td>' +
                            '<td>' + element.instruksi_tbak +
                            '</td>' +
                            '<td>' +
                            '<div class="form-group">' +
                            '<button class="btn btn-danger konfirmasi-tbak" data-id="' + element.id + '" > <i class="fa fa-times"></i></button>' +
                            '</div>' +
                            '</td>' +
                            '</tr>';
                        $('#tbak').append(html);
                    });

                    if (ruangan == 105120101) {
                        var getRTOG = getJSON("<?= base_url('rekam_medis/rawat_inap/catatanTerintegrasi/Cppt/action/getRTOG') ?>", {
                            id: id
                        });
                        $("#pilihRtog").val(getRTOG.dataToksitas['rtog']).change();
                        setTimeout(() => {
                            $(":radio[value=" + getRTOG.dataToksitas['jenis_tokisisitas'] + "]").attr('checked', true);
                            $(":radio[value=" + getRTOG.dataToksitas['kemoradiasi'] + "]").attr('checked', true).change();
                            $('input[name=deskKemoradiasi]').val(getRTOG.dataToksitas['kemoradiasi_lainnya']);
                            $('input[name=id_tokisistas]').val(getRTOG.dataToksitas['id']);

                            $.each(getRTOG.dataToksitasJaringan, function(index, value) {
                                $("#gradeToksitas" + value['id_toksisitas_jaringan'] + "").val(value['grade']);
                            });
                        }, 1000);

                    }
                }

                $('#info_verif').html('');
                $('#info_24jam').html('');
                if (detail.data['pemberi_cppt'] == 2) {
                    $('#info_verif').html('<div class="alert alert-danger"><strong>Informasi!</strong> CPPT belum diverifikasi.</div>');
                    if (detail.data['status_verif'] == 1) {
                        $('#info_verif').html('<div class="alert alert-success"><marquee behavior="" direction="left"><strong>Informasi!</strong> CPPT sudah diverifikasi oleh <b>' + detail.data["VERIFOLEH"] + '</b>.</marquee></div>');
                        $('#formCppt :input').attr('readonly', true);
                    }
                }

                if (detail.data['STATUS_EDIT_CPPT'] == 0) {
                    $('#info_24jam').html('<div class="alert alert-warning"><marquee behavior="" direction="right"><strong>Informasi!</strong> Tidak dapat di ubah, Sudah lebih dari 1x24 jam. Terima kasih sudah mengisi CPPT.</marquee></div>');
                    $('#simpan_cppt').css('display', 'none');
                    $('#formCppt :input').attr('readonly', true);
                }

                var user = "<?= $this->session->userdata('id') ?>";
                if (detail.data['oleh'] != user) {
                    $('#simpan_cppt').css('display', 'none');
                    $('#formCppt :input').attr('readonly', true);
                    $('.konfirmasi-tbak').css('display', 'none');
                }
            }
        }

        function loadnyeri(id, status = 0) {
            if (id) {
                if ($('#nyeriTerakhir').is(':checked')) {
                    if (status == 1) {
                        var nyeri = getJSON("<?= base_url('rekam_medis/rawat_inap/keperawatan/PemantauanNyeri/action/ambilNyeri') ?>", {
                            id: id
                        });
                    } else {
                        var nyeri = getJSON("<?= base_url('rekam_medis/rawat_inap/keperawatan/PemantauanNyeri/action/ambilNyeri') ?>", {
                            nokun: id
                        });
                    }

                    if (nyeri.data != null) {
                        $('#detailNyeri').html(' (Dibuat oleh ' + nyeri.data['USER'] + ' pada ' + moment(nyeri.data['TANGGAL']).format("LLL") + ' sumber dari ' + nyeri.data['DATA'] + ')');
                        $('#id_nyeri').val(nyeri.data['ID']);
                        $("input[class=skriningNyeri][value=" + nyeri.data['id_metode'] + "]").attr('checked', true);
                        $("input[class=skorNyeri]").attr('onclick', 'javascript: return false;');
                        $("input[class=skriningNyeri]").attr('onclick', 'javascript: return false;');
                        $("input[class=skorNyeri][value=" + nyeri.data['id_skor'] + "]").attr('checked', true);
                    } else {
                        toastr.warning('Data Nyeri Tidak Tersedia');
                    }

                    // $('#farmakologi').val(nyeri.data['farmakologi']);
                    // $('#non_farmakologi').val(nyeri.data['non_farmakologi']);
                    // $("input[class=efeksampingmetode][value="+nyeri.data['efek_samping']+"]").attr('checked',true);
                    // $('#efek_samping').val(nyeri.data['ket_efek_samping']);
                } else {
                    // $('.SkriningNyeriMetode')[0].reset();
                    $('#detailNyeri').html('');
                    $('#id_nyeri').val('');
                    $("input[class=skorNyeri]").attr('onclick', 'javascript: return true;').attr('checked', false);
                    $("input[class=skriningNyeri]").attr('onclick', 'javascript: return true;').attr('checked', false);
                    // $('.SkriningNyeriMetode input[type="text"]').val('');
                    // $('.SkriningNyeriMetode input[type="radio"]').attr('checked', false);
                    // $('.skriningNyeri').attr('checked', false);
                }
            }

            //Skrining Nyeri Metode
            $('.skriningNyeri').on('change', function() {
                var id = $(this).val();
                if (id == 18 || id == 19 || id == 347 || id == 348) {
                    $('.SkriningNyeriMetode').css("display", "block");
                    if (id == 18) {
                        $('#pilihan_metode_1').css("display", "block");
                    } else {
                        $('#pilihan_metode_1').css("display", "none");
                    }
                    if (id == 19) {
                        $('#pilihan_metode_2').css("display", "block");
                    } else {
                        $('#pilihan_metode_2').css("display", "none");
                    }
                    if (id == 347) {
                        $('#pilihan_metode_3').css("display", "block");
                    } else {
                        $('#pilihan_metode_3').css("display", "none");
                    }
                    if (id == 348) {
                        $('#pilihan_metode_4').css("display", "block");
                    } else {
                        $('#pilihan_metode_4').css("display", "none");
                    }
                } else {
                    $('.SkriningNyeriMetode').css("display", "none");
                }
            });

            if ($('.skriningNyeri:checked').val() == 18 || $('.skriningNyeri:checked').val() == 19 || $('.skriningNyeri:checked').val() == 347 || $('.skriningNyeri:checked').val() == 348) {
                $('.SkriningNyeriMetode').css("display", "block");
                if ($('.skriningNyeri:checked').val() == 18) {
                    $('#pilihan_metode_1').css("display", "block");
                } else {
                    $('#pilihan_metode_1').css("display", "none");
                }
                if ($('.skriningNyeri:checked').val() == 19) {
                    $('#pilihan_metode_2').css("display", "block");
                } else {
                    $('#pilihan_metode_2').css("display", "none");
                }
                if ($('.skriningNyeri:checked').val() == 347) {
                    $('#pilihan_metode_3').css("display", "block");
                } else {
                    $('#pilihan_metode_3').css("display", "none");
                }
                if ($('.skriningNyeri:checked').val() == 348) {
                    $('#pilihan_metode_4').css("display", "block");
                } else {
                    $('#pilihan_metode_4').css("display", "none");
                }
            } else {
                $('.SkriningNyeriMetode').css("display", "none");
            }

            // Efek Samping Nyeri
            // $('.efeksampingmetode').on('change', function () {
            //     var id = $(this).val();
            //     if (id == 338) {
            //         $('#efeksampingmetode').css("display", "block");
            //     } else {
            //         $('#efeksampingmetode').css("display", "none");
            //     }
            // });

            // if ($(".efeksampingmetode:checked").val() == 338) {
            //     $('#efeksampingmetode').css('display', 'block');
            // } else {
            //     $('#efeksampingmetode').css("display", "none");
            // }

            //Skrining Nyeri Quality NRS
            // $('.nyeriquality').on('change', function () {
            //     var id = $(this).val();
            //     if (id == 26) {
            //         $('#nyeriquality').css("display", "block");
            //     } else {
            //         $('#nyeriquality').css("display", "none");
            //     }
            // });
            // if ($(".nyeriquality:checked").val() == 26) {
            //     $('#nyeriquality').css('display', 'block');
            // } else {
            //     $('#nyeriquality').css("display", "none");
            // }

            //Skrining Nyeri Time NRS
            // $('.nyeritime').on('change', function () {
            //     var id = $(this).val();
            //     if (id == 31) {
            //         $('#nyeritime').css("display", "block");
            //     } else {
            //         $('#nyeritime').css("display", "none");
            //     }
            // });
            // if ($(".nyeritime:checked").val() == 31) {
            //     $('#nyeritime').css('display', 'block');
            // } else {
            //     $('#nyeritime').css("display", "none");
            // }
        }

        function loadasuhan() {
            let id_cppt = "<?= $pasien['ID_CPPT_TERAKHIR'] ?? null ?>";
            let id_emr = "<?= $pasien['ID_EMR_KEPERAWATAN_DEWASA_RI'] ?? null ?>";
            if (id_cppt != '') {
                var asuhan2 = getJSON("<?= base_url('rekam_medis/medis/getAsuhanKeperawatanCppt') ?>", {
                    id: id_cppt
                });
            } else if (id_emr != '') {
                var asuhan2 = getJSON("<?= base_url('rekam_medis/medis/getAsuhanKeperawatanPengkajian') ?>", {
                    id: id_emr
                });
            }

            if (asuhan2) {
                $('.form_asuhan').html('');
                $(":checkbox[class=form_asuhan_keperawatan]").attr('checked', false);

                if (asuhan2.data['id_asuhan'] != null) {
                    $.each(JSON.parse(asuhan2.data['parent']), function(index, value) {
                        $(":checkbox[class=form_asuhan_keperawatan][value=" + value + "]").attr('checked', true);
                    });
                } else {
                    $(":checkbox[class=form_asuhan_keperawatan][value=1471]").attr('checked', true);
                }

                setTimeout(function() {
                    $(".form_asuhan_keperawatan").trigger("change");
                }, 100);

                setTimeout(function() {
                    $(":checkbox[name='asuhankeperawatancppt[]']").attr('checked', false);
                    if (asuhan2.data['id_asuhan'] != null) {
                        $.each(asuhan2.data['id_asuhan'].split(','), function(index, value) {
                            $(":checkbox[name='asuhankeperawatancppt[]'][value=" + value + "]").attr('checked', true);
                        });
                    }
                }, 1000);
            }
        }

        $('#backdate').click(function() {
            if ($(this).is(':checked')) {
                $('#tanggal-backdate').removeClass('d-none');
            } else {
                $('#tanggal-backdate').addClass('d-none');
            }
        });

        $('#tbBbTerakhir').click(function() {
            if ($(this).is(':checked')) {
                tBbB("<?= $pasien['NOKUN'] ?>");
            } else {
                $('#detailTbBb').html('');
                $('#id_tb_bb').val('');
                $('#tb').val('').attr('readonly', false);
                $('#bb').val('').attr('readonly', false);
            }
        });

        function tBbB(id, status = 0) {
            if (id) {
                if (status == 1) {
                    var tbbb = getJSON("<?= base_url('rekam_medis/TbBb/action/ambil') ?>", {
                        id: id
                    });
                } else {
                    var tbbb = getJSON("<?= base_url('rekam_medis/TbBb/action/ambil') ?>", {
                        nokun: id
                    });
                }

                if (tbbb.data != null) {
                    $('#detailTbBb').html(' (Dibuat oleh ' + tbbb.data['oleh_desc'] + ' pada ' + moment(tbbb.data['created_at']).format("LLL") + ' sumber dari ' + tbbb.data['deskripsi'] + ')');
                    $('#id_tb_bb').val(tbbb.data['id']);
                    $('#tb').val(tbbb.data['tb']).attr('readonly', true);
                    $('#bb').val(tbbb.data['bb']).attr('readonly', true);
                } else {
                    toastr.warning('Data TB dab BB Tidak Tersedia');
                }
            }
        }

        $('#tandaVitalTerakhir').click(function() {
            if ($(this).is(':checked')) {
                tanda_vital("<?= $pasien['NOKUN'] ?>");
            } else {
                $('#detailTandaVital').html('');
                $('#id_tanda_vital').val('');
                $('#sistolik').val('').attr('readonly', false);
                $('#diastolik').val('').attr('readonly', false);
                $('#pernapasan').val('').attr('readonly', false);
                $('#nadi').val('').attr('readonly', false);
                $('#suhu').val('').attr('readonly', false);
            }
        });

        function tanda_vital(id, status = 0) {
            if (id) {
                if (status == 1) {
                    var tandavital = getJSON("<?= base_url('rekam_medis/TandaVital/action/ambil') ?>", {
                        id: id
                    });
                } else {
                    var tandavital = getJSON("<?= base_url('rekam_medis/TandaVital/action/ambil') ?>", {
                        nokun: id
                    });
                }

                if (tandavital.data != null) {
                    $('#detailTandaVital').html(' (Dibuat oleh ' + tandavital.data['oleh_desc'] + ' pada ' + moment(tandavital.data['waktu']).format("LLL") + ' sumber dari ' + tandavital.data['deskripsi'] + ')');
                    $('#id_tanda_vital').val(tandavital.data['id']);
                    $('#sistolik').val(parseInt(tandavital.data['td_sistolik'])).attr('readonly', true);
                    $('#diastolik').val(parseInt(tandavital.data['td_diastolik'])).attr('readonly', true);
                    $('#pernapasan').val(parseInt(tandavital.data['pernapasan'])).attr('readonly', true);
                    $('#nadi').val(parseInt(tandavital.data['nadi'])).attr('readonly', true);
                    $('#suhu').val(tandavital.data['suhu']).attr('readonly', true);
                } else {
                    toastr.warning('Data Tanda Vital Tidak Tersedia');
                }
            }
        }

        $('#kesadaranTerakhir').click(function() {
            if ($(this).is(':checked')) {
                kesadaran("<?= $pasien['NOKUN'] ?>");
            } else {
                $('#detailKesadaran').html('');
                $("input[class=kesadaran]").attr('checked', false);
                $("input[id=kesadaranTerakhir]").attr('checked', false);
                $("input[class=kesadaran]").attr('onclick', 'javascript: return true;');
                $('#id_kesadaran').val('');
            }
        });

        function kesadaran(id, status = 0) {

            if (id) {
                if (status == 1) {
                    var kesadaran = getJSON("<?= base_url('rekam_medis/kesadaran/action/ambil') ?>", {
                        id: id
                    });
                } else {
                    var kesadaran = getJSON("<?= base_url('rekam_medis/kesadaran/action/ambil') ?>", {
                        nokun: id
                    });
                }

                if (kesadaran.data != null) {
                    $('#detailKesadaran').html(' (Dibuat oleh ' + kesadaran.data['oleh_desc'] + ' pada ' + moment(kesadaran.data['created_at']).format("LLL") + ' sumber dari ' + kesadaran.data['deskripsi'] + ')');
                    $("input[class=kesadaran][value=" + kesadaran.data['kesadaran'] + "]").attr('checked', true);
                    $("input[class=kesadaran]").attr('onclick', 'javascript: return false;');
                    $('#id_kesadaran').val(kesadaran.data['id']);
                } else {
                    toastr.warning('Data Kesadaran Tidak Tersedia');
                }
            }
        }

        $('#ewsBalanceTerakhir').click(function() {
            if ($(this).is(':checked')) {
                ewsBalance();
            } else {
                let obyektif = $('#obyektif').val();
                let res = obyektif.replace(/Skor EWS :(.+)\nBalance :(.+)\nResiko Jatuh :(.+)/i, '');
                $('#obyektif').val(res);
            }
        });

        $('.form_asuhan_keperawatan').change(function() {
            var id = $(this).val();
            var wrap = $(this).data('wrap');
            if ($('[class="form_asuhan_keperawatan"]:checked').length > 0) {
                $('.form_asuhan').css("display", "block");
            } else {
                $('.form_asuhan').css("display", "none");
            }
            if ($(this).is(':checked')) {
                $('#perencanaanPerawat').css("display", "none");
                $.ajax({
                    url: "<?= base_url('pengkajianAwal/asuhanKeperawatan_cppt_header') ?>",
                    method: "POST",
                    // dataType:'json',
                    data: {
                        id: id
                    },
                    success: function(html) {
                        $('.form_asuhan').append('<div id="' + wrap + '">' + html + '</div>');
                    }
                });
            } else {
                $('#' + wrap).remove();
            }
        });

        $(document).on('click', '.history_cppt', function() {
            var id = $(this).data('id');
            $('#modal').modal('hide');
            load(id);
        });

        function ewsBalance() {
            var usia = "<?= $pasien['USIA'] ?>";
            var obyektif = $('#obyektif').val();
            var stringEWS = '';
            var stringBalance = '';

            if (usia == 1) {
                var ews = getJSON("<?= base_url('rekam_medis/Medis/pewsDashboard') ?>", {
                    nokun: "<?= $pasien['NOKUN'] ?>"
                });
                var txt = 'PEWS';
            } else {
                var ews = getJSON("<?= base_url('rekam_medis/Medis/ewsDashboard') ?>", {
                    nokun: "<?= $pasien['NOKUN'] ?>"
                });
                var txt = 'EWS';
            }
            var balance = getJSON("<?= base_url('rekam_medis/medis/observasiKeperawatanDashboard') ?>", {
                nokun: "<?= $pasien['NOKUN'] ?>"
            });
            var resikoJatuh = getJSON("<?= base_url('rekam_medis/Medis/resikoJatuhDashboard') ?>", {
                nopen: "<?= $pasien['NOPEN'] ?>"
            });

            var dataBalance = balance.data[0];

            if (ews.data != null) {
                var stringEWS = '\n' + 'Skor ' + txt + ' : ' + ews.data['SKOR'] + ' pada : ' + moment(ews.data['TANGGAL_EWS']).format("LLL");
            } else {
                toastr.warning('EWS tidak ada.');
            }

            if (dataBalance != null) {
                var stringBalance = '\n' + 'Balance : ' + dataBalance[3] + ' pada : ' + moment(dataBalance[2]).format("LLL");
            } else {
                toastr.warning('Balance tidak ada.');
            }

            if (resikoJatuh.data != null) {
                var stringResikoJatuh = '\n' + 'Resiko Jatuh : ' + resikoJatuh.data['Deskripsi'] + ' dengan skor ' + resikoJatuh.data['SKOR'] + ', dengan ' + resikoJatuh.data['JENIS'];
            } else {
                toastr.warning('EWS tidak ada.');
            }

            $('#obyektif').val(obyektif + stringEWS + stringBalance + stringResikoJatuh);
        }
    });

    $('#simpan_cppt').on('click', function(e) {
        e.preventDefault(); // Mencegah form default submit

        var action = $('#formCppt').attr('action');
        var form = $('#formCppt').serialize();

        $.ajax("<?= base_url('rekam_medis/rawat_inap/catatanTerintegrasi/Cppt/action/') ?>" + action, {
            dataType: 'json',
            type: 'POST',
            data: form,
            success: function(data) {
                if (data.status === 'success') {
                    <?php if (($pasien['ID_RUANGAN'] == '105020101' || $pasien['ID_RUANGAN'] == '105020102') && $pasien['IDPENJAMIN'] == '2' && $this->session->userdata('profesi') == '11'): ?>
                        // Jika memenuhi syarat, tampilkan modal untuk konfirmasi
                        $('#tabelDiagnosisCppt').DataTable().ajax.reload();
                        $('#viewInputDiagnosisCppt').modal('show');
                    <?php else: ?>
                        // Jika tidak perlu modal, reload halaman langsung
                        simpanTindakan();
                        location.reload();
                    <?php endif ?>
                } else {
                    displayErrors(data.errors);
                }
            },
            error: function() {
                toastr.error('Internal Server Error!');
            }
        });
    });

    $('#simpanModalCppt').on('click', function(e) {
        if ($('.kategori-diagnosis-cppt:checked').length === 0) {
            e.preventDefault(); // Mencegah aksi default tombol
            alertify.error('Harap pilih minimal satu diagnosis sebelum menyimpan.');
            return false; // Mencegah tindakan lebih lanjut dan modal tetap terbuka
        }
        location.reload();
    });
    $('#viewInputDiagnosisCppt').on('hide.bs.modal', function(e) {
        if ($('.kategori-diagnosis-cppt:checked').length === 0) {
            e.preventDefault(); // Mencegah modal tertutup
            alertify.error('Harap pilih minimal satu diagnosis sebelum menutup modal.');
            return false;
        }
        location.reload();
    });

    function displayErrors(errors) {
        var textError = '';
        var no = 1;

        $.each(errors, function(index, element) {
            $('#' + index).addClass('is-invalid');
            $('#' + index).parents('.form-group').find('.error').html(element);
            textError += no + '. ' + element + '<br>';
            no++;
        });

        alertify.alert('<h3>Gagal Simpan</h3>', '<h4 class="text-warning">' + textError + '</h4>');
        setTimeout(() => {
            $('#' + Object.keys(errors)[0]).focus();
        }, 1000);

        $('#formCppt textarea').on('keyup', function() {
            $(this).removeClass('is-invalid');
            $(this).parents('.form-group').find('.error').html("");
        });
    }

    $('#verif_cppt').on('click', function(e) {
        e.preventDefault();

        alertify.confirm('Data diverifikasi', 'Pilih Ok, jika setuju diverifikasi',
            function() {
                var action = 'verif';
                var form = $('#formCppt').serialize();

                $.ajax("<?= base_url('rekam_medis/rawat_inap/catatanTerintegrasi/Cppt/action/') ?>" + action, {
                    dataType: 'json',
                    type: 'POST',
                    data: form,

                    success: function(data) {
                        if (data.status == 'success') {
                            toastr.success('Berhasil Disimpan');
                            location.reload();
                        } else {
                            $.each(data.errors, function(index, element) {
                                toastr.warning(element);
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        toastr.error('Internal Server Error!');
                    }
                });
            },
            function() {
                alertify.error('Cancel')
            });
    });

    $('#history_cppt').on('click', function() {
        $('#modal').modal('show');
        $('#modal .modal-title').html('History CPPT');
        $('#modal .modal-body').html('<div class="table-responsive">' +
            '<table  class="table table-bordered table-hover dt-responsive dt-responsive table-custom dataTable" id="tbl_history_cppt" cellspacing="0" width="100%">' +
            '<thead>' +
            '<tr class="table-tr-custom">' +
            '<th>VIEW</th>' +
            '<th>CETAK</th>' +
            '<th>VERIF</th>' +
            '<th>TBAK</th>' +
            '<th>TANGGAL</th>' +
            '<th>RUANGAN</th>' +
            '<th>PROFESI</th>' +
            '<th>USER</th>' +
            '<th>DPJP</th>' +
            '</tr>' +
            '</thead>' +
            '<tbody>' +
            '</tbody>' +
            '</table>' +
            '</div>');

        var tbl_history_cppt = $('#tbl_history_cppt').dataTable({
            "sPaginationType": "full_numbers",
            "responsive": true,
            "bPaginate": true,
            "lengthMenu": [
                [1, 5, 10, 25, 50, 100, -1],
                [1, 5, 10, 25, 50, 100, "All"]
            ],
            "processing": false,
            "serverSide": true,
            "bFilter": true,
            "bLengthChange": true,
            "iDisplayLength": 5,
            "ordering": false,
            "order": [],
            "ajax": {
                url: "<?= base_url('rekam_medis/rawat_inap/catatanTerintegrasi/Cppt/datatables') ?>",
                type: 'POST',
                data: function(data) {
                    data.nomr = "<?= $pasien['NORM'] ?>";
                }
            }
        });

        $('#tbl_history_cppt_filter input').unbind();
        $('#tbl_history_cppt_filter input').bind('keyup', function(e) {
            if (e.keyCode == 13) {
                tbl_history_cppt.fnFilter(this.value);
            }
        });

        $(document).on('click', '#view-cppt', function() {
            var url = $(this).data('url');
            $('#modal2').modal('show');
            $('#modal2 .modal-title').html('View Cetak CPPT');
            $('#modal2 .modal-body').html('<iframe src="' + url + '" width="100%" height="600"></iframe>');
        });
    });

    $('#history_observasi_keperawatan').on('click', function() {
        $(":checkbox[class=form_asuhan_keperawatan]").attr('checked', false);
        $(".form_asuhan_keperawatan").trigger("change");
        $('#modal').modal('show');
        $('#modal .modal-title').html('History Observasi Keperawatan');
        $('#modal .modal-body').html('<div class="table-responsive">' +
            '<table  class="table table-bordered table-hover dt-responsive dt-responsive nowrap table-custom dataTable" id="tbl_history_observasi_keperawatan" cellspacing="0" width="100%">' +
            '<thead>' +
            '<tr class="table-tr-custom">' +
            '<th>#</th>' +
            '<th>Ruangan</th>' +
            '<th>Tanggal</th>' +
            '<th>Jam</th>' +
            '<th>Perawat</th>' +
            '<th>PAK</th>' +
            '</tr>' +
            '</thead>' +
            '<tbody>' +
            '</tbody>' +
            '</table>' +
            '</div>');
        $('#modal .modal-footer').html('<button type="button" class="btn btn-success btn-lg" id="selesai_observasi" data-toggle="tooltip" data-placement="left">' +
            'Selesai' +
            '</button>' +
            '</tfoot>');
        var tbl_history_observasi_keperawatan = $('#tbl_history_observasi_keperawatan').DataTable({
            "sPaginationType": "full_numbers",
            "responsive": true,
            "bPaginate": true,
            "lengthMenu": [
                [1, 5, 10, 25, 50, 100, -1],
                [1, 5, 10, 25, 50, 100, "All"]
            ],
            "processing": false,
            "bFilter": true,
            "bLengthChange": true,
            "iDisplayLength": 5,
            "ordering": false,
            "order": [],
            "ajax": {
                url: "<?= base_url('rekam_medis/Medis/historyObservasiKeperawatan') ?>",
                type: 'POST',
                data: function(data) {
                    data.nokun = "<?= $pasien['NOKUN'] ?>";
                }
            }
        });
    });

    $(document).on('click', '#selesai_observasi', function() {
        var parent = [];
        var perencanaan = [];

        $.each($("input[class='ckasuhan']:checked"), function(index1, value1) {
            $.each($(this).data('parent'), function(index2, value2) {
                if ($.inArray(value2, parent) === -1) {
                    parent.push(value2);
                }
            });

            $.each($(this).val().split(','), function(index3, value3) {
                if ($.inArray(value3, perencanaan) === -1) {
                    perencanaan.push(value3);
                }
            });
        });

        $.each(parent, function(index, value) {
            $(":checkbox[class=form_asuhan_keperawatan][value=" + value + "]").attr('checked', true);
        });

        $(".form_asuhan_keperawatan").trigger("change");

        setTimeout(function() {
            $.each(perencanaan, function(index, value) {
                $(":checkbox[name='asuhankeperawatancppt[]'][value=" + value + "]").attr('checked', true);
            });
            $('#modal').modal('hide');
        }, 1000);
    });

    // Tambah TBAK
    var i = 1;
    $(document).on('click', '.tambah-tbak', function(e) {
        e.preventDefault();
        var html = '<tr>' +
            '<td>' +
            `<select name="tbakDpjpp[]" id="tbakDpjpp" class="form-control tbakDpjpp">
                            <option disabled selected>[ Pilih Dokter TBAK ]</option>
                            <?php foreach ($listDr as $dpjpSmf): ?>
                                <option value="<?= $dpjpSmf['ID_DOKTER'] ?>"><?= $dpjpSmf['DOKTER'] ?></option>
                            <?php endforeach ?>
                        </select>` +
            '</td>' +
            '<td>' +
            `<div class="row form-group">
                                <textarea name="instruksi_tbak[]" id="instruksiTbak" rows="5" class="form-control" placeholder="[ Pesan yang dilaporkan dan instruksi ]"></textarea>
                            </div>
                            <div class="row form-group">
                                <div class="col-md-2">
                                <div class="checkbox checkbox-primary">
                                    <input type="checkbox" id="hasilKritis_${i}" class="hasilKritis" value="1">
                                    <label for="hasilKritis_${i}">
                                    Hasil Kritis
                                    </label>
                                </div>
                                </div>
                                <div class="col-md-10 d-none inputTanggalHasilKritis">
                                <div class="input-group">
                                    <input type="hidden" name="hasil_kritis[]" value="0">
                                    <input type="text" name="waktu_lapor[]" class="form-control waktuLapor" placeholder="yyyy/mm/dd" value="<?= date('Y-m-d H:i:s') ?>">
                                    <div class="input-group-append">
                                    <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                    </div>
                                </div>
                                </div>
                            </div>` +
            '</td>' +
            '<td>' +
            '<div class="form-group">' +
            '<button class="btn btn-danger hapus-tbak"> <i class="fa fa-times"></i></button>' +
            '</div>' +
            '</td>' +
            '</tr>';
        $('#tbak').append(html);
        $('.tbakDpjpp').select2();
        i++;

        $(".hasilKritis").change(function() {
            var $tanggalInput = $(this).closest(".form-group").find(".inputTanggalHasilKritis");

            if ($(this).is(":checked")) {
                $tanggalInput.removeClass("d-none");
                $tanggalInput.html(`<div class="input-group">
                                        <input type="hidden" name="hasil_kritis[]" value="1">
                                        <input type="text" name="waktu_lapor[]" class="form-control waktuLapor" placeholder="yyyy/mm/dd" value="<?= date('Y-m-d H:i:s') ?>">
                                        <div class="input-group-append">
                                        <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                        </div>
                                    </div>`);
                $('.waktuLapor').datetimepicker({
                    format: 'Y-m-d H:i:s'
                });
            } else {
                $tanggalInput.addClass("d-none");
                $tanggalInput.html(`<div class="input-group">
                                        <input type="hidden" name="hasil_kritis[]" value="0">
                                        <input type="text" name="waktu_lapor[]" class="form-control waktuLapor" placeholder="yyyy/mm/dd" value="<?= date('Y-m-d H:i:s') ?>">
                                        <div class="input-group-append">
                                        <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                        </div>
                                    </div>`);
            }
        });
    });

    // Hapus TBAK
    $(document).on('click', '.hapus-tbak', function() {
        $(this).closest('tr').remove();
    });
    $(document).on('click', '.konfirmasi-tbak', function(e) {
        e.preventDefault();
        var id = $(this).data('id');
        $('#modal3').modal('show');
        $('#modal3 .modal-title').html('Hapus Data');
        $('#modal3 .modal-body').html(`Data TBAK akan dihapus, pilih ok untuk melanjutkan.`);
        $('#modal3 .modal-footer').html(`<button type="button" class="btn btn-lg btn-secondary btn-trans" data-dismiss="modal" data-toggle="tooltip" data-placement="left">Tutup</button><button type="button" class="btn btn-success btn-lg" id="delete-tbak" data-id="` + id + `" data-toggle="tooltip" data-placement="left">Ok</button>`);
    });

    $(document).on('click', '#delete-tbak', function() {
        var id = $(this).data('id');
        $.ajax("<?= base_url('rekam_medis/rawat_inap/catatanTerintegrasi/Cppt/action/deleteTbak') ?>", {
            dataType: 'json',
            type: 'POST',
            data: {
                id: id
            },
            success: function(data) {
                if (data.status == 'success') {
                    $("#tbak-" + id + "").closest('tr').remove();
                    $("#modal3").modal('hide');
                    toastr.success('Berhasil di Hapus');
                } else {
                    $.each(data.errors, function(index, element) {
                        toastr.warning(element);
                    });
                }
            },
            error: function(e) {
                $("#modal3").modal('hide');
                toastr.error('Terjadi Kesalahan Koneksi');
            }
        });
    });

    $('#modalPanduanRad').on('click', function() {
        $('#modal').modal('show');
        $('#modal .modal-title').html('Data Panduan');
        $('#modal .modal-body').html(`
        <div class="row">
          <div class="col-md-12">
            <ul class="nav nav-tabs">
              <li class="nav-item">
                <a href="#rtogAcuteToxicity" data-toggle="tab" aria-expanded="false" class="nav-link active">
                  RTOG LATE TOXICITY
                </a>
              </li>
              <li class="nav-item">
                <a href="#rtogLateToxicity" data-toggle="tab" aria-expanded="true" class="nav-link">
                  RTOG ACUTE TOXICITY
                </a>
              </li>
            </ul>

            <div class="tab-content">
              <div role="tabpanel" class="tab-pane fade show active" id="rtogAcuteToxicity">
                <div class="table-responsive">
                  <table class="table table-bordered table-striped">
                    <thead class="thead-light">
                      <tr>
                        <th>TISSUE</th>
                        <th>GRADE 1</th>
                        <th>GRADE 2</th>
                        <th>GRADE 3</th>
                        <th>GRADE 4</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>Skin</td>
                        <td>
                          Slightly atrophy; pigmentation change; some hair loss
                        </td>
                        <td>Patch atrophy; moderate telangiectasia; total hair loss</td>
                        <td>Marked atrophy; gross telangiectasia</td>
                        <td>ulceration</td>
                      </tr>
                      <tr>
                        <td>Mucous membrane</td>
                        <td>Slight atrophy and dryness</td>
                        <td>Moderate atrophy and telangiectasia; little mucous</td>
                        <td>Marked atrophy with complete dryness</td>
                        <td>ulceration</td>
                      </tr>
                      <tr>
                        <td>Eye</td>
                        <td>Asymptomatic cataract; minor corneal ulceration or keratitis</td>
                        <td>Symptomatic cataract; moderate corneal ulceration; minor retinopathy
                          or glaucoma
                        </td>
                        <td>Severe keratitis; severe retinopathy or detachment</td>
                        <td>Panophthalmitis/blindness</td>
                      </tr>
                      <tr>
                        <td>Ear</td>
                        <td>Mild external otitis with erythema, pruritus, secondary to dry
                          desquamation not requiring medication. Audiogram unchanged from
                          baseline
                        </td>
                        <td>Moderate external otitis requiring topical medication/serous
                          otitis media/hypoacusis on testing only
                        </td>
                        <td>Severe external otitis with discharge or moist desquamation /
                          symptomatic hypoacusis/tinnitus, not drug related
                        </td>
                        <td>Deafness</td>
                      </tr>
                      <tr>
                        <td>Salivary gland</td>
                        <td>Slight dryness of mouth; good response on stimulation</td>
                        <td>Moderate druness of mouth; poor response on stimulation</td>
                        <td>Complete dryness of mouth; no response on stimulation</td>
                        <td>Fibrosis</td>
                      </tr>
                      <tr>
                        <td>Esophagus</td>
                        <td>Mild fibrosis; slight difficulty in swallowing solids; no pain on
                          swallowing
                        </td>
                        <td>Unable to take solid food normally; swallowing semisolid food;
                          dilatation maybe indicated
                        </td>
                        <td>Severe fibrosis; able to swallow only liquids; may have pain on
                          swallowing; dilatation required
                        </td>
                        <td>Necrosis/perforation fistula</td>
                      </tr>
                      <tr>
                        <td>Larynx</td>
                        <td>Hoarseness; slight arytenoid edema</td>
                        <td>Moderate arytenoid edema; chondritis</td>
                        <td>Severe edema; severe chondritis</td>
                        <td>necrosis</td>
                      </tr>
                      <tr>
                        <td>Liver</td>
                        <td>Mild lassitude; nausea dyspepsia; slighty abnormal liver function
                        </td>
                        <td>Moderate symptoms; some abnormal liver function tests; serum albumin
                          normal
                        </td>
                        <td>Disabling hepatic insufficiency; liver function test grossly
                          abnormal; low albumin; edema or ascites
                        </td>
                        <td>Necrosis/hepatic coma or encephalopathy</td>
                      </tr>
                      <tr>
                        <td>Kidney</td>
                        <td>Transient albuminuria; no hypertension; mild impairment of renal
                          function; ure 25-35 mg/dL; creatinine 1.5-2 mg/dL; creatinine
                          clearance >75%
                        </td>
                        <td>Persistent moderate albuminuria (2+); mild hypertension; no related
                          anemia; moderate impairment of renal function; urea >35-60 mg/dL;
                          creatinine >2-4 mg/dL; creatinine clearance 50-74%
                        </td>
                        <td>Severe albuminuria; severe hypertension; persistent anemia (<10
                          g/dL); severe renal failure; urea>60mg/dL; creatinine >4 mg/dL;
                          creatinine clearance <50%
                        </td>
                        <td>Malignant hypertension; uremic coma; urea>100</td>
                      </tr>
                      <tr>
                        <td>Intestine</td>
                        <td>mild diarrhea; mild cramping; bowel movement 5x daily; slight rectal
                          discharge or bleeding
                        </td>
                        <td>moderate diarrhea and colic; bowel movement >5x daily; excessive
                          rectal discharge or intermittent bleeding
                        </td>
                        <td>Obstruction or bleeding requiring surgery</td>
                        <td>Necrosis or perforation fistula</td>
                      </tr>
                      <tr>
                        <td>Bladder</td>
                        <td>Slight epithelial atrophy; minor telangiectasia (microscopic
                          hematuria)
                        </td>
                        <td>Moderate frequency; generalized telangiectasia; intermittent
                          macroscopic hematuria
                        </td>
                        <td>Severe frequency and dysuria; severe telangiectasia; frequent
                          hematuria; reduction in bladder capacity (<150cc)
                        </td>
                        <td>Necrosis/contracted bladder (capacity <100cc); severe hemorrhagic
                          cystitis
                        </td>
                      </tr>
                      <tr>
                        <td>Bone</td>
                        <td>Asymptomatic; no growth retardation; reduce bone density</td>
                        <td>Moderate pain or tenderness; growth retardation; irregular bone
                          sclerosis
                        </td>
                        <td>Severe pain or tenderness; complete arrest of bone growth; dense
                          bone sclerosis
                        </td>
                        <td>Necrosis/spontaneous fracture</td>
                      </tr>
                      <tr>
                        <td>Joint</td>
                        <td>Mild joint stiffness; slight limitation of movement</td>
                        <td>Moderate stiffness; intermittent or moderate joint pain; moderate
                          limitation of movement
                        </td>
                        <td>Severe joint stiffness; pain with severe limitation of movement</td>
                        <td>Necrosis/complete fixation</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
              <div role="tabpanel" class="tab-pane fade" id="rtogLateToxicity">
                <table class="table table-bordered table-striped">
                  <thead class="thead-light">
                    <tr>
                      <th>TISSUE</th>
                      <th>GRADE 1</th>
                      <th>GRADE 2</th>
                      <th>GRADE 3</th>
                      <th>GRADE 4</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>Skin</td>
                      <td>Follicular, faint or dull erythema/epilation/dry desquamation /
                        decreased sweating
                      </td>
                      <td>Tender or bright erythema, patchy moist desquamation/moderate edema
                      </td>
                      <td>Confluent, moist desquamation other than skin folds, pitting edema</td>
                      <td>Ulceration, hemorrhage, necrosis</td>
                    </tr>
                    <tr>
                      <td>Mucous membrane</td>
                      <td>Irritation/may experience mild pain not requiring analgesic</td>
                      <td>Patchy mucositis that may produce an inflammatory serosanguinous
                        discharge/may experience moderate pain requiring analgesia
                      </td>
                      <td>Confluent fibrinous mucositis /. May include sever pain requiring
                        narcotic
                      </td>
                      <td>Ulceration, hemorrhage, necrosis</td>
                    </tr>
                    <tr>
                      <td>Eye</td>
                      <td>Mild conjunctivitis w/ or w/o scleral injection/increased tearing</td>
                      <td>Moderate conjunctivitis w/ or w/o keratitis requiring steroids and/or
                        antibiotics /dry eye requiring artificial tears/iritis with
                        photophobia
                      </td>
                      <td>Severe external otitis with corneal ulceration/objective decrease in
                        visual acuity or in visual fields/acute glaucoma/panophthalmitis
                      </td>
                      <td>Loss of vision (uni or bilateral)</td>
                    </tr>
                    <tr>
                      <td>Ear</td>
                      <td>Mild external otitis with erythema, pruritus, secondary to dry
                        desquamation not requiring medication. Audiogram unchanged from baseline
                      </td>
                      <td>Moderate external otitis requiring topical medication/serous otitis
                        media/hypoacusis on testing only
                      </td>
                      <td>Severe external otitis with discharge or moist desquamation /
                        symptomatic hypoacusis/tinnitus, not drug related
                      </td>
                      <td>Deafness</td>
                    </tr>
                    <tr>
                      <td>Salivary gland</td>
                      <td>Mild mouth dryness/slightly thickened salive/may have slightly
                        altered taste such as metallic taste/these changes not reflected
                      </td>
                      <td>Moderate to complete dryness/thick, sticky saliva/markedly altered
                        taste
                      </td>
                      <td>(none)</td>
                      <td>Acute salivary gland necrosis</td>
                    </tr>
                    <tr>
                      <td>Pharynx & esophagus</td>
                      <td>Mild dysphagia or odynophagia/may require topical anesthetic or
                        non-narcotic analgesics/may require soft diet
                      </td>
                      <td>Moderate dysphagia or odynophagia/may require narcotic anesthetics /
                        may require puree or liquid diet
                      </td>
                      <td>Severe dysphagia or odynophagia with dehydration or weight loss > 15%
                        from pretreatment baseline requiring NG feeding tube, IV fluids or
                        hyperalimentation
                      </td>
                      <td>Complete obstruction, ulceration, perforation, fistula</td>
                    </tr>
                    <tr>
                      <td>Larynx</td>
                      <td>Mild or intermittent hoarseness/cough not requiring antitussive /
                        erythema of mucosa
                      </td>
                      <td>Persistent hoarseness but able to vocalize/referred ear pain, sore
                        throat, patchy fibrinous exudate not requiring narcotic/cough
                        requiring antitussive
                      </td>
                      <td>Whispered speech, throat pain or referred ear pain requiring narcotic /
                        confluent fibrinous exudate, marked arytenoid edema
                      </td>
                      <td>Marked dyspnea, stridor or hemoptysis with tracheostomy or intubation
                        necessary
                      </td>
                    </tr>
                    <tr>
                      <td>Upper GI</td>
                      <td>Anorexia with <5% weight loss from pretreatment baseline/nausea not
                        requiring antiemetics/abdominal discomfort not requiring
                        parasympatholytic drugs or analgesics
                      </td>
                      <td>Anorexia with <15% weight loss from pretreatment baseline/nausea
                        and/or vomiting requiring antiemetics/abdominal pain requiring
                        analgesics
                      </td>
                      <td>Anorexia with >15% weight loss from pretreatment baseline or requiring
                        NG tube or parenteral support. Nausea and/or vomiting requiring tube or
                        parenteral support/abdominal pain, severe despite medication /
                        hematemesis or melena/abdominal distention (flat plate radiograph
                        demonstrate distended bowel loops)
                      </td>
                      <td>Ileus, subacute or acute obstruction, perforation, GI bleeding requiring
                        transfusion/abdominal pain requiring tube decompression or bowel
                        diversion
                      </td>
                    </tr>
                    <tr>
                      <td>Lower GI/Pelvis</td>
                      <td>Increased frequency or change in quality of bowel habits not requiring
                        medication/rectal discomfort not requiring analgesics
                      </td>
                      <td>Diarrhea requiring parasympatholytic rugs (e.g. Lomotil)/mucous
                        discharge not necessitating sanitary pads/rectal or abdominal pain
                        requiring analgetics
                      </td>
                      <td>Diarrhea requiring parenteral support/severe mucous or blood discharge
                        necessitating sanitary pads/abdominal distention (flat plate
                        radiograph demonstrates distended bowel loops)
                      </td>
                      <td>Acute or subacute obstruction, fistula or perforation; GI bleeding
                        requiring transfusion; abdominal pain or tenesmus requiring tube
                        decompression or bowel diversion
                      </td>
                    </tr>
                    <tr>
                      <td>Lung</td>
                      <td>Mild symptoms of dry cough or dyspnea on exertion</td>
                      <td>Persistent cough requiring narcotic antitussive agents/dyspnea with
                        minimal effort but not at rest
                      </td>
                      <td>Severe cough unresponsive to narcotic antitussive agent or dyspnea at
                        rest/clinical or radiological evidence of acute pneumonitis /
                        intermittent oxygen or steroids may be required
                      </td>
                      <td>Severe respiratory insufficiency/continuous oxygen or assisted
                        ventilation
                      </td>
                    </tr>
                    <tr>
                      <td>Genitourinary</td>
                      <td>Frequency of urination or nocturia twice pretreatment habit/dysuria,
                        urgency not requiring medication
                      </td>
                      <td>Frequency of urination or nocturia that is less frequent than every
                        hour. Dysuria, urgency, bladder spasm requiring local anesthetic (e.g.
                        pyridium)
                      </td>
                      <td>Frequency with urgency and nocturia hourly or more frequently/dysuria,
                        pelvis pain or bladder spasm requiring regular, frequent narcotic /
                        gross hematuria
                      </td>
                      <td>Hematuria requiring transfusion/acute bladder obstruction not
                        secondary to clot passage, ulceration, or necrosis
                      </td>
                    </tr>
                    <tr>
                      <td>Heart</td>
                      <td>Asymptomatic but objective evidence of ECG changes or pericardial
                        abnormalities w/o evidence of other heart disease/no specific
                        treatment required
                      </td>
                      <td>Symptomatic with ECG changes and radiological findings of congestive
                        heart failure or pericardial disease
                      </td>
                      <td>Congestive heart failure, angina pectoris, pericardial disease
                        responding to therapy
                      </td>
                      <td>Congestive heart failure, angina pectoris, pericardial disease,
                        arrhythmias not responsive to nonsurgical measures
                      </td>
                    </tr>
                    <tr>
                      <td>CNS</td>
                      <td>Fully functional status (i.e. able to work) with minor neurological
                        findings, no medication needed
                      </td>
                      <td>Neurological findings present sufficient to require home care/nursing
                        assistance may be required/medications including steroids/antiseizure
                        agent may be required
                      </td>
                      <td>Neurological findings requiring hospitalization for initial management
                      </td>
                      <td>Serious neurological impairment that includes paralysis, coma, or
                        seizures > 3 per week despite medication/hospitalization required
                      </td>
                    </tr>
                    <tr>
                      <td>Heme</td>
                      <td>1</td>
                      <td>2</td>
                      <td>3</td>
                      <td>4</td>
                    </tr>
                    <tr>
                      <td>WBC</td>
                      <td>3.0 - < 4.0</td>
                      <td>2.0 - < 3.0</td>
                      <td>1.0 - < 2.0</td>
                      <td>< 1.0</td>
                    </tr>
                    <tr>
                      <td>Platelets</td>
                      <td>75 - < 100</td>
                      <td>50 - < 75</td>
                      <td>25 - < 50</td>
                      <td>< 25 or spontaneous bleeding</td>
                    </tr>
                    <tr>
                      <td>Neutrophils</td>
                      <td>1.5 - < 1.9</td>
                      <td>1.0 - < 1.5</td>
                      <td>0.5 - < 1.0</td>
                      <td>< 0.5 or sepsis</td>
                    </tr>
                    <tr>
                      <td>Hgb/Hct</td>
                      <td>11 – 9.5 (28% - < 32%)</td>
                      <td><9.5 – 7.5 (< 28%)</td>
                      <td>< 7.5 – 5.0 (packed cell transfusion required)</td>
                      <td>(none)</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div><!-- end col -->
        </div>`);
    });

    //RTOG
    $('#pilihRtog').on('change', function() {
        var idRtog = $(this).val();

        $.ajax({
            type: 'POST',
            url: "<?= base_url('PengkajianAwal/toksitasRadiasi') ?>",
            data: {
                idRtog: idRtog
            },
            success: function(data) {
                $('#toksitasRadiasi').html(data);

                $(".pilihKemoradiasi").on('change', function() {
                    var idKemo = $('input[name=kemoradiasi]:checked', '.pilihKemoradiasi').val()
                    if (idKemo == 1058) {
                        $('#deskKemoradiasi').css("display", "block");
                    } else {
                        $('input[name=deskKemoradiasi]').val("");
                        $('#deskKemoradiasi').css("display", "none");
                    }
                });
            }
        });
    });

    $('#cppt_template').select2({
        width: 'resolve'
    }).on('change', function() {
        var id = $(this).val();
        var detail = getJSON("<?= base_url('rekam_medis/rawat_inap/catatanTerintegrasi/CpptTemplate/action/ambil') ?>", {
            id: id
        });
        if (id != 0) {
            $('#subyektif').val(detail.data['subyektif']);
            $('#obyektif').val(detail.data['obyektif']);
            $('#analisis').val(detail.data['analisis']);
            $('#perencanaan').val(detail.data['perencanaan']);
            $('#instruksi').val(detail.data['instruksi']);
        } else {
            $('#subyektif').val('');
            $('#obyektif').val('');
            $('#analisis').val('');
            $('#perencanaan').val('');
            $('#instruksi').val('');
        }
    });
    loadListTemplate();
    // Load Template
    function loadListTemplate() {
        $('#cppt_template').html('<option value="0"  selected> Tanpa Template</option>');
        var listTemplate = getJSON("<?= base_url('rekam_medis/rawat_inap/catatanTerintegrasi/CpptTemplate/action/getTemplate') ?>", {});
        if (listTemplate.length !== 0) {
            $.each(listTemplate.data, function(index, element) {
                var html = '<option value="' + element.id + '">' + element.deskripsi + '</option>';
                $('#cppt_template').append(html);
            });
        } else {
            $('#cppt_template').html('<option value="0"  selected> Tanpa Template</option>');
        }
    }

    $('#buat_template').on('click', function() {
        $('#modal').modal('show');
        $('#modal .modal-title').html(`FORM TEMPLATE CPPT`);
        $('#modal .modal-body').html(`<div class="row form-group">
                                        <div class="col-md-12">
                                            <div class="btn-group pull-left m-t-20" style="width: 100%;">
                                                <select name="cppt_template" id="cppt_template_modal" class="form-control">
                                                    <option value="0" selected> Buat Baru</option>
                                                </select>
                                                <button type="button" class="btn btn-success btn-sm" id="reset_form" data-toggle="tooltip" data-placement="left" data-original-title="Reset Form">
                                                    <i class="fa fa-refresh"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <form method="POST" action="tambah" id="formCpptTemplate">
                                        <input type="hidden" name="id" id="id_cppt_t">
                                        <div class="form-group">
                                            <label for="judul">Judul</label>
                                            <input type="text" name="deskripsi" id="judul" class="form-control" placeholder="[ Judul ]" autocomplete="off">
                                        </div>
                                        <div class="form-group">
                                            <label for="subyektif">S (Subyektif)</label>
                                            <textarea name="subyektif" id="subyektif_t" rows="5" class="form-control" placeholder="[ Keluhan Pasien ]"
                                            required></textarea>
                                        </div>
                                        <div class="form-group">
                                            <label for="obyektif">O (Obyektif)</label>
                                            <textarea name="obyektif" id="obyektif_t" rows="5" class="form-control"
                                            placeholder="[ Pemeriksaan dan Hasil penunjang lainya ]" required></textarea>
                                        </div>
                                        <div class="form-group">
                                            <div class="form-group">
                                                <label for="analisis">A (Analisis)</label>
                                                <textarea name="analisis" id="analisis_t" rows="5" class="form-control" placeholder="[ Analisis ]"></textarea>
                                            </div>
                                            <div class="form-group">
                                                <label for="perencanaan">P (Perencanaan)</label>
                                                <textarea name="perencanaan" id="perencanaan_t" rows="5" class="form-control" placeholder="[ Perencanaan ]"></textarea>
                                            </div>
                                            <div class="form-group">
                                                <label for="instruksi">I (Instruksi)</label>
                                                <textarea name="instruksi" id="instruksi_t" rows="5" class="form-control" placeholder="[ Instruksi ]"></textarea>
                                            </div>
                                        </div>
                                    </form>`);
        $('#modal .modal-footer').html('<button type="button" class="btn btn-success btn-lg" id="simpan_template" data-toggle="tooltip" data-placement="left">' +
            'Simpan' +
            '</button>' +
            '</tfoot>');
        $('#cppt_template_modal').select2().on('change', function() {
            var id = $(this).val();
            var detail = getJSON("<?= base_url('rekam_medis/rawat_inap/catatanTerintegrasi/CpptTemplate/action/ambil') ?>", {
                id: id
            });
            if (id != 0) {
                $('#id_cppt_t').val(detail.data['id']);
                $('#judul').val(detail.data['deskripsi']);
                $('#subyektif_t').val(detail.data['subyektif']);
                $('#obyektif_t').val(detail.data['obyektif']);
                $('#analisis_t').val(detail.data['analisis']);
                $('#perencanaan_t').val(detail.data['perencanaan']);
                $('#instruksi_t').val(detail.data['instruksi']);
            } else {
                $('#id_cppt_t').val('');
                $('#judul').val('');
                $('#subyektif_t').val('');
                $('#obyektif_t').val('');
                $('#analisis_t').val('');
                $('#perencanaan_t').val('');
                $('#instruksi_t').val('');
            }
        });

        $('#reset_form').on('click', function() {
            $('#cppt_template_modal').val('0').trigger('change');
        });

        // Load Template
        var listTemplate = getJSON("<?= base_url('rekam_medis/rawat_inap/catatanTerintegrasi/CpptTemplate/action/getTemplate') ?>", {});
        if (listTemplate.length !== 0) {
            $.each(listTemplate.data, function(index, element) {
                var html = '<option value="' + element.id + '">' + element.deskripsi + '</option>';
                $('#cppt_template_modal').append(html);
            });
        } else {
            $('#cppt_template_modal').html('');
        }
    });

    $(document).on('click', '#simpan_template', function(e) {
        e.preventDefault();

        alertify.confirm('Data disimpan', 'Pilih Ok, jika setuju disimpan',
            function() {
                var action = $('#formCpptTemplate').attr('action');
                var form = $('#formCpptTemplate').serialize();

                $.ajax("<?= base_url('rekam_medis/rawat_inap/catatanTerintegrasi/CpptTemplate/action/') ?>" + action, {
                    dataType: 'json',
                    type: 'POST',
                    data: form,

                    success: function(data) {
                        if (data.status == 'success') {
                            toastr.success('Berhasil Disimpan');
                            $('#modal').modal('hide');
                            loadListTemplate();
                        } else {
                            $.each(data.errors, function(index, element) {
                                toastr.warning(element);
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        toastr.error('Internal Server Error!');
                    }
                });
            },
            function() {
                alertify.error('Cancel')
            });
    });

    $('.status_gizi').select2({
        placeholder: "[ Pilih Status Gizi ]",
    });

    $('#buat-eresep-cppt').on('click', function(e) {
        e.preventDefault();
        var open = false;
        var status = $('#showHasilPenunjangSampingRi').hasClass('d-block');

        $('#viewHasilPenunjangSideRi').trigger('click');

        $(document).ajaxStop(function() {
            if (!open) {
                $('#eresepSideBarRi-tab').trigger('click');
                open = true;
            }
        });
    });

    $('#resepTerakhir').click(function() {
        if ($(this).is(':checked')) {
            var instruksi = $('#instruksi').val();
            $.ajax("<?= base_url('pengkajianAwal/getCpptResep') ?>", {
                type: 'POST',
                dataType: 'json',
                data: {
                    nokun: "<?= $pasien['NOKUN'] ?>"
                },
                success: function(data) {
                    $('#instruksi').val(instruksi + '\n[RESEP] \n' + data['RESEP']);
                }
            });
        } else {
            var instruksi = $('#instruksi').val();
            var res = instruksi.replace(/\[RESEP][\s\S]*/g, '');
            $('#instruksi').val(res);
        }
    });

    var tindakanArr = [];
    <?php if (isset($pasien['ID_RUANGAN']) && (in_array($pasien['ID_RUANGAN'], ['105140101', '105140102']) || $pasien['JENIS_KUNJUNGAN'] == 3) && $pasien['status_pasien'] == "1"): ?>
        $(document).ready(function() {
            getTindakan();
        });
    <?php endif ?>
    // $(document).on('click', '.tambah_tindakan', function () {
    function tambah_tindakan() {
        var val = $(".select_tindakan").val();
        var txt = $(".select_tindakan option:selected").text();
        //   var tarif=$('.select_tindakan').find(':selected').data('tarif');
        if (jQuery.inArray(val, tindakanArr) !== -1) {
            alertify.confirm('Konfirmasi', `Tindakan ${txt} sudah ada,\r\nTetap tambahkan tindakan?`, function() {
                tambahTindakan(val, txt);
                // simpanTindakan();
            }, function() {}).set('labels', {
                ok: 'Ya',
                cancel: 'Tidak'
            });
        } else {
            tambahTindakan(val, txt);
        }
    };


    function tambahTindakan(val, txt) {
        tindakanArr.push(val)
        var no = $('.table_tindakan tbody').find('tr').length + 1;
        var newRowContent = `<tr class="trt" alt="${val}"><td>${no}</td><td>${txt}</td><td><i alt="${val}" class="fa fa-trash hapus_tindakan text-danger"></i></td></tr>`;
        $(".table_tindakan tbody").append(newRowContent);
    }

    $(document).on('click', '.hapus_tindakan', function() {
        var id = $(this).attr("alt");
        var vm = this;
        var rowIndex = $('.table_tindakan tbody tr').index($(this).closest('tr'));
        alertify.confirm('Konfirmasi', 'Hapus Tindakan?', function() {
            $(vm).closest('tr').remove();
            tindakanArr.splice(rowIndex, 1);
        }, function() {}).set('labels', {
            ok: 'Ya',
            cancel: 'Tidak'
        });

    });

    function getTindakan() {
        var ID_RUANGAN_CUY = "<?= $pasien['ID_RUANGAN'] ?>";
        $.ajax({
            url: "<?= base_url('tindakan/tindakancppt') ?>",
            method: 'POST',
            data: {
                ID_RUANGAN: ID_RUANGAN_CUY
            },
            dataType: 'json',
            success: function(resp) {
                $(".select_tindakan").select2({
                    placeholder: 'Cari Tindakan',
                    'data': resp,
                    templateSelection: function(data, container) {
                        // $(data.element).attr('data-tarif', data.tarif);
                        return `${data.text}`;
                    },
                    templateResult: function(data, container) {
                        return `${data.text}`;
                    }
                });
            }
        });
    }

    function simpanTindakan() {
        if (tindakanArr.length > 0) {
            var nokun = "<?= $pasien['NOKUN'] ?>";
            $.ajax({
                url: "<?= base_url('tindakan/simpantindakancppt') ?>",
                method: 'POST',
                dataType: 'json',
                data: {
                    nokun: nokun,
                    tindakan: tindakanArr
                },
                success: function(resp) {
                    if (resp.success) {
                        toastr.success(resp.message || "Tindakan berhasil disimpan!");
                        tindakanArr = [];
                    } else {
                        toastr.error(resp.message || "Gagal menyimpan tindakan.");
                    }
                },
                error: function() {
                    toastr.error("Terjadi kesalahan pada server. Coba lagi nanti.");
                }
            });
        } else {
            toastr.warning("Tidak ada tindakan yang dipilih.");
        }
    }

    $(document).ready(function() {
        let nomrModal = '<?= $pasien['NORM'] ?>';
        // alert(nomr);
        $('#tabelDiagnosisCppt').DataTable({
            ajax: {
                url: "<?= base_url('DiagnosisDpjp/tableNew') ?>",
                type: 'post',
                data: function(d) {
                    d.nomr = nomrModal;
                    d.nopen = <?= $pasien['NOPEN'] ?>;
                    d.source = 'cppt';
                }
            },

            autowide: true,
            // order: [
            // 	[1, 'desc'],
            //     [2, 'desc']
            // ],

            // mulai pendefinisian kolom
            columnDefs: [{
                targets: [0],
                orderable: false,
            }],
            // akhir pendefinisian kolom

            language: {
                sEmptyTable: 'Maaf, tidak ada data yang tersedia',
                sInfo: 'Menampilkan _START_ sampai _END_ dari _TOTAL_ data',
                sInfoEmpty: 'Menampilkan 0 sampai 0 dari 0 data',
                sInfoFiltered: '(Pencarian dari _MAX_ total data)',
                sInfoPostFix: '',
                sInfoThousands: ',',
                sLengthMenu: 'Menampilkan _MENU_ data',
                sLoadingRecords: 'Harap tunggu...',
                sProcessing: 'Sedang memproses...',
                sSearch: 'Pencarian:',
                sZeroRecords: 'Data tidak ditemukan',
                oPaginate: {
                    sFirst: 'Pertama',
                    sLast: 'Terakhir',
                    sNext: 'Selanjutnya',
                    sPrevious: 'Sebelumnya'
                }
            },

            processing: true,
            serverSide: true,
            iDisplayLength: 10,
            clear: true,
            destroy: true,
            search: {
                regex: true
            },
        }).ajax.reload();
        // akhir tabel

        // mulai tambah
        $('#tombolTambahDiagnosisCppt').click(function() {
            $.ajax({
                type: 'post',
                url: "<?= base_url('DiagnosisDpjp/cek') ?>",
                data: {
                    nomr: nomrModal,
                    kategori: 1,
                },
                success: function(data) {
                    $('#tombolTambahDiagnosisCppt').addClass('d-none');
                    let newRow = $('<tr>')
                    let jumlah = data;

                    markup = "<td colspan='2'><input type='text' name='namaDiagnosa' class='form-control form-control-sm' placeholder='[ Input Diagnosis ]'></td>";

                    // if (jumlah == 0) {
                    // 	markup += "<td>Primer<input type='hidden' value='1' name='kategoriDiagnosa'></td>";
                    // } else if (jumlah >= 1) {
                    // 	markup += "<td>Sekunder<input type='hidden' value='2' name='kategoriDiagnosa'></td>";
                    // } else {
                    // 	markup += "<td>0</td>";
                    // }

                    markup += "<td colspan='5'><div class='row'><div class='col-md'><button type='button' class='btn btn-success btn-block' id='simpanDiagnosisCppt'><i class='fa fa-check'></i> Simpan</button></div></div></td>";

                    newRow.append(markup);
                    $('#tabelDiagnosisCppt').find('tbody').prepend(newRow).fadeIn(1000);
                }
            });
        });
        // akhir tambah

        // mulai simpan
        $('table tbody#hasilTambahDiagnosisCppt').on('click', '#simpanDiagnosisCppt', function() {
            let namaDiagnosa = $(this).closest('tr').find('[name="namaDiagnosa"]').val();
            let kategoriDiagnosa = $(this).closest('tr').find('[name="kategoriDiagnosa"]').val();

            $.confirm({
                title: 'Informasi',
                content: 'Apakah Anda ingin menyimpan diagnosis tersebut?',
                autoClose: 'tutup|20000',
                theme: 'material',
                buttons: {
                    tutup: {
                        text: 'Cancel',
                        btnClass: 'btn-secondary'
                    },
                    setuju: {
                        text: 'Ya',
                        btnClass: 'btn-success',
                        action: function() {
                            if (!namaDiagnosa || namaDiagnosa.trim() === '') {
                                $.alert({
                                    title: 'Error!',
                                    content: 'Nama diagnosis harus diisi!',
                                    type: 'red',
                                });
                                // return false;  // Prevent the action if the field is empty
                            } else {
                                $.ajax({
                                    url: "<?= base_url('DiagnosisDpjp/simpan') ?>",
                                    type: 'post',
                                    data: {
                                        nmdiagnosa: namaDiagnosa,
                                        katdiagnosa: kategoriDiagnosa,
                                        nomr: $('#nomrformInputDiagnosisCppt').val(),
                                        nopen: $('#nopenformInputDiagnosisCppt').val(),
                                        nokun: $('#nokunformInputDiagnosisCppt').val(),
                                    },
                                    success: function(response) {
                                        let result = JSON.parse(response);
                                        if (result.status === 'error') {
                                            $.alert({
                                                title: 'Error!',
                                                content: 'diagnosis sudah ada!',
                                                type: 'red',
                                            });
                                            alertify.error(result.message);
                                        } else if (result.status === 'success') {
                                            alertify.success(result.message);
                                            $('#tabelDiagnosisCppt').DataTable().ajax.reload();
                                            $('#tombolTambahDiagnosisCppt').removeClass('d-none');
                                        }
                                    }
                                });
                            }
                        }
                    }
                }
            });
        });
        // akhir simpan

        // mulai ubah status
        $('table tbody#hasilTambahDiagnosisCppt').on('change', '.kategori-diagnosis-cppt', function() {
            let cek = $(this).is(':checked');
            let id = $(this).data('id');
            let diagnosis = $(this).data('diagnosis');

            if (cek == true) {
                cek = 1;
            } else if (cek == false) {
                cek = 0;
            }

            $.ajax({
                url: "<?= base_url('DiagnosisDpjp/ubahNew') ?>",
                type: 'post',
                data: {
                    cek: cek,
                    id: id,
                    diagnosis: diagnosis,
                    nomr: <?= $pasien['NORM'] ?>,
                    nopen: <?= $pasien['NOPEN'] ?>,
                    nokun: <?= $pasien['NOKUN'] ?>
                },
                success: function(data) {
                    alertify.success('Data disimpan');
                    $('#tabelDiagnosisCppt').DataTable().ajax.reload();
                }
            });
        });
    });

    var pengkajianMedis = '<?=$cekPengkajianAwalRanap['pengkajianAwal'];?>';
    var dpjp = '<?=$pasien['ID_DPJP'];?>';
    var idDokter = '<?=$this->session->userdata('iddokter');?>';

    if(pengkajianMedis == 0 && dpjp == idDokter){
        $('.simpan_cppt').attr("disabled", "");
    }else{
        $('.simpan_cppt').removeAttr("disabled", "");
    }

    // Pengkajian RI Dewasa Medis
    $('.isiPengkajian').click(function () {
        $('.pengkajianRiDewasaMedis').click();
    });
</script>
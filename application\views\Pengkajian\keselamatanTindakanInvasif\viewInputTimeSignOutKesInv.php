<div class="modal-header">
	<h4 class="modal-title">Input Time & Sign Out</h4>
	<button type="button" class="close" data-dismiss="modal">&times;</button>
</div>
<form id="formTimeSignOutKesInv" autocomplete="off">
	<div class="modal-body" style="max-height: calc(100vh - 200px); overflow-y: auto;">
		<div class="container-fluid">
			<input type="hidden" name="idkes" value="<?=$idkes;?>">
			<input type="hidden" name="nokun" value="<?=$nokun;?>">
			<input type="hidden" name="pengisi" value="<?= $this->session->userdata('id'); ?>">

			<fieldset disabled="disabled">
				<div class="card new-card">
					<div class="card-header new-card-header" id="headingOne">
						<H6 class="m-0">
							<span>
								<b>A. DATA UMUM PASIEN</b>
							</span>
						</H6>
					</div>

					<div id="dataUmumPasien">
						<div class="card-body">
							<div class="row jarak">
								<div class="col-md-1">
									<span>1</span>
								</div>
								<div class="col-md-5">
									Tanda vital
								</div>
							</div>

							<div class="row jarak">
								<div class="offset-md-3 col-md-3">
									Pukul
								</div>
								<div class="col-md-6">
									<input type="text" class="form-control pukulTimeSignOutKesInv" id="pukulTimeSignOutKesInv" name="pukulKesInv" value="<?= isset($getKTI['pukul']) ? $getKTI['pukul'] : "" ?>">
								</div>
							</div>

							<div class="row jarak">
								<div class="offset-md-3 col-md-3">
									Tekanan darah
								</div>
								<div class="col-md-3">
									<input type="text" name="tekananDarSistolikKesInv" id="tekananDarSistolikTimeSignOutKesInv" class="form-control jarak3 tekananDarSistolikTimeSignOutKesInv" placeholder="[ Sistolik ]" value="<?= isset($getKTI['tekanan_darah_1']) ? $getKTI['tekanan_darah_1'] : "" ?>" onkeypress="if ( isNaN(this.value + String.fromCharCode(event.keyCode) )) return false;">
								</div>

								<div class="col-md-3">
									<input type="text" name="tekananDarDiastolikKesInv" id="tekananDarDiastolikTimeSignOutKesInv" class="form-control jarak3 tekananDarDiastolikTimeSignOutKesInv" placeholder="[ Diastolik ]" value="<?= isset($getKTI['tekanan_darah_2']) ? $getKTI['tekanan_darah_2'] : "" ?>" onkeypress="if ( isNaN(this.value + String.fromCharCode(event.keyCode) )) return false;">
								</div>
							</div>

							<div class="row jarak">
								<div class="offset-md-3 col-md-3">
									Nadi
								</div>
								<div class="col-md-6">
									<input type="text" name="NadiKesInvasif" id="NadiTimeSignOutKesInv" class="form-control jarak3 NadiTimeSignOutKesInv" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['nadi']) ? $getKTI['nadi'] : "" ?>" onkeypress="if ( isNaN(this.value + String.fromCharCode(event.keyCode) )) return false;">
								</div>
							</div>

							<div class="row jarak">
								<div class="offset-md-3 col-md-3">
									Pernafasan
								</div>
								<div class="col-md-6">
									<input type="text" name="pernafasanKesInvasif" id="pernafasanTimeSignOutKesInv" class="form-control jarak3 pernafasanTimeSignOutKesInv" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['napas']) ? $getKTI['napas'] : "" ?>" onkeypress="if ( isNaN(this.value + String.fromCharCode(event.keyCode) )) return false;">
								</div>
							</div>

							<div class="row jarak">
								<div class="offset-md-3 col-md-3">
									Suhu
								</div>
								<div class="col-md-6">
									<input type="text" name="suhuKesInvasif" id="suhuTimeSignOutKesInv" class="form-control jarak3 suhuTimeSignOutKesInv" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['suhu']) ? $getKTI['suhu'] : "" ?>" onkeypress="if ( isNaN(this.value + String.fromCharCode(event.keyCode) )) return false;">
								</div>
							</div>

							<div class="row jarak">
								<div class="col-md-1">
									<span>2</span>
								</div>
								<div class="col-md-5">
									Diagnosis
								</div>
								<div class="col-md-6">
									<input type="text" name="diagnosisKeselamatanInvasif" id="diagnosisTimeSignOutKesInv" class="form-control jarak3 diagnosisTimeSignOutKesInv" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['diagnosis']) ? $getKTI['diagnosis'] : "" ?>">
								</div>
							</div>
						</div>
					</div>
				</div>
			</fieldset>

			<fieldset disabled="disabled">
				<div class="card new-card">
					<div class="card-header new-card-header" id="headingTwo">
						<H6 class="m-0">
							<span>
								<b>B. PERENCANAAN TINDAKAN</b>
							</span>
						</H6>
					</div>
					<div id="perencanaanTindakaniNV">
						<div class="card-body">

							<div class="row jarak">
								<div class="col-md-1">
									<span>1</span>
								</div>
								<div class="col-md-5">
									Identitas Pasien
								</div>
								<div class="col-md-6">
									<div class="checkbox checkbox-primary form-check-inline jarak2">
										<input type="checkbox" name="identitasPasien" id="identitasPasienTimeSignOutKesInv" value="" class="identitasPasienTimeSignOutKesInv" <?php
										if(isset($getKTI['identitas_pasien'])) {
											if($getKTI['identitas_pasien'] == 1) {
												echo "checked";
											} else{
												echo "";
											}
										}?>>
										<label for="identitasPasienTimeSignOutKesInv">Sesuai</label>
									</div>
								</div>
							</div>

							<div class="row jarak">
								<div class="col-md-1">
									<span>2</span>
								</div>
								<div class="col-md-5">
									Rencana tindakan
								</div>
								<div class="col-md-6">
									<div class="checkbox checkbox-primary form-check-inline jarak2">
										<input type="checkbox" name="rencanaTindakan" id="rencanaTindakanTimeSignOutKesInv" value="" class="rencanaTindakanTimeSignOutKesInv" <?php
										if(isset($getKTI['rencana_tindakan'])) {
											if($getKTI['rencana_tindakan'] == 1) {
												echo "checked";
											} else{
												echo "";
											}
										}?>>
										<label for="rencanaTindakanTimeSignOutKesInv">Sesuai</label>
									</div>
								</div>
							</div>

							<div class="row jarak">
								<div class="col-md-1">
									<span>3</span>
								</div>
								<div class="col-md-5">
									Penandaan sisi tindakan
								</div>
								<div class="col-md-6">
									<?php foreach ($penandaanSisiLokasi as $penandaSisLok): ?>
										<div class="radio radio-primary form-check-inline jarak2">
											<input type="radio" name="penandaSisLok" id="penandaSisLokTimeSignOutKesInv<?=$penandaSisLok['id_variabel']?>" value="<?=$penandaSisLok['id_variabel']?>" class="penandaSisLokTimeSignOutKesInv" <?php
											if(isset($getKTI['penandaan_sisi_tindakan'])) {
												if($getKTI['penandaan_sisi_tindakan'] == $penandaSisLok['id_variabel']) {
													echo "checked";
												} else{
													echo "";
												}
											}?>>
											<label for="penandaSisLokTimeSignOutKesInv<?=$penandaSisLok['id_variabel']?>"><?= $penandaSisLok['variabel'] ?></label>
										</div>
									<?php endforeach; ?>
								</div>
							</div>

							<div class="row jarak">
								<div class="col-md-1">
									<span>4</span>
								</div>
								<div class="col-md-5">
									Informed consent tindakan
								</div>
								<div class="col-md-6">
									<?php foreach ($inforConTin as $inforCoTn): ?>
										<div class="radio radio-primary form-check-inline jarak2">
											<input type="radio" name="informedConTin" id="informedConTinTimeSignOutKesInv<?=$inforCoTn['id_variabel']?>" value="<?=$inforCoTn['id_variabel']?>" class="informedConTinTimeSignOutKesInv" <?php
											if(isset($getKTI['informed_consent_tindakan'])) {
												if($getKTI['informed_consent_tindakan'] == $inforCoTn['id_variabel']) {
													echo "checked";
												} else{
													echo "";
												}
											}?>>
											<label for="informedConTinTimeSignOutKesInv<?=$inforCoTn['id_variabel']?>"><?= $inforCoTn['variabel'] ?></label>
										</div>
									<?php endforeach; ?>
								</div>
							</div>

							<div class="row jarak">
								<div class="col-md-1">
									<span>5</span>
								</div>
								<div class="col-md-5">
									Informed consent tindakan anestesi / sedasi
								</div>
								<div class="col-md-6">
									<?php foreach ($inforConTinAnesSed as $inConTinAS): ?>
										<div class="radio radio-primary form-check-inline jarak2">
											<input type="radio" name="informedConTinAS" id="informedConTinASTimeSignOutKesInv<?=$inConTinAS['id_variabel']?>" value="<?=$inConTinAS['id_variabel']?>" class="informedConTinASTimeSignOutKesInv" <?php
											if(isset($getKTI['informed_consent_anatesi_sedasi'])) {
												if($getKTI['informed_consent_anatesi_sedasi'] == $inConTinAS['id_variabel']) {
													echo "checked";
												} else{
													echo "";
												}
											}?>>
											<label for="informedConTinASTimeSignOutKesInv<?=$inConTinAS['id_variabel']?>"><?= $inConTinAS['variabel'] ?></label>
										</div>
									<?php endforeach; ?>
								</div>
							</div>

							<div class="row jarak">
								<div class="col-md-1">
									<span>6</span>
								</div>
								<div class="col-md-5">
									Pemeriksaan penunjang :
								</div>
							</div>

							<div class="row jarak">
								<div class="offset-md-3 col-md-3">
									- Panoramic
								</div>
								<div class="col-md-6">
									<?php foreach ($pemPenunjangPanoramic as $pemPenPano): ?>
										<div class="radio radio-primary form-check-inline jarak2">
											<input type="radio" name="pemPenPano" id="pemPenPanoTimeSignOutKesInv<?=$pemPenPano['id_variabel']?>" value="<?=$pemPenPano['id_variabel']?>" class="pemPenPanoTimeSignOutKesInv" <?php
											if(isset($getKTI['panoramic'])) {
												if($getKTI['panoramic'] == $pemPenPano['id_variabel']) {
													echo "checked";
												} else{
													echo "";
												}
											}?>>
											<label for="pemPenPanoTimeSignOutKesInv<?=$pemPenPano['id_variabel']?>"><?= $pemPenPano['variabel'] ?></label>
										</div>
									<?php endforeach; ?>
								</div>
							</div>
							<div id="showPanoramicTimeSignOutKesInv" style="display: none;">
								<div class="row jarak">
									<div class="offset-md-6 col-md-6">
										<input type="text" name="panoramic_desk_TimeSignOutKesInv" id="panoramic_desk_TimeSignOutKesInv" class="form-control jarak3 panoramic_desk_TimeSignOutKesInv" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['panoramic_desk']) ? $getKTI['panoramic_desk'] : "" ?>">
									</div>
								</div>
							</div>

							<div class="row jarak">
								<div class="offset-md-3 col-md-3">
									- Foto Thoraks
								</div>
								<div class="col-md-6">
									<?php foreach ($pemPenunjangFoto as $pemPenFot): ?>
										<div class="radio radio-primary form-check-inline jarak2">
											<input type="radio" name="pemPenFoto" id="pemPenFotoTimeSignOutKesInv<?=$pemPenFot['id_variabel']?>" value="<?=$pemPenFot['id_variabel']?>" class="pemPenFotoTimeSignOutKesInv" <?php
											if(isset($getKTI['foto_thoraks'])) {
												if($getKTI['foto_thoraks'] == $pemPenFot['id_variabel']) {
													echo "checked";
												} else{
													echo "";
												}
											}?>>
											<label for="pemPenFotoTimeSignOutKesInv<?=$pemPenFot['id_variabel']?>"><?= $pemPenFot['variabel'] ?></label>
										</div>
									<?php endforeach; ?>
								</div>
							</div>

							<div class="row jarak">
								<div class="offset-md-3 col-md-3">
									- CT Scan
								</div>
								<div class="col-md-6">
									<?php foreach ($pemPenunjangCtScan as $pemPenCS): ?>
										<div class="radio radio-primary form-check-inline jarak2">
											<input type="radio" name="pemPenCtScan" id="pemPenCtScanTimeSignOutKesInv<?=$pemPenCS['id_variabel']?>" value="<?=$pemPenCS['id_variabel']?>" class="pemPenCtScanTimeSignOutKesInv" <?php
											if(isset($getKTI['ctscan'])) {
												if($getKTI['ctscan'] == $pemPenCS['id_variabel']) {
													echo "checked";
												} else{
													echo "";
												}
											}?>>
											<label for="pemPenCtScanTimeSignOutKesInv<?=$pemPenCS['id_variabel']?>"><?= $pemPenCS['variabel'] ?></label>
										</div>
									<?php endforeach; ?>
								</div>
							</div>

							<div class="row jarak">
								<div class="offset-md-3 col-md-3">
									- USG
								</div>
								<div class="col-md-6">
									<?php foreach ($pemPenunjangUSG as $pemPenUSG): ?>
										<div class="radio radio-primary form-check-inline jarak2">
											<input type="radio" name="pemPenunjangUSG" id="pemPenunjangUSGTimeSignOutKesInv<?=$pemPenUSG['id_variabel']?>" value="<?=$pemPenUSG['id_variabel']?>" class="pemPenunjangUSGTimeSignOutKesInv" <?php
											if(isset($getKTI['usg'])) {
												if($getKTI['usg'] == $pemPenUSG['id_variabel']) {
													echo "checked";
												} else{
													echo "";
												}
											}?>>
											<label for="pemPenunjangUSGTimeSignOutKesInv<?=$pemPenUSG['id_variabel']?>"><?= $pemPenUSG['variabel'] ?></label>
										</div>
									<?php endforeach; ?>
								</div>
							</div>

							<div class="row jarak">
								<div class="offset-md-3 col-md-3">
									- MRI
								</div>
								<div class="col-md-6">
									<?php foreach ($pemPenunjangMRI as $pemPenMRI): ?>
										<div class="radio radio-primary form-check-inline jarak2">
											<input type="radio" name="pemPenunjangMRI" id="pemPenunjangMRITimeSignOutKesInv<?=$pemPenMRI['id_variabel']?>" value="<?=$pemPenMRI['id_variabel']?>" class="pemPenunjangMRITimeSignOutKesInv" <?php
											if(isset($getKTI['mri'])) {
												if($getKTI['mri'] == $pemPenMRI['id_variabel']) {
													echo "checked";
												} else{
													echo "";
												}
											}?>>
											<label for="pemPenunjangMRITimeSignOutKesInv<?=$pemPenMRI['id_variabel']?>"><?= $pemPenMRI['variabel'] ?></label>
										</div>
									<?php endforeach; ?>
								</div>
							</div>

							<div class="row jarak">
								<div class="offset-md-3 col-md-3">
									- Laboratorium
								</div>
								<div class="col-md-6">
									<?php foreach ($pemPenunjangLab as $pemPenLab): ?>
										<div class="radio radio-primary form-check-inline jarak2">
											<input type="radio" name="pemPenunjangLaboratorium" id="pemPenunjangLaboratoriumTimeSignOutKesInv<?=$pemPenLab['id_variabel']?>" value="<?=$pemPenLab['id_variabel']?>" class="pemPenunjangLaboratoriumTimeSignOutKesInv" <?php
											if(isset($getKTI['lab'])) {
												if($getKTI['lab'] == $pemPenLab['id_variabel']) {
													echo "checked";
												} else{
													echo "";
												}
											}?>>
											<label for="pemPenunjangLaboratoriumTimeSignOutKesInv<?=$pemPenLab['id_variabel']?>"><?= $pemPenLab['variabel'] ?></label>
										</div>
									<?php endforeach; ?>
								</div>
							</div>

							<div class="row jarak">
								<div class="offset-md-3 col-md-3">
									- Lainnya
								</div>
								<div class="col-md-6">
									<input type="text" name="pemeriksaanPenunjangLainnya" id="pemeriksaanPenunjangLainnyaTimeSignOutKesInv" class="form-control jarak3 pemeriksaanPenunjangLainnyaTimeSignOutKesInv" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['pemeriksaaan_penunjang_lainnya']) ? $getKTI['pemeriksaaan_penunjang_lainnya'] : "" ?>">
								</div>
							</div>

							<div class="row jarak">
								<div class="col-md-1">
									<span>7</span>
								</div>
								<div class="col-md-5">
									Persediaan darah
								</div>
								<div class="col-md-6">
									<?php foreach ($persediaanDarah as $persDar): ?>
										<div class="radio radio-primary form-check-inline jarak2">
											<input type="radio" name="persediDarh" id="persediDarhTimeSignOutKesInv<?=$persDar['id_variabel']?>" value="<?=$persDar['id_variabel']?>" class="persediDarhTimeSignOutKesInv" <?php
											if(isset($getKTI['persediaan_darah'])) {
												if($getKTI['persediaan_darah'] == $persDar['id_variabel']) {
													echo "checked";
												} else{
													echo "";
												}
											}?>>
											<label for="persediDarhTimeSignOutKesInv<?=$persDar['id_variabel']?>"><?= $persDar['variabel'] ?></label>
										</div>
									<?php endforeach; ?>
								</div>
							</div>

							<div id="showPersediaanDarahTimeSignOutKesInv" style="display: none;">
								<div class="row jarak">
									<div class="offset-md-3 col-md-3">
										Golongan darah
									</div>
									<div class="col-md-3">
										<input type="text" name="deskGolonganDarah" id="deskGolonganDarahTimeSignOutKesInv" class="form-control jarak3 deskGolonganDarahTimeSignOutKesInv" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['golongan_darah']) ? $getKTI['golongan_darah'] : "" ?>">
									</div>
								</div>
								<div class="row jarak">
									<div class="offset-md-3 col-md-3">
										PRC (cc)
									</div>
									<div class="col-md-3">
										<input type="text" name="deskPRC" id="deskPRCTimeSignOutKesInv" class="form-control jarak3 deskPRCTimeSignOutKesInv" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['prc']) ? $getKTI['prc'] : "" ?>">
									</div>
								</div>
								<div class="row jarak">
									<div class="offset-md-3 col-md-3">
										FFP (cc/unit)
									</div>
									<div class="col-md-3">
										<input type="text" name="deskFFP" id="deskFFPTimeSignOutKesInv" class="form-control jarak3 deskFFPTimeSignOutKesInv" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['ffp']) ? $getKTI['ffp'] : "" ?>">
									</div>
								</div>
								<div class="row jarak">
									<div class="offset-md-3 col-md-3">
										Lainnya
									</div>
									<div class="col-md-3">
										<input type="text" name="deskLainnyaPersediaanDarah" id="deskLainnyaPersediaanDarahTimeSignOutKesInv" class="form-control jarak3 deskLainnyaPersediaanDarahTimeSignOutKesInv" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['lainnya_persediaan_darah']) ? $getKTI['lainnya_persediaan_darah'] : "" ?>">
									</div>
								</div>
							</div>

							<div class="row jarak">
								<div class="col-md-1">
									<span>8</span>
								</div>
								<div class="col-md-5">
									Ketersediaan Implant / aplikator / obat
								</div>
								<div class="col-md-6">
									<?php foreach ($ketersediaanImplant as $keterImp): ?>
										<div class="radio radio-primary form-check-inline jarak2">
											<input type="radio" name="ketersediaanImp" id="ketersediaanImpTimeSignOutKesInv<?=$keterImp['id_variabel']?>" value="<?=$keterImp['id_variabel']?>" class="ketersediaanImpTimeSignOutKesInv" <?php
											if(isset($getKTI['ketersediaan_implant'])) {
												if($getKTI['ketersediaan_implant'] == $keterImp['id_variabel']) {
													echo "checked";
												} else{
													echo "";
												}
											}?>>
											<label for="ketersediaanImpTimeSignOutKesInv<?=$keterImp['id_variabel']?>"><?= $keterImp['variabel'] ?></label>
										</div>
									<?php endforeach; ?>
								</div>
							</div>

							<div id="showKetersediaanImplantTimeSignOutKesInv" style="display: none;">
								<div class="row jarak">
									<div class="offset-md-6 col-md-6">
										<input type="text" name="deskKetersediaanImplant" id="deskKetersediaanImplantTimeSignOutKesInv" class="form-control jarak3 deskKetersediaanImplantTimeSignOutKesInv" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['isi_ketersediaan_implant']) ? $getKTI['isi_ketersediaan_implant'] : "" ?>">
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</fieldset>

				<div class="card new-card jarak">
					<div class="card-header new-card-header" id="headingThree">
						<H6 class="m-0">
							<span>
								<b>C. CEKLIS KESELAMATAN TINDAKAN INVASIF</b>
							</span>
						</H6>
					</div>
					<div id="ceklisKeselamatanTindakanInvasif">
						<div class="card-body">

							<div class="row">
								<div class="offset-md-3 col-md-6">
									<div class="text-center header-sub-pengkajian">
										TIME OUT
									</div>
								</div>
							</div>

							<div class="row jarak">
								<div class="offset-md-2 col-md-3">
									Pukul
								</div>
								<div class="col-md-4">
									<input type="text" class="form-control pukulTimeOutInvTimeSignOutKesInv" id="pukulTimeOutInvTimeSignOutKesInv" name="pukulTimeOutInv" value="<?= isset($getKTI['pukul_timeout_inv']) ? $getKTI['pukul_timeout_inv'] : "" ?>">
								</div>
							</div>

							<div class="row jarak">
								<div class="col-md-1">
									<span>1</span>
								</div>
								<div class="col-md-5">
									Seluruh anggota tim menyebutkan nama dan perannya
								</div>
								<div class="col-md-6">
									<div class="checkbox checkbox-primary form-check-inline jarak2">
										<input type="checkbox" name="sebutNamaPeranTimeOut" id="sebutNamaPeranTimeOutTimeSignOutKesInv" value="" class="sebutNamaPeranTimeOutTimeSignOutKesInv" <?php
										if(isset($getKTI['menyebutkan_nama'])) {
											if($getKTI['menyebutkan_nama'] == 1) {
												echo "checked";
											} else{
												echo "";
											}
										}?>>
										<label for="sebutNamaPeranTimeOutTimeSignOutKesInv">Ya</label>
									</div>
								</div>
							</div>

							<div class="row jarak">
								<div class="col-md-1">
									<span>2</span>
								</div>
								<div class="col-md-5">
									Perawat konfirmasi secara verbal :
								</div>
							</div>

							<div class="row jarak">
								<div class="offset-md-2 col-md-4">
									- Benar identitas pasien
								</div>
								<div class="col-md-6">
									<div class="checkbox checkbox-primary form-check-inline jarak2">
										<input type="checkbox" name="benarIdentitasTimeOut" id="benarIdentitasTimeOutTimeSignOutKesInv" value="" class="benarIdentitasTimeOutTimeSignOutKesInv" <?php
										if(isset($getKTI['benar_identitas_pasien'])) {
											if($getKTI['benar_identitas_pasien'] == 1) {
												echo "checked";
											} else{
												echo "";
											}
										}?>>
										<label for="benarIdentitasTimeOutTimeSignOutKesInv">Ya</label>
									</div>
								</div>
							</div>

							<div class="row jarak">
								<div class="offset-md-2 col-md-4">
									- Benar tindakan
								</div>
								<div class="col-md-6">
									<div class="checkbox checkbox-primary form-check-inline jarak2">
										<input type="checkbox" name="benarTindakanTimeOut" id="benarTindakanTimeOutTimeSignOutKesInv" value="" class="benarTindakanTimeOutTimeSignOutKesInv" <?php
										if(isset($getKTI['benar_tindakan'])) {
											if($getKTI['benar_tindakan'] == 1) {
												echo "checked";
											} else{
												echo "";
											}
										}?>>
										<label for="benarTindakanTimeOutTimeSignOutKesInv">Ya</label>
									</div>
								</div>
							</div>

							<div class="row jarak">
								<div class="offset-md-2 col-md-4">
									- Benar sisi lokasi tindakan
								</div>
								<div class="col-md-6">
									<?php foreach ($benarSisiLokasiTin as $benSisLokTin): ?>
										<div class="radio radio-primary form-check-inline jarak2">
											<input type="radio" name="benarSisiLokasiTin" id="benarSisiLokasiTinTimeSignOutKesInv<?=$benSisLokTin['id_variabel']?>" value="<?=$benSisLokTin['id_variabel']?>" class="benarSisiLokasiTinTimeSignOutKesInv" <?php
											if(isset($getKTI['benar_sisi_lokasi_tindakan'])) {
												if($getKTI['benar_sisi_lokasi_tindakan'] == $benSisLokTin['id_variabel']) {
													echo "checked";
												} else{
													echo "";
												}
											}?>>
											<label for="benarSisiLokasiTinTimeSignOutKesInv<?=$benSisLokTin['id_variabel']?>"><?= $benSisLokTin['variabel'] ?></label>
										</div>
									<?php endforeach; ?>
								</div>
							</div>

							<div class="row jarak">
								<div class="col-md-1">
									<span>3</span>
								</div>
								<div class="col-md-5">
									Adakah hal khusus yang perlu diperhatikan ?
								</div>
								<div class="col-md-6">
									<?php foreach ($khususPerhatikan as $khusPerh): ?>
										<div class="radio radio-primary form-check-inline jarak2">
											<input type="radio" name="khususPerhatikan" id="khususPerhatikanTimeSignOutKesInv<?=$khusPerh['id_variabel']?>" value="<?=$khusPerh['id_variabel']?>" class="khususPerhatikanTimeSignOutKesInv" <?php
											if(isset($getKTI['hal_khusus'])) {
												if($getKTI['hal_khusus'] == $khusPerh['id_variabel']) {
													echo "checked";
												} else{
													echo "";
												}
											}?>>
											<label for="khususPerhatikanTimeSignOutKesInv<?=$khusPerh['id_variabel']?>"><?= $khusPerh['variabel'] ?></label>
										</div>
									<?php endforeach; ?>
								</div>
							</div>

							<div id="showKhususPerhatikanTimeSignOutKesInv" style="display: none;">
								<div class="row jarak">
									<div class="offset-md-6 col-md-6">
										<input type="text" name="deskKhususPerhatikan" id="deskKhususPerhatikanTimeSignOutKesInv" class="form-control jarak3 deskKhususPerhatikanTimeSignOutKesInv" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['isi_hal_khusus']) ? $getKTI['isi_hal_khusus'] : "" ?>">
									</div>
								</div>
							</div>

							<div class="row jarak">
								<div class="col-md-1">
									<span>4</span>
								</div>
								<div class="col-md-5">
									Jika ada, langkah apa yang akan dilakukan
								</div>
								<div class="col-md-6">
									<input type="text" name="jikaAdaLangkahApaTimeOut" id="jikaAdaLangkahApaTimeOutTimeSignOutKesInv" class="form-control jarak3 jikaAdaLangkahApaTimeOutTimeSignOutKesInv" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['langkah_yang_dilakukan']) ? $getKTI['langkah_yang_dilakukan'] : "" ?>">
								</div>
							</div>

							<div class="row jarak">
								<div class="col-md-1">
									<span>5</span>
								</div>
								<div class="col-md-5">
									Apakah instrumen sudah benar ?
								</div>
								<div class="col-md-6">
									<?php foreach ($instrumenSudahBenar as $instruSudBen): ?>
										<div class="radio radio-primary form-check-inline jarak2">
											<input type="radio" name="instrusudahBenar" id="instrusudahBenarTimeSignOutKesInv<?=$instruSudBen['id_variabel']?>" value="<?=$instruSudBen['id_variabel']?>" class="instrusudahBenarTimeSignOutKesInv" <?php
											if(isset($getKTI['instrumen'])) {
												if($getKTI['instrumen'] == $instruSudBen['id_variabel']) {
													echo "checked";
												} else{
													echo "";
												}
											}?>>
											<label for="instrusudahBenarTimeSignOutKesInv<?=$instruSudBen['id_variabel']?>"><?= $instruSudBen['variabel'] ?></label>
										</div>
									<?php endforeach; ?>
								</div>
							</div>

							<div class="row jarak">
								<div class="col-md-1">
									<span>6</span>
								</div>
								<div class="col-md-5">
									Apakah foto radiologi sudah sesuai dan ditayangkan ?
								</div>
								<div class="col-md-6">
									<?php foreach ($fotoRadiologiSesuai as $fotRadSes): ?>
										<div class="radio radio-primary form-check-inline jarak2">
											<input type="radio" name="fotRadioSesuai" id="fotRadioSesuaiTimeSignOutKesInv<?=$fotRadSes['id_variabel']?>" value="<?=$fotRadSes['id_variabel']?>" class="fotRadioSesuaiTimeSignOutKesInv" <?php
											if(isset($getKTI['foto_radiologi'])) {
												if($getKTI['foto_radiologi'] == $fotRadSes['id_variabel']) {
													echo "checked";
												} else{
													echo "";
												}
											}?>>
											<label for="fotRadioSesuaiTimeSignOutKesInv<?=$fotRadSes['id_variabel']?>"><?= $fotRadSes['variabel'] ?></label>
										</div>
									<?php endforeach; ?>
								</div>
							</div>

							<div class="row">
								<div class="offset-md-3 col-md-6">
									<div class="text-center header-sub-pengkajian">
										SIGN OUT
									</div>
								</div>
							</div>

							<div class="row jarak">
								<div class="offset-md-2 col-md-3">
									Pukul
								</div>
								<div class="col-md-4">
									<input type="text" class="form-control pukulSignOutInvTimeSignOutKesInv" id="pukulSignOutInvTimeSignOutKesInv" name="pukulSignOutInv" value="<?= isset($getKTI['pukul_signout_inv']) ? $getKTI['pukul_signout_inv'] : "" ?>">
								</div>
							</div>

							<div class="row jarak">
								<div class="col-md-1">
									<span>1</span>
								</div>
								<div class="col-md-5">
									Perawat melakukan konfirmasi verbal, tentang :
								</div>
							</div>

							<div class="row jarak">
								<div class="offset-md-1 col-md-1">
									<span>A</span>
								</div>
								<div class="col-md-4">
									Nama tindakan
								</div>
								<div class="col-md-6">
									<div class="checkbox checkbox-primary form-check-inline jarak2">
										<input type="checkbox" name="namaTindakanSignOut" id="namaTindakanSignOutTimeSignOutKesInv" value="" class="namaTindakanSignOutTimeSignOutKesInv" <?php
										if(isset($getKTI['nama_tindakan'])) {
											if($getKTI['nama_tindakan'] == 1) {
												echo "checked";
											} else{
												echo "";
											}
										}?>>
										<label for="namaTindakanSignOutTimeSignOutKesInv">Ya</label>
									</div>
								</div>
							</div>

							<div class="row jarak">
								<div class="offset-md-1 col-md-1">
									<span>B</span>
								</div>
								<div class="col-md-4">
									Kelengkapan kasa jarum / instrumen
								</div>
								<div class="col-md-6">
									<?php foreach ($kelengkapanKasaJarum as $keleKapKasJar): ?>
										<div class="radio radio-primary form-check-inline jarak2">
											<input type="radio" name="kelengkapanKapKasaJarum" id="kelengkapanKapKasaJarumTimeSignOutKesInv<?=$keleKapKasJar['id_variabel']?>" value="<?=$keleKapKasJar['id_variabel']?>" class="kelengkapanKapKasaJarumTimeSignOutKesInv" <?php
											if(isset($getKTI['kelengkapan_kasa'])) {
												if($getKTI['kelengkapan_kasa'] == $keleKapKasJar['id_variabel']) {
													echo "checked";
												} else{
													echo "";
												}
											}?>>
											<label for="kelengkapanKapKasaJarumTimeSignOutKesInv<?=$keleKapKasJar['id_variabel']?>"><?= $keleKapKasJar['variabel'] ?></label>
										</div>
									<?php endforeach; ?>
								</div>
							</div>

							<div class="row jarak">
								<div class="offset-md-1 col-md-1">
									<span>C</span>
								</div>
								<div class="col-md-4">
									Spesimen di beri label
								</div>
								<div class="col-md-6">
									<?php foreach ($spesimenBeriLabel as $spesBerLab): ?>
										<div class="radio radio-primary form-check-inline jarak2">
											<input type="radio" name="spesimenBeriLab" id="spesimenBeriLabTimeSignOutKesInv<?=$spesBerLab['id_variabel']?>" value="<?=$spesBerLab['id_variabel']?>" class="spesimenBeriLabTimeSignOutKesInv" <?php
											if(isset($getKTI['spesimen'])) {
												if($getKTI['spesimen'] == $spesBerLab['id_variabel']) {
													echo "checked";
												} else{
													echo "";
												}
											}?>>
											<label for="spesimenBeriLabTimeSignOutKesInv<?=$spesBerLab['id_variabel']?>"><?= $spesBerLab['variabel'] ?></label>
										</div>
									<?php endforeach; ?>
								</div>
							</div>

							<div class="row jarak">
								<div class="offset-md-1 col-md-1">
									<span>D</span>
								</div>
								<div class="col-md-4">
									Nama implan dan lokasi pemasangan
								</div>
								<div class="col-md-6">
									<?php foreach ($namaImplanDanLokasi as $namImplanDnLok): ?>
										<div class="radio radio-primary form-check-inline jarak2">
											<input type="radio" name="namaImplanLok" id="namaImplanLokTimeSignOutKesInv<?=$namImplanDnLok['id_variabel']?>" value="<?=$namImplanDnLok['id_variabel']?>" class="namaImplanLokTimeSignOutKesInv" <?php
											if(isset($getKTI['nama_implant'])) {
												if($getKTI['nama_implant'] == $namImplanDnLok['id_variabel']) {
													echo "checked";
												} else{
													echo "";
												}
											}?>>
											<label for="namaImplanLokTimeSignOutKesInv<?=$namImplanDnLok['id_variabel']?>"><?= $namImplanDnLok['variabel'] ?></label>
										</div>
									<?php endforeach; ?>
								</div>
							</div>

							<div id="showNamaImplanDanLokasiTimeSignOutKesInv" style="display: none;">
								<div class="row jarak">
									<div class="offset-md-6 col-md-6">
										<input type="text" name="deskNamaImplanDanLokasi" id="deskNamaImplanDanLokasiTimeSignOutKesInv" class="form-control jarak3 deskNamaImplanDanLokasiTimeSignOutKesInv" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['isi_implant']) ? $getKTI['isi_implant'] : "" ?>">
									</div>
								</div>
							</div>

							<div class="row jarak">
								<div class="offset-md-1 col-md-1">
									<span>E</span>
								</div>
								<div class="col-md-4">
									Apakah ada masalah peralatan yang perlu diperhatikan
								</div>
								<div class="col-md-6">
									<?php foreach ($peralatanYangPerlu as $perYgPerlu): ?>
										<div class="radio radio-primary form-check-inline jarak2">
											<input type="radio" name="peralatanygPrl" id="peralatanygPrlTimeSignOutKesInv<?=$perYgPerlu['id_variabel']?>" value="<?=$perYgPerlu['id_variabel']?>" class="peralatanygPrlTimeSignOutKesInv" <?php
											if(isset($getKTI['masalah_peralatan'])) {
												if($getKTI['masalah_peralatan'] == $perYgPerlu['id_variabel']) {
													echo "checked";
												} else{
													echo "";
												}
											}?>>
											<label for="peralatanygPrlTimeSignOutKesInv<?=$perYgPerlu['id_variabel']?>"><?= $perYgPerlu['variabel'] ?></label>
										</div>
									<?php endforeach; ?>
								</div>
							</div>

							<div id="showperalatanYangPerluTimeSignOutKesInv" style="display: none;">
								<div class="row jarak">
									<div class="offset-md-6 col-md-6">
										<input type="text" name="deskperalatanYangPerlu" id="deskperalatanYangPerluTimeSignOutKesInv" class="form-control jarak3 deskperalatanYangPerluTimeSignOutKesInv" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['isi_peralatan_yang_perlu']) ? $getKTI['isi_peralatan_yang_perlu'] : "" ?>">
									</div>
								</div>
							</div>

							<div class="row jarak">
								<div class="col-md-1">
									<span>2</span>
								</div>
								<div class="col-md-5">
									Adakah masalah yang harus diperhatikan
								</div>
								<div class="col-md-6">
									<?php foreach ($masalahHarusPerhatikan as $mslhHrsPerh): ?>
										<div class="radio radio-primary form-check-inline jarak2">
											<input type="radio" name="mslhHrsPerhatikan" id="mslhHrsPerhatikanTimeSignOutKesInv<?=$mslhHrsPerh['id_variabel']?>" value="<?=$mslhHrsPerh['id_variabel']?>" class="mslhHrsPerhatikanTimeSignOutKesInv" <?php
											if(isset($getKTI['masalah_yang_harus_diperhatikan'])) {
												if($getKTI['masalah_yang_harus_diperhatikan'] == $mslhHrsPerh['id_variabel']) {
													echo "checked";
												} else{
													echo "";
												}
											}?>>
											<label for="mslhHrsPerhatikanTimeSignOutKesInv<?=$mslhHrsPerh['id_variabel']?>"><?= $mslhHrsPerh['variabel'] ?></label>
										</div>
									<?php endforeach; ?>
								</div>
							</div>

							<div id="showMasalahHarusPerhatikanTimeSignOutKesInv" style="display: none;">
								<div class="row jarak">
									<div class="offset-md-6 col-md-6">
										<input type="text" name="deskMasalahHarusPerhatikan" id="deskMasalahHarusPerhatikanTimeSignOutKesInv" class="form-control jarak3 deskMasalahHarusPerhatikanTimeSignOutKesInv" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['isi_masalah_yang_harus_diperhatikan']) ? $getKTI['isi_masalah_yang_harus_diperhatikan'] : "" ?>">
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="form-group">
					<label for="dok-pelaksana-lap-operasiTimeSignOutKesInv">
						Dokter Pelaksana
					</label>
					<select name="dokter_pelaksana" id="dok-pelaksana-lap-operasiTimeSignOutKesInv" class="form-control">
						<option value="" selected disabled>[ PILIH ]</option>
						<?php foreach ($listDrPelaksana as $ld) : ?>
							<option id="dok-pelaksana-lap-operasi-TimeSignOutKesInv<?= $ld['ID_DOKTER'] ?>" value="<?= $ld['ID_DOKTER'] ?>" <?php
											if(isset($getKTI['dokter_pelaksana'])) {
												if($getKTI['dokter_pelaksana'] == $ld['ID_DOKTER']) {
													echo "selected";
												} else{
													echo "";
												}
											}?>>
								<?= $ld['DOKTER'] ?> - <?= $ld['SMF'] ?>
							</option>
						<?php endforeach; ?>
					</select>
				</div>

				<div class="form-group">
					<label for="dok-anestesi-lap-operasiTimeSignOutKesInv">
						Dokter Anestesi
					</label>
					<select name="dokter_anestesi" id="dok-anestesi-lap-operasiTimeSignOutKesInv" class="form-control">
						<option value="" selected disabled>[ PILIH ]</option>
						<?php foreach ($listDrAnestesi as $ld) : ?>
							<option id="dok-anestesi-lap-operasi-TimeSignOutKesInv<?= $ld['ID_DOKTER'] ?>" value="<?= $ld['ID_DOKTER'] ?>" <?php
											if(isset($getKTI['dokter_anestesi'])) {
												if($getKTI['dokter_anestesi'] == $ld['ID_DOKTER']) {
													echo "selected";
												} else{
													echo "";
												}
											}?>>
								<?= $ld['DOKTER'] ?> - <?= $ld['SMF'] ?>
							</option>
						<?php endforeach; ?>
					</select>
				</div>

				<div class="form-group">
					<label for="sirkuler-lap-operasiTimeSignOutKesInv">
						Perawat Sirkuler
					</label>
					<select name="perawat_sirkuler" id="sirkuler-lap-operasiTimeSignOutKesInv" class="form-control">
						<option value="" selected disabled>[ PILIH ]</option>
						<?php foreach ($listPerawat as $lp) : ?>
							<option id="sirkuler-lap-operasi-TimeSignOutKesInv<?= $lp['NIP'] ?>" value="<?= $lp['NIP'] ?>" <?php
											if(isset($getKTI['perawat_sirkuler'])) {
												if($getKTI['perawat_sirkuler'] == $lp['NIP']) {
													echo "selected";
												} else{
													echo "";
												}
											}?>>
								<?= $lp['NAMA'] ?>
							</option>
						<?php endforeach; ?>
					</select>
				</div>

				<div class="form-group">
					<label for="radiografer-lap-operasiTimeSignOutKesInv">
						Radiografer
					</label>
					<select name="perawat_radiografer" id="radiografer-lap-operasiTimeSignOutKesInv" class="form-control">
						<option value="" selected disabled>[ PILIH ]</option>
						<?php foreach ($listRadiografer as $lp) : ?>
							<option id="radiografer-lap-operasi-TimeSignOutKesInv<?= $lp['NIP'] ?>" value="<?= $lp['NIP'] ?>" <?php
											if(isset($getKTI['radiografer'])) {
												if($getKTI['radiografer'] == $lp['NIP']) {
													echo "selected";
												} else{
													echo "";
												}
											}?>>
								<?= $lp['NAMA'] ?>
							</option>
						<?php endforeach; ?>
					</select>
				</div>

				<div class="form-group">
					<label for="fisikawanMedis-lap-operasiTimeSignOutKesInv">
						Fisikawan medis
					</label>
					<select name="perawat_fisikawanMedis" id="fisikawanMedis-lap-operasiTimeSignOutKesInv" class="form-control">
						<option value="" selected disabled>[ PILIH ]</option>
						<?php foreach ($listFisikawanMedis as $lp) : ?>
							<option id="fisikawanMedis-lap-operasi-TimeSignOutKesInv<?= $lp['NIP'] ?>" value="<?= $lp['NIP'] ?>" <?php
											if(isset($getKTI['fisikawan_medis'])) {
												if($getKTI['fisikawan_medis'] == $lp['NIP']) {
													echo "selected";
												} else{
													echo "";
												}
											}?>>
								<?= $lp['NAMA'] ?>
							</option>
						<?php endforeach; ?>
					</select>
				</div>

		</div>
	</div>
	<div class="modal-footer">
		<a href="#" class="btn btn-secondary" data-toggle="modal" data-dismiss="modal"><i class="fa fa-times-circle"></i> Tutup
		</a>
		<button type="button" class="btn btn-primary tombolSimpanTimeSignOutKesInv"><i class="fa fa-check"></i> Simpan</button>
	</div>
</form>

<script>
	$(document).ready(function(){
		$('#dok-pelaksana-lap-operasiTimeSignOutKesInv').select2({
			dropdownParent: $('#formTimeSignOutKesInv'),
			width: '100%',
			allowClear: true,
			placeholder: '[ PILIH ]',
			searchInputPlaceholder: 'Cari...',
			language: {
				noResults: function() {
					return 'Data tidak ditemukan';
				}
			}
		});

		$('#dok-anestesi-lap-operasiTimeSignOutKesInv').select2({
			dropdownParent: $('#formTimeSignOutKesInv'),
			width: '100%',
			allowClear: true,
			placeholder: '[ PILIH ]'
		});

		$('#sirkuler-lap-operasiTimeSignOutKesInv').select2({
			dropdownParent: $('#formTimeSignOutKesInv'),
			width: '100%',
			allowClear: true,
			placeholder: '[ PILIH ]'
		});

		$('#radiografer-lap-operasiTimeSignOutKesInv').select2({
			dropdownParent: $('#formTimeSignOutKesInv'),
			width: '100%',
			allowClear: true,
			placeholder: '[ PILIH ]'
		});

		$('#fisikawanMedis-lap-operasiTimeSignOutKesInv').select2({
			dropdownParent: $('#formTimeSignOutKesInv'),
			width: '100%',
			allowClear: true,
			placeholder: '[ PILIH ]'
		});

		$('.tombolSimpanTimeSignOutKesInv').on("click", function(e){
			dataKesInv = $("#formTimeSignOutKesInv").serializeArray();
			$.ajax({
				url: "<?php echo base_url('pengkajianprorad/FormKeselamatanTindakanInvasif/inputTimeSignOutKesInv/tambah') ?>",
				method: "POST",
				data: dataKesInv,
				success: function (data) {
					alertify.success('Data Tersimpan');
					location.reload();
				}
			});
		});

		// Khusus perhatikan
    $('.khususPerhatikanTimeSignOutKesInv').on('click',function(){
     var id = $(this).val();
     if(id == 1421){
       $('#showKhususPerhatikanTimeSignOutKesInv').css("display", "block");
       $('.deskKhususPerhatikanTimeSignOutKesInv').attr("required", "");
     }else{
       $('#showKhususPerhatikanTimeSignOutKesInv').css("display", "none");
       $('.deskKhususPerhatikanTimeSignOutKesInv').removeAttr("required", "");
     }
   });

    if($(".khususPerhatikanTimeSignOutKesInv:checked").val() == 1421){
     $('#showKhususPerhatikanTimeSignOutKesInv').css('display','block');
   }

    // Nama implan dan lokasi
    $('.namaImplanLokTimeSignOutKesInv').on('click',function(){
     var id = $(this).val();
     if(id == 1448){
       $('#showNamaImplanDanLokasiTimeSignOutKesInv').css("display", "block");
       $('.deskNamaImplanDanLokasiTimeSignOutKesInv').attr("required", "");
     }else{
       $('#showNamaImplanDanLokasiTimeSignOutKesInv').css("display", "none");
       $('.deskNamaImplanDanLokasiTimeSignOutKesInv').removeAttr("required", "");
     }
   });

    if($(".namaImplanLokTimeSignOutKesInv:checked").val() == 1448){
     $('#showNamaImplanDanLokasiTimeSignOutKesInv').css('display','block');
   }

   // Pukul tanda vital
   $('#pukulTimeSignOutKesInv').timepicker({
    defaultTIme : false,
    showMeridian: false,
    icons: {
      up: 'fas fa-chevron-up',
      down: 'fas fa-chevron-down'
    }
  });

   // Pukul time out
   $('#pukulTimeOutInvTimeSignOutKesInv').timepicker({
    defaultTIme : false,
    showMeridian: false,
    icons: {
      up: 'fas fa-chevron-up',
      down: 'fas fa-chevron-down'
    }
  });

   // Pukul sign out
   $('#pukulSignOutInvTimeSignOutKesInv').timepicker({
    defaultTIme : false,
    showMeridian: false,
    icons: {
      up: 'fas fa-chevron-up',
      down: 'fas fa-chevron-down'
    }
  });

  	if($('.pemPenPanoTimeSignOutKesInv').val() == 6292){
		$('#showPanoramicTimeSignOutKesInv').css("display", "block");
		$('.panoramic_desk_TimeSignOutKesInv').attr("required", "");
	}else{
		$('#showPanoramicTimeSignOutKesInv').css("display", "none");
		$('.panoramic_desk_TimeSignOutKesInv').removeAttr("required", "");
	}
   	// Panoramic
  	$('.pemPenPanoTimeSignOutKesInv').on('click',function(){
		var id = $(this).val();
		if(id == 6292){
			$('#showPanoramicTimeSignOutKesInv').css("display", "block");
			$('.panoramic_desk_TimeSignOutKesInv').attr("required", "");
		}else{
			$('#showPanoramicTimeSignOutKesInv').css("display", "none");
			$('.panoramic_desk_TimeSignOutKesInv').removeAttr("required", "");
		}
   	});

   // Masalah harus perhatikan
   $('.mslhHrsPerhatikanTimeSignOutKesInv').on('click',function(){
     var id = $(this).val();
     if(id == 1452){
       $('#showMasalahHarusPerhatikanTimeSignOutKesInv').css("display", "block");
       $('.deskMasalahHarusPerhatikanTimeSignOutKesInv').attr("required", "");
     }else{
       $('#showMasalahHarusPerhatikanTimeSignOutKesInv').css("display", "none");
       $('.deskMasalahHarusPerhatikanTimeSignOutKesInv').removeAttr("required", "");
     }
   });

   if($(".mslhHrsPerhatikanTimeSignOutKesInv:checked").val() == 1452){
     $('#showMasalahHarusPerhatikanTimeSignOutKesInv').css('display','block');
   }

   // Ketersediaan Implant
   $('.ketersediaanImpTimeSignOutKesInv').on('click',function(){
     var id = $(this).val();
     if(id == 1417){
       $('#showKetersediaanImplantTimeSignOutKesInv').css("display", "block");
       $('.deskKetersediaanImplantTimeSignOutKesInv').attr("required", "");
     }else{
       $('#showKetersediaanImplantTimeSignOutKesInv').css("display", "none");
       $('.deskKetersediaanImplantTimeSignOutKesInv').removeAttr("required", "");
     }
   });

   if($(".ketersediaanImpTimeSignOutKesInv:checked").val() == 1417){
     $('#showKetersediaanImplantTimeSignOutKesInv').css('display','block');
   }

   $('.peralatanygPrlTimeSignOutKesInv').on('click',function(){
     var id = $(this).val();
     if(id == 1450){
       $('#showperalatanYangPerluTimeSignOutKesInv').css("display", "block");
       $('.deskperalatanYangPerluTimeSignOutKesInv').attr("required", "");
     }else{
       $('#showperalatanYangPerluTimeSignOutKesInv').css("display", "none");
       $('.deskperalatanYangPerluTimeSignOutKesInv').removeAttr("required", "");
     }
   });

   if($(".peralatanygPrlTimeSignOutKesInv:checked").val() == 1450){
     $('#showperalatanYangPerluTimeSignOutKesInv').css('display','block');
   }

   // Persediaan darah
   $('.persediDarhTimeSignOutKesInv').on('click',function(){
     var id = $(this).val();
     if(id == 1415){
       $('#showPersediaanDarahTimeSignOutKesInv').css("display", "block");
       $('.deskGolonganDarahTimeSignOutKesInv').attr("required", "");
       $('.deskPRCTimeSignOutKesInv').attr("required", "");
       $('.deskFFPTimeSignOutKesInv').attr("required", "");
       $('.deskLainnyaPersediaanDarahTimeSignOutKesInv').attr("required", "");
     }else{
       $('#showPersediaanDarahTimeSignOutKesInv').css("display", "none");
       $('.deskGolonganDarahTimeSignOutKesInv').removeAttr("required", "");
       $('.deskPRCTimeSignOutKesInv').removeAttr("required", "");
       $('.deskFFPTimeSignOutKesInv').removeAttr("required", "");
       $('.deskLainnyaPersediaanDarahTimeSignOutKesInv').removeAttr("required", "");
     }
   });

   if($(".persediDarhTimeSignOutKesInv:checked").val() == 1415){
     $('#showPersediaanDarahTimeSignOutKesInv').css('display','block');
   }

   // Simpan Form Keselamatan Tindakan Invasif
   $("#formKeselamatanTindakanInvasif").submit(function( event ) {
    dataKESELAMATANTINDAKANINVASIF = $("#formKeselamatanTindakanInvasif").serializeArray();
    $.ajax({
      url:"<?= base_url('pengkajianprorad/FormKeselamatanTindakanInvasif/action_keselamatantindakaninvasif/tambah') ?>",
      method:"POST",
      data:dataKESELAMATANTINDAKANINVASIF,
      success:function(data)
      {
        alertify.success('Data Tersimpan');
        location.reload();
      }
    });
    event.preventDefault();
  });
	});
</script>
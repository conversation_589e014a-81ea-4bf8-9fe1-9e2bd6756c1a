<?php if (empty($history)) : ?>
    <div class="alert alert-info">
        <i class="fa fa-info-circle"></i> Belum ada history JPOK untuk pasien ini.
    </div>
<?php else: ?>
<div class="table-responsive">
    <table class="table table-bordered table-hover" id="tbl-history-jpok">
        <thead>
            <tr>
                <th width="5%">No</th>
                <th width="15%">Tanggal</th>
                <th width="10%">Siklus</th>
                <th width="10%">Lini</th>
                <th width="20%">Protokol Kemo</th>
                <th width="10%">Jumlah <PERSON></th>
                <th width="20%">Daftar Obat</th>
                <th width="10%">Aksi</th>
            </tr>
        </thead>
        <tbody>
            <?php $no = 1; foreach($history as $row): ?>
            <tr>
                <td><?= $no++ ?></td>
                <td>
                    <small class="text-muted">
                        <?= date('d/m/Y H:i', strtotime($row['CREATED_AT'])) ?>
                    </small>
                </td>
                <td>
                    <span class="badge badge-primary"><?= htmlspecialchars($row['JUMLAH_SIKLUS']) ?></span>
                </td>
                <td>
                    <span class="badge badge-info"><?= htmlspecialchars($row['LINI']) ?></span>
                </td>
                <td>
                    <strong><?= htmlspecialchars($row['PROTOKOL_KEMO']) ?></strong>
                </td>
                <td class="text-center">
                    <span class="badge badge-success"><?= $row['JUMLAH_OBAT'] ?> obat</span>
                </td>
                <td>
                    <small class="text-muted">
                        <?= htmlspecialchars(substr($row['DAFTAR_OBAT'], 0, 50)) ?>
                        <?php if (strlen($row['DAFTAR_OBAT']) > 50): ?>...<?php endif; ?>
                    </small>
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-outline-primary btn-lihat-detail" 
                            data-id="<?= $row['ID_JPOK'] ?>" 
                            title="Lihat Detail Jadwal">
                        <i class="fa fa-eye"></i> Detail
                    </button>
                </td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>

<div class="mt-3">
    <div class="alert alert-info">
        <i class="fa fa-info-circle"></i> 
        <strong>Keterangan:</strong> Klik tombol "Detail" untuk melihat jadwal pemberian obat di tab Detail.
    </div>
</div>
<?php endif; ?>

<script>
$(document).ready(function() {
    // Event handler untuk tombol lihat detail
    $('.btn-lihat-detail').click(function() {
        var idJpok = $(this).data('id');
        
        
        // Pindah ke tab detail
        $('#detail-tab').tab('show');
        
        // Tunggu tab detail terbuka, lalu set select2 dan load detail
        setTimeout(function() {
            // Set value di select2
            var $selectJpok = $('#select-jpok');
            
            if ($selectJpok.length > 0) {
                // Ambil data JPOK untuk select2
                $.ajax({
                    url: '<?= base_url('kemoterapi/JPOKEM/get_jpok_list') ?>',
                    type: 'GET',
                    data: { nokun: '<?= $nokun ?>', q: '' },
                    success: function(response) {
                        if (response.results) {
                            var selectedJpok = response.results.find(function(item) {
                                return item.id == idJpok;
                            });
                            
                            if (selectedJpok) {
                                // Clear existing options
                                $selectJpok.empty();
                                
                                // Add selected option
                                var newOption = new Option(selectedJpok.text, selectedJpok.id, true, true);
                                $selectJpok.append(newOption).trigger('change');
                                
                                // Load detail JPOK
                                setTimeout(function() {
                                    loadDetailJpok(idJpok);
                                }, 500);
                            }
                        }
                    },
                    error: function() {
                    }
                });
            }
        }, 300);
    });
    
    // Inisialisasi DataTable untuk history
    if ($.fn.DataTable) {
        $('#tbl-history-jpok').DataTable({
            "pageLength": 10,
            "ordering": true,
            "searching": true,
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
            },
            "columnDefs": [
                { "orderable": false, "targets": [7] } // Kolom aksi tidak bisa di-sort
            ],
            "order": [] // Sort by tanggal descending
        });
    }
});
</script>

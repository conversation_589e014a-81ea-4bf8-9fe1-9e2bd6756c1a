<?php if (empty($detail) && $id_jpok > 0) : ?>
    <div class="alert alert-warning">Tidak ada data detail JPOK.</div>
<?php elseif (empty($detail) && $id_jpok == 0) : ?>
    <!-- Select JPOK sudah ada di view utama, tidak perlu duplikat -->
<?php else: ?>
<div class="mb-3 d-flex justify-content-between align-items-center">
    <button type="button" class="btn btn-success" id="btn-tambah-jadwal">
        <i class="fa fa-plus"></i> Jadwal
    </button>
</div>

<?php foreach($detail as $periode_idx => $periode_data): ?>
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fa fa-calendar"></i> <?= htmlspecialchars($periode_data['bulan_nama']) ?>
        </h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-bordered table-hover mb-0" id="tbl-pemberian-<?= $periode_idx ?>">
                <thead>
                    <tr style="background-color: #2c3e50; color: white;">
                        <th rowspan="2" style="vertical-align: middle;">PEMBERIAN</th>
                        <th colspan="7" class="text-center">Minggu 1</th>
                        <th colspan="7" class="text-center">Minggu 2</th>
                        <th colspan="7" class="text-center">Minggu 3</th>
                        <th colspan="7" class="text-center">Minggu 4</th>
                        <th colspan="7" class="text-center">Minggu 5</th>
                    </tr>
                    <tr style="background-color: #34495e; color: white;">
                        <?php for($minggu = 1; $minggu <= 5; $minggu++): ?>
                            <?php for($hari = 1; $hari <= 7; $hari++): ?>
                                <th class="text-center"><?= $hari ?></th>
                            <?php endfor; ?>
                        <?php endfor; ?>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach($periode_data['obat_list'] as $row): ?>
                    <tr>
                        <td style="background-color: #34495e;">
                            <strong><?= htmlspecialchars($row['NAMA_OBAT']) ?></strong><br>
                            <small>Dosis: <?= htmlspecialchars($row['DOSIS']) ?></small><br>
                            <small><?= htmlspecialchars($row['pemberian']) ?></small>
                        </td>
                        <?php for($minggu = 1; $minggu <= 5; $minggu++): ?>
                            <?php for($hari = 1; $hari <= 7; $hari++): ?>
                                <td class="text-center" style="width: 30px; background-color: #487eb0; border: 1px solid #487eb0;">
                                    <span class="jadwal-indicator" 
                                          data-detail-id="<?= $row['id_detail'] ?>" 
                                          data-minggu="<?= $minggu ?>" 
                                          data-hari="<?= $hari ?>"
                                          data-periode-bulan="<?= $periode_data['bulan'] ?>"
                                          data-periode-tahun="<?= $periode_data['tahun'] ?>">
                                        <!-- Akan diisi dengan JavaScript berdasarkan data jadwal -->
                                    </span>
                                </td>
                            <?php endfor; ?>
                        <?php endfor; ?>
                    </tr>
                    <?php endforeach; ?>
                    
                    <!-- Baris BB/TB/LBB -->
                    <tr style="background-color: #34495e;">
                        <td style="background-color: #34495e;"><strong>BB / TB (kg / cm)</strong></td>
                        <?php for($minggu = 1; $minggu <= 5; $minggu++): ?>
                            <?php for($hari = 1; $hari <= 7; $hari++): ?>
                                <td class="text-center bb-tb-cell" 
                                    data-minggu="<?= $minggu ?>" 
                                    data-hari="<?= $hari ?>"
                                    data-periode-bulan="<?= $periode_data['bulan'] ?>"
                                    data-periode-tahun="<?= $periode_data['tahun'] ?>"
                                    style="background-color: #487eb0; border: 1px solid #487eb0; min-height: 40px; vertical-align: middle;">
                                    <!-- Akan diisi dengan data BB/TB -->
                                </td>
                            <?php endfor; ?>
                        <?php endfor; ?>
                    </tr>

                    <!-- Baris LBB -->
                    <tr style="background-color: #34495e;">
                        <td style="background-color: #34495e;"><strong>LBB (m2)</strong></td>
                        <?php for($minggu = 1; $minggu <= 5; $minggu++): ?>
                            <?php for($hari = 1; $hari <= 7; $hari++): ?>
                                <td class="text-center lbb-cell" 
                                    data-minggu="<?= $minggu ?>" 
                                    data-hari="<?= $hari ?>"
                                    data-periode-bulan="<?= $periode_data['bulan'] ?>"
                                    data-periode-tahun="<?= $periode_data['tahun'] ?>"
                                    style="background-color: #487eb0; border: 1px solid #487eb0; min-height: 40px; vertical-align: middle;">
                                    <!-- Akan diisi dengan data LBB -->
                                </td>
                            <?php endfor; ?>
                        <?php endfor; ?>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php endforeach; ?>

<!-- Modal untuk input jadwal -->
<div class="modal fade" id="modal-jadwal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Form Input : PEMASUKKAN</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true" style="color: white;">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="form-jadwal">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Bulan</label>
                                <select class="form-control" name="bulan" required>
                                    <option value="">Pilih Bulan</option>
                                    <?php for($i = 1; $i <= 12; $i++): ?>
                                        <option value="<?= $i ?>"><?= date('F', mktime(0, 0, 0, $i, 1)) ?></option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Tahun</label>
                                <select class="form-control" name="tahun" required>
                                    <option value="">Pilih Tahun</option>
                                    <?php for($i = date('Y') - 2; $i <= date('Y') + 2; $i++): ?>
                                        <option value="<?= $i ?>" <?= $i == date('Y') ? 'selected' : '' ?>><?= $i ?></option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Minggu</label>
                                <select class="form-control" name="minggu" required>
                                    <option value="">Pilih Minggu</option>
                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                        <option value="<?= $i ?>">Minggu <?= $i ?></option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Seri ke</label>
                                <select class="form-control" name="seri" required>
                                    <option value="">Pilih Seri</option>
                                    <?php for($i = 1; $i <= 10; $i++): ?>
                                        <option value="<?= $i ?>">Seri <?= $i ?></option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>TB</label>
                                <input type="number" step="0.01" class="form-control" name="tb" placeholder="Tinggi Badan (cm)">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>BB</label>
                                <input type="number" step="0.01" class="form-control" name="bb" placeholder="Berat Badan (kg)">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>LBB (m2)</label>
                                <input type="number" step="0.01" class="form-control" name="lbb_m2" placeholder="LBB (m2)">
                            </div>
                        </div>
                    </div>
                    
                    <div id="obat-checkbox-container">
                        <!-- Akan diisi dengan checkbox obat via JavaScript -->
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="btn-simpan-jadwal">
                    <i class="fa fa-save"></i> Simpan
                </button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    var currentIdJpok = <?= $id_jpok ?>;

    // Tidak perlu inisialisasi select2 di sini karena sudah ada di view utama
    
    // Load data jadwal untuk tabel
    loadJadwalData();
    
    // Event handler untuk tombol tambah jadwal
    $('#btn-tambah-jadwal').click(function() {
        loadObatCheckboxes();
        loadPatientVitals(); // Auto-fill TB, BB, LBB

        // Auto-select bulan dan minggu sekarang
        setTimeout(function() {
            setCurrentMonthWeek();
        }, 500);

        $('#modal-jadwal').modal('show');
    });

    // Event handler untuk simpan jadwal - gunakan off() untuk mencegah duplikasi
    $(document).off('click', '#btn-simpan-jadwal').on('click', '#btn-simpan-jadwal', function(e) {
        e.preventDefault();
        e.stopPropagation();

        var $btn = $(this);

        // Cek jika sedang dalam proses simpan
        if ($btn.prop('disabled')) {
            return false;
        }

        var formData = $('#form-jadwal').serialize();
        formData += '&id_jpok=' + currentIdJpok;

        $.ajax({
            url: '<?= base_url('kemoterapi/JPOKEM/simpan_jadwal') ?>',
            type: 'POST',
            data: formData,
            dataType: 'json',
            beforeSend: function() {
                $btn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Menyimpan...');
            },
            success: function(response) {
                if (response.status === 'success') {
                    // Clear existing toasts untuk mencegah spam
                    toastr.clear();
                    toastr.success(response.message || 'Jadwal berhasil disimpan');

                    $('#modal-jadwal').modal('hide');
                    $('#form-jadwal')[0].reset();

                    // Reload seluruh halaman agar tabel periode ikut terupdate
                    setTimeout(function() {
                        location.reload();
                    }, 500);
                } else {
                    toastr.clear();
                    toastr.error(response.message || 'Gagal menyimpan jadwal');
                }
            },
            error: function() {
                toastr.clear();
                toastr.error('Terjadi kesalahan saat menyimpan jadwal');
            },
            complete: function() {
                $btn.prop('disabled', false).html('<i class="fa fa-save"></i> Simpan');
            }
        });

        return false;
    });

    function loadObatCheckboxes() {
        var html = '';
        var obatIndex = 0; // Counter untuk unique ID

        <?php if (!empty($detail)): ?>
            <?php 
            // Ambil obat dari periode pertama karena obat sama untuk semua periode
            $obat_list = !empty($detail[0]['obat_list']) ? $detail[0]['obat_list'] : [];
            ?>
            <?php foreach($obat_list as $row): ?>
            html += '<div class="form-group border-bottom pb-3 mb-3">';
            html += '<label><strong><?= addslashes($row['NAMA_OBAT']) ?></strong></label>';
            html += '<small class="text-muted d-block">Dosis: <?= addslashes($row['DOSIS']) ?></small>';
            html += '<div class="row mt-2">';

            for (var i = 1; i <= 7; i++) {
                var uniqueId = 'obat_<?= $row['id_detail'] ?>_hari_' + i;
                html += '<div class="col-md-1 text-center">';
                html += '<div class="checkbox checkbox-primary">';
                html += '<input type="checkbox" id="' + uniqueId + '" name="obat_<?= $row['id_detail'] ?>_hari_' + i + '" value="1">';
                html += '<label for="' + uniqueId + '" class="form-check-label">' + i + '</label>';
                html += '</div>';
                html += '</div>';
            }

            html += '</div>';
            html += '</div>';
            obatIndex++;
            <?php endforeach; ?>
        <?php endif; ?>

        $('#obat-checkbox-container').html(html);
    }

    // Function reloadTabelPemberian dihapus karena menyebabkan spam
    // Cukup gunakan loadJadwalData() saja

    function setCurrentMonthWeek() {
        // Auto-select bulan dan minggu sekarang
        var now = new Date();
        var currentMonth = now.getMonth() + 1; // JavaScript month is 0-based
        var currentDate = now.getDate();

        // Hitung minggu dalam bulan (sederhana: tanggal 1-7 = minggu 1, dst)
        var currentWeek = Math.ceil(currentDate / 7);
        if (currentWeek > 4) currentWeek = 4; // Max 4 minggu

        // Set bulan
        $('select[name="bulan"]').val(currentMonth);

        // Set minggu
        $('select[name="minggu"]').val(currentWeek);
    }

    function loadPatientVitals() {
        // Auto-fill TB, BB, LBB dari data pasien terbaru
        $.ajax({
            url: '<?= base_url('kemoterapi/JPOKEM/get_patient_vitals') ?>',
            type: 'GET',
            data: { nokun: '<?= isset($nokun) ? $nokun : '' ?>' },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    // Auto-fill form
                    if (response.data.tb) {
                        $('input[name="tb"]').val(response.data.tb);
                    }
                    if (response.data.bb) {
                        $('input[name="bb"]').val(response.data.bb);
                    }
                    if (response.data.lbb_m2) {
                        $('input[name="lbb_m2"]').val(response.data.lbb_m2);
                    }

                    // Tampilkan info data terakhir
                    var infoHtml = '<div class="alert alert-info alert-sm mt-2">' +
                        '<i class="fa fa-info-circle"></i> Data terakhir: ' + response.data.created_at +
                        '</div>';
                    $('.modal-body .row:last').after(infoHtml);

                } else {
                    // Tampilkan peringatan jika tidak ada data
                    var warningHtml = '<div class="alert alert-warning alert-sm mt-2">' +
                        '<i class="fa fa-exclamation-triangle"></i> ' + response.message +
                        '</div>';
                    $('.modal-body .row:last').after(warningHtml);
                }
            },
            error: function(xhr, status, error) {
            }
        });
    }

    function loadJadwalData() {
        // Load data jadwal dari database dan update indikator di tabel
        $.ajax({
            url: '<?= base_url('kemoterapi/JPOKEM/get_jadwal_data') ?>',
            type: 'POST',
            data: { id_jpok: currentIdJpok },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    updateTabelPemberian(response.data);
                }
            }
        });
    }

    function updateTabelPemberian(jadwalData) {
        // Reset semua indikator
        $('.jadwal-indicator').html('');
        $('.bb-tb-cell').html('');
        $('.lbb-cell').html('');

        // Update indikator berdasarkan data jadwal
        if (jadwalData && jadwalData.length > 0) {
            jadwalData.forEach(function(item) {
                var minggu = item.MINGGU;
                var detailId = item.ID_JPOK_DETAIL;
                var bulan = item.BULAN;
                var tahun = item.TAHUN;

                // Update indikator minum obat dengan filter periode
                for (var hari = 1; hari <= 7; hari++) {
                    var indicator = $('.jadwal-indicator[data-detail-id="' + detailId + '"][data-minggu="' + minggu + '"][data-hari="' + hari + '"][data-periode-bulan="' + bulan + '"][data-periode-tahun="' + tahun + '"]');
                    if (item['HARI_' + hari] == 1) {
                        indicator.html('<i class="fa fa-check text-success"></i>');
                    } else {
                        indicator.html('<i class="fa fa-times text-danger"></i>');
                    }
                }

                // Update BB/TB dan LBB dengan filter periode
                for (var hari = 1; hari <= 7; hari++) {
                    var bbTbCell = $('.bb-tb-cell[data-minggu="' + minggu + '"][data-hari="' + hari + '"][data-periode-bulan="' + bulan + '"][data-periode-tahun="' + tahun + '"]');
                    var lbbCell = $('.lbb-cell[data-minggu="' + minggu + '"][data-hari="' + hari + '"][data-periode-bulan="' + bulan + '"][data-periode-tahun="' + tahun + '"]');

                    if (item.TB && item.BB) {
                        bbTbCell.html(item.BB + '<br>' + item.TB);
                    }
                    if (item.LBB_M2) {
                        lbbCell.html(item.LBB_M2);
                    }
                }
            });
        }
    }
});
</script>
<?php endif; ?>

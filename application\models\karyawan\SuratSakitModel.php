<?php
defined('BASEPATH') or exit('No direct script access allowed');

class SuratSakitModel extends MY_Model
{
    protected $_table_name = 'dbsdm.sakit_pegawai';
    protected $_primary_key = 'ID_SAKIT';
    protected $_order_by = 'ID_SAKIT';
    protected $_order_by_type = 'DESC';

    var $tabel = 'medis.tb_sakit_pegawai sp';
    var $urutan_kolom = array(null, 'created_at', 'no_absen', 'mulai_sakit', 'selesai_sakit', 'pengisi');
    var $pencarian_kolom = array('created_at', 'no_absen', 'mulai_sakit', 'selesai_sakit', 'pengisi');
    var $urutan = array('created_at');

    public $rules = array(
        'id_pegawai' => array(
            'field' => 'ID_PEGAWAI',
            'label' => 'ID pegawai',
            'rules' => array(
                'required' => '%s wajib diisi',
                'numeric' => '%s wajib angka'
            ),
        ),

        'mulai' => array(
            'field' => 'TGL_MULAI_SAKIT',
            'label' => 'Tanggal mulai sakit',
            'rules' => array(
                'required' => '%s wajib diisi',
            ),
        ),

        'selesai' => array(
            'field' => 'TGL_SELESAI_SAKIT',
            'label' => 'Tanggal selesai sakit',
            'rules' => array(
                'required' => '%s wajib diisi',
            ),
        ),
    );

    function __construct()
    {
        parent::__construct();
        $this->db7 = $this->load->database('238', true);
    }

    public function jumlahSuratPerBulan()
    {
        $this->db->select('sp.id');
        $this->db->from($this->tabel);
        $this->db->where('MONTH(sp.created_at) = MONTH(CURRENT_DATE())');
        $this->db->where('YEAR(sp.created_at) = YEAR(CURRENT_DATE())');
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function simpan($data)
    {
        $this->db7->replace('dbsdm.sakit_pegawai', $data);
        return $this->db7->insert_id();
    }

    public function simpanLog($data)
    {
        $this->db7->insert('dbsdm.log_verifikasi_sakit', $data);
        return $this->db7->insert_id();
    }

    function simpanDiagnosis($data)
    {
        $this->db7->insert_batch('dbsdm.sakit_pegawai_diagnosis', $data);
    }

    public function simpanIntegrasi($data)
    {
        $this->db->insert('medis.tb_sakit_pegawai', $data);
    }

    public function history($nomr)
    {
        $this->db->select(
            'sp.id, sp.nokun, sp.id_sakit, sp.id_log, sp.no_absen, sp.no_surat, sp.mulai_sakit, sp.selesai_sakit,
            sp.sumber_surat,master.getNamaLengkapPegawai(ap.NIP) pengisi, sp.created_at'
        );
        $this->db->from($this->tabel);
        $this->db->join('pendaftaran.kunjungan k', 'k.NOMOR = sp.nokun', 'left');
        $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = k.NOPEN', 'left');
        $this->db->join('aplikasi.pengguna ap', 'ap.ID = sp.oleh', 'left');
        $this->db->where('p.NORM', $nomr);

        $i = 0;
        foreach ($this->pencarian_kolom as $pk) { // Loop kolom
            $_POST['search']['value'] = (isset($_POST['search']['value']) && !empty($_POST['search']['value'])) ? $_POST['search']['value'] : '';

            if ($_POST['search']['value']) { // Jika datatable tidak mengirim POST untuk pencarian
                if ($i === 0) { // Loop pertama
                    $this->db->group_start();
                    $this->db->like($pk, $_POST['search']['value']);
                } else {
                    $this->db->or_like($pk, $_POST['search']['value']);
                }

                if (count($this->pencarian_kolom) - 1 == $i) { // Loop terakhir
                    $this->db->group_end(); // Tutup kurung
                }
            }
            $i++;
        }

        if (isset($_POST['order'])) { // Pemrosesan order
            $this->db->order_by($this->urutan_kolom[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } elseif (isset($this->urutan)) {
            $urutan = $this->urutan;
            $this->db->order_by(key($urutan), $urutan[key($urutan)]);
        }
    }

    public function ambilTabel($nomr)
    {
        $this->history($nomr);
        $_POST['length'] = (isset($_POST['length']) && $_POST['length'] < 1) ? '10' : $_POST['length'];

        if (isset($_POST['start']) && $_POST['start'] > 1) {
            $_POST['start'] = $_POST['start'];
        }

        $this->db->limit($_POST['length'], $_POST['start']);
        // print_r($_POST);die;
        $query = $this->db->get();
        return $query->result();
    }

    public function hitungTersaring($nomr)
    {
        $this->history($nomr);
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function hitungSemua($nomr)
    {
        $this->db->from($this->tabel);
        $this->db->join('pendaftaran.kunjungan k', 'k.NOMOR = sp.nokun', 'left');
        $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = k.NOPEN', 'left');
        $this->db->join('aplikasi.pengguna ap', 'ap.ID = sp.oleh', 'left');
        $this->db->where('p.NORM', $nomr);
        return $this->db->count_all_results();
    }
}

/* End of file SuratSakitModel.php */
/* Location: ./application/models/karyawan/SuratSakitModel.php */
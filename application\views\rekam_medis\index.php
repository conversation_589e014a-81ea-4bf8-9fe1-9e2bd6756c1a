<style>
    .nav-tabs .nav-link {
        border: 1px solid #40739e;
    }
</style>
<!-- Start Page Title -->
<div class="row">
    <div class="col-md-3">
        <h4 class="page-title">Rekam Medik Elektronik</h4>
    </div>
    <div class="col-md mt-4">
        <button type="button" class="btn btn-primary btn-rounded openbtnGeser waves-effect" status="nonaktif">
            <i class="fa-solid fa-bars"></i> Data Pasien
        </button>
    </div>
</div>
<!-- End Page Title -->

<div class="row">
    <!-- SART LIST PASIEN -->
    <!-- <div class="col-sm-3"> -->
    <div id="mySidebarGeser" class="sidebarGeser" style="width: 350px;">
        <div class="card-box stickyTerbang">
            <div class="row mb-3">
                <a href="javascript:void(0)" class="closebtnGeser" status="aktif">×</a>
            </div>
            <!-- <div class="dropdown pull-right">
                <a href="#" class="dropdown-toggle arrow-none card-drop" data-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-ellipsis-v"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-right">
                    <a href="#" data-id="105010101" class="dropdown-item ruangan">Anggrek 1</a>
                    <a href="#" data-id="105010301" class="dropdown-item ruangan">Mawar 1</a>
                </div>
            </div> -->
            <div class="row form-group">
                <div class="col-sm-12">
                    <div class="pull-right">
                        Total Pasien &nbsp
                        <span class="badge badge-danger badge-pill" id="count-list-pasien"></span>
                    </div>
                    <h4 class="header-title">List Pasien</h4>
                </div>
            </div>
            <div class="form-group">
                <div class="btn-group btn-block">
                    <a class="btn btn-success waves-effect filter_aktif" role="button" data-status="1">Aktif</a>
                    <a class="btn btn-primary waves-effect filter_aktif" role="button" data-status="0">Semua</a>
                    <a class="btn btn-danger waves-effect filter_aktif" role="button" data-status="2">Pulang/Keluar</a>
                </div>
            </div>
            <!-- <div class="row form-group">
                <div class="col-sm">
                    <select name="rawat" id="rawat" class="form-control select2">
                        <option value="1">Rawat Jalan</option>
                        <option value="2">Rawat Inap</option>
                    </select>
                </div>
            </div> -->
            <div class="row form-group">
                <div class="col-sm-6">
                    <select name="ruangan" id="ruangan" class="form-control select2"></select>
                </div>
                <div class="col-sm-6">
                    <input type="text" class="form-control" id="filter_norm" name="filter_norm" placeholder="[ CARI PASIEN ]">
                </div>
            </div>
            <!-- <div class="row form-group">
                <div class="col-sm">
                    <input type="text" class="form-control" id="filter_norm" name="filter_norm" placeholder="[ CARI PASIEN ]">
                </div>
            </div> -->
            <ul class="list-group user-list" style="max-height: calc(100vh - 420px); overflow-y: auto;"></ul>
        </div>
    </div>
    <!-- </div> -->
    <!-- END LIST PASIEN -->

    <!-- <div class="col-sm-9"> -->
    <div id="mainGeser" class="col-md mt-1" style="margin-left: 350px;">
        <?php if ($pasien != null) { ?>
            <div class="card-box">
                <!-- Mulai info konsultasi -->
                <?php if ($jumlahNotif > 0): ?>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="alert alert-info text-justify">
                                <i class="fa fa-info-circle"></i> Ada <strong><?= $jumlahNotif ?> konsultasi untuk Anda yang belum dijawab</strong> di pasien ini
                            </div>
                        </div>
                    </div>
                <?php endif ?>
                <!-- Akhir info konsultasi -->
                <div class="row">
                    <div class="col-sm-2">
                        <div class="profile-info-name">
                            <?php if ($pasien['ID_JK'] == 1) { ?>
                                <img src="<?= base_url('assets/admin/assets/images/users/profile.jpg') ?>" class="img-thumbnail" alt="profile-image">
                            <?php } else { ?>
                                <img src="<?= base_url('assets/admin/assets/images/users/profile2.jpg') ?>" class="img-thumbnail" alt="profile-image">
                            <?php } ?>
                        </div>
                    </div>
                    <div class="col-sm-10">
                        <div class="profile-info-detail">
                            <div class="btn-group pull-right">
                                <a href="#viewInputDiagnosaDpjpRI" class="btn btn-danger btn-sm waves-effect tombolInputDiagnosaDpjpRI pad3px" data-nopen="<?= $getNomr['NOPEN'] ?>" data-nomr="<?= $getNomr['NORM'] ?>" data-nokun="<?= $getNomr['NOKUN'] ?>" data-toggle="modal">
                                    <i class="fa fa-stethoscope"></i>
                                </a>
                                <?php if (isset($getNomr['IDPENJAMIN']) && $getNomr['IDPENJAMIN'] == '2'): ?>
                                    <a href="#" class="btn btn-success btn-sm waves-effect" id="buka-icare">
                                        <i class="fa fa-hand-holding-medical"></i> i-Care
                                    </a>
                                <?php endif ?>
                                <button type="button" class="btn btn-warning btn-sm waves-effect d-none" id="tbl-ews-profil" data-container="body" data-toggle="popover" data-placement="top">
                                    <span class="text-black">Skor EWS</span> <span class="badge badge-danger" id="jml-ews-profil"></span>
                                </button>
                                <!-- Mulai REGKAN -->
                                <a href="#viewModalREGKANRi" class="btn btn-success btn-sm waves-effect viewModalRegkanRi" data-toggle="modal" data-nokun="<?= $pasien['NOKUN'] ?>" data-nomr="<?= $pasien['NORM'] ?>" data-nopen="<?= $pasien['NOPEN'] ?>">
                                    <i class="fas fa-edit"></i> Pelayanan Kanker
                                </a>
                                <!-- Akhir REGKAN -->
                                <a class="btn btn-secondary btn-sm waves-effect" href="#modal_cp" data-id="<?= $pasien['NORM'] ?>" data-target="#modal_cp" data-toggle="modal" data-backdrop="static" data-keyboard="false">
                                    <em>Clinical Pathway</em>
                                </a>
                                <a class="btn btn-purple btn-sm waves-effect" href="#modal_ppk" data-id="<?= $pasien['NORM'] ?>" data-target="#modal_ppk" data-toggle="modal" data-backdrop="static" data-keyboard="false">PPK</a>
                                <button type="button" class="btn btn-primary btn-sm waves-effect" id="history_pendaftaran" data-id="<?= $pasien['NORM'] ?>" data-toggle="tooltip" data-placement="bottom" data-original-title="History Pendaftaran">
                                    <i class="fa fa-history"></i>
                                </button>
                            </div>

                            <h4 class="card-title">
                                <?php if ($jmlHasiLabKritis > 0): ?>
                                    <?php if ($ceklogKritis == 0): ?>
                                        <div class="row" id="rowKritis">
                                            <div class="col-md-12">
                                                <div class="alert alert-danger text-justify" style="margin-left:-10px">
                                                    <i class="fa fa-info-circle"></i> Nilai Kritis Hasil Lab: <strong>Kritis.</strong>
                                                    <a class="text-white" id="buka_hasil_lab" href="#">Cek Hasil Lab</a>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endif ?>
                                    <?php if ($ceklogKritis > 0 && $cekHasilLabPk['NOKUN'] > $logKritis['nokun']): ?>
                                        <div class="row" id="rowKritis">
                                            <div class="col-md-12">
                                                <div class="alert alert-warning text-justify" style="margin-left:-10px">
                                                    <i class="fa fa-info-circle"></i> Nilai Kritis Hasil Lab: <strong>Kritis.</strong>
                                                    <a class="text-white" id="buka_hasil_lab" href="#">Cek Hasil Lab</a>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endif ?>
                                <?php endif ?>
                                <strong><?= $pasien['NAMA_PASIEN'] . ' [' . $pasien['NORM'] . ']' ?></strong>
                                <?php if ($dataHIV['STATUS_HIV'] != "") { ?>
                                    <span class="alert alert-danger p-0 ml-1">HIV</span>
                                <?php } ?>
                                <?php if ($infoPaliatif['paliatif'] == 1) { ?>
                                    <span class="alert alert-warning p-0 ml-1">Paliatif</span>
                                <?php } else if ($infoPaliatif['paliatif'] == 2) { ?>
                                        <span class="alert alert-danger p-0 ml-1" style="color:#7d5bff;background-color:rgba(118, 80, 240, 0.2);border-color:rgba(118, 80, 240, 0.5)">Paliatif</span>
                                <?php } ?>
                                <?php if (isset($dataDNR['NORM'])) { ?>
                                    <span class="alert alert-danger p-0 ml-1">DNR</span>
                                <?php } ?>
                            </h4>
                            <h5 class="card-subtitle mb-3 text-muted">
                                <i><?= date('d/m/Y', strtotime($pasien['TANGGAL_LAHIR'])) . ' - ' . $pasien['UMUR'] . ' - ' . $pasien['JK'] ?></i>
                            </h5>
                            <p class="text-muted mb-2">
                                <i><?= $pasien['DOKTER_TUJUAN'] . ' - ' . $pasien['RUANGAN_TUJUAN'] . ' - ' . date('d/m/Y, H.i.s', strtotime($pasien['TANGGAL_DAFTAR'])) ?></i>
                            </p>
                            <p class="text-muted mb-2">
                                <em>
                                    <?php
                                    $pasienCOB = "";
                                    if ($pasien['ID_STATUS_PENILAIAN_GIZI'] == 4081 || $pasien['ID_STATUS_PENILAIAN_GIZI'] == 4082) {
                                        $statusGizi = $pasien['STATUS_PENILAIAN_GIZI'] == '' ? 'Belum ada info' : '<strong class="text-warning">' . $pasien['STATUS_PENILAIAN_GIZI'] . '</strong>';
                                        $statusGiziBtn = '<button type="button" class="btn btn-info btn-sm btn-rounded waves-effect" data-container="body" data-toggle="popover" data-placement="bottom" data-content="' . $pasien['STATUS_PENILAIAN_GIZI_KETERANGAN'] . '"> <i class="fa fa-info"></i></button>';
                                        $statusGiziKet = ' / Info : ' . $pasien['STATUS_PENILAIAN_GIZI_KETERANGAN'];
                                    } else {
                                        $statusGizi = $pasien['STATUS_PENILAIAN_GIZI'] == '' ? 'Belum ada info' : $pasien['STATUS_PENILAIAN_GIZI'];
                                        $statusGiziBtn = '<button type="button" class="btn btn-info btn-sm btn-rounded waves-effect" data-container="body" data-toggle="popover" data-placement="bottom" data-content="' . $pasien['STATUS_PENILAIAN_GIZI_KETERANGAN'] . '"> <i class="fa fa-info"></i></button>';
                                        $statusGiziKet = "";
                                    }

                                    if (isset($pasien['PENJAMIN']) && $pasien['STATUS_PENJAMIN_COB'] == 1) {
                                        $pasienCOB = "<span style='color: #F1CF5A;'><b>COB</b></span> - ";
                                    }

                                    $statusLab = ($jmlHasiLabKritis > 0) ? '<strong class="text-danger">'.$jmlHasiLabKritis.' HASIL KRITIS</strong> - ' : '<strong class="text-success">AMAN</strong>';

                                    echo 'Penjamin: ' . $pasienCOB . $pasien['PENJAMIN'] . ' / ' . 'Tujuan: ' . $pasien['TUJUAN_PASIEN'] . ' / TB: ' . $pasien['tinggi_badan'] . ' / BB: ' . $pasien['berat_badan'] . ' / Kesadaran: ' . $pasien['KESADARAN'] . ' / G. Darah: ' . $pasien['GOL_DARAH'] . ' / Diagnosa Masuk: ' . $pasien['DIAGNOSA_MASUK'] . ' - ' . $pasien['DESKRIPSI_DIAGNOSA_MASUK'] . ' / Asesmen Gizi: <button type="button" class="btn btn-info btn-sm btn-rounded" data-container="body" data-toggle="popover" data-placement="bottom" data-content="' . $pasien['ASESMEN_GIZI'] . '"> <i class="fa fa-info"></i></button> / Status Gizi: <span id="viewTourPageGizi">' . $statusGizi . '</span> ' . $statusGiziBtn . ' / Pendaftaran Sebelumnya: ' . $pasien['PENDAFTARAN_SEBELUMNYA']
                                        ?>
                                </em>
                            </p>

                            <?php if (isset($pasien['ASN_TGL_INPUT']) || isset($infoconsenBgsi) || isset($statusBgsi)): ?>
                                <div class="row form-group">
                                    <?php if (isset($pasien['ASN_TGL_INPUT'])): ?>
                                        <div class="col-sm-12 col-md">
                                            <div class="alert alert-warning mb-0">
                                                <img src="<?= base_url('assets/admin/assets/images/favicon.png') ?>" style="max-height: 20px;">
                                                ASN Kemenkes
                                            </div>
                                        </div>
                                        <?php
                                    endif;
                                    if (isset($infoconsenBgsi)):
                                        ?>
                                        <div class="col-sm-12 col-md">
                                            <?php if ($infoconsenBgsi == 0) { ?>
                                                <div class="alert alert-danger mb-0">
                                                    <i class="fa fa-info-circle"></i> Belum <em>inform concern</em> BGSi
                                                </div>
                                            <?php } elseif ($infoconsenBgsi > 0) { ?>
                                                <div class="alert alert-success mb-0">
                                                    <i class="fa fa-info-circle"></i> Sudah <em>inform concern</em> BGSi
                                                </div>
                                            <?php } ?>
                                        </div>
                                        <?php
                                    endif;
                                    if (isset($statusBgsi)):
                                        ?>
                                        <div class="col-sm-12 col-md">
                                            <?php if ($statusBgsi == 0) { ?>
                                                <div class="alert alert-danger mb-0">
                                                    <i class="fa fa-info-circle"></i> Belum disampel BGSi
                                                </div>
                                            <?php } elseif ($statusBgsi > 0) { ?>
                                                <div class="alert alert-success mb-0">
                                                    <i class="fa fa-info-circle"></i> Sudah disampel BGSi
                                                </div>
                                            <?php } ?>
                                        </div>
                                    <?php endif ?>
                                </div>
                            <?php endif ?>

                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-sm-2">
                        <p class="text-white m-t-5">History Penunjang</p>
                    </div>
                    <div class="col-sm-10">
                        <div class="form-group">
                            <div class="btn-group btn-block">
                                <a class="btn btn-danger waves-effect" role="button" id="history_patologi_klinik">
                                    Lab P. Klinik
                                </a>
                                <a class="btn btn-purple waves-effect" role="button" id="history_patologi_anatomi">
                                    Lab P. Anatomi
                                </a>
                                <a class="btn btn-secondary waves-effect text-white" role="button" id="history_radiologi">
                                    Radiologi
                                </a>
                                <a href="#viewTandaVitalMon" class="btn btn-warning waves-effect tombolTandaVitalMon" role="button" id="tandaVitalMon" data-toggle="modal" data-nomr="<?= $pasien['NORM'] ?>" data-nokun="<?= $pasien['NOKUN'] ?>">
                                    Tanda Vital
                                </a>
                                <a href="#modal-history-etimja-info" class="btn btn-custom waves-effect" id="tbl-history-etimja-info" data-toggle="modal" data-id="<?= $pasien['NORM'] ?>">
                                    eTimja <span class="badge badge-danger badge-pill" id="jumlah-etimja-info"></span>
                                </a>
                                <a href="#modal-history-rekonsiliasi-obat" class="btn btn-info waves-effect text-white" data-toggle="modal" id="tbl-history-rekonsiliasi-obat" data-id="<?= $pasien['NORM'] ?>">
                                    Rekon. Obat <span class="badge badge-danger badge-pill" id="count_rekon_obat"></span>
                                </a>
                                <a href="#" class="btn btn-primary waves-effect text-white" data-toggle="modal" id="history_kenseling_menu" data-id="<?= $pasien['NORM'] ?>">
                                    Konseling <span class="badge badge-danger badge-pill" id="count_konseling"></span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <?php
                if (isset($infoPermintaanDarah)) {
                    if ($infoPermintaanDarah['tgl_minta'] >= $pasien['TANGGAL_DAFTAR']) {
                        ?>
                        <!-- Mulai info permintaan darah -->
                        <div class="row">
                            <div class="col-sm-2">
                                <span class="text-white">Info Permintaan Darah</span>
                            </div>
                            <div class="col-sm-10">
                                <div class="row mx-0">
                                    <div class="col-sm">
                                        <div class="row">
                                            <strong>Tanggal Permintaan</strong>
                                        </div>
                                        <div class="row"><?= date('d/m/Y, H.i.s', strtotime($infoPermintaanDarah['tgl_minta'])) ?></div>
                                    </div>
                                    <div class="col-sm">
                                        <div class="row">
                                            <strong>Tanggal Diperlukan</strong>
                                        </div>
                                        <div class="row"><?= date('d/m/Y', strtotime($infoPermintaanDarah['tgl_diperlukan'])) ?></div>
                                    </div>
                                    <div class="col-sm">
                                        <div class="row">
                                            <strong>Alasan</strong>
                                        </div>
                                        <div class="row"><?= !empty($infoPermintaanDarah['alasan']) ? $infoPermintaanDarah['alasan'] : '-' ?></div>
                                    </div>
                                    <div class="col-sm">
                                        <div class="row">
                                            <strong>Status</strong>
                                        </div>
                                        <div class="row"><?= $infoPermintaanDarah['status'] ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Akhir info permintaan darah -->
                        <?php
                    }
                }
                ?>

                <?php
                if (isset($tarifPasien)):
                    $formatUang = new NumberFormatter('id_ID', NumberFormatter::CURRENCY);
                    $warnaTarif = null;
                    $selisihTarif = $tarifPasien['KLAIM_BPJS'] - $tarifPasien['TARIF_RS'];
                    if ($selisihTarif < 0) {
                        $warnaTarif = 'alert-danger';
                    } elseif ($selisihTarif > 0) {
                        $warnaTarif = 'alert-success';
                    } else {
                        $warnaTarif = 'alert-warning';
                    }
                    ?>
                    <!-- Mulai tarif pasien -->
                    <div class="row mx-0 alert text-justify <?= $warnaTarif ?>">
                        <div class="col-sm">
                            <div class="row">
                                <strong>Tarif RS</strong>
                            </div>
                            <div class="row"><?= $formatUang->formatCurrency($tarifPasien['TARIF_RS'], 'IDR') ?></div>
                        </div>
                        <div class="col-sm">
                            <div class="row">
                                <strong>Perkiraan Klaim BPJS</strong>
                            </div>
                            <div class="row"><?= $formatUang->formatCurrency($tarifPasien['KLAIM_BPJS'], 'IDR') ?></div>
                        </div>
                        <div class="col-sm">
                            <div class="row">
                                <strong>Selisih</strong>
                            </div>
                            <div class="row"><?= $formatUang->formatCurrency($selisihTarif, 'IDR') ?></div>
                        </div>
                    </div>
                    <!-- Akhir tarif pasien -->
                <?php endif ?>

                

                <?php if (isset($infoPemberianAntiBiotik)): ?>
                    <!-- Mulai info Alert Antibiotik -->
                    <div class="row">
                        <div class="col-md">
                            <div class="alert alert-warning text-justify" id="notifAntiBiotikTourPage">
                                <i class="fa fa-info-circle"></i> <?= $infoPemberianAntiBiotik['NOTIF_DESKRIPSI'] ?>
                            </div>
                        </div>
                    </div>
                    <!-- Akhir info Alert Antibiotik -->
                <?php endif ?>

                <?php if (isset($infoResikoJatuh)): ?>
                    <!-- Mulai info risiko jatuh -->
                    <div class="row">
                        <div class="col-md">
                            <div class="alert alert-warning text-justify">
                                <i class="fa fa-info-circle"></i> <?= 'Risiko Jatuh: <strong>' . $infoResikoJatuh['Deskripsi'] . ' </strong>Skor: <strong>' . $infoResikoJatuh['SKOR'] . ' </strong>dengan: <strong>' . $infoResikoJatuh['JENIS'] . ' </strong>pada <strong>' . date('d-m-Y, H.i.s', strtotime($infoResikoJatuh['TANGGAL_PEMBUATAN'])) . '.</strong> Silakan lakukan pemantauan risiko jatuh.' ?>
                            </div>
                        </div>
                    </div>
                    <!-- Akhir info risiko jatuh -->
                <?php endif ?>

                <!-- Mulai info covid -->
                <!-- <!?php //if (isset($infoCovid['STATUS_HASIL']) && $infoCovid['STATUS_HASIL'] == 1) : ?>
                    <div class="row">
                        <div class="col-md">
                            <div class="alert alert-danger text-justify">
                                <i class="fa fa-info-circle"></i> <strong>Informasi!</strong> Tanggal Pemeriksaan: <strong><?//= $infoCovid['TANGGAL_PEMERIKSAAN'] ?></strong>, Pemeriksaan: <strong><?//= $infoCovid['PEMERIKSAAN'] ?></strong>, Hasil Pemeriksaan: <strong><?//= $infoCovid['HASIL'] ?></strong>
                            </div>
                        </div>
                    </div>
                <!?php //endif ?> -->
                <!-- Akhir info covid -->

                <?php if (isset($infoEWS['SKOR_EWS']) && $infoEWS['SKOR_EWS'] != ''): ?>
                    <!-- Mulai info EWS -->
                    <div class="row">
                        <div class="col-md">
                            <div class="alert alert-warning text-justify">
                                <i class="fa fa-info-circle"></i> Skor EWS <strong><?= $infoEWS['SKOR_EWS'] ?></strong> pada <strong><?= date('d-m-Y, H.i.s', strtotime($infoEWS['EWS_TERAKHIR'])) ?>.</strong>
                                <?php if (isset($infoEWS['EWS_LAGI_PADA_JAM'])): ?>
                                    Isi EWS lagi dalam <strong><?= $infoEWS['EWS_LAGI_DALAM'] ?></strong> pada <strong><?= date('d-m-Y, H.i.s', strtotime($infoEWS['EWS_LAGI_PADA_JAM'])) ?>.</strong>
                                <?php endif ?>
                                <a class="text-white" id="buka-pemantauan-EWS">Buka Pemantauan EWS</a>
                            </div>
                        </div>
                    </div>
                    <!-- Akhir info EWS -->
                <?php endif ?>

                <?php if (isset($nyeriAkhir) && $nyeriAkhir != ''): ?>
                    <!-- Mulai info nyeri -->
                    <div class="row">
                        <div class="col-md">
                            <div class="alert alert-warning text-justify">
                                <i class="fa fa-info-circle"></i> Skor nyeri <strong><?= $nyeriAkhir['skor'] ?></strong> pada <strong><?= $waktuNyeri ?></strong> dengan metode <?= $nyeriAkhir['metode'] ?>.
                                <?= $pemantauanNyeri . $waktuPantauNyeriLagi ?>. <a class="text-white" id="buka-pemantauan-nyeri">Buka Pemantauan Nyeri</a>
                            </div>
                        </div>
                    </div>
                    <!-- Akhir info nyeri -->
                <?php endif ?>
                <div class="clearfix"></div>
            </div>

            <div class="card-box bg-primary text-white d-none" id="profil-mini-rawat-inap" style="z-index: 2;">
                <strong><?= $pasien['NAMA_PASIEN'] . ' [' . $pasien['NORM'] . ']' ?></strong>
                <em><?= $pasien['UMUR'] . '/' . $pasien['JK'] . '/' . $pasien['RUANGAN_TUJUAN'] . '/ Status Gizi: <strong class="text-warning">' . $pasien['STATUS_PENILAIAN_GIZI'] . '</strong>' ?></em>
            </div>

            <div class="card-box menuTourpage">
                <ul class="nav nav-tabs" id="myTab">
                    <!-- START MENU DASHBOARD -->
                    <li class="nav-item">
                        <a href="#dashboard" data-toggle="tab" aria-expanded="false" class="nav-link dashboard bg-success">
                            <i class="fa fa-stethoscope"></i>
                        </a>
                    </li>
                    <!-- END MENU DASHBOARD -->
                    <!-- START CPPT LIST -->
                    <li class="nav-item">
                        <a href="#cpptList" data-toggle="tab" aria-expanded="false" class="nav-link cpptList">
                            CPPT <em>List</em>
                        </a>
                    </li>
                    <!-- END CPPT LIST -->
                    <!-- START PENGKAJIAN LIST -->
                    <li class="nav-item">
                        <a href="#menu-pengkajian-list" data-toggle="tab" aria-expanded="false" class="nav-link" id="pengkajian-list">Pengkajian <em>List</em></a>
                    </li>

                    <!-- <li class="nav-item">
                        <a href="#menu-berkas-list" data-toggle="tab" aria-expanded="false" class="nav-link" id="berkas-list">Berkas Pasien</a>
                    </li> -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Berkas <em>Scan</em>
                        </a>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="#menu-berkas-list" data-toggle="tab" aria-expanded="false" id="berkas-list">
                                Berkas <em>Scan</em> Rekam Medis
                            </a>
                            <a class="dropdown-item" href="#menu-berkas-pasien-luar" data-toggle="tab" aria-expanded="false" id="berkas-pasien-luar">
                                Berkas <em>Scan</em> Pasien Luar RSKD
                            </a>
                            <a class="dropdown-item" href="#menu-berkas-scan" data-toggle="tab" aria-expanded="false" id="berkas-scan">
                                Berkas <em>Scan</em> Lainnya
                            </a>
                        </div>
                    </li>
                    <li class="nav-item">
                        <a href="#menu-faktor-resiko" data-toggle="tab" aria-expanded="false" class="nav-link" id="faktor-resiko">Faktor Resiko</a>
                    </li>
                    <!-- END PENGKAJIAN LIST -->
                    <!-- START TBAK -->
                    <li class="nav-item">
                        <a href="#menu-tbak" data-toggle="tab" aria-expanded="false" class="nav-link" id="tbak-menu">
                            TBAK
                        </a>
                    </li>
                    <!-- END TBAK -->
                    <!-- START KHUSUS JIKA RUANGAN ANYELIR 1 -->
                    <?php if ($pasien['ID_RUANGAN'] == 105020101 || $pasien['ID_RUANGAN'] == 105020103) { ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle bg-success" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                                Anyelir
                            </a>
                            <div class="dropdown-menu pre-scrollable" role="menu">
                                <a href="#permintaanDirawat" class="dropdown-item permintaanDirawat" data-toggle="tab">
                                    Permintaan Dirawat
                                </a>
                                <a class="dropdown-item pengkajianTerapiSistemikRJAnyelir1" data-toggle="tab" href="#pengkajianTerapiSistemikRJAnyelir1">
                                    [Perawat] Pengkajian Terapi Sistemik Kanker RJ
                                </a>
                                <a class="dropdown-item pengkajianRiDewasaMedis" data-toggle="tab" href="#pengkajianRiDewasaMedis">
                                    [Medis] Pengkajian Medis
                                </a>
                                <a class="dropdown-item pttd" data-toggle="tab" href="#menu-pttd">
                                    Persetujuan Tindakan Transfusi Darah
                                </a>
                                <a class="dropdown-item persetujuanTindakanPengobatanKemoterapi" data-toggle="tab" href="#persetujuanTindakanPengobatanKemoterapi">
                                    Persetujuan Tindakan Pengobatan Kemoterapi
                                </a>
                                <a class="dropdown-item persetujuan-tindakan-kedokteran" data-toggle="tab" href="#persetujuan-tindakan-kedokteran">
                                    Persetujuan Tindakan Kedokteran
                                </a>
                                <a class="dropdown-item ot-keperawatan" data-toggle="tab" href="#menu-ot-keperawatan">
                                    Observasi dan Tindakan Keperawatan
                                </a>
                                <a class="dropdown-item ews" data-toggle="tab" href="#menu-ews">
                                    EWS <i>(Early Warning Score)</i>
                                </a>
                                <a class="dropdown-item pews" data-toggle="tab" href="#menu-pews">
                                    PEWS <i>(Pediatric Early Warning Score)</i>
                                </a>
                                <a href="#barthelIndek" class="dropdown-item barthelIndek" data-toggle="tab">
                                    <i>Berthel Index</i>
                                </a>
                                <a href="#pemberianIntravena" class="dropdown-item pemberianIntravena" data-toggle="tab">
                                    Pemberian Cairan Intravena
                                </a>
                                <a class="dropdown-item perencanaan-ak" data-toggle="tab" href="#menu-perencanaan-ak">
                                    Perencanaan Asuhan Keperawatan
                                </a>
                                <a href="#cppt" class="dropdown-item cppt" data-toggle="tab">
                                    CPPT
                                </a>
                                <a class="dropdown-item pemberianDanPemantauanDarah" data-toggle="tab" href="#pemberianDanPemantauanDarah">
                                    Pemberian dan Pemantauan Tranfusi Darah
                                </a>
                                <a href="#pemantauanNyeri" class="dropdown-item pemantauanNyeri" data-toggle="tab">
                                    Pemantauan Nyeri
                                </a>
                                <a href="#serahTerimaPemRi" class="dropdown-item serahTerimaPemRi" data-toggle="tab">
                                    Serah Terima Pemeriksaan
                                </a>
                                <a class="dropdown-item formulir-pindah-ruangan" data-toggle="tab" href="#menu-formulir-pindah-ruangan">
                                    Formulir Perpindahan Pasien Antar Ruang
                                </a>
                            </div>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle bg-success" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                                Transfusi (PRC/TC)
                            </a>
                            <div class="dropdown-menu pre-scrollable" role="menu">
                                <a href="#permintaanDirawat" class="dropdown-item permintaanDirawat" data-toggle="tab">
                                    Permintaan Dirawat
                                </a>
                                <a class="dropdown-item pttd" data-toggle="tab" href="#menu-pttd">
                                    Persetujuan Tindakan Transfusi Darah
                                </a>
                                <a class="dropdown-item ot-keperawatan" data-toggle="tab" href="#menu-ot-keperawatan">
                                    Observasi dan Tindakan Keperawatan
                                </a>
                                <a class="dropdown-item ews" data-toggle="tab" href="#menu-ews">
                                    EWS <i>(Early Warning Score)</i>
                                </a>
                                <a href="#pemberianIntravena" class="dropdown-item pemberianIntravena" data-toggle="tab">
                                    Pemberian Cairan Intravena
                                </a>
                                <a class="dropdown-item perencanaan-ak" data-toggle="tab" href="#menu-perencanaan-ak">
                                    Perencanaan Asuhan Keperawatan
                                </a>
                                <a href="#cppt" class="dropdown-item cppt" data-toggle="tab">
                                    CPPT
                                </a>
                                <a class="dropdown-item pemberianDanPemantauanDarah" data-toggle="tab" href="#pemberianDanPemantauanDarah">
                                    Pemberian dan Pemantauan Tranfusi Darah
                                </a>
                                <a href="#pemantauanNyeri" class="dropdown-item pemantauanNyeri" data-toggle="tab">
                                    Pemantauan Nyeri
                                </a>
                                <a class="dropdown-item formulir-pindah-ruangan" data-toggle="tab" href="#menu-formulir-pindah-ruangan">
                                    Formulir Perpindahan Pasien Antar Ruang
                                </a>
                            </div>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle bg-success" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                                Tindakan Prosedur
                            </a>
                            <div class="dropdown-menu pre-scrollable" role="menu">
                                <a href="#permintaanDirawat" class="dropdown-item permintaanDirawat" data-toggle="tab">
                                    Permintaan Dirawat
                                </a>
                                <a class="dropdown-item persetujuan-tindakan-kedokteran" data-toggle="tab" href="#persetujuan-tindakan-kedokteran">
                                    Persetujuan Tindakan Kedokteran
                                </a>
                                <a class="dropdown-item ot-keperawatan" data-toggle="tab" href="#menu-ot-keperawatan">
                                    Observasi dan Tindakan Keperawatan
                                </a>
                                <a class="dropdown-item ews" data-toggle="tab" href="#menu-ews">
                                    EWS <i>(Early Warning Score)</i>
                                </a>
                                <a href="#pemberianIntravena" class="dropdown-item pemberianIntravena" data-toggle="tab">
                                    Pemberian Cairan Intravena
                                </a>
                                <a class="dropdown-item perencanaan-ak" data-toggle="tab" href="#menu-perencanaan-ak">
                                    Perencanaan Asuhan Keperawatan
                                </a>
                                <a href="#cppt" class="dropdown-item cppt" data-toggle="tab">
                                    CPPT
                                </a>
                                <a href="#pemantauanNyeri" class="dropdown-item pemantauanNyeri" data-toggle="tab">
                                    Pemantauan Nyeri
                                </a>
                                <a href="#serahTerimaPemRi" class="dropdown-item serahTerimaPemRi" data-toggle="tab">
                                    Serah Terima Pemeriksaan
                                </a>
                                <a class="dropdown-item formulir-pindah-ruangan" data-toggle="tab" href="#menu-formulir-pindah-ruangan">
                                    Formulir Perpindahan Pasien Antar Ruang
                                </a>
                                <a class="dropdown-item laporanHasilPemeriksaan" data-toggle="tab" href="#laporanHasilPemeriksaan">
                                    Laporan Hasil Pemeriksaan
                                </a>
                            </div>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle bg-success" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                                Bonevell & Zometa
                            </a>
                            <div class="dropdown-menu pre-scrollable" role="menu">
                                <a href="#permintaanDirawat" class="dropdown-item permintaanDirawat" data-toggle="tab">
                                    Permintaan Dirawat
                                </a>
                                <a class="dropdown-item persetujuan-tindakan-kedokteran" data-toggle="tab" href="#persetujuan-tindakan-kedokteran">
                                    Persetujuan Tindakan Kedokteran
                                </a>
                                <a class="dropdown-item ot-keperawatan" data-toggle="tab" href="#menu-ot-keperawatan">
                                    Observasi dan Tindakan Keperawatan
                                </a>
                                <a class="dropdown-item ews" data-toggle="tab" href="#menu-ews">
                                    EWS <i>(Early Warning Score)</i>
                                </a>
                                <a class="dropdown-item perencanaan-ak" data-toggle="tab" href="#menu-perencanaan-ak">
                                    Perencanaan Asuhan Keperawatan
                                </a>
                                <a href="#cppt" class="dropdown-item cppt" data-toggle="tab">
                                    CPPT
                                </a>
                                <a href="#pemantauanNyeri" class="dropdown-item pemantauanNyeri" data-toggle="tab">
                                    Pemantauan Nyeri
                                </a>
                                <a class="dropdown-item formulir-pindah-ruangan" data-toggle="tab" href="#menu-formulir-pindah-ruangan">
                                    Formulir Perpindahan Pasien Antar Ruang
                                </a>
                            </div>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle bg-success" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                                Kemoterapi Sistemik & Intravesical
                            </a>
                            <div class="dropdown-menu pre-scrollable" role="menu">
                                <a href="#permintaanDirawat" class="dropdown-item permintaanDirawat" data-toggle="tab">
                                    Permintaan Dirawat
                                </a>
                                <a class="dropdown-item pengkajianTerapiSistemikRJKemoterapi" data-toggle="tab" href="#pengkajianTerapiSistemikRJKemoterapi">
                                    [Perawat] Pengkajian Terapi Sistemik Kanker RJ
                                </a>
                                <a class="dropdown-item pengkajianRiDewasaMedis" data-toggle="tab" href="#pengkajianRiDewasaMedis">
                                    [Medis] Pengkajian Medis
                                </a>
                                <a class="dropdown-item persetujuanTindakanPengobatanKemoterapi" data-toggle="tab" href="#persetujuanTindakanPengobatanKemoterapi">
                                    Persetujuan Tindakan Pengobatan Kemoterapi
                                </a>
                                <a class="dropdown-item persetujuan-tindakan-kedokteran" data-toggle="tab" href="#persetujuan-tindakan-kedokteran">
                                    Persetujuan Tindakan Kedokteran
                                </a>
                                <a class="dropdown-item ot-keperawatan" data-toggle="tab" href="#menu-ot-keperawatan">
                                    Observasi dan Tindakan Keperawatan
                                </a>
                                <a class="dropdown-item ews" data-toggle="tab" href="#menu-ews">
                                    EWS <i>(Early Warning Score)</i>
                                </a>
                                <a class="dropdown-item pews" data-toggle="tab" href="#menu-pews">
                                    PEWS <i>(Pediatric Early Warning Score)</i>
                                </a>
                                <a href="#barthelIndek" class="dropdown-item barthelIndek" data-toggle="tab">
                                    <i>Berthel Index</i>
                                </a>
                                <a href="#pemberianIntravena" class="dropdown-item pemberianIntravena" data-toggle="tab">
                                    Pemberian Cairan Intravena
                                </a>
                                <a class="dropdown-item perencanaan-ak" data-toggle="tab" href="#menu-perencanaan-ak">
                                    Perencanaan Asuhan Keperawatan
                                </a>
                                <a href="#cppt" class="dropdown-item cppt" data-toggle="tab">
                                    CPPT
                                </a>
                                <a class="dropdown-item pemberianDanPemantauanDarah" data-toggle="tab" href="#pemberianDanPemantauanDarah">
                                    Pemberian dan Pemantauan Tranfusi Darah
                                </a>
                                <a href="#pemantauanNyeri" class="dropdown-item pemantauanNyeri" data-toggle="tab">
                                    Pemantauan Nyeri
                                </a>
                                <a href="#serahTerimaPemRi" class="dropdown-item serahTerimaPemRi" data-toggle="tab">
                                    Serah Terima Pemeriksaan
                                </a>
                                <a class="dropdown-item formulir-pindah-ruangan" data-toggle="tab" href="#menu-formulir-pindah-ruangan">
                                    Formulir Perpindahan Pasien Antar Ruang
                                </a>
                            </div>
                        </li>
                    <?php } ?>
                    <!-- END KHUSUS JIKA RUANGAN ANYELIR 1 -->
                    <!-- START KHUSUS JIKA RUANGAN ANYELIR 2 -->
                    <?php if ($pasien['ID_RUANGAN'] == 105020102) { ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle bg-success" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                                Anyelir
                            </a>
                            <div class="dropdown-menu pre-scrollable" role="menu">
                                <a href="#permintaanDirawat" class="dropdown-item permintaanDirawat" data-toggle="tab">
                                    Permintaan Dirawat
                                </a>
                                <a class="dropdown-item pengkajianTerapiSistemikRJAnyelir2" data-toggle="tab" href="#pengkajianTerapiSistemikRJAnyelir2">
                                    [Perawat] Pengkajian Terapi Sistemik Kanker RJ
                                </a>
                                <a class="dropdown-item pengkajianRiDewasaMedis" data-toggle="tab" href="#pengkajianRiDewasaMedis">
                                    [Medis] Pengkajian Medis
                                </a>
                                <a class="dropdown-item pttd" data-toggle="tab" href="#menu-pttd">
                                    Persetujuan Tindakan Transfusi Darah
                                </a>
                                <a class="dropdown-item persetujuanTindakanPengobatanKemoterapi" data-toggle="tab" href="#persetujuanTindakanPengobatanKemoterapi">
                                    Persetujuan Tindakan Pengobatan Kemoterapi
                                </a>
                                <a class="dropdown-item persetujuan-tindakan-kedokteran" data-toggle="tab" href="#persetujuan-tindakan-kedokteran">
                                    Persetujuan Tindakan Kedokteran
                                </a>
                                <a class="dropdown-item ot-keperawatan" data-toggle="tab" href="#menu-ot-keperawatan">
                                    Observasi dan Tindakan Keperawatan
                                </a>
                                <a class="dropdown-item ews" data-toggle="tab" href="#menu-ews">
                                    EWS <i>(Early Warning Score)</i>
                                </a>
                                <a class="dropdown-item pews" data-toggle="tab" href="#menu-pews">
                                    PEWS <i>(Pediatric Early Warning Score)</i>
                                </a>
                                <a href="#barthelIndek" class="dropdown-item barthelIndek" data-toggle="tab">
                                    <i>Berthel Index</i>
                                </a>
                                <a href="#pemberianIntravena" class="dropdown-item pemberianIntravena" data-toggle="tab">
                                    Pemberian Cairan Intravena
                                </a>
                                <a class="dropdown-item perencanaan-ak" data-toggle="tab" href="#menu-perencanaan-ak">
                                    Perencanaan Asuhan Keperawatan
                                </a>
                                <a href="#cppt" class="dropdown-item cppt" data-toggle="tab">
                                    CPPT
                                </a>
                                <a class="dropdown-item pemberianDanPemantauanDarah" data-toggle="tab" href="#pemberianDanPemantauanDarah">
                                    Pemberian dan Pemantauan Tranfusi Darah
                                </a>
                                <a href="#pemantauanNyeri" class="dropdown-item pemantauanNyeri" data-toggle="tab">
                                    Pemantauan Nyeri
                                </a>
                                <a href="#serahTerimaPemRi" class="dropdown-item serahTerimaPemRi" data-toggle="tab">
                                    Serah Terima Pemeriksaan
                                </a>
                                <a class="dropdown-item formulir-pindah-ruangan" data-toggle="tab" href="#menu-formulir-pindah-ruangan">
                                    Formulir Perpindahan Pasien Antar Ruang
                                </a>
                            </div>
                        </li>
                    <?php } ?>
                    <!-- END KHUSUS JIKA RUANGAN ANYELIR 2 -->
                    <!-- START KHUSUS JIKA RUANGAN IGD -->
                    <?php if ($pasien['ID_RUANGAN'] == 105140101 || $pasien['ID_RUANGAN'] == 105140102) { ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle bg-success" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                                IGD
                            </a>
                            <div class="dropdown-menu pre-scrollable" role="menu">
                                <a class="dropdown-item" id="dashboard-igd" data-toggle="tab" href="#menu-dashboard-igd">
                                    <em>Dashboard</em> IGD
                                </a>
                                <a href="#formulirSkriningVisualRi" class="dropdown-item formulirSkriningVisualRi" data-toggle="tab">
                                    Skrining Visual
                                </a>
                                <a class="dropdown-item formulirSkriningCovid19" data-toggle="tab" href="#formulirSkriningCovid19">
                                    Formulir Skrining Covid-19
                                </a>
                                <a class="dropdown-item formulirTriaseRi" data-toggle="tab" href="#formulirTriaseRi">
                                    Formulir Triase
                                </a>
                                <a class="dropdown-item surKetEm" data-toggle="tab" href="#surKetEm">
                                    Surat Keterangan Emergency
                                </a>
                                <a class="dropdown-item pengkajianRiDewasaMedis" data-toggle="tab" href="#pengkajianRiDewasaMedis">
                                    [Medis] Pengkajian Medis
                                </a>
                                <a class="dropdown-item pengkajianIgdRi" data-toggle="tab" href="#pengkajianIgdRi">
                                    [Perawat] Pengkajian IGD
                                </a>
                                <a href="#eKardekRi" data-toggle="tab" class="dropdown-item eKardekRi">
                                    E-Kardek
                                </a>
                                <a class="dropdown-item ot-keperawatan" data-toggle="tab" href="#menu-ot-keperawatan">
                                    Observasi dan Tindakan Keperawatan
                                </a>
                                <a class="dropdown-item ews" data-toggle="tab" href="#menu-ews">
                                    EWS <i>(Early Warning Score)</i>
                                </a>
                                <a href="#cppt" class="dropdown-item cppt" data-toggle="tab">
                                    CPPT
                                </a>
                                <a href="#pemantauanNyeri" class="dropdown-item pemantauanNyeri" data-toggle="tab">
                                    Pemantauan Nyeri
                                </a>
                                <a class="dropdown-item catatan-edukasi" data-toggle="tab" href="#menu-catatan-edukasi">
                                    Catatan Edukasi dan Informasi Terintegrasi
                                </a>
                                <a href="#serahTerima" class="dropdown-item serahTerima" data-toggle="tab">
                                    Serah Terima Antar Shift
                                </a>
                                <a href="#laporDPJP" class="dropdown-item laporDPJP" data-toggle="tab">
                                    Lapor DPJP
                                </a>
                                <a class="dropdown-item" id="poc-igd" data-toggle="tab" href="#menu-poc">
                                    <em>Plan of Care</em>
                                </a>
                                <a href="#pemberianIntravena" class="dropdown-item pemberianIntravena" data-toggle="tab">
                                    Pemberian Cairan Intravena
                                </a>
                            </div>
                        </li>
                    <?php } ?>
                    <!-- END KHUSUS JIKA RUANGAN IGD -->
                    <!-- START PENGKAJIAN -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Pengkajian
                        </a>
                        <div class="dropdown-menu pre-scrollable" role="menu">
                            <a class="dropdown-item pengkajianRiDewasaMedis" data-toggle="tab" href="#pengkajianRiDewasaMedis">
                                [Medis] Pengkajian Medis
                            </a>
                            <a class="dropdown-item pengkajianRiDewasa" data-toggle="tab" href="#pengkajianRiDewasa">
                                [Perawat] Pengkajian RI Dewasa
                            </a>
                            <a class="dropdown-item pengkajianRadioterapiRi" data-toggle="tab" href="#pengkajianRadioterapiRi">
                                [Perawat] Pengkajian RI Radioterapi
                            </a>
                            <a class="dropdown-item pengkajianRiAnak" data-toggle="tab" href="#pengkajianRiAnak">
                                [Perawat] Pengkajian RI Anak
                            </a>
                            <a class="dropdown-item pengkajianRiRemaja" data-toggle="tab" href="#pengkajianRiRemaja">
                                [Perawat] Pengkajian Remaja
                            </a>
                            <a class="dropdown-item pengkajianIgdRi" data-toggle="tab" href="#pengkajianIgdRi">
                                [Perawat] Pengkajian IGD
                            </a>
                            <a class="dropdown-item pengkajianRiim" data-toggle="tab" href="#pengkajianRiim">
                                [Perawat] Pengkajian RIIM
                            </a>
                            <a class="dropdown-item pengkajianRira" data-toggle="tab" href="#pengkajianRira">
                                [Perawat] Pengkajian Rira
                            </a>
                            <a class="dropdown-item pengkajianRiKritis" data-toggle="tab" href="#pengkajianRiKritis">
                                [Perawat] Pengkajian Pasien Kritis
                            </a>
                            <a class="dropdown-item pengkajianRiMedisKritis" data-toggle="tab" href="#pengkajianRiMedisKritis">
                                [Medis] Pengkajian Pasien Kritis
                            </a>
                            <a class="dropdown-item pengkajianTerapiSistemikRJ" data-toggle="tab" href="#pengkajianTerapiSistemikRJ">
                                [Perawat] Pengkajian Terapi Sistemik Kanker RJ
                            </a>
                        </div>
                    </li>
                    <!-- END MENU PENGKAJIAN -->
                    <!-- START PENGKAJIAN LAIN -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Pengkajian Lainnya
                        </a>
                        <div class="dropdown-menu pre-scrollable" role="menu">
                            <a class="dropdown-item asesmenRestrain" data-toggle="tab" href="#asesmenRestrain">
                                <i>Assesmen Restrain</i>
                            </a>
                            <a class="dropdown-item skriningPerasaanTertekan" data-toggle="tab" href="#skriningPerasaanTertekan">
                                Skrining Perasaan Tertekan
                            </a>
                            <a href="#barthelIndek" class="dropdown-item barthelIndek" data-toggle="tab">
                                <i>Berthel Index</i>
                            </a>
                            <a class="dropdown-item pengkajianLuka" data-toggle="tab" href="#pengkajianLuka">
                                Pengkajian Luka
                            </a>
                            <a class="dropdown-item pengkajianStoma" data-toggle="tab" href="#pengkajianStoma">
                                Pengkajian Stoma
                            </a>
                            <a class="dropdown-item pengkajianRiPAKPerawat" data-toggle="tab" href="#pengkajianRiPAKPerawat">
                                [Perawat] Pengkajian Pasien Akhir Kehidupan
                            </a>
                            <a class="dropdown-item pengkajianRiPAKMedis" data-toggle="tab" href="#pengkajianRiPAKMedis">
                                [Medis] Pengkajian Pasien Akhir Kehidupan
                            </a>
                            <a class="dropdown-item cpis" data-toggle="tab" href="#cpis">
                                CPIS
                            </a>
                            <a class="dropdown-item persiapanEkstubasi" data-toggle="tab" href="#persiapanEkstubasi">
                                Persiapan Ekstubasi
                            </a>
                            <a class="dropdown-item fasthug" data-toggle="tab" href="#fasthug">
                                Formulir Fast Hug
                            </a>
                            <!-- START MENU FLAP -->
                            <a href="#flap" data-toggle="tab" class="dropdown-item flap">
                                FLAP Monitoring
                            </a>
                            <!-- END MENU FLAP -->
                            <a href="#formulirPMFAK" class="dropdown-item formulirPMFAK" data-toggle="tab">
                                Formulir PMFAK
                            </a>
                            <a class="dropdown-item pengkajianTerapiSistemikRi" data-toggle="tab" href="#pengkajianTerapiSistemikRi">
                                Pengkajian Terapi Sistemik
                            </a>
                        </div>
                    </li>
                    <!-- END MENU PENGKAJIAN LAIN -->
                    <!-- START MENU CATATAN TERINTEGRASI -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Catatan Terintegrasi
                        </a>
                        <div class="dropdown-menu" role="menu">
                            <a href="#cppt" class="dropdown-item cppt" data-toggle="tab">
                                CPPT
                            </a>
                            <?php if ($pasien['ID_RUANGAN'] != 105140101) { ?>
                                <a href="#pemberianIntravena" class="dropdown-item pemberianIntravena" data-toggle="tab">
                                    Pemberian Cairan Intravena
                                </a>
                            <?php } ?>
                            <!-- <a href="#kardekRi" class="dropdown-item kardekRi" data-toggle="tab">
                                                Kardek Oral dan Injeksi
                                            </a> -->
                            <a class="dropdown-item" id="poc" data-toggle="tab" href="#menu-poc">
                                <em>Plan of Care</em>
                            </a>
                            <!-- <a class="dropdown-item" id="prmjri" data-toggle=" tab" href="#menu-prmjri">
                                                Ringkas Medis <em>(Summary List)</em> Pasien Rawat Inap
                                            </a> -->
                            <!-- <a href="#eKardekRi" class="dropdown-item eKardekRi" data-toggle="tab">
                                                E-Kardek Rawat Inap
                                            </a> -->
                            <a class="dropdown-item catatan-edukasi" data-toggle="tab" href="#menu-catatan-edukasi">
                                Catatan Edukasi dan Informasi Terintegrasi
                            </a>
                        </div>
                    </li>
                    <!-- END CATATAN TERINTEGRASI -->
                    <!-- START EKARDEK -->
                    <li class="nav-item">
                        <a href="#eKardekRi" data-toggle="tab" aria-expanded="false" class="nav-link eKardekRi">
                            E-Kardek
                        </a>
                    </li>
                    <!-- END EKARDEK -->
                    <!-- START MENU KEPERAWATAN -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Keperawatan
                        </a>
                        <div class="dropdown-menu pre-scrollable" role="menu">
                            <a class="dropdown-item perencanaan-ak" data-toggle="tab" href="#menu-perencanaan-ak">
                                Perencanaan Asuhan Keperawatan
                            </a>
                            <a class="dropdown-item ot-keperawatan" data-toggle="tab" href="#menu-ot-keperawatan">
                                Observasi dan Tindakan Keperawatan
                            </a>
                            <a class="dropdown-item ews" data-toggle="tab" href="#menu-ews">
                                EWS <i>(Early Warning Score)</i>
                            </a>
                            <a class="dropdown-item pews" data-toggle="tab" href="#menu-pews">
                                PEWS <i>(Pediatric Early Warning Score)</i>
                            </a>
                            <a href="#pemantauanNyeri" class="dropdown-item pemantauanNyeri" data-toggle="tab">
                                Pemantauan Nyeri
                            </a>
                            <a href="#pengkajianRisikoJatuhPasienDewasa" class="dropdown-item pengkajianRisikoJatuhPasienDewasa" data-toggle="tab">
                                Risiko Jatuh Dewasa (Skala Morse)
                            </a>
                            <a href="#skalaOntario" class="dropdown-item skalaOntario" data-toggle="tab">
                                Risiko Jatuh Geriatri (Skala Ontario)
                            </a>
                            <!-- <a href="#skalaOntarioMASS" class="dropdown-item skalaOntarioMASS" data-toggle="tab">
                                Risiko Jatuh Geriatri (Skala Ontario Modified Atratify-Sidney Scorsing)
                            </a> -->
                            <a href="#humptyDumptyRi" class="dropdown-item humptyDumptyRi" data-toggle="tab">
                                Risiko Jatuh Anak <i>(Humpty Dumpty)</i>
                            </a>
                            <a class="dropdown-item" id="skala-braden" data-toggle="tab" href="#menu-skala-braden">
                                Skala Braden
                            </a>
                            <a href="#serahTerima" class="dropdown-item serahTerima" data-toggle="tab">
                                Serah Terima Antar Shift
                            </a>
                            <a class="dropdown-item pemulanganPasienRi" data-toggle="tab" href="#pemulanganPasienRi">
                                Perancanaan Pemulangan Pasien
                            </a>
                            <a class="dropdown-item" id="otki" data-toggle="tab" href="#menu-otki">
                                Observasi dan Tindakan Keperawatan Radiofarmaka
                            </a>
                            <a class="dropdown-item" id="preido" data-toggle="tab" href="#menu-preido">
                                Surveilans Pasien Pre-Operatif Infeksi Daerah Operasi (IDO)
                            </a>
                            <a class="dropdown-item" id="postido" data-toggle="tab" href="#menu-postido">
                                Surveilans Pasien Post-Operatif Infeksi Daerah Operasi (IDO)
                            </a>
                        </div>
                    </li>
                    <!-- END MENU KEPERAWATAN -->
                    <!-- START MENU ERESEP -->
                    <li class="nav-item">
                        <a href="#eresep" data-toggle="tab" aria-expanded="false" class="nav-link eresep">
                            E-Resep
                        </a>
                    </li>
                    <!-- END MENU ERESEP -->
                    <!-- START MENU FORMULIR MENUNJANG -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Formulir Penunjang
                        </a>
                        <div class="dropdown-menu" role="menu">
                            <a class="dropdown-item patologi_klinik" data-toggle="tab" href="#patologi_klinik">Lab Patologi Klinik</a>
                            <a class="dropdown-item" id="lab-pa" data-toggle="tab" href="#menu-lab-pa">Lab Patologi Anatomi</a>
                            <a class="dropdown-item reevaluasi_lab" data-toggle="tab" href="#reevaluasi_lab">Re-Evaluasi Laboratorium</a>
                            <a href="#radiologi" data-toggle="tab" class="dropdown-item radiologi">Radiologi</a>
                            <a href="#prosedur" data-toggle="tab" class="dropdown-item prosedur">Prosedur Diagnostik</a>
                            <a class="dropdown-item" id="lpk" data-toggle="tab" href="#menu-lpk">Laboratorium Patologi Klinik (Baru)</a>
                            <?php if ($isStockartRoom) { ?>
                                <a class="dropdown-item" id="stockart" data-toggle="tab" href="#menu-stockart">Stockart</a>
                            <?php } ?>
                        </div>
                    </li>
                    <!-- END MENU FORMULIR MENUNJANG -->
                    <!-- START MENU FORMULIR KONSULTASI -->
                    <li class="nav-item dropdown formKonsul">
                        <a class="nav-link dropdown-toggle konsul" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Konsultasi
                        </a>
                        <div class="dropdown-menu" role="menu">
                            <a class="dropdown-item konsultasi" id="konsultasi" data-toggle="tab" href="#menu-konsultasi">
                                Konsultasi ke Dokter Lain
                            </a>
                            <a class="dropdown-item ppra" data-toggle="tab" href="#ppra">
                                PPRA
                            </a>
                            <!-- <a class="dropdown-item" id="ruj-in" data-toggle="tab" href="#menu-ruj-in">
                                            Rujukan internal
                                        </a> -->
                        </div>
                    </li>
                    <!-- END MENU FORMULIR KONSULTASI -->
                    <!-- START MENU GIZI -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Gizi
                        </a>
                        <div class="dropdown-menu" role="menu">
                            <a class="dropdown-item validasiMalnutrisiDewasa" data-toggle="tab" href="#validasiMalnutrisiDewasa">
                                Validasi Malnutrisi Dewasa
                            </a>
                            <a class="dropdown-item penilaianStatusGizi" data-toggle="tab" href="#penilaianStatusGizi">
                                Penilaian Status Gizi
                            </a>
                            <a class="dropdown-item pengkajianGiziLanjutan" data-toggle="tab" href="#pengkajianGiziLanjutan">
                                Pengkajian Gizi Lanjutan
                            </a>
                            <a class="dropdown-item observasiIntakeOralPasien" data-toggle="tab" href="#observasiIntakeOralPasien">
                                Observasi Intake Oral Pasien
                            </a>
                        </div>
                    </li>
                    <!-- END MENU GIZI -->
                    <!-- START MENU KEMOTERAPI -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Pengobatan Sistemik Kanker
                        </a>
                        <div class="dropdown-menu" role="menu">
                            <a class="dropdown-item protokolKemoterapiRi" data-toggle="tab" href="#protokolKemoterapiRi">
                                Protokol Kemoterapi
                            </a>
                            <a class="dropdown-item" id="prokem-anak" data-toggle="tab" href="#menu-pro-kem-anak">
                                Protokol Kemoterapi Anak
                            </a>
                            <a class="dropdown-item" id="jpokem" data-toggle="tab" href="#menu-jpokem">
                                JPOK
                            </a>
                        </div>
                    </li>
                    <!-- END MENU KEMOTERAPI -->
                    <!-- START MENU INVASIF DAN ANESTESI -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Invasif & Anestesi
                        </a>
                        <div class="dropdown-menu" role="menu">
                            <a class="dropdown-item kesInvRi" data-toggle="tab" href="#kesInvRi">
                                Keselamatan Tindakan Invasif
                            </a>
                            <a class="dropdown-item sttsAnestesiaRi" data-toggle="tab" href="#sttsAnestesiaRi">
                                Status Anestesia
                            </a>
                            <!-- <a class="dropdown-item tandaVitalAnestesia" data-toggle="tab" href="#tandaVitalAnestesia">
                                Tanda Vital Anestesia
                            </a> -->
                            <a class="dropdown-item" id="csoasema" data-toggle="tab" href="#menu-csoasema">
                                <i>Checklist</i> STATICS, Obat Anastesi, Sedasi, Emergensi, dan Mesin Anestesi
                            </a>
                            <a class="dropdown-item EdukasiTAS" data-toggle="tab" href="#EdukasiTAS">
                                Formulir Edukasi Tindakan Anastesi dan Sedasi
                            </a>
                            <a href="#pengkajianPraSedasi" class="dropdown-item pengkajianPraSedasi" data-toggle="tab">
                                Pengkajian Pra Anastesi/Sedasi
                            </a>
                            <a class="dropdown-item pemTinAnesLokRjRi" data-toggle="tab" href="#pemTinAnesLokRjRi">
                                Form Pemantauan Tindakan Anestesi Lokal
                            </a>
                        </div>
                    </li>
                    <!-- END MENU INVASIF DAN ANESTESI -->
                    <!-- START MENU OPERASI -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Operasi
                        </a>
                        <div class="dropdown-menu" role="menu">
                            <a class="dropdown-item" id="daftar-operasi" data-toggle="tab" href="#menu-daftar-operasi">
                                Pendaftaran Pra Operasi
                            </a>
                            <a class="dropdown-item" id="pengkajian-pra-operasi" data-toggle="tab" href="#menu-pengkajian-pra-operasi">
                                Pengkajian Pra Operasi
                            </a>
                            <a class="dropdown-item siteMarking" data-toggle="tab" href="#siteMarking">
                                Site Marking
                            </a>
                            <a class="dropdown-item" id="lap-operasi" data-toggle="tab" href="#menu-lap-operasi">
                                Laporan Operasi
                            </a>
                            <a class="dropdown-item" id="rapo" data-toggle="tab" href="#menu-rapo">
                                Rencana Asuhan Pasca Operasi
                            </a>
                            <a class="dropdown-item intOpeRi" data-toggle="tab" href="#intOpeRi">
                                Catatan Keperawatan Perioperatif Intra Operasi
                            </a>
                            <a class="dropdown-item kesOpeRi" data-toggle="tab" href="#kesOpeRi">
                                Formulir Keselamatan Operasi
                            </a>
                            <a class="dropdown-item" id="alat-operasi" data-toggle="tab" href="#menu-alat-operasi">
                                Catatan Penggunaan Alat Operasi
                            </a>
                            <a class="dropdown-item" id="cpao" data-toggle="tab" href="#menu-cpao">
                                Catatan Penggunaan Alat Operasi (Baru)
                            </a>
                            <a class="dropdown-item asPraAnes" data-toggle="tab" href="#asPraAnes">
                                Assesmen Pra Anestesi
                            </a>
                            <a href="#permintaanDirawat" class="dropdown-item permintaanDirawat" data-toggle="tab">
                                Permintaan Dirawat
                            </a>
                            <a class="dropdown-item PTKBedah" data-toggle="tab" href="#PTKBedah">
                                Persetujuan Tindakan Kedokteran Bedah
                            </a>
                            <a class="dropdown-item cpo" data-toggle="tab" href="#cpo">
                                Ceklis Persiapan Operasi
                            </a>
                        </div>
                    </li>
                    <!-- END MENU OPERASI -->
                    <!-- START MENU RUANGAN INTENSIF -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Ruangan Intensif
                        </a>
                        <div class="dropdown-menu" role="menu">
                            <a class="dropdown-item sofa" data-toggle="tab" href="#sofa">
                                SOFA
                            </a>
                            <a class="dropdown-item bundleVAP" data-toggle="tab" href="#bundleVAP">
                                BUNDLE VAP
                            </a>
                            <a class="dropdown-item ceklisEdukasiRuanganIntensif" data-toggle="tab" href="#ceklisEdukasiRuanganIntensif">
                                Ceklis Edukasi Dan Orientasi Pasien Baru
                            </a>
                        </div>
                    </li>
                    <!-- END MENU RUANGAN INTENSIF -->
                    <!-- START MENU IGD -->
                    <?php if ($pasien['ID_RUANGAN'] == 105140101 || $pasien['ID_RUANGAN'] == 105140102) { ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                                IGD
                            </a>
                            <div class="dropdown-menu" role="menu">
                                <a class="dropdown-item formulirTriaseRi" data-toggle="tab" href="#formulirTriaseRi">
                                    Formulir Triase
                                </a>
                                <a class="dropdown-item surKetEm" data-toggle="tab" href="#surKetEm">
                                    Surat Keterangan Emergency
                                </a>
                                <a class="dropdown-item laporDPJP" data-toggle="tab" href="#laporDPJP">
                                    Lapor DPJP
                                </a>
                                <a class="dropdown-item" id="poc-non-igd" data-toggle="tab" href="#menu-poc">
                                    <em>Plan of Care</em>
                                </a>
                            </div>
                        </li>
                    <?php } ?>
                    <!-- END MENU IGD -->
                    <!-- START MENU BRAKHITERAPI -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Brakhiterapi
                        </a>
                        <div class="dropdown-menu" role="menu">
                            <a class="dropdown-item pengkajianRiBrak" data-toggle="tab" href="#pengkajianRiBrak">
                                [Perawat] Pengkajian Brachiterapy
                            </a>
                            <a class="dropdown-item pengkajianRiMedisBrakhiterapi" data-toggle="tab" href="#pengkajianRiMedisBrakhiterapi">
                                [Medis] Pengkajian Brachiterapy
                            </a>
                            <a class="dropdown-item" id="otkb" data-toggle="tab" href="#menu-otkb">
                                Observasi dan Tindakan Keperawatan Brakhiterapi
                            </a>
                            <a class="dropdown-item ltbg" data-toggle="tab" href="#ltbg">
                                Laporan Tindakan Brakhiterapi Ginekologi
                            </a>
                            <a class="dropdown-item brakNaso" data-toggle="tab" href="#brakNaso">
                                Laporan Tindakan Brakhiterapi Nasofaring
                            </a>
                        </div>
                    </li>
                    <!-- END MENU BRAKHITERAPI -->

                    <!-- START MENU RADIOTERAPI (Bekas Rawat Jalan) -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Radio Terapi
                        </a>
                        <div class="dropdown-menu">
                            <a href="#pengkajianRiRadio" data-toggle="tab" class="dropdown-item radioterapiii">
                                Form Pengkajian
                            </a>
                            <a class="dropdown-item ct_Simulator" data-toggle="tab" href="#CTRiRadio">
                                CT Simulator
                            </a>
                            <a class="dropdown-item simulatorInformation" data-toggle="tab" href="#simulatorRiRadio">
                                Simulator Konvensional
                            </a>
                            <a class="dropdown-item treatmentDose" data-toggle="tab" href="#doseRiRadio">
                                Treatment Dose
                            </a>
                            <a class="dropdown-item penjadwalanradioterapi" data-toggle="tab" href="#penjadwalanradioterapi">
                                Penjadwalan Radioterapi
                            </a>
                        </div>
                    </li>
                    <!-- END MENU RADIOTERAPI -->


                    <!-- START ADMISSION -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Admission
                        </a>
                        <div class="dropdown-menu" role="menu">
                            <a href="#permintaanDirawat" class="dropdown-item permintaanDirawat" data-toggle="tab">
                                Permintaan Dirawat
                            </a>
                            <!-- <a href="#formulirSkriningVisualRi" class="dropdown-item formulirSkriningVisualRi" data-toggle="tab">
                                Skrining Visual
                            </a> -->
                            <a href="#CekPIRI" class="dropdown-item CekPIRI" data-toggle="tab">
                                Formulir Ceklis Program Pelayanan Instalasi Rawat Inap
                            </a>
                        </div>
                    </li>
                    <!-- END ADMISSION -->
                    <!-- START MENU PERMINTAAN DIRAWAT -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Transfer Ruangan
                        </a>
                        <div class="dropdown-menu" role="menu">
                            <a class="dropdown-item formulir-pindah-ruangan" data-toggle="tab" href="#menu-formulir-pindah-ruangan">
                                Formulir Perpindahan Pasien Antar Ruang
                            </a>
                            <a href="#formulirKriteriaRawatIntensif" class="dropdown-item formulirKriteriaRawatIntensif" data-toggle="tab">
                                Kriteria Pasien Masuk Rawat Intensif
                            </a>
                            <a href="#formulirKriteriaRawatPicu" class="dropdown-item formulirKriteriaRawatPicu" data-toggle="tab">
                                Kriteria Pasien Masuk Rawat PICU
                            </a>
                            <a href="#serahTerimaPemRi" class="dropdown-item serahTerimaPemRi" data-toggle="tab">
                                Serah Terima Pemeriksaan
                            </a>
                            <a class="dropdown-item" id="riim" data-toggle="tab" href="#menu-riim">
                                Formulir <i>Checklist</i> Kriteria Pasien Masuk dan Keluar Ruang Isolasi Imunitas Menurun (RIIM)
                            </a>
                            <a class="dropdown-item" id="rira" data-toggle="tab" href="#menu-rira">
                                Formulir <i>Checklist</i> Kriteria Pasien Masuk dan Keluar Ruang Isolasi Radioaktif (Rira)
                            </a>
                            <a class="dropdown-item" id="rinad" data-toggle="tab" href="#menu-rinad">
                                Formulir <i>Checklist</i> Kriteria Pasien Masuk dan Keluar Ruang Isolasi <i>Non Airbone Disease</i>
                            </a>
                            <a class="dropdown-item ceklisEdukasiOrientasiRI" data-toggle="tab" href="#ceklisEdukasiOrientasiRI">
                                Ceklis Edukasi Dan Orientasi Pasien Baru Rawat Inap
                            </a>
                            <a class="dropdown-item surveilans" data-toggle="tab" href="#surveilans">
                                Surveilans Rumah Sakit
                            </a>
                            <!--?php //if ($pasien['ID_RUANGAN'] == 105140101) {?-->
                            <!-- <a href="#formulirKriteriaRawatIntensif" class="dropdown-item" data-toggle="tab">
                                                                                                    Formulir Kriteria Pasien Masuk Rawat Intensif
                                                                                                </a>
                                                                                                <a href="#formulirKriteriaRawatPicu" class="dropdown-item" data-toggle="tab">
                                                                                                    Formulir Kriteria Pasien Masuk Rawat Picu
                                                                                                </a> -->
                            <!--?php //}?-->
                        </div>
                    </li>
                    <!-- END MENU PERMINTAAN DIRAWAT -->
                    <!-- START MENU PERSETUJUAN DAN EDUKASI -->
                    <!-- <li class="nav-item dropdown">
                                                                                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                                                                                            Persetujuan dan Edukasi
                                                                                        </a>
                                                                                    </li> -->
                    <!-- END MENU PERSETUJUAN DAN EDUKASI -->
                    <!-- START MENU RESUME -->
                    <!-- <li class="nav-item dropdown">
                                                                                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                                                                                            Resume
                                                                                        </a>
                                                                                    </li> -->
                    <!-- END MENU RESUME -->
                    <!-- START MENU GERIATRI -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Geriatri
                        </a>
                        <div class="dropdown-menu" role="menu">
                            <a class="dropdown-item instrumenPPPG" data-toggle="tab" href="#instrumenPPPG">
                                Instrumen Pengkajian Paripurna Pasien Geriatri (P3G)
                            </a>
                            <a class="dropdown-item penilaianADL" data-toggle="tab" href="#penilaianADL">
                                Penilaian <i>Activity of Daily Living</i>
                            </a>
                            <a class="dropdown-item iadl" data-toggle="tab" href="#iadl">
                                <i>Instrumental Activities of Daily Living (IADL) Lawton</i>
                            </a>
                            <a href="#instrumen-gds" class="dropdown-item instrumen-gds" data-toggle="tab">
                                Instrumen <i>Geriatric Depression Scale</i>
                            </a>
                            <a href="#pemeriksaanMiniCOG" class="dropdown-item pemeriksaanMiniCOG" data-toggle="tab">
                                Pemeriksaan Mini COG Dan Clock Drawing Test
                            </a>
                            <a href="#instrumenMmse" class="dropdown-item instrumenMmse" data-toggle="tab">
                                Instrumen Evaluasi Status Mental Mini (MMSE)
                            </a>
                            <a href="#abbreviatedMentalTest" class="dropdown-item abbreviatedMentalTest" data-toggle="tab">
                                <i>Abbreviated Mental Test</i>
                            </a>
                            <a href="#instrumenMna" class="dropdown-item instrumenMna" data-toggle="tab">
                                Instrumen Mini Nutrional Assesment (MNA)
                            </a>
                            <a href="#formulirG8Geriatri" class="dropdown-item formulirG8Geriatri" data-toggle="tab">
                                Formulir G8 Geriatri
                            </a>
                            <a href="#resikojatuhlanjutusia" class="dropdown-item resikojatuhlanjutusia" data-toggle="tab">
                                Formulir Risiko Jatuh Pasien Lanjut Usia
                            </a>
                        </div>
                    </li>
                    <!-- END MENU GERIATRI -->
                    <!-- START MENU SEDASI -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Sedasi
                        </a>
                        <div class="dropdown-menu" role="menu">
                            <a class="dropdown-item statusSedasi" data-toggle="tab" href="#statusSedasi">
                                Sedasi
                            </a>
                            <a class="dropdown-item medikasiObat" data-toggle="tab" href="#medikasiObat">
                                Medikasi Obat
                            </a>
                            <a class="dropdown-item medikasiNapas" data-toggle="tab" href="#medikasiNapas">
                                Medikasi Napas
                            </a>
                            <a class="dropdown-item sedasiPemulihan" data-toggle="tab" href="#sedasiPemulihan">
                                Kamar Pemulihan
                            </a>
                            <a class="dropdown-item pemulihanNapas" data-toggle="tab" href="#pemulihanNapas">
                                Kamar Pemulihan NNTD
                            </a>
                        </div>
                    </li>
                    <!-- END MENU SEDASI -->
                    <!-- START MENU INFORMED CONSENT -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            <em>Informed Consent</em>
                        </a>
                        <div class="dropdown-menu pre-scrollable" role="menu">
                            <a class="dropdown-item persetujuan-tindakan-kedokteran" data-toggle="tab" href="#persetujuan-tindakan-kedokteran">
                                Persetujuan Tindakan Kedokteran
                            </a>
                            <a class="dropdown-item persetujuanTindakanPengobatanKemoterapi" data-toggle="tab" href="#persetujuanTindakanPengobatanKemoterapi">
                                Persetujuan Tindakan Pengobatan Kemoterapi
                            </a>
                            <a class="dropdown-item PTKemoRhabdomiosarkoma" data-toggle="tab" href="#PTKemoRhabdomiosarkoma">
                                Persetujuan Tindakan Kemoterapi Rhabdomiosarkoma
                            </a>
                            <a class="dropdown-item" data-toggle="tab" href="#persetujuanTindakanKemoterapiLLAAnak">
                                Persetujuan Tindakan Kemoterapi LLA Anak
                            </a>
                            <a class="dropdown-item penolakanTindakanKedokteran" data-toggle="tab" href="#penolakanTindakanKedokteran">
                                Penolakan Tindakan Kedokteran
                            </a>
                            <a class="dropdown-item pttd" data-toggle="tab" href="#menu-pttd">
                                Persetujuan Tindakan Transfusi Darah
                            </a>
                            <a class="dropdown-item spinalEpi" data-toggle="tab" href="#spinalEpi">
                                Persetujuan Tindakan Kedokteran (Spinal/Epidural)
                            </a>
                            <a class="dropdown-item PTKBedah" data-toggle="tab" href="#PTKBedah">
                                Persetujuan Tindakan Kedokteran Bedah
                            </a>
                            <a class="dropdown-item dnr" data-toggle="tab" href="#dnr">
                                Persetujuan Tindakan DNR
                            </a>
                            <a class="dropdown-item PIPTKS" data-toggle="tab" href="#PIPTKS">
                                Pemberian Informasi dan Persetujuan Tindakan Kedokteran (Sedasi)
                            </a>
                            <a class="dropdown-item PIPTKAU" data-toggle="tab" href="#PIPTKAU">
                                Pemberian Informasi dan Persetujuan Tindakan Kedokteran (Anestesi Umum)
                            </a>
                        </div>
                    </li>
                    <!-- END MENU INFORMED CONSENT -->
                    <!-- START MENU FLOSHEET -->
                    <li class="nav-item">
                        <a href="#flosheet" data-toggle="tab" aria-expanded="false" class="nav-link flosheet">
                            Flowsheet
                        </a>
                    </li>
                    <!-- END MENU FLOSHEET -->
                    <!-- START MENU COVID-19 -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Covid-19
                        </a>
                        <div class="dropdown-menu" role="menu">
                            <a class="dropdown-item formulirSkriningCovid19" data-toggle="tab" href="#formulirSkriningCovid19">
                                Formulir Skrining Covid-19
                            </a>
                            <a class="dropdown-item formulirEpidemologiCovid" data-toggle="tab" href="#formulirEpidemologiCovid">
                                Formulir Penyelidikan Epidemiologi Covid-19
                            </a>
                        </div>
                    </li>
                    <!-- END MENU COVID-19 -->
                    <!-- START MENU BANK DARAH-->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Bank Darah
                        </a>
                        <div class="dropdown-menu" role="menu">
                            <a class="dropdown-item pemberianDanPemantauanDarah" data-toggle="tab" href="#pemberianDanPemantauanDarah">
                                Pemberian dan Pemantauan Tranfusi Darah
                            </a>
                        </div>
                    </li>
                    <!-- END MENU BANK DARAH -->
                    <!-- START MENU REKONSILIASI OBAT -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Farmasi
                        </a>
                        <div class="dropdown-menu" role="menu">
                            <a href="#rekonsiliasiObat" class="dropdown-item rekonsiliasiObat" data-toggle="tab">
                                Rekonsiliasi Obat
                            </a>
                            <a href="#konseling" class="dropdown-item konseling" data-toggle="tab">
                                Formulir Konseling Pasien
                            </a>
                            <a href="#feso" class="dropdown-item feso" data-toggle="tab">
                                Formulir Efek Samping Obat
                            </a>
                        </div>
                    </li>
                    <!-- END MENU REKONSILIASI OBAT -->
                    <!-- START MENU ETIMJA -->
                    <li class="nav-item">
                        <a href="#menu-etimja" data-toggle="tab" aria-expanded="false" class="nav-link" id="etimja">
                            eTimja
                        </a>
                    </li>
                    <!-- END MENU ETIMJA -->
                    <!-- START MENU BERKAS -->
                    <!-- <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Berkas
                        </a>
                        <div class="dropdown-menu" role="menu">
                            <a class="dropdown-item" id="berkas-pasien-luar" data-toggle="tab" href="#menu-berkas-pasien-luar">
                                Berkas <em>Scan</em> Pasien Luar RSKD
                            </a>
                            <a class="dropdown-item" id="berkas-scan" data-toggle="tab" href="#menu-berkas-scan">
                                Berkas <em>Scan</em>
                            </a>
                        </div>
                    </li> -->
                    <!-- END MENU BERKAS -->
                    <!-- START MENU RESUME KEMATIAN -->
                    <li class="nav-item">
                        <a href="#resumeKematian" data-toggle="tab" aria-expanded="false" class="nav-link resumeKematian">
                            Resume Kematian
                        </a>
                    </li>
                    <!-- END MENU RESUME KEMATIAN -->
                    <!-- START MENU PEMAKAIAN O2 -->
                    <li class="nav-item">
                        <a href="#pemakaianO2Ri" data-toggle="tab" aria-expanded="false" class="nav-link pemakaianO2Ri">
                            Pemakaian O<sub>2</sub>
                        </a>
                    </li>
                    <!-- END MENU PEMAKAIAN O2 -->
                    <!-- START MENU MANAJEMEN PELAYANAN PASIEN -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Manajemen Pelayanan Pasien (MPP)
                        </a>
                        <div class="dropdown-menu" role="menu">
                            <a class="dropdown-item" id="smpp" data-toggle="tab" href="#menu-smpp">
                                Skrining Pasien Membutuhkan Manajer Pelayanan Pasien (MPP)
                            </a>
                            <a class="dropdown-item" id="ampp" data-toggle="tab" href="#menu-ampp">
                                Asesmen Awal Manajer Pelayanan Pasien <em>(Case Manager)</em>
                            </a>
                            <a class="dropdown-item" id="impp" data-toggle="tab" href="#menu-impp">
                                Implementasi
                            </a>
                        </div>
                    </li>
                    <!-- END MENU MANAJEMEN PELAYANAN PASIEN -->
                    <!-- START MENU HIV -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            HIV
                        </a>
                        <div class="dropdown-menu" role="menu">
                            <a class="dropdown-item" id="fuart" data-toggle="tab" href="#menu-fuart">
                                Ikhtisar <em>Follow Up</em> Perawatan Pasien HIV dan Terapi Antiretroviral
                            </a>
                            <a class="dropdown-item" id="rart" data-toggle="tab" href="#menu-rart">
                                Formulir Rujukan
                            </a>
                            <?php if ($dataHIV['STATUS_HIV'] == ""): ?>
                                <a href="#tandaHIV" class="dropdown-item tandaHIV" id="tandaHIV-info" data-toggle="modal" data-nomr="<?= $getNomr['NORM'] ?>" data-user="<?= $this->session->userdata('id') ?>">
                                    Penanda HIV
                                </a>
                            <?php endif ?>
                        </div>
                    </li>
                    <!-- END MENU HIV -->
                    <!-- START MENU PALIATIF -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Paliatif
                        </a>
                        <div class="dropdown-menu" role="menu">
                            <a class="dropdown-item pengkajianPaliatifRI" data-toggle="tab" href="#pengkajianPaliatifRI">
                                Pengkajian Paliatif
                            </a>
                            <a class="dropdown-item ffm" id="ffm" data-toggle="tab" href="#menu-ffm">
                                Formulir <em>Family Meeting</em>
                            </a>
                            <a class="dropdown-item hads" id="hads" data-toggle="tab" href="#menu-hads">
                                Formulir <em>Hospital Anxiety and Depression Scale</em>
                            </a>
                            <a class="dropdown-item iikpp" id="iikpp" data-toggle="tab" href="#menu-iikpp">
                                Instrumen Identifikasi Kebutuhan Perawatan Paliatif
                            </a>
                            <a class="dropdown-item kpppp" id="kpppp" data-toggle="tab" href="#menu-kpppp">
                                Formulir Kriteria Persiapan Pulang Pasien
                            </a>
                        </div>
                    </li>
                    <!-- END MENU PALIATIF -->
                    <li class="nav-item">
                        <a class="nav-link echoekg" data-toggle="tab" href="#echoekg">
                            Echo EKG
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link inputTpn" data-toggle="tab" href="#inputTpn">
                            TPN
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link formulirSkriningVisualRi" data-toggle="tab" href="#formulirSkriningVisualRi">
                            Skrining Visual
                        </a>
                    </li>
                </ul>
                <div class="row">
                    <div class="col-sm-6 d-none" id="cppt-view-side"></div>
                    <div class="col-sm-6 d-none" id="view-side-konsultasi"></div>
                    <!-- <div class="col-sm-6 d-none mt-4" id="showHasilSpeechSampingRi" style="max-height: 400px; overflow-y: auto;">
                    <iframe src="https://www.google.com/intl/en/chrome/demos/speech.html" height="100%" width="100%" title="Iframe Example" allow="camera *;microphone *" style="background-color:white"></iframe>
                </div> -->
                    <div class="col-sm-6 d-none mt-4" id="showHasilPenunjangSampingRi">
                        <!-- <div class="d-flex align-items-start"> -->
                        <div class="nav nav-tabs nav-justified" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                            <button class="tombolHasilPenunjangSideRi nav-link " data-jenis="5" id="cpptSideBarRi-tab" data-bs-toggle="pill" data-bs-target="#cpptSideBarRi" type="button" role="tab" aria-controls="cpptSideBarRi" aria-selected="false">
                                CPPT
                            </button>
                            <button class="tombolHasilPenunjangSideRi nav-link" data-jenis="10" id="tombol-pengkajian-side-ri" data-bs-toggle="pill" data-bs-target="#history-pengkajian-side-ri" type="button" role="tab" aria-controls="history-pengkajian-side-ri" aria-selected="false">
                                Pengkajian Awal
                            </button>
                            <button class="tombolHasilPenunjangSideRi nav-link active" data-jenis="1" id="labPkSideBarRi-tab" data-bs-toggle="pill" data-bs-target="#labPkSideBarRi" type="button" role="tab" aria-controls="labPkSideBarRi" aria-selected="true">
                                Lab PK
                            </button>
                            <button class="tombolHasilPenunjangSideRi nav-link" data-jenis="2" id="labPaSideBarRi-tab" data-bs-toggle="pill" data-bs-target="#labPaSideBarRi" type="button" role="tab" aria-controls="labPaSideBarRi" aria-selected="false">
                                Lab PA
                            </button>
                            <button class="tombolHasilPenunjangSideRi nav-link" data-jenis="9" id="labPtSideBarRi-tab" data-bs-toggle="pill" data-bs-target="#labPtSideBarRi" type="button" role="tab" aria-controls="labPtSideBarRi" aria-selected="false">
                                Lab Patmol
                            </button>
                            <button class="tombolHasilPenunjangSideRi nav-link" data-jenis="3" id="radiologiSideBarRi-tab" data-bs-toggle="pill" data-bs-target="#radiologiSideBarRi" type="button" role="tab" aria-controls="radiologiSideBarRi" aria-selected="false">
                                Radiologi
                            </button>
                            <button class="tombolHasilPenunjangSideRi nav-link" data-jenis="4" id="resepSideBarRi-tab" data-bs-toggle="pill" data-bs-target="#resepSideBarRi" type="button" role="tab" aria-controls="resepSideBarRi" aria-selected="false">
                                Resep
                            </button>
                            <button class="tombolHasilPenunjangSideRi nav-link" data-jenis="11" id="eresepSideBarRi-tab" data-bs-toggle="pill" data-bs-target="#eresepSideBarRi" type="button" role="tab" aria-controls="eresepSideBarRi" aria-selected="false">
                                E-Resep
                            </button>
                            <button class="tombolHasilPenunjangSideRi nav-link" data-jenis="6" id="kardekSideBarRi-tab" data-bs-toggle="pill" data-bs-target="#kardekSideBarRi" type="button" role="tab" aria-controls="kardekSideBarRi" aria-selected="false">
                                Kardek
                            </button>
                            <button class="tombolHasilPenunjangSideRi nav-link" data-jenis="7" id="cairanSideBarRi-tab" data-bs-toggle="pill" data-bs-target="#cairanSideBarRi" type="button" role="tab" aria-controls="cairanSideBarRi" aria-selected="false">
                                Balance Cairan
                            </button>
                            <button class="tombolHasilPenunjangSideRi nav-link" data-jenis="8" id="konsultasiSideBarRi-tab" data-bs-toggle="pill" data-bs-target="#konsultasiSideBarRi" type="button" role="tab" aria-controls="konsultasiSideBarRi" aria-selected="false">
                                Konsultasi
                            </button>
                            <button class="tombolHasilPenunjangSideRi nav-link" data-jenis="12" id="formulariumSideBarRi-tab" data-bs-toggle="pill" data-bs-target="#formulariumSideBarRi" type="button" role="tab" aria-controls="formulariumSideBarRi" aria-selected="false">
                                Daftar Obat Formularium
                            </button>
                        </div>

                        <div class="tab-content" id="v-pills-tabContent">
                            <div class="tab-pane fade" id="cpptSideBarRi" role="tabpanel" aria-labelledby="cpptSideBarRi-tab">
                                <div id="show_cpptSideBarRi" style="max-height: 1500px; overflow-y: auto;"></div>
                            </div>
                            <div class="tab-pane fade" id="history-pengkajian-side-ri" role="tabpanel" style="max-height: calc(100vh - 200px); overflow-y: auto;"></div>
                            <div class="tab-pane fade show active" id="labPkSideBarRi" role="tabpanel" aria-labelledby="labPkSideBarRi-tab">
                                <!--?php //$this->load->view('Pengkajian/hasilLabPK') ?-->
                            </div>
                            <div class="tab-pane fade" id="labPaSideBarRi" role="tabpanel" aria-labelledby="labPaSideBarRi-tab">
                                <!--?php //$this->load->view('Pengkajian/hasilLabPa') ?-->
                            </div>
                            <div class="tab-pane fade" id="labPtSideBarRi" role="tabpanel" aria-labelledby="labPtSideBarRi-tab">
                                <!--?php //$this->load->view('Pengkajian/hasilLabPt') ?-->
                            </div>
                            <div class="tab-pane fade" id="radiologiSideBarRi" role="tabpanel" aria-labelledby="radiologiSideBarRi-tab">
                                <!--?php //$this->load->view('Pengkajian/hasilRadiologi') ?-->
                            </div>
                            <div class="tab-pane fade" id="resepSideBarRi" role="tabpanel" aria-labelledby="resepSideBarRi-tab">
                                <div id="show_resepSideBarRi"></div>
                            </div>
                            <div class="tab-pane fade" id="eresepSideBarRi" role="tabpanel" aria-labelledby="eresepSideBarRi-tab">
                                <div id="show_eresepSideBarRi"></div>
                            </div>
                            <div class="tab-pane fade" id="kardekSideBarRi" role="tabpanel" aria-labelledby="kardekSideBarRi-tab">
                                <div id="show_kardekSideBarRi"></div>
                            </div>
                            <div class="tab-pane fade" id="cairanSideBarRi" role="tabpanel" aria-labelledby="cairanSideBarRi-tab">
                                <div id="show_cairanSideBarRi"></div>
                            </div>
                            <div class="tab-pane fade" id="konsultasiSideBarRi" role="tabpanel" aria-labelledby="konsultasiSideBarRi-tab">
                                <div id="show_konsultasiSideBarRi"></div>
                            </div>
                            <div class="tab-pane fade" id="formulariumSideBarRi" role="tabpanel" aria-labelledby="formulariumSideBarRi-tab">
                                <div id="show_formulariumSideBarRi"></div>
                            </div>
                        </div>

                        <!-- </div> -->
                        <!-- <ul class="nav nav-tabs nav-justified">
                            <li class="nav-item">
                                <a href="#labPkSideBarRi" data-toggle="tab" aria-expanded="false" class="nav-link active">Lab PK</a>
                            </li>
                            <li class="nav-item">
                                <a href="#labPaSideBarRi" data-toggle="tab" aria-expanded="true" class="nav-link">Lab PA</a>
                            </li>
                            <li class="nav-item">
                                <a href="#radiologiSideBarRi" data-toggle="tab" aria-expanded="true" class="nav-link">Radiologi</a>
                            </li>
                            <li class="nav-item">
                                <a href="#resepSideBarRi" data-toggle="tab" aria-expanded="true" class="nav-link resepSideBarRi">Resep</a>
                            </li>
                        </ul>

                        <div class="tab-content">
                            <div role="tabpanel" class="tab-pane fade show active" id="labPkSideBarRi">
                                <!?php $this->load->view('Pengkajian/hasilLabPK') ?>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="labPaSideBarRi">
                                <!?php $this->load->view('Pengkajian/hasilLabPa') ?>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="radiologiSideBarRi">
                                <!?php $this->load->view('Pengkajian/hasilRadiologi') ?>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="resepSideBarRi" style="max-height: calc(100vh - 200px); overflow-y: auto;">
                                <div id="show_resepSideBarRi"></div>
                            </div>
                        </div> -->
                    </div>
                    <div class="col-sm-6 d-none" id="showHasilKardekSideRi"></div>
                    <div class="col-sm-6 d-none" id="showBalanceCairanSideRi"></div>
                    <div class="col-sm-12" id="main-menu">
                        <div class="tab-content" style="border-color:#40739e">
                            <!-- START INC VIEW MENU DASHBOARD -->
                            <div role="tabpanel" class="tab-pane fade" id="dashboard">
                                <div class="text-white">
                                    <div id="view_dashboard" class="load-view"></div>
                                </div>
                            </div>
                            <!-- END INC VIEW MENU DASHBOARD -->
                            <!-- START INC VIEW MENU CPPT LIST -->
                            <div role="tabpanel" class="tab-pane fade" id="cpptList">
                                <div class="text-white">
                                    <div id="view_cpptList"></div>
                                </div>
                            </div>
                            <!-- END INC VIEW MENU CPPT LIST -->
                            <!-- START INC VIEW PENGKAJIAN LIST -->
                            <div role="tabpanel" class="tab-pane fade" id="menu-pengkajian-list">
                                <div class="text-white">
                                    <div id="view-pengkajian-list"></div>
                                </div>
                            </div>

                            <!-- START INC VIEW BERKAS LIST -->
                            <div role="tabpanel" class="tab-pane fade" id="menu-berkas-list">
                                <div class="text-white">
                                    <div id="view-berkas-list"></div>
                                </div>
                            </div>
                            <!-- END INC VIEW BERKAS LIST -->
                            <div role="tabpanel" class="tab-pane fade" id="menu-faktor-resiko">
                                <div class="text-white">
                                    <div id="view-faktor-resiko"></div>
                                </div>
                            </div>
                            <!-- START INC VIEW MENU TBAK -->
                            <div role="tabpanel" class="tab-pane fade" id="menu-tbak">
                                <div class="text-white">
                                    <div id="view-tbak"></div>
                                </div>
                            </div>
                            <!-- END INC VIEW MENU TBAK -->
                            <!-- START INC VIEW PENGKAJIAN -->
                            <div role="tabpanel" class="tab-pane fade" id="pengkajianRiDewasa">
                                <div class="text-white">
                                    <div id="view_pengkajianRiDewasa" class="load-view"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="pengkajianRadioterapiRi">
                                <div class="text-white">
                                    <div id="view_pengkajianRadioterapiRi"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="pengkajianRiAnak">
                                <div class="text-white">
                                    <div id="view_pengkajianRiAnak"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="pengkajianRiKritis">
                                <div class="text-white">
                                    <div id="view_pengkajianRiKritis"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="pengkajianRiMedisKritis">
                                <div class="text-white">
                                    <div id="view_pengkajianRiMedisKritis" class="load-view"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="pengkajianTerapiSistemikRJ">
                                <div class="text-white">
                                    <div id="view_pengkajianTerapiSistemikRJ"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="pengkajianTerapiSistemikRJAnyelir1">
                                <div class="text-white">
                                    <div id="view_pengkajianTerapiSistemikRJAnyelir1"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="pengkajianTerapiSistemikRJAnyelir2">
                                <div class="text-white">
                                    <div id="view_pengkajianTerapiSistemikRJAnyelir2"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="pengkajianTerapiSistemikRJKemoterapi">
                                <div class="text-white">
                                    <div id="view_pengkajianTerapiSistemikRJKemoterapi"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="pengkajianRiPAKPerawat">
                                <div class="text-white">
                                    <div id="view_pengkajianRiPAKPerawat"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="pengkajianRiPAKMedis">
                                <div class="text-white">
                                    <div id="view_pengkajianRiPAKMedis"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="pengkajianRiim">
                                <div class="text-white">
                                    <div id="view_pengkajianRiim"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="pengkajianRira">
                                <div class="text-white">
                                    <div id="view_pengkajianRira"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="pengkajianRiRemaja">
                                <div class="text-white">
                                    <div id="view_pengkajianRiRemaja"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="pengkajianRiDewasaMedis">
                                <div class="text-white">
                                    <div id="view_pengkajianRiDewasaMedis" class="load-view"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="skriningPerasaanTertekan">
                                <div class="text-white">
                                    <div id="view_skriningPerasaanTertekan"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="pengkajianPraSedasi">
                                <div class="text-white">
                                    <div id="view_pengkajianPraSedasi"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="pemTinAnesLokRjRi">
                                <div class="text-white">
                                    <div id="view_pemTinAnesLokRjRi"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="barthelIndek">
                                <div class="text-white">
                                    <div id="view_barthelIndek"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="asesmenRestrain">
                                <div class="text-white">
                                    <div id="view_asesmenRestrain"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="formulirPMFAK">
                                <div class="text-white">
                                    <div id="view_formulirPMFAK"></div>
                                </div>
                            </div>
                            <!-- START INC VIEW KEPERAWATAN -->
                            <div role="tabpanel" class="tab-pane fade" id="menu-perencanaan-ak">
                                <div class="text-white">
                                    <div id="view-perencanaan-ak"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-ot-keperawatan">
                                <div class="text-white">
                                    <div id="view-ot-keperawatan"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-ews">
                                <div class="text-white">
                                    <div id="view-ews"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-pews">
                                <div class="text-white">
                                    <div id="view-pews"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="pengkajianRisikoJatuhPasienDewasa">
                                <div class="text-white">
                                    <div id="view_pengkajianRisikoJatuhPasienDewasa"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="skalaOntario">
                                <div class="text-white">
                                    <div id="view_skalaOntario"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="skalaOntarioMASS">
                                <div class="text-white">
                                    <div id="view_skalaOntarioMASS"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="humptyDumptyRi">
                                <div class="text-white">
                                    <div id="view_humptyDumptyRi"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="pemantauanNyeri">
                                <div class="text-white">
                                    <div id="view_pemantauanNyeri"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-skala-braden">
                                <div class="text-white">
                                    <div id="view-skala-braden"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="serahTerima">
                                <div class="text-white">
                                    <div id="view_serahTerima"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-otki">
                                <div class="text-white">
                                    <div id="view-otki"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-preido">
                                <div class="text-white">
                                    <div id="view-preido"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-postido">
                                <div class="text-white">
                                    <div id="view-postido"></div>
                                </div>
                            </div>
                            <!-- END INC VIEW KEPERAWATAN -->
                            <!-- START INC VIEW MENU GIZI -->
                            <div role="tabpanel" class="tab-pane fade" id="validasiMalnutrisiDewasa">
                                <div class="text-white">
                                    <div id="view_validasiMalnutrisiDewasa"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="penilaianStatusGizi">
                                <div class="text-white">
                                    <div id="view_penilaianStatusGizi"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="pengkajianGiziLanjutan">
                                <div class="text-white">
                                    <div id="view_pengkajianGiziLanjutan"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="observasiIntakeOralPasien">
                                <div class="text-white">
                                    <div id="view_observasiIntakeOralPasien"></div>
                                </div>
                            </div>
                            <!-- END INC VIEW MENU GIZI -->
                            <!-- START INC VIEW MENU RUANGAN INTENSIF -->
                            <div role="tabpanel" class="tab-pane fade" id="cpis">
                                <div class="text-white">
                                    <div id="view_cpis"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="sofa">
                                <div class="text-white">
                                    <div id="view_sofa"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="bundleVAP">
                                <div class="text-white">
                                    <div id="view_bundleVAP"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="ceklisEdukasiRuanganIntensif">
                                <div class="text-white">
                                    <div id="view_ceklisEdukasiRuanganIntensif"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="persiapanEkstubasi">
                                <div class="text-white">
                                    <div id="view_persiapanEkstubasi"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="fasthug">
                                <div class="text-white">
                                    <div id="view_fasthug"></div>
                                </div>
                            </div>
                            <!-- END INC VIEW MENU RUANGAN INTENSIF -->
                            <!-- START INC VIEW MENU ERESEP -->
                            <div role="tabpanel" class="tab-pane fade" id="eresep">
                                <div class="text-white">
                                    <div id="view_eresep"></div>
                                </div>
                            </div>
                            <!-- END INC VIEW MENU ERESEP -->
                            <!-- START INC VIEW MENU KONSULTASI -->
                            <div role="tabpanel" class="tab-pane fade" id="menu-konsultasi">
                                <div class="text-white">
                                    <div id="view-konsultasi"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="ppra">
                                <div class="text-white">
                                    <div id="view_ppra"></div>
                                </div>
                            </div>
                            <!-- <div role="tabpanel" class="tab-pane fade" id="menu-ruj-in">
                                                                                                <div class="text-white">
                                                                                                    <div id="view-ruj-in"></div>
                                                                                                </div>
                                                                                            </div> -->
                            <!-- END INC VIEW MENU KONSULTASI -->
                            <!-- START INC VIEW MENU ORDER DAN HASIL PENUNJANG -->
                            <div role="tabpanel" class="tab-pane fade" id="patologi_klinik">
                                <div class="text-white">
                                    <!-- Mulai pencarian -->
                                    <div class="form-group" id="form-cari-pk" style="z-index: 2;">
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="cari-pk" name="cari" placeholder="[ Cari ]" aria-label="Cari tindakan" autocomplete="off">
                                            <div class="input-group-append">
                                                <button class="btn btn-primary" type="button" id="aksi-cari-pk"><i class="fa fa-search"></i></button>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Akhir pencarian -->
                                    <div id="view_patologi_klinik"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-lab-pa">
                                <div class="text-white">
                                    <div id="view-lab-pa"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="reevaluasi_lab">
                                <div class="text-white">
                                    <div id="view_reevaluasi_lab"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="radiologi">
                                <div class="text-white">
                                    <div class="cariRadiologiRi" id="cari-radiologi" style="z-index: 2">
                                        <div class="row mb-4">
                                            <div class="col-md-12">
                                                <div class="input-group mb-2 mr-sm-2">
                                                    <div class="input-group-prepend">
                                                        <div class="input-group-text"><i class="fas fa-search"></i></div>
                                                    </div>
                                                    <input type="text" class="form-control form-control-lg cariInputanRadRi bg-info" placeholder="[ Cari ]">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="view_radiologi" class="view_radiologi" style="z-index: 1">
                                    </div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="prosedur">
                                <div class="text-white">
                                    <div id="view_prosedur"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="laporanHasilPemeriksaan">
                                <div class="text-white">
                                    <div id="view_laporanHasilPemeriksaan"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-lpk">
                                <div class="text-white">
                                    <div id="view-lpk"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-stockart">
                                <div class="text-white">
                                    <div id="view-stockart"></div>
                                </div>
                            </div>
                            <!-- END INC VIEW MENU ORDER DAN HASIL PENUNJANG -->

                            <!-- START CATATAN TERINTEGRASI -->
                            <div role="tabpanel" class="tab-pane fade" id="cppt">
                                <div class="text-white">
                                    <div id="view_cppt" class="load-view"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-poc">
                                <div class="text-white">
                                    <div id="view-poc"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="pemberianIntravena">
                                <div class="text-white">
                                    <div id="view_pemberianIntravena"></div>
                                </div>
                            </div>
                            <!-- END CATATAN TERINTEGRASI -->
                            <!-- START INC VIEW MENU LUKA -->
                            <!--?php //if($this->session->userdata('status') == 1){ ?-->
                            <div role="tabpanel" class="tab-pane fade" id="pengkajianLuka">
                                <div class="text-white" id="view_pengkajianLuka"></div>
                            </div>
                            <!--?php //} ?-->
                            <div role="tabpanel" class="tab-pane fade" id="pengkajianStoma">
                                <div class="text-white">
                                    <div id="view_pengkajian_stoma"></div>
                                </div>
                            </div>
                            <!-- END INC VIEW MENU LUKA -->
                            <!-- START INC VIEW TRANSFER RUANGAN -->
                            <div role="tabpanel" class="tab-pane fade" id="permintaanDirawat">
                                <div class="text-white">
                                    <div id="view_permintaanDirawat"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-formulir-pindah-ruangan">
                                <div class="text-white">
                                    <div id="view-formulir-pindah-ruangan"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="ceklisEdukasiOrientasiRI">
                                <div class="text-white">
                                    <div id="view_ceklisEdukasiOrientasiRI"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="surveilans">
                                <div class="text-white">
                                    <div id="view_surveilans"></div>
                                </div>
                            </div>
                            <!--?php //if ($pasien['ID_RUANGAN'] == 105140101) { ?-->
                            <div role="tabpanel" class="tab-pane fade" id="formulirKriteriaRawatIntensif">
                                <div class="text-white">
                                    <div id="view_formulirKriteriaRawatIntensif"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="formulirKriteriaRawatPicu">
                                <div class="text-white">
                                    <div id="view_formulirKriteriaRawatPicu"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="serahTerimaPemRi">
                                <div class="text-white">
                                    <div id="view_serahTerimaPemRi"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-riim">
                                <div class="text-white">
                                    <div id="view-riim"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-rira">
                                <div class="text-white">
                                    <div id="view-rira"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-rinad">
                                <div class="text-white">
                                    <div id="view-rinad"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="kesInvRi">
                                <div class="text-white">
                                    <div id="view_kesInvRi"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="sttsAnestesiaRi">
                                <div class="text-white">
                                    <div id="view_sttsAnestesiaRi"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="tandaVitalAnestesia">
                                <div class="text-white">
                                    <div id="view_tandaVitalAnestesia"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-csoasema">
                                <div class="text-white">
                                    <div id="view-csoasema"></div>
                                </div>
                            </div>
                            <!--?php //} ?-->
                            <!-- END INC VIEW TRANSFER RUANGAN -->
                            <!-- START VIEW ADMISSION -->
                            <div role="tabpanel" class="tab-pane fade" id="formulirSkriningVisualRi">
                                <div class="text-white">
                                    <div id="view_formulirSkriningVisualRi"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="CekPIRI">
                                <div class="text-white">
                                    <div id="view_CekPIRI"></div>
                                </div>
                            </div>
                            <!-- END VIEW ADMISSION -->
                            <!-- START VIEW PENGKAJIAN TINDAKAN -->
                            <div role="tabpanel" class="tab-pane fade" id="menu-pengkajian-pra-operasi">
                                <div class="text-white">
                                    <div id="view-pengkajian-pra-operasi"></div>
                                </div>
                            </div>
                            <!-- END VIEW PENGKAJIAN TINDAKAN -->
                            <!-- START INC VIEW MENU OPERASI -->
                            <div role="tabpanel" class="tab-pane fade" id="menu-daftar-operasi">
                                <div class="text-white">
                                    <div id="view-daftar-operasi"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="pengPraOperasi">
                                <div class="text-white">
                                    <div id="view_pengPraOperasi"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="siteMarking">
                                <div class="text-white">
                                    <div id="view_siteMarking"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-lap-operasi">
                                <div class="text-white">
                                    <div id="view-lap-operasi"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-rapo">
                                <div class="text-white">
                                    <div id="view-rapo"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="intOpeRi">
                                <div class="text-white">
                                    <div id="view_intOpeRi"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="asPraAnes">
                                <div class="text-white">
                                    <div id="view_asPraAnes"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="kesOpeRi">
                                <div class="text-white">
                                    <div id="view_kesOpeRi"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-alat-operasi">
                                <div class="text-white">
                                    <div id="view-alat-operasi"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-cpao">
                                <div class="text-white">
                                    <div id="view-cpao"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="cpo">
                                <div class="text-white">
                                    <div id="view_cpo"></div>
                                </div>
                            </div>
                            <!-- END INC VIEW MENU OPERASI -->
                            <!-- START INC VIEW MENU KEMOTERAPI -->
                            <div role="tabpanel" class="tab-pane fade" id="pengkajianTerapiSistemikRi">
                                <div class="text-white">
                                    <div id="view_pengkajianTerapiSistemikRi"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="protokolKemoterapiRi">
                                <div class="text-white">
                                    <div id="view_protokolKemoterapiRi"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-pro-kem-anak">
                                <div class="text-white">
                                    <div id="view-pro-kem-anak"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-jpokem">
                                <div class="text-white">
                                    <div id="view-jpokem"></div>
                                </div>
                            </div>
                            <!-- END INC VIEW MENU KEMOTERAPI -->
                            <!-- START INC VIEW MENU KARDEK -->
                            <div role="tabpanel" class="tab-pane fade" id="kardekRi">
                                <div class="text-white">
                                    <div id="view_kardekRi"></div>
                                </div>
                            </div>
                            <!-- END INC VIEW MENU KARDEK -->
                            <!-- START INC VIEW MENU KARDEK -->
                            <div role="tabpanel" class="tab-pane fade" id="eKardekRi">
                                <div class="text-white">
                                    <div id="view_eKardekRi"></div>
                                </div>
                            </div>
                            <!-- END INC VIEW MENU KARDEK -->
                            <!-- START PRMJRI -->
                            <!-- <div role="tabpanel" class="tab-pane fade" id="menu-prmjri">
                                                                                                <div class="text-white">
                                                                                                    <div id="view-prmjri"></div>
                                                                                                </div>
                                                                                            </div> -->
                            <!-- END PRMJRI -->
                            <!-- START INC VIEW MENU IGD -->
                            <div role="tabpanel" class="tab-pane fade" id="menu-dashboard-igd">
                                <div class="text-white">
                                    <div id="view-dashboard-igd"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="formulirTriaseRi">
                                <div class="text-white">
                                    <div id="view_formulirTriaseRi"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="surKetEm">
                                <div class="text-white">
                                    <div id="view_surKetEm"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="pengkajianIgdRi">
                                <div class="text-white">
                                    <div id="view_pengkajianIgdRi"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="laporDPJP">
                                <div class="text-white">
                                    <div id="view_laporDPJP"></div>
                                </div>
                            </div>
                            <!-- END INC VIEW MENU IGD -->

                            <!-- START INC VIEW MENU FARMASI -->
                            <div role="tabpanel" class="tab-pane fade" id="rekonsiliasiObat">
                                <div class="text-white">
                                    <div id="view_rekonsiliasiObat"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="konseling">
                                <div class="text-white">
                                    <div id="view_konseling"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="feso">
                                <div class="text-white">
                                    <div id="view_feso"></div>
                                </div>
                            </div>
                            <!-- END INC VIEW MENU FARMASI -->


                            <!-- START INC VIEW MENU RESUME -->
                            <div role="tabpanel" class="tab-pane fade" id="pemulanganPasienRi">
                                <div class="text-white">
                                    <div id="view_pemulanganPasienRi"></div>
                                </div>
                            </div>
                            <!-- END INC VIEW MENU RESUME -->

                            <!-- START INC VIEW MENU PERSETUJUAN DAN EDUKASI -->
                            <div role="tabpanel" class="tab-pane fade" id="menu-catatan-edukasi">
                                <div class="text-white">
                                    <div id="view-catatan-edukasi"></div>
                                </div>
                            </div>
                            <!-- END INC VIEW MENU PERSETUJUAN DAN EDUKASI -->

                            <!-- START INC VIEW MENU BRAKHITERAPI -->
                            <div role="tabpanel" class="tab-pane fade" id="pengkajianRiBrak">
                                <div class="text-white">
                                    <div id="view_pengkajianRiBrak"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="pengkajianRiMedisBrakhiterapi">
                                <div class="text-white">
                                    <div id="view_pengkajianRiMedisBrakhiterapi" class="load-view"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-otkb">
                                <div class="text-white">
                                    <div id="view-otkb"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="ltbg">
                                <div class="text-white">
                                    <div id="view_ltbg"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="brakNaso">
                                <div class="text-white">
                                    <div id="view_brakNaso"></div>
                                </div>
                            </div>
                            <!-- END INC VIEW MENU BRAKHITERAPI -->


                            <!-- START INC VIEW MENU RADIOTERAPI (Bekas Rawat Jalan)-->
                            <div role="tabpanel" class="tab-pane fade" id="pengkajianRiRadio">
                                <div class="text-white">
                                    <div id="view_pengkajianRiRadio"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="CTRiRadio">
                                <div class="text-white">
                                    <div id="view_CTRiRadio" class="load-view"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="simulatorRiRadio">
                                <div class="text-white">
                                    <div id="view_simulatorRiRadio"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="doseRiRadio">
                                <div class="text-white">
                                    <div id="view_doseRiRadio"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="penjadwalanradioterapi">
                                <div class="text-white">
                                    <div id="view_penjadwalanradioterapi"></div>
                                </div>
                            </div>
                            <!-- END INC VIEW MENU RADIOTERAPI -->

                            <!-- START INC VIEW MENU GERIATRI -->
                            <div role="tabpanel" class="tab-pane fade" id="instrumenPPPG">
                                <div class="text-white">
                                    <div id="view_instrumenPPPG"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="penilaianADL">
                                <div class="text-white">
                                    <div id="view_penilaianADL"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="iadl">
                                <div class="text-white">
                                    <div id="view_iadl"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="instrumen-gds">
                                <div class="text-white">
                                    <div id="view-instrumen-gds"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="pemeriksaanMiniCOG">
                                <div class="text-white">
                                    <div id="view_pemeriksaanMiniCOG"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="instrumenMmse">
                                <div class="text-white">
                                    <div id="view_instrumenMmse"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="abbreviatedMentalTest">
                                <div class="text-white">
                                    <div id="view_abbreviatedMentalTest"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="instrumenMna">
                                <div class="text-white">
                                    <div id="view_instrumenMna"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="formulirG8Geriatri">
                                <div class="text-white">
                                    <div id="view_formulirG8Geriatri"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="resikojatuhlanjutusia">
                                <div class="text-white">
                                    <div id="view_resikojatuhlanjutusia"></div>
                                </div>
                            </div>
                            <!-- END INC VIEW MENU GERIATRI -->
                            <!-- START SEDASI -->
                            <div role="tabpanel" class="tab-pane fade" id="statusSedasi">
                                <div class="text-white">
                                    <div id="view_statusSedasi"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="medikasiObat">
                                <div class="text-white">
                                    <div id="view_medikasiObat"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="medikasiNapas">
                                <div class="text-white">
                                    <div id="view_medikasiNapas"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="sedasiPemulihan">
                                <div class="text-white">
                                    <div id="view_sedasiPemulihan"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="pemulihanNapas">
                                <div class="text-white">
                                    <div id="view_pemulihanNapas"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="PIPTKS">
                                <div class="text-white">
                                    <div id="view_PIPTKS"></div>
                                </div>
                            </div>
                            <!-- END SEDASI -->
                            <!-- START INFORMED CONSENT -->
                            <div role="tabpanel" class="tab-pane fade" id="persetujuan-tindakan-kedokteran">
                                <div class="text-white">
                                    <div id="view-persetujuan-tindakan-kedokteran"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="persetujuanTindakanPengobatanKemoterapi">
                                <div class="text-white">
                                    <div id="view_persetujuanTindakanPengobatanKemoterapi"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="PTKemoRhabdomiosarkoma">
                                <div class="text-white">
                                    <div id="view_PTKemoRhabdomiosarkoma"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="persetujuanTindakanKemoterapiLLAAnak">
                                <div class="text-white">
                                    <?php $this->load->view('Pengkajian/informedConsent/persetujuanTindakanKemoterapiLLAAnak/index') ?>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="penolakanTindakanKedokteran">
                                <div class="text-white">
                                    <div id="view_penolakanTindakanKedokteran"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-pttd">
                                <div class="text-white">
                                    <div id="view-pttd"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="spinalEpi">
                                <div class="text-white">
                                    <div id="view_spinalEpi"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="PTKBedah">
                                <div class="text-white">
                                    <div id="view_PTKBedah"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="dnr">
                                <div class="text-white">
                                    <div id="view_dnr"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="PIPTKAU">
                                <div class="text-white">
                                    <div id="view_PIPTKAU"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="EdukasiTAS">
                                <div class="text-white">
                                    <div id="view_EdukasiTAS"></div>
                                </div>
                            </div>
                            <!-- END INFORMED CONSENT -->

                            <!-- START FLAP -->
                            <div role="tabpanel" class="tab-pane fade" id="flap">
                                <div class="text-white">
                                    <div id="view_flap"></div>
                                </div>
                            </div>
                            <!-- END FLAP -->

                            <!-- START FLOSHEET -->
                            <div role="tabpanel" class="tab-pane fade" id="flosheet">
                                <div class="text-white">
                                    <div id="view_flosheet"></div>
                                </div>
                            </div>
                            <!-- END FLOSHEET -->

                            <!-- START FORMULIR SKRINING COVID-19 -->
                            <div role="tabpanel" class="tab-pane fade" id="formulirSkriningCovid19">
                                <div class="text-white">
                                    <div id="view_formulirSkriningCovid19"></div>
                                </div>
                            </div>
                            <!-- END FORMULIR SKRINING COVID-19 -->
                            <!-- START FORM EPID COVID-19 -->
                            <div role="tabpanel" class="tab-pane fade" id="formulirEpidemologiCovid">
                                <div class="text-white">
                                    <div id="view_formulirEpidemologiCovid"></div>
                                </div>
                            </div>
                            <!-- END FORM EPID COVID-19 -->
                            <!-- START PEMEBERIAN DARAH -->
                            <div role="tabpanel" class="tab-pane fade" id="pemberianDanPemantauanDarah">
                                <div class="text-white">
                                    <div id="view_pemberianDanPemantauanDarah"></div>
                                </div>
                            </div>
                            <!-- END PEMEBERIAN DARAH -->
                            <!-- START VIEW ETIMJA -->
                            <div role="tabpanel" class="tab-pane fade" id="menu-etimja">
                                <div class="text-white">
                                    <div id="view-etimja"></div>
                                </div>
                            </div>
                            <!-- END VIEW ETIMJA -->
                            <!-- START VIEW BERKAS -->
                            <div role="tabpanel" class="tab-pane fade" id="menu-berkas-pasien-luar">
                                <div class="text-white">
                                    <div id="view-berkas-pasien-luar"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-berkas-scan">
                                <div class="text-white">
                                    <div id="view-berkas-scan"></div>
                                </div>
                            </div>
                            <!-- END VIEW BERKAS -->
                            <!-- START INC VIEW RESUME KEMATIAN -->
                            <div role="tabpanel" class="tab-pane fade" id="resumeKematian">
                                <div class="text-white">
                                    <div id="view_resumeKematian"></div>
                                </div>
                            </div>
                            <!-- END INC VIEW RESUME KEMATIAN -->
                            <!-- START INC VIEW PEMAKAIAN O2 -->
                            <div role="tabpanel" class="tab-pane fade" id="pemakaianO2Ri">
                                <div class="text-white">
                                    <div id="view_pemakaianO2Ri"></div>
                                </div>
                            </div>
                            <!-- END INC VIEW PEMAKAIAN O2 -->
                            <!-- START INC VIEW MENU MANAJER PELAYANAN PASIEN -->
                            <div role="tabpanel" class="tab-pane fade" id="menu-smpp">
                                <div class="text-white">
                                    <div id="view-smpp"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-ampp">
                                <div class="text-white">
                                    <div id="view-ampp"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-impp">
                                <div class="text-white">
                                    <div id="view-impp"></div>
                                </div>
                            </div>
                            <!-- END INC VIEW MENU MANAJER PELAYANAN PASIEN -->
                            <!-- START INC VIEW MENU HIV -->
                            <div role="tabpanel" class="tab-pane fade" id="menu-fuart">
                                <div class="text-white">
                                    <div id="view-fuart"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-rart">
                                <div class="text-white">
                                    <div id="view-rart"></div>
                                </div>
                            </div>
                            <!-- END INC VIEW MENU HIV -->
                            <!-- START VIEW MENU PALIATIF -->
                            <div role="tabpanel" class="tab-pane fade" id="pengkajianPaliatifRI">
                                <div class="text-white">
                                    <div id="view_pengkajianPaliatifRI"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-ffm">
                                <div class="text-white">
                                    <div id="view_ffm"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-hads">
                                <div class="text-white">
                                    <div id="view_hads"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-iikpp">
                                <div class="text-white">
                                    <div id="view_iikpp"></div>
                                </div>
                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="menu-kpppp">
                                <div class="text-white">
                                    <div id="view_kpppp"></div>
                                </div>
                            </div>
                            <!-- END VIEW MENU PALIATIF -->
                            <!-- START INC VIEW ECHO EKG -->
                            <div role="tabpanel" class="tab-pane fade" id="echoekg">
                                <div class="text-white">
                                    <div id="view_echoekg"></div>
                                </div>
                            </div>
                            <!-- END ECHO EKG -->
                            <!-- START VIEW INPUT TPN -->
                            <div role="tabpanel" class="tab-pane fade" id="inputTpn">
                                <div class="text-white">
                                    <div id="view_inputTpn"></div>
                                </div>
                            </div>
                            <!-- END VIEW INPUT TPN -->
                        </div>
                    </div>
                </div>
            </div>

            <div class="btn-group" style="position:fixed; margin:0px; right:-4.58rem; top:50%;transform: rotate(-90deg);-webkit-transform: rotate(-90deg);-moz-transform: rotate(-90deg);-o-transform: rotate(-90deg);">
                <!-- <a class="btn btn-warning waves-effect" role="button" id="view_all_cppt_side">CPPT</a> -->
                <!-- <button class="btn btn-custom waves-effect" id="buka-side-konsultasi">Konsultasi</button> -->
                <!-- <a class="btn btn-danger waves-effect" role="button" id="viewHasilSpeechSideRi"><span style="color: black">Speech</span></a> -->
                <a class="btn btn-warning waves-effect" role="button" id="viewHasilPenunjangSideRi"><span style="color: black">CPPT & Hasil Penunjang</span></a>
                <!-- <a class="btn btn-success waves-effect" role="button" id="viewHasilKardekSideRi">Kardek</a> -->
                <!-- <a class="btn btn-secondary waves-effect" role="button" id="viewInfoBalanceCairan"><span class="text-white">Balance Cairan</span></a> -->
            </div>
        <?php } else { ?>
            <div class="col-md">
                <div class="card m-b-20 card-inverse text-white">
                    <img class="card-img img-fluid" src="<?= base_url('assets/admin/assets/images/banner.jpg') ?>" alt="Card image" style="filter:hue-rotate(50deg);">
                    <div class="card-img-overlay">
                        <h4 class="card-title text-white">
                            <span class="badge" style="font-size: 36px; background-color:#5b69bcbd">
                                Selamat datang
                            </span>
                        </h4>
                        <div class="card-text">
                            <h3>
                                <span class="badge" style="font-size: 24px; background-color:#5b69bcbd">
                                    <i class='fa fa-arrow-left'></i> Silakan pilih pasien pada list pasien.
                                </span>
                            </h3>
                        </div>
                    </div>
                </div>
            </div>
        <?php } ?>
        <!-- </div> -->
    </div>
</div>

<!-- Mulai modal history eTimja -->
<div class="modal fade" id="modal-history-etimja-info" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-dialog-centered" style="max-width: 90%">
        <div class="modal-content" id="lihat-history-modal-etimja-info"></div>
    </div>
</div>
<!-- Akhir modal history eTimja -->

<!-- Mulai modal iCare -->
<div class="modal fade" id="modal-icare" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" style="max-width: 90%">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">iCARE</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="lihat-icare" style="overflow-y: auto; max-height: 80vh;">
                <iframe id="icare-iframe" width="100%" height="600px" frameborder="0"></iframe>
            </div>
        </div>
    </div>
</div>
<!-- Akhir modal iCare -->

<!-- Start Rekonsiliasi Obat -->
<div class="modal fade" id="modal-history-rekonsiliasi-obat" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" style="max-width: 90%">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="viewExpertise">History Rekonsiliasi obat</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-content" id="lihat-history-modal-rekonsiliasi-obat"></div>
        </div>
    </div>
</div>
<!-- End Rekonsiliasi Obat -->

<!-- START MODAL VIEW MONITORING TANDA VITAL -->
<div id="viewTandaVitalMon" class="modal fade" role="dialog">
    <div class="modal-dialog modal-xl" style="max-width: 1300px;">
        <div class="modal-content" style="padding: 0px">
            <div id="hasilShowTandaVital">

            </div>
        </div>
    </div>
</div>
<!-- END MODAL VIEW MONITORING TANDA VITAL -->

<!-- START MODAL VIEW PPK -->
<div class="modal fade" id="modal_ppk" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-scrollable" id="scrolling">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">PPK</h3>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div id="modalDetilPPK" style="overflow: auto"></div>
            <div class="modal-footer">
                <a href="#" class="btn btn-secondary" data-toggle="modal" data-dismiss="modal"><i class="fa fa-times-circle"></i> Tutup
                </a>
            </div>
        </div>
    </div>
</div>
<!-- END MODAL VIEW PPK -->

<!-- START MODAL VIEW CP -->
<div class="modal fade" id="modal_cp" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-scrollable" id="scrolling">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Clinical Pathway</h3>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div id="modalDetilCP" style="overflow: auto"></div>
            <div class="modal-footer">
                <a href="#" class="btn btn-secondary" data-toggle="modal" data-dismiss="modal"><i class="fa fa-times-circle"></i> Tutup
                </a>
            </div>
        </div>
    </div>
</div>
<!-- END MODAL VIEW CP -->

<!-- START MODAL VIEW REGKAN -->
<div id="viewModalREGKANRi" class="modal fade" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div id="hasilModalREGKANRi">

            </div>
        </div>
    </div>
</div>
<!-- END MODAL VIEW REGKAN -->

<!-- START MODAL TANDA HIV -->
<div id="tandaHIV" class="modal fade" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Penanda Pasien HIV</h3>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <form id="formTandaHIV">
                <input type="hidden" name="normHIV" value="<?= $getNomr['NORM'] ?>">
                <input type="hidden" name="userHIV" value="<?= $this->session->userdata('id') ?>">
                <div class="row form-group">
                    <label for="namaDNR_edit" class="col-sm-12 col-md-4 col-form-label">
                        Keterangan
                    </label>
                    <div class="col-sm-12 col-md-8">
                        <input type="text" class="form-control" id="keteranganTandaHIV" name="keteranganTandaHIV">
                    </div>
                </div>
                <div class="row">
                    <div class="offset-10 col-lg-2">
                        <div class="form-group">
                            <div class="pull-right">
                                <button type="button" class="btn btn-primary btn-block" id="simpanTandaHIV"><i class="fa fa-save"></i> Simpan</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- END MODAL TANDA HIV -->

<!-- START MODAL INPUT DIAGNOSA -->
<div id="viewInputDiagnosaDpjpRI" class="modal fade" role="dialog">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div id="hasilInputDiagnosaDpjpRI">

            </div>
        </div>
    </div>
</div>
<!-- END MODAL INPUT DIAGNOSA -->

<div class="modal fade" id="modal-warning-poc" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0">
            <div class="modal-header bg-warning text-white justify-content-center position-relative py-3">
                <h4 class="modal-title text-center mb-0 font-weight-bold">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    Peringatan Plan of Care
                </h4>
                <button type="button" class="close text-white position-absolute" data-dismiss="modal" aria-hidden="true" style="right: 15px; top: 50%; transform: translateY(-50%);">&times;</button>
            </div>
            <div class="modal-body text-center py-4">
                <div class="mb-3">
                    <i class="fas fa-clipboard-list text-warning" style="font-size: 48px;"></i>
                </div>
                <p class="mb-0 font-weight-medium">Pasien ini belum mengisi <i>Plan of Care</i> pada kunjungan ini, silahkan isi terlebih dahulu.</p>
            </div>
            <div class="modal-footer border-0 justify-content-center">
                <button type="button" class="btn btn-light px-4" data-dismiss="modal">
                    <i class="fas fa-times mr-1"></i>Tutup
                </button>
                <button type="button" class="btn btn-success px-4 text-white" id="btn-isi-poc">
                    <i class="fas fa-pen mr-1"></i>Isi Sekarang
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function () {

        $('#modal_ppk').on('show.bs.modal', function (e) {
            var id = $(e.relatedTarget).data('id');
            $.ajax({
                type: 'POST',
                url: '<?= base_url() ?>rekam_medis/rawat_inap/ppk/PPK/modalDetailPPK',
                data: {
                    id: id
                },
                success: function (data) {
                    $('#modalDetilPPK').html(data);
                }
            });
        });

        $('.viewModalRegkanRi').click(function () {
            var nomr = $(this).data('nomr');
            var nopen = $(this).data('nopen');
            var nokun = $(this).data('nokun');
            $.ajax({
                type: 'POST',
                url: '<?= base_url() ?>PengkajianAwal/viewModalRegkan',
                data: {
                    nomr: nomr,
                    nokun: nokun,
                    nopen: nopen,
                },
                success: function (data) {
                    $('#hasilModalREGKANRi').html(data);
                }
            });
        });

        $('#modal_cp').on('show.bs.modal', function (e) {
            var id = $(e.relatedTarget).data('id');
            $.ajax({
                type: 'POST',
                url: '<?= base_url() ?>rekam_medis/rawat_inap/clinical_pathway/CP/modalDetailCP',
                data: {
                    id: id
                },
                success: function (data) {
                    $('#modalDetilCP').html(data);
                }
            });
        });
    });
</script>

<script type="text/javascript">
    var contentTourPage = "<div class='row mt-1'>" +
        "<div class='col-md'>" +
        "<strong>Silahkan lanjutkan Konsultasi ke Sp.GK</strong>" +
        "</div>" +
        "</div>";
    var anno2 = new Anno([{
        target: '#viewTourPageGizi',
        position: 'center-top',
        className: 'anno-width-400',
        content: $('#viewTourPageGizi').text() + contentTourPage,
        buttons: [{
            text: 'KONSUL',
            className: 'anno-btn-ok',
            click: function (anno, evt) {
                $('#konsultasi').trigger('click');
                anno2.hide();
                $('#pilihan-waktu-konsul-otomatis').focus();
                $(".nav-item, .nav-link").addClass("disabled");
                // $("#form-konsultasi").html('<input type="text" name="tourpageKonsul" id="tourpageKonsul" value="1">');
                // var x = document.querySelector(".preloader");
                // var element = document.getElementById('konsultasi');
                // if ($('#konsultasi').hasClass('active') == true) {
                //     $('#tourpageKonsul').val('1').trigger("input");
                //     console.log($('#konsultasi').hasClass('active'));
                // }
                // console.log(window.getComputedStyle(element).visibility);
                setTimeout(function () {
                    $('#tourpageKonsul').val('1').trigger("input");
                }, 600);

            }
        }
            // ,{
            //     text: 'TIDAK',
            //     className: 'anno-btn-batal',
            //     click: function (anno, evt) {
            //         $.confirm({
            //             theme: 'Modern',
            //             title: 'Informasi!',
            //             content: 'Apa alasan tidak konsul?',
            //             buttons: {
            //                 tombolPertama: {
            //                     text: 'Dapat ditangani sendiri',
            //                     btnClass: 'btn-blue',
            //                     action: function () {
            //                         $.ajax({
            //                             url: "<?= base_url('rekam_medis/Medis/notifGizi') ?>",
            //                             method: "POST",
            //                             data: {
            //                                 status: 1,
            //                                 nomr: "<?= $pasien['NORM'] ?? null ?>",
            //                                 nokun: "<?= $nokun ?>"
            //                             },
            //                             success: function (data) {
            //                                 alertify.success('Data Tersimpan');
            //                             }
            //                         });
            //                     }
            //                 },
            //                 tombolKedua: {
            //                     text: 'Bukan malnutrisi',
            //                     btnClass: 'btn-warning',
            //                     action: function () {
            //                         $.ajax({
            //                             url: "<?= base_url('rekam_medis/Medis/notifGizi') ?>",
            //                             method: "POST",
            //                             data: {
            //                                 status: 2,
            //                                 nomr: "<?= $pasien['NORM'] ?? null ?>",
            //                                 nokun: "<?= $nokun ?>"
            //                             },
            //                             success: function (data) {
            //                                 alertify.success('Data Tersimpan');
            //                             }
            //                         });
            //                     }
            //                 }
            //             }
            //         });
            //         anno2.hide();
            //     }
            // }
        ]
    }]);

    <?php if (isset($infoPemberianAntiBiotik)) { ?>
        var contentTourPageAntiBiotik = "<div class='row mt-1'>" +
            "<div class='col-md'>" +
            "<strong>";

        contentTourPageAntiBiotik += "<?= $infoPemberianAntiBiotik['NOTIF_DESKRIPSI'] ?>";

        contentTourPageAntiBiotik += "</strong>" +
            "</div>" +
            "</div>";
        var anno3 = new Anno([{
            target: '#notifAntiBiotikTourPage',
            position: 'center-top',
            className: 'anno-width-400',
            content: contentTourPageAntiBiotik,
            buttons: [{
                text: 'Done',
                className: 'anno-btn-ok',
                click: function (anno, evt) {
                    anno3.hide();
                }
            }]
        }]);
        anno3.show();
    <?php } ?>
</script>

<script>
    $(document).ready(function () {
        var dpjp = '<?= $pasien['ID_DPJP'] ?? null ?>';
        var dokter = '<?= $this->session->userdata('iddokter') ?>';
        var ruangan = '<?=$pasien['ID_RUANGAN']?>';
        let towerc = ['105011701', '105011801', '105011901', '105030108'];

        if (dpjp == dokter) {
            if(towerc.includes(ruangan) == false){
                // anno2.show();
                if (<?= isset($pasien['ID_USER']) ? $pasien['ID_USER'] : "0" ?> == <?= $this->session->userdata('id') ?> || <?= $this->session->userdata('smf') ?> == "31") {
                    if (<?= $pasienKonsul ?? 0 ?> < 1) {
                        if (<?= isset($pasien['ID_STATUS_PENILAIAN_GIZI']) && $pasien['ID_STATUS_PENILAIAN_GIZI'] != "" ? $pasien['ID_STATUS_PENILAIAN_GIZI'] : '0' ?> == 4081 || <?= isset($pasien['ID_STATUS_PENILAIAN_GIZI']) && $pasien['ID_STATUS_PENILAIAN_GIZI'] != "" ? $pasien['ID_STATUS_PENILAIAN_GIZI'] : '0' ?> == 4082) {
                            if ($('#viewTourPageGizi').text() != "") {
                                anno2.show();
                            }
                        }
                    }
                }
            }
        }
        // var ukuranLayar = $(window).width();
        // alert(ukuranLayar);

        // TOMBOL HASIL PENUNJANG UNTUK SIDE BAR
        $('.tombolHasilPenunjangSideRi').on("click", function () {
            var jenis = $(this).data("jenis");
            if (jenis == 1) {
                $('#labPkSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link active');
                $('#labPkSideBarRi').attr('class', 'tab-pane fade show active');
                // START LOAD JENIS 1
                $('#labPkSideBarRi').load('<?= base_url() ?>rekam_medis/Medis/labPkSideBarRi/<?= $getNomr['NOKUN'] ?? null ?>');
                // END LOAD JENIS 1

                $('#labPaSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPaSideBarRi').attr('class', 'tab-pane fade');

                $('#labPtSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPtSideBarRi').attr('class', 'tab-pane fade');

                $('#radiologiSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#radiologiSideBarRi').attr('class', 'tab-pane fade');

                $('#resepSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#resepSideBarRi').attr('class', 'tab-pane fade');

                $('#eresepSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#eresepSideBarRi').attr('class', 'tab-pane fade');

                $('#cpptSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#cpptSideBarRi').attr('class', 'tab-pane fade');

                $('#kardekSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#kardekSideBarRi').attr('class', 'tab-pane fade');

                $('#cairanSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#cairanSideBarRi').attr('class', 'tab-pane fade');

                $('#konsultasiSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#konsultasiSideBarRi').attr('class', 'tab-pane fade');

                $('#menu-history-pengkajian-side-ri').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#history-pengkajian-side-ri').attr('class', 'tab-pane fade');

                $('#formulariumSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#formulariumSideBarRi').attr('class', 'tab-pane fade');

            } else if (jenis == 2) {
                $('#labPaSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link active');
                $('#labPaSideBarRi').attr('class', 'tab-pane fade show active');
                // START LOAD JENIS 1
                $('#labPaSideBarRi').load('<?= base_url() ?>rekam_medis/Medis/labPaSideBarRi/<?= $getNomr['NOKUN'] ?? null ?>');
                // END LOAD JENIS 1

                $('#labPkSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPkSideBarRi').attr('class', 'tab-pane fade');

                $('#labPtSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPtSideBarRi').attr('class', 'tab-pane fade');

                $('#radiologiSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#radiologiSideBarRi').attr('class', 'tab-pane fade');

                $('#resepSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#resepSideBarRi').attr('class', 'tab-pane fade');

                $('#eresepSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#eresepSideBarRi').attr('class', 'tab-pane fade');

                $('#cpptSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#cpptSideBarRi').attr('class', 'tab-pane fade');

                $('#kardekSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#kardekSideBarRi').attr('class', 'tab-pane fade');

                $('#cairanSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#cairanSideBarRi').attr('class', 'tab-pane fade');

                $('#konsultasiSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#konsultasiSideBarRi').attr('class', 'tab-pane fade');

                $('#menu-history-pengkajian-side-ri').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#history-pengkajian-side-ri').attr('class', 'tab-pane fade');

                $('#formulariumSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#formulariumSideBarRi').attr('class', 'tab-pane fade');
            } else if (jenis == 3) {
                $('#radiologiSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link active');
                $('#radiologiSideBarRi').attr('class', 'tab-pane fade show active');
                // START LOAD JENIS 1
                $('#radiologiSideBarRi').load('<?= base_url() ?>rekam_medis/Medis/radiologiSideBarRi/<?= $getNomr['NOKUN'] ?? null ?>');
                // END LOAD JENIS 1

                $('#labPkSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPkSideBarRi').attr('class', 'tab-pane fade');

                $('#labPaSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPaSideBarRi').attr('class', 'tab-pane fade');

                $('#labPtSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPtSideBarRi').attr('class', 'tab-pane fade');

                $('#resepSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#resepSideBarRi').attr('class', 'tab-pane fade');

                $('#eresepSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#eresepSideBarRi').attr('class', 'tab-pane fade');

                $('#cpptSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#cpptSideBarRi').attr('class', 'tab-pane fade');

                $('#kardekSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#kardekSideBarRi').attr('class', 'tab-pane fade');

                $('#cairanSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#cairanSideBarRi').attr('class', 'tab-pane fade');

                $('#konsultasiSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#konsultasiSideBarRi').attr('class', 'tab-pane fade');

                $('#menu-history-pengkajian-side-ri').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#history-pengkajian-side-ri').attr('class', 'tab-pane fade');

                $('#formulariumSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#formulariumSideBarRi').attr('class', 'tab-pane fade');
            } else if (jenis == 4) {
                $('#resepSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link active');
                $('#resepSideBarRi').attr('class', 'tab-pane fade show active');

                $('#eresepSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#eresepSideBarRi').attr('class', 'tab-pane fade');

                $('#labPkSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPkSideBarRi').attr('class', 'tab-pane fade');

                $('#labPaSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPaSideBarRi').attr('class', 'tab-pane fade');

                $('#labPtSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPtSideBarRi').attr('class', 'tab-pane fade');

                $('#radiologiSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#radiologiSideBarRi').attr('class', 'tab-pane fade');

                $('#cpptSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#cpptSideBarRi').attr('class', 'tab-pane fade');

                $('#kardekSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#kardekSideBarRi').attr('class', 'tab-pane fade');

                $('#cairanSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#cairanSideBarRi').attr('class', 'tab-pane fade');

                $('#konsultasiSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#konsultasiSideBarRi').attr('class', 'tab-pane fade');

                $('#menu-history-pengkajian-side-ri').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#history-pengkajian-side-ri').attr('class', 'tab-pane fade');

                $('#formulariumSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#formulariumSideBarRi').attr('class', 'tab-pane fade');
            } else if (jenis == 5) {
                $('#cpptSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link active');
                $('#cpptSideBarRi').attr('class', 'tab-pane fade show active');

                $('#labPkSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPkSideBarRi').attr('class', 'tab-pane fade');

                $('#labPaSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPaSideBarRi').attr('class', 'tab-pane fade');

                $('#labPtSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPtSideBarRi').attr('class', 'tab-pane fade');

                $('#radiologiSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#radiologiSideBarRi').attr('class', 'tab-pane fade');

                $('#resepSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#resepSideBarRi').attr('class', 'tab-pane fade');

                $('#eresepSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#eresepSideBarRi').attr('class', 'tab-pane fade');

                $('#kardekSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#kardekSideBarRi').attr('class', 'tab-pane fade');

                $('#cairanSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#cairanSideBarRi').attr('class', 'tab-pane fade');

                $('#konsultasiSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#konsultasiSideBarRi').attr('class', 'tab-pane fade');

                $('#menu-history-pengkajian-side-ri').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#history-pengkajian-side-ri').attr('class', 'tab-pane fade');

                $('#formulariumSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#formulariumSideBarRi').attr('class', 'tab-pane fade');
            } else if (jenis == 6) {
                $('#kardekSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link active');
                $('#kardekSideBarRi').attr('class', 'tab-pane fade show active');

                $('#labPkSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPkSideBarRi').attr('class', 'tab-pane fade');

                $('#labPaSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPaSideBarRi').attr('class', 'tab-pane fade');

                $('#labPtSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPtSideBarRi').attr('class', 'tab-pane fade');

                $('#radiologiSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#radiologiSideBarRi').attr('class', 'tab-pane fade');

                $('#resepSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#resepSideBarRi').attr('class', 'tab-pane fade');

                $('#eresepSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#eresepSideBarRi').attr('class', 'tab-pane fade');

                $('#cpptSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#cpptSideBarRi').attr('class', 'tab-pane fade');

                $('#cairanSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#cairanSideBarRi').attr('class', 'tab-pane fade');

                $('#konsultasiSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#konsultasiSideBarRi').attr('class', 'tab-pane fade');

                $('#menu-history-pengkajian-side-ri').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#history-pengkajian-side-ri').attr('class', 'tab-pane fade');

                $('#formulariumSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#formulariumSideBarRi').attr('class', 'tab-pane fade');
            } else if (jenis == 7) {
                $('#cairanSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link active');
                $('#cairanSideBarRi').attr('class', 'tab-pane fade show active');

                $('#labPkSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPkSideBarRi').attr('class', 'tab-pane fade');

                $('#labPaSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPaSideBarRi').attr('class', 'tab-pane fade');

                $('#labPtSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPtSideBarRi').attr('class', 'tab-pane fade');

                $('#radiologiSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#radiologiSideBarRi').attr('class', 'tab-pane fade');

                $('#resepSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#resepSideBarRi').attr('class', 'tab-pane fade');

                $('#eresepSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#eresepSideBarRi').attr('class', 'tab-pane fade');

                $('#cpptSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#cpptSideBarRi').attr('class', 'tab-pane fade');

                $('#kardekSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#kardekSideBarRi').attr('class', 'tab-pane fade');

                $('#konsultasiSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#konsultasiSideBarRi').attr('class', 'tab-pane fade');

                $('#menu-history-pengkajian-side-ri').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#history-pengkajian-side-ri').attr('class', 'tab-pane fade');

                $('#formulariumSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#formulariumSideBarRi').attr('class', 'tab-pane fade');
            } else if (jenis == 8) {
                $('#konsultasiSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link active');
                $('#konsultasiSideBarRi').attr('class', 'tab-pane fade show active');

                $('#labPkSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPkSideBarRi').attr('class', 'tab-pane fade');

                $('#labPaSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPaSideBarRi').attr('class', 'tab-pane fade');

                $('#labPtSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPtSideBarRi').attr('class', 'tab-pane fade');

                $('#radiologiSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#radiologiSideBarRi').attr('class', 'tab-pane fade');

                $('#resepSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#resepSideBarRi').attr('class', 'tab-pane fade');

                $('#eresepSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#eresepSideBarRi').attr('class', 'tab-pane fade');

                $('#cpptSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#cpptSideBarRi').attr('class', 'tab-pane fade');

                $('#kardekSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#kardekSideBarRi').attr('class', 'tab-pane fade');

                $('#cairanSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#cairanSideBarRi').attr('class', 'tab-pane fade');

                $('#menu-history-pengkajian-side-ri').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#history-pengkajian-side-ri').attr('class', 'tab-pane fade');

                $('#formulariumSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#formulariumSideBarRi').attr('class', 'tab-pane fade');
            } else if (jenis == 9) {
                $('#labPtSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link active');
                $('#labPtSideBarRi').attr('class', 'tab-pane fade show active');
                // START LOAD JENIS 1
                $('#labPtSideBarRi').load('<?= base_url() ?>rekam_medis/Medis/labPtSideBarRi/<?= $getNomr['NOKUN'] ?? null ?>');
                // END LOAD JENIS 1

                $('#labPkSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPkSideBarRi').attr('class', 'tab-pane fade');

                $('#labPaSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPaSideBarRi').attr('class', 'tab-pane fade');

                $('#radiologiSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#radiologiSideBarRi').attr('class', 'tab-pane fade');

                $('#resepSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#resepSideBarRi').attr('class', 'tab-pane fade');

                $('#eresepSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#eresepSideBarRi').attr('class', 'tab-pane fade');

                $('#cpptSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#cpptSideBarRi').attr('class', 'tab-pane fade');

                $('#kardekSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#kardekSideBarRi').attr('class', 'tab-pane fade');

                $('#cairanSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#cairanSideBarRi').attr('class', 'tab-pane fade');

                $('#konsultasiSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#konsultasiSideBarRi').attr('class', 'tab-pane fade');

                $('#menu-history-pengkajian-side-ri').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#history-pengkajian-side-ri').attr('class', 'tab-pane fade');

                $('#formulariumSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#formulariumSideBarRi').attr('class', 'tab-pane fade');
            } else if (jenis == 10) {
                $('#menu-history-pengkajian-side-ri').attr('class', 'tombolHasilPenunjangSideRi nav-link active');
                $('#history-pengkajian-side-ri').attr('class', 'tab-pane fade show active').load('<?= base_url() ?>PengkajianAwal/historySidebar/<?= $pasien['NORM'] ?? null ?>');

                $('#labPkSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPkSideBarRi').attr('class', 'tab-pane fade');

                $('#labPaSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPaSideBarRi').attr('class', 'tab-pane fade');

                $('#labPtSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPtSideBarRi').attr('class', 'tab-pane fade');

                $('#radiologiSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#radiologiSideBarRi').attr('class', 'tab-pane fade');

                $('#resepSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#resepSideBarRi').attr('class', 'tab-pane fade');

                $('#eresepSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#eresepSideBarRi').attr('class', 'tab-pane fade');

                $('#cpptSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#cpptSideBarRi').attr('class', 'tab-pane fade');

                $('#kardekSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#kardekSideBarRi').attr('class', 'tab-pane fade');

                $('#cairanSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#cairanSideBarRi').attr('class', 'tab-pane fade');

                $('#konsultasiSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#konsultasiSideBarRi').attr('class', 'tab-pane fade');

                $('#formulariumSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#formulariumSideBarRi').attr('class', 'tab-pane fade');
            } else if (jenis == 11) {
                $('#eresepSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link active');
                $('#eresepSideBarRi').attr('class', 'tab-pane fade show active');

                $('#resepSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#resepSideBarRi').attr('class', 'tab-pane fade');

                $('#labPkSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPkSideBarRi').attr('class', 'tab-pane fade');

                $('#labPaSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPaSideBarRi').attr('class', 'tab-pane fade');

                $('#labPtSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPtSideBarRi').attr('class', 'tab-pane fade');

                $('#radiologiSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#radiologiSideBarRi').attr('class', 'tab-pane fade');

                $('#cpptSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#cpptSideBarRi').attr('class', 'tab-pane fade');

                $('#kardekSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#kardekSideBarRi').attr('class', 'tab-pane fade');

                $('#cairanSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#cairanSideBarRi').attr('class', 'tab-pane fade');

                $('#konsultasiSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#konsultasiSideBarRi').attr('class', 'tab-pane fade');

                $('#menu-history-pengkajian-side-ri').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#history-pengkajian-side-ri').attr('class', 'tab-pane fade');

                $('#formulariumSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#formulariumSideBarRi').attr('class', 'tab-pane fade');
            } else if (jenis == 12) {
                $('#eresepSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#eresepSideBarRi').attr('class', 'tab-pane fade');

                $('#resepSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#resepSideBarRi').attr('class', 'tab-pane fade');

                $('#labPkSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPkSideBarRi').attr('class', 'tab-pane fade');

                $('#labPaSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPaSideBarRi').attr('class', 'tab-pane fade');

                $('#labPtSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#labPtSideBarRi').attr('class', 'tab-pane fade');

                $('#radiologiSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#radiologiSideBarRi').attr('class', 'tab-pane fade');

                $('#cpptSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#cpptSideBarRi').attr('class', 'tab-pane fade');

                $('#kardekSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#kardekSideBarRi').attr('class', 'tab-pane fade');

                $('#cairanSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#cairanSideBarRi').attr('class', 'tab-pane fade');

                $('#konsultasiSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#konsultasiSideBarRi').attr('class', 'tab-pane fade');

                $('#menu-history-pengkajian-side-ri').attr('class', 'tombolHasilPenunjangSideRi nav-link');
                $('#history-pengkajian-side-ri').attr('class', 'tab-pane fade');

                $('#formulariumSideBarRi-tab').attr('class', 'tombolHasilPenunjangSideRi nav-link active');
                $('#formulariumSideBarRi').attr('class', 'tab-pane fade  show active');
            }
        });

        <?php if ($pasien != null) { ?>
            $('#jumlah-etimja-info').html(getJSON("<?= base_url('Etimja/jumlah') ?>", {
                nomr: "<?= $getNomr['NORM'] ?>"
            }));
            $('#count_konseling').html(getJSON('<?= base_url() ?>rekam_medis/rawat_inap/farmasi/Konseling/action/count', {
                nomr: "<?= $getNomr['NORM'] ?>"
            }));
            $('#count_rekon_obat').html(getJSON('<?= base_url() ?>igd/FormulirRekonsiliasiObat/count', {
                nomr: "<?= $getNomr['NORM'] ?>"
            }));

            // Mulai profil mini
            let profilSticky = $('#profil-mini-rawat-inap').offset().top;
            $(window).scroll(function () {
                let windowTop = $(window).scrollTop();
                if (windowTop > 350) {
                    $('#profil-mini-rawat-inap').fadeIn(300, function () {
                        $('#profil-mini-rawat-inap').css('position', 'fixed');
                        $('#profil-mini-rawat-inap').css('top', '120px').removeClass('d-none').fadeIn(300);
                    });
                } else {
                    $('#profil-mini-rawat-inap').fadeOut(300, function () {
                        $('#profil-mini-rawat-inap').css('position', 'relative');
                        $('#profil-mini-rawat-inap').css('top', '').addClass('d-none').fadeOut(300);
                    });
                }
            });
            // Akhir profil mini
        <?php } ?>

        // Mulai pencarian sticky radiologi dan lab PK
        let jumlahSegment = "<?= $this->uri->segment(2) ?>";
        if (jumlahSegment != '') {
            let stickyTop = $('#cari-radiologi, #form-cari-pk').offset().top;

            $(window).scroll(function () {
                $('.cariRadiologiRi').click(function () {
                    $('.cariRadiologiRi').animate({
                        width: '400px'
                    });
                });
                let windowTop = $(window).scrollTop();
                if (windowTop > 600) {
                    $('#cari-radiologi, #form-cari-pk').css('position', 'fixed');
                    $('#cari-radiologi, #form-cari-pk').css('top', '120px').css('right', '3.3em');
                } else {
                    $('#cari-radiologi, #form-cari-pk').css('position', 'relative');
                    $('#cari-radiologi, #form-cari-pk').css('top', '').css('right', '0em');
                }
                // $('.cariInputanRadRi').focus();
            });
        }
        // Akhir pencarian sticky radiologi dan lab PK

        // $('.pilihTandaVitalMon').val(null).trigger('change');
        $('.tombolTandaVitalMon').click(function () {
            // $('#viewTandaVitalMon').modal('show');
            var nomr = $(this).data('nomr');
            var nokun = $(this).data('nokun');
            $.ajax({
                type: 'POST',
                url: '<?= base_url() ?>PengkajianAwal/viewTandaVital',
                data: {
                    nomr: nomr,
                    nokun: nokun
                },
                success: function (data) {
                    // $('#showChartTandaVitalMon').html(data);
                    $('#hasilShowTandaVital').html(data);
                }
            });
        });

        $('.openbtnGeser').click(function () {
            var id = $(this).attr('status');
            if (id == 'aktif') {
                $('#mySidebarGeser').css('width', '350px');
                $('#mainGeser').css('marginLeft', '350px');
                // alert('buka');
                $('.openbtnGeser').attr('status', 'nonaktif');
                $('.closebtnGeser').attr('status', 'aktif');
            } else if (id == 'nonaktif') {
                $('#mySidebarGeser').css('width', '0');
                $('#mainGeser').css('marginLeft', '0');
                // alert('tutup');
                $('.openbtnGeser').attr('status', 'aktif');
                $('.closebtnGeser').attr('status', 'nonaktif');
            }
        });

        $('.closebtnGeser').click(function () {
            var id = $(this).attr('status');
            if (id == 'aktif') {
                $('#mySidebarGeser').css('width', '0');
                $('#mainGeser').css('marginLeft', '0');
                // alert('tutup');
                $('.closebtnGeser').attr('status', 'nonaktif');
                $('.openbtnGeser').attr('status', 'aktif');
            } else if (id == 'nonaktif') {
                $('#mySidebarGeser').css('width', '380px');
                $('#mainGeser').css('marginLeft', '350px');
                // alert('buka');
                $('.closebtnGeser').attr('status', 'aktif');
                $('.openbtnGeser').attr('status', 'nonaktif');
            }
        });

        // resep hasil penunjang
        $('#resepSideBarRi-tab').click(function () {
            $('#show_resepSideBarRi').load('<?= base_url() ?>PengkajianAwal/hasilPenunjangResep/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
        });

        $('#eresepSideBarRi-tab').click(function () {
            $('#view_eresep').html(' ');
            $('#show_eresepSideBarRi').load('<?= base_url() ?>eresep/<?= $this->uri->segment(2) ?>');
        });

        $('#cpptSideBarRi-tab').click(function () {
            $('#show_cpptSideBarRi').load('<?= base_url() ?>cpptView/<?= $pasien['NORM'] ?>');
        });
        $(document).on('change', '.history_cppt_side', function () {
            var id = $(this).val();
            $('#show_cpptSideBarRi').load('<?= base_url() ?>cpptView/<?= $pasien['NORM'] ?>/' + id);
        });

        $('#kardekSideBarRi-tab').click(function () {
            $('#show_kardekSideBarRi').load('<?= base_url() ?>rekam_medis/rawat_inap/catatanTerintegrasi/KardekRi/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
        });

        $('#cairanSideBarRi-tab').click(function () {
            $('#show_cairanSideBarRi').load('<?= base_url() ?>rekam_medis/rawat_inap/keperawatan/OTKeperawatan/sidebar/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
        });

        $('#konsultasiSideBarRi-tab').click(function () {
            // $('#show_konsultasiSideBarRi').load("<!?= base_url('konsultasi/Konsultasi/sidePane/' . $getNomr['NORM'] . '/' . $pasien['RUANGAN_TUJUAN']) ?-->");

            $.ajax({
                url: "<?= base_url('konsultasi/Konsultasi/sidePane') ?>",
                type: 'POST',
                data: {
                    nomr: '<?= $pasien['NORM'] ?>',
                    ruangTujuan: '<?= $pasien['RUANGAN_TUJUAN'] ?>'
                },
                success: function (data) {
                    $('#show_konsultasiSideBarRi').html(data);
                }
            });
        });

        $('#formulariumSideBarRi-tab').click(function () {
            $('#show_formulariumSideBarRi').load("<?= base_url('rekam_medis/eresep/daftarobat/' . $getNomr['NORM']) ?>");

        });

        // Load Ruangan
        var listRuangan = getJSON('<?= base_url() ?>rekam_medis/medis/ruangan', {});
        if (listRuangan.length !== 0) {
            $.each(listRuangan, function (index, element) {
                var html = '<option value="' + element.ID + '">' + element.DESKRIPSI + '/' + element.JUMLAH_PASIEN + '</option>';
                $('#ruangan').append(html);
            });
            if (localStorage.filterstatusselected != undefined) {
                loadRuangan($('#ruangan').val(), localStorage.filterstatusselected);
            } else {
                loadRuangan($('#ruangan').val());
            }
        } else {
            $('#ruangan').html('');
        }
        if (localStorage.ruangan != undefined) {
            if (localStorage.filterstatusselected != undefined) {
                loadRuangan(localStorage.ruangan, localStorage.filterstatusselected);
            } else {
                loadRuangan(localStorage.ruangan);
            }
            $('#ruangan').val(localStorage.ruangan).trigger('change');
            $('.header-title').html($('#ruangan option:selected').text());
        }
        if (localStorage.activeTab == undefined) {
            $('.dashboard').trigger('click');
        }
        $(function () {
            // Pengkajian
            // Pengkajian RI Dewasa
            $('.pengkajianRiDewasa').click(function () {
                $('#view_pengkajianRiDewasa').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRI/Dewasa/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/00000');
            });
            if ($('.pengkajianRiDewasa').hasClass('active')) {
                $('#view_pengkajianRiDewasa').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRI/Dewasa/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/00000');
            }

            // Pengkajian RI Pasien Kritis
            $('.pengkajianRiKritis').click(function () {
                $('#view_pengkajianRiKritis').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/PasienKritis/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/00000');
            });
            $(document).on('click', '.pengkajianRiKritisReset', function () {
                $('#view_pengkajianRiKritis').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/PasienKritis/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/000000');
            });
            if ($('.pengkajianRiKritis').hasClass('active')) {
                $('#view_pengkajianRiKritis').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/PasienKritis/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/00000');
            }
            $(document).on('click', '.verif-perawat', function () {
                var id = $(this).data('id');
                $('#modal').modal('hide');
                $('.pengkajianRiDewasa').trigger('click');
                $('#view_pengkajianRiDewasa').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRI/Dewasa/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/' + id + '/' + '00000');
            });

            // Pengkajian RI Pasien Kritis
            $('.pengkajianRiMedisKritis').click(function () {
                $('#view_pengkajianRiMedisKritis').load('<?= base_url() ?>pengkajianRiMedisKritis/<?= $pasien['NOKUN'] ?>');
            });
            $(document).on('click', '.pengkajianRiMedisKritisReset', function () {
                $('#view_pengkajianRiMedisKritis').load('<?= base_url() ?>pengkajianRiMedisKritis/<?= $pasien['NOKUN'] ?>/1');
            });
            if ($('.pengkajianRiMedisKritis').hasClass('active')) {
                $('#view_pengkajianRiMedisKritis').load('<?= base_url() ?>pengkajianRiMedisKritis/<?= $pasien['NOKUN'] ?>');
            }
            $(document).on('click', '.editPengkajianRIMedisKritis', function () {
                var id = $(this).data('id');
                var idemr = $(this).data('idemr');

                var status = $(this).data('status');
                if (status == 1) {
                    $('#modal').modal('hide');
                    $('.pengkajianRiMedisKritis').trigger('click');
                    $('#view_pengkajianRiMedisKritis').load('<?= base_url() ?>pengkajianRiMedisKritis/' + id + '/' + idemr);
                } else {
                    $('#view_pengkajianRiDewasa').load('<?= base_url() ?>pengkajianRiMedisKritis/' + id);
                    $('#modal').modal('hide');
                }
            });

            $(document).on('click', '.verif-perawat-pasien-kritis', function () {
                var id = $(this).data('id');
                $('#modal').modal('hide');
                $('.pengkajianRiKritis').trigger('click');
                $('#view_pengkajianRiKritis').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/PasienKritis/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/' + id);
            });

            // pengkajian Terapi Sistemik Kanker RJ
            $('.pengkajianTerapiSistemikRJ').click(function () {
                $('#view_pengkajianTerapiSistemikRJ').load('<?= base_url() ?>pengkajianTerapiSistemikRJ/<?= $pasien['NOKUN'] ?>');
            });
            if ($('.pengkajianTerapiSistemikRJ').hasClass('active')) {
                // $('.pengkajianTerapiSistemikRJ').trigger('click');
                $('#view_pengkajianTerapiSistemikRJ').load('<?= base_url() ?>pengkajianTerapiSistemikRJ/<?= $pasien['NOKUN'] ?>');
            }

            $('.pengkajianTerapiSistemikRJAnyelir1').click(function () {
                $('#view_pengkajianTerapiSistemikRJAnyelir1').load('<?= base_url() ?>pengkajianTerapiSistemikRJ/<?= $pasien['NOKUN'] ?>');
            });
            if ($('.pengkajianTerapiSistemikRJAnyelir1').hasClass('active')) {
                // $('.pengkajianTerapiSistemikRJ').trigger('click');
                $('#view_pengkajianTerapiSistemikRJAnyelir1').load('<?= base_url() ?>pengkajianTerapiSistemikRJ/<?= $pasien['NOKUN'] ?>');
            }

            $('.pengkajianTerapiSistemikRJAnyelir2').click(function () {
                $('#view_pengkajianTerapiSistemikRJAnyelir2').load('<?= base_url() ?>pengkajianTerapiSistemikRJ/<?= $pasien['NOKUN'] ?>');
            });
            if ($('.pengkajianTerapiSistemikRJAnyelir2').hasClass('active')) {
                // $('.pengkajianTerapiSistemikRJ').trigger('click');
                $('#view_pengkajianTerapiSistemikRJAnyelir2').load('<?= base_url() ?>pengkajianTerapiSistemikRJ/<?= $pasien['NOKUN'] ?>');
            }

            $('.pengkajianTerapiSistemikRJKemoterapi').click(function () {
                $('#view_pengkajianTerapiSistemikRJKemoterapi').load('<?= base_url() ?>pengkajianTerapiSistemikRJ/<?= $pasien['NOKUN'] ?>');
            });
            if ($('.pengkajianTerapiSistemikRJKemoterapi').hasClass('active')) {
                // $('.pengkajianTerapiSistemikRJ').trigger('click');
                $('#view_pengkajianTerapiSistemikRJKemoterapi').load('<?= base_url() ?>pengkajianTerapiSistemikRJ/<?= $pasien['NOKUN'] ?>');
            }

            $(document).on('click', '.editPengkajianTerapiSistemikRJ', function () {
                var id = $(this).data('id');
                $('#modal').modal('hide');
                $('.pengkajianTerapiSistemikRJ').trigger('click');
                $('#view_pengkajianTerapiSistemikRJ').load('<?= base_url() ?>pengkajianTerapiSistemikRJ/<?= $pasien['NOKUN'] ?>/' + id);
            });

            // Pengkajian RI PAK Perawat
            $('.pengkajianRiPAKPerawat').click(function () {
                $('#view_pengkajianRiPAKPerawat').load('<?= base_url() ?>PAKPerawat/<?= $pasien['NOPEN'] ?>');
            });
            if ($('.pengkajianRiPAKPerawat').hasClass('active')) {
                $('#view_pengkajianRiPAKPerawat').load('<?= base_url() ?>PAKPerawat/<?= $pasien['NOPEN'] ?>');
            }
            $(document).on('click', '.editPAKPerawat', function () {
                $('#modal').modal('hide');
                var id = $(this).data('id');
                $('.pengkajianRiPAKPerawat').trigger('click');
                $('#view_pengkajianRiPAKPerawat').load('<?= base_url() ?>PAKPerawat/<?= $pasien['NOPEN'] ?>/' + id);
            });

            // Pengkajian RI PAK Medis
            $('.pengkajianRiPAKMedis').click(function () {
                $('#view_pengkajianRiPAKMedis').load('<?= base_url() ?>PAKMedis/<?= $pasien['NOPEN'] ?>');
            });
            if ($('.pengkajianRiPAKMedis').hasClass('active')) {
                $('#view_pengkajianRiPAKMedis').load('<?= base_url() ?>PAKMedis/<?= $pasien['NOPEN'] ?>');
            }
            $(document).on('click', '.editPAKMedis', function () {
                $('#modal').modal('hide');
                var id = $(this).data('id');
                $('.pengkajianRiPAKMedis').trigger('click');
                $('#view_pengkajianRiPAKMedis').load('<?= base_url() ?>PAKMedis/<?= $pasien['NOPEN'] ?>/' + id);
            });

            // Pengkajian RI Anak
            $('.pengkajianRiAnak').click(function () {
                $('#view_pengkajianRiAnak').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRI/Anak/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/00000');
            });
            if ($('.pengkajianRiAnak').hasClass('active')) {
                $('#view_pengkajianRiAnak').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRI/Anak/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/00000');
            }

            // Pengkajian RI Remaja
            $('.pengkajianRiRemaja').click(function () {
                $('#view_pengkajianRiRemaja').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRI/Remaja/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.pengkajianRiRemaja').hasClass('active')) {
                $('#view_pengkajianRiRemaja').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRI/Remaja/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            // Asesemen Restrain
            $('.asesmenRestrain').click(function () {
                $('#view_asesmenRestrain').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRI/AsesmenRestrain/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.asesmenRestrain').hasClass('active')) {
                $('#view_asesmenRestrain').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRI/AsesmenRestrain/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }
            $(document).on('click', '.editAsesmenRestrain', function () {
                $('#modal').modal('hide');
                var id = $(this).data('id');
                $('.asesmenRestrain').trigger('click');
                $('#view_asesmenRestrain').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRI/AsesmenRestrain/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] ?>/' + id);
            });

            // Formulir PMFAK
            $('.formulirPMFAK').click(function () {
                $('#view_formulirPMFAK').load('<?= base_url() ?>formulirPMFAK/FormulirPMFAK/index/<?= $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) ?>');
            });
            if ($('.formulirPMFAK').hasClass('active')) {
                $('#view_formulirPMFAK').load('<?= base_url() ?>formulirPMFAK/FormulirPMFAK/index/<?= $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) ?>');
            }

            // Pengkajian RI Dewasa Medis
            $('.pengkajianRiDewasaMedis').click(function () {
                $('#view_pengkajianRiDewasaMedis').load('<?= base_url() ?>pengkajianRiDewasaMedis/<?= $pasien['NOKUN'] ?>');
            });
            if ($('.pengkajianRiDewasaMedis').hasClass('active')) {
                $('#view_pengkajianRiDewasaMedis').load('<?= base_url() ?>pengkajianRiDewasaMedis/<?= $pasien['NOKUN'] ?>');
            }
            $(document).on('click', '.editPengkajianRIMedisDewasa', function () {
                var id = $(this).data('id');
                var status = $(this).data('status');
                if (status == 1) {
                    $('#modal').modal('hide');
                    $('.pengkajianRiDewasaMedis').trigger('click');
                    $('#view_pengkajianRiDewasaMedis').load('<?= base_url() ?>pengkajianRiDewasaMedis/' + id);
                } else {
                    $('#view_pengkajianRiDewasa').load('<?= base_url() ?>pengkajianRiDewasaMedis/' + id);
                    $('#modal').modal('hide');
                }
            });
            $(document).on('click', '.editPengkajianPerawat', function () {
                var id = $(this).data('id');
                // $("#formPengkajianRiDewasa :input").removeAttr("disabled", "");
                $('#view_pengkajianRiDewasa').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRI/Dewasa/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/' + id);
                $('#modal').modal('hide');
            });
            $(document).on('click', '.editPengkajianPerawatAnak', function () {
                var id = $(this).data('id');
                $('.pengkajianRiAnak').trigger('click');
                $('#view_pengkajianRiAnak').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRI/Anak/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/' + id);
                $('#modal').modal('hide');
            });
            $(document).on('click', '.editPengkajianPerawatIGD', function () {
                var id = $(this).data('id');
                $('.pengkajianIgdRi').trigger('click');
                $('#view_pengkajianIgdRi').load('<?= base_url() ?>rekam_medis/rawat_inap/igd/PengkajianIgdRi/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/' + id);
                $('#modal').modal('hide');
            });
            $(document).on('click', '.editPengkajianRadioterapiRi', function () {
                var id = $(this).data('id');
                $('#view_pengkajianRadioterapiRi').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRI/RadioterapiRi/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/' + id);
                $('#modal').modal('hide');
            });
            $(document).on('click', '.editPengkajianPerawatRiKritis', function () {
                var id = $(this).data('id');
                $('.pengkajianRiKritis').trigger('click');
                $('#view_pengkajianRiKritis').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/PasienKritis/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/' + id);
                $('#modal').modal('hide');
            });
            $(document).on('click', '.editPengkajianRiRemaja', function () {
                var id = $(this).data('id');
                $('#modal').modal('hide');
                $('.pengkajianRiRemaja').trigger('click');
                $('#view_pengkajianRiRemaja').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRI/Remaja/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] ?>/' + id);
            });
            $(document).on('click', '.editPengkajianTerapiSistemik', function () {
                var id = $(this).data('id');
                $('.pengkajianTerapiSistemikRi').trigger('click');

                $('#view_pengkajianTerapiSistemikRi').load('<?= base_url() ?>rekam_medis/rawat_inap/kemoterapi/PengkajianTerapiSistemik/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/' + id);
                $('#modal').modal('hide');

                // $('#view_pengkajianTerapiSistemikRi').load('<?= base_url() ?>rekam_medis/rawat_inap/kemoterapi/PengkajianTerapiSistemik/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/' + id);
                // $('#modal').modal('hide');
            });
            $(document).on('click', '.editPengkajianPerawatRiim', function () {
                var id = $(this).data('id');
                $('.pengkajianRiim').trigger('click');
                $('#view_pengkajianRiim').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/PengkajianRiim/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/' + id);
                $('#modal').modal('hide');
            });
            $(document).on('click', '.editPengkajianPerawatRira', function () {
                var id = $(this).data('id');
                $('.pengkajianRira').trigger('click');
                $('#view_pengkajianRira').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/PengkajianRira/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/' + id);
                $('#modal').modal('hide');
            });
            $(document).on('click', '.editPengkajianPerawatRiBrak', function () {
                var id = $(this).data('id');
                $('#view_pengkajianRiBrak').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/PengBrak/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/' + id);
                $('#modal').modal('hide');
            });
            // Formulir Kriteria Pasien Masuk Rawat Intensif
            $(document).on('click', '.editFormulirKriteriaRawatIntensif', function () {
                var id = $(this).data('id');
                $('#view_formulirKriteriaRawatIntensif').load('<?= base_url() ?>formulirKriteriaRawatIntensif/<?= $this->uri->segment(2) ?>/' + id);
            });

            // Formulir Kriteria Pasien Masuk Rawat PICU
            $(document).on('click', '.editFormulirKriteriaRawatPicu', function () {
                var id = $(this).data('id');
                $('#view_formulirKriteriaRawatPicu').load('<?= base_url() ?>igd/FormulirKriteriaRawatPicu/indexRawatInap/<?= $this->uri->segment(2) ?>/' + id);
            });

            // Dashboard
            $('.dashboard').click(function () {
                $('#view_dashboard').load('<?= base_url() ?>dashboard/<?= $this->uri->segment(2) ?>');
            });
            if ($('.dashboard').hasClass('active')) {
                $('#view_dashboard').load('<?= base_url() ?>dashboard/<?= $this->uri->segment(2) ?>');
            }

            // CPPT LIST
            $(document).on('click', '.cpptList', function () {
                $('#view_cpptList').load("<?= base_url('cpptList/' . $pasien['NORM'] . '/bagian/0') ?>");
            });
            if ($('.cpptList').hasClass('active')) {
                $('#view_cpptList').load("<?= base_url('cpptList/' . $pasien['NORM'] . '/bagian/0') ?>");
            }

            // Pengkajian list
            let listPengkajian = $('#pengkajian-list');
            listPengkajian.click(function () {
                $('#view-pengkajian-list').load('<?= base_url('PengkajianAwal/historySidebar/' . $pasien['NORM']) ?>');
            });
            if (listPengkajian.hasClass('active')) {
                $('#view-pengkajian-list').load('<?= base_url('PengkajianAwal/historySidebar/' . $pasien['NORM']) ?>');
            }

            // berkas list
            let listBerkas = $('#berkas-list');
            listBerkas.click(function () {
                $('#view-berkas-list').load('<?= base_url('rekam_medis/UploadRM/historyRM/' . $pasien['NORM']) ?>');
            });
            if (listBerkas.hasClass('active')) {
                $('#view-berkas-list').load('<?= base_url('rekam_medis/UploadRM/historyRM/' . $pasien['NORM']) ?>');
            }

            // faktor resiko
            let faktorresiko = $('#faktor-resiko');
            faktorresiko.click(function () {
                $('#view-faktor-resiko').load('<?= base_url('rekam_medis/faktorresiko/index/' . $pasien['NORM']) ?>');
            });
            if (faktorresiko.hasClass('active')) {
                $('#view-faktor-resiko').load('<?= base_url('rekam_medis/faktorresiko/index/' . $pasien['NORM']) ?>');
            }

            // TBAK
            let tbak = $('#tbak-menu');
            tbak.click(function () {
                $('#view-tbak').load("<?= base_url('TBAK/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN']) ?>");
            });
            if (tbak.hasClass('active')) {
                $('#view-tbak').load("<?= base_url('TBAK/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN']) ?>");
            };

            // Keperawatan
            // Perencanaan Asuhan Keperawatan
            $('.perencanaan-ak').click(function () {
                $('#view-perencanaan-ak').load('<?= base_url() . 'PerencanaanAK/' . $this->uri->segment(2) ?>');
            });
            if ($('.perencanaan-ak').hasClass('active')) {
                $('#view-perencanaan-ak').load('<?= base_url() . 'PerencanaanAK/' . $this->uri->segment(2) ?>');
            }

            // Observasi dan Tindakan Keperawatan
            $('.ot-keperawatan').click(function () {
                $('#view-ot-keperawatan').load('<?= base_url() . 'OTKeperawatan/' . $this->uri->segment(2) ?>');
            });
            if ($('.ot-keperawatan').hasClass('active')) {
                $('#view-ot-keperawatan').load('<?= base_url() . 'OTKeperawatan/' . $this->uri->segment(2) ?>');
            }

            // EWS
            $('.ews').click(function () {
                $('#view-ews').load('<?= base_url() . 'EWS/' . $this->uri->segment(2) ?>');
            });
            if ($('.ews').hasClass('active')) {
                $('#view-ews').load('<?= base_url() . 'EWS/' . $this->uri->segment(2) ?>');
            }
            $('#buka-pemantauan-EWS').click(function () {
                $('.ews').tab('show');
                $('#view-ews').load('<?= base_url() . 'EWS/' . $this->uri->segment(2) ?>');
            });

            // PEWS
            $('.pews').click(function () {
                $('#view-pews').load('<?= base_url() . 'PEWS/' . $this->uri->segment(2) ?>');
            });
            if ($('.pews').hasClass('active')) {
                $('#view-pews').load('<?= base_url() . 'PEWS/' . $this->uri->segment(2) ?>');
            }

            // Pengkajian Risiko Jatuh Pasien Dewasa
            $('.pengkajianRisikoJatuhPasienDewasa').click(function () {
                $('#view_pengkajianRisikoJatuhPasienDewasa').load('<?= base_url() ?>igd/PengkajianRisikoJatuhPasienDewasa/index/<?= $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) ?>');
            });
            if ($('.pengkajianRisikoJatuhPasienDewasa').hasClass('active')) {
                $('#view_pengkajianRisikoJatuhPasienDewasa').load('<?= base_url() ?>igd/PengkajianRisikoJatuhPasienDewasa/index/<?= $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) ?>');
            }

            // Skala Ontario
            $('.skalaOntario').click(function () {
                $('#view_skalaOntario').load('<?= base_url() ?>igd/SkalaMorse/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.skalaOntario').hasClass('active')) {
                $('#view_skalaOntario').load('<?= base_url() ?>igd/SkalaMorse/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            // Edit Skala Ontario
            $(document).on('click', '.editSkalaOntario', function () {
                var id = $(this).data('id');
                $('#view_skalaOntario').load('<?= base_url() ?>igd/SkalaMorse/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/' + id + '/' + id);
            });

            // Skala Ontario Modified Atratify Sidney Scorsing
            $('.skalaOntarioMASS').click(function () {
                $('#view_skalaOntarioMASS').load('<?= base_url() ?>SkalaOntarioMASS/<?= $this->uri->segment(2) ?>');
            });
            if ($('.skalaOntarioMASS').hasClass('active')) {
                $('#view_skalaOntarioMASS').load('<?= base_url() ?>SkalaOntarioMASS/<?= $this->uri->segment(2) ?>');
            }

            // Edit Skala Ontario Modified Atratify Sidney Scorsing
            $(document).on('click', '.editSkalaOntarioMASS', function () {
                $('#modal').modal('hide');
                var id = $(this).data('id');
                $('#view_skalaOntarioMASS').load('<?= base_url() ?>SkalaOntarioMASS/<?= $this->uri->segment(2) ?>/' + id);
            });

            // Humpty Dumpty
            $('.humptyDumptyRi').click(function () {
                $('#view_humptyDumptyRi').load('<?= base_url() ?>igd/HumptyDumptyScale/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.humptyDumptyRi').hasClass('active')) {
                $('#view_humptyDumptyRi').load('<?= base_url() ?>igd/HumptyDumptyScale/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            // Pemantauan Nyeri
            $(document).on('click', '.pemantauanNyeri', function () {
                $('#view_pemantauanNyeri').load('<?= base_url() ?>keperawatan/pemantauanNyeri/index/<?= $this->uri->segment(2) ?>');
            });
            if ($('.pemantauanNyeri').hasClass('active')) {
                $('#view_pemantauanNyeri').load('<?= base_url() ?>keperawatan/pemantauanNyeri/index/<?= $this->uri->segment(2) ?>');
            }
            $('#buka-pemantauan-nyeri').click(function () {
                $('.pemantauanNyeri').tab('show');
                $('#view_pemantauanNyeri').load('<?= base_url() ?>keperawatan/pemantauanNyeri/index/<?= $this->uri->segment(2) ?>');
            });

            // Skala Braden
            $('#skala-braden').click(function () {
                $('#view-skala-braden').load('<?= base_url() . 'skalaBraden/' . $this->uri->segment(2) ?>');
            });
            if ($('#skala-braden').hasClass('active')) {
                $('#view-skala-braden').load('<?= base_url() . 'skalaBraden/' . $this->uri->segment(2) ?>');
            }

            // Serah Terima
            $(document).on('click', '.serahTerima', function () {
                $('#view_serahTerima').load('<?= base_url() ?>keperawatan/serahTerima/index/<?= $this->uri->segment(2) ?>');
            });
            if ($('.serahTerima').hasClass('active')) {
                $('#view_serahTerima').load('<?= base_url() ?>keperawatan/serahTerima/index/<?= $this->uri->segment(2) ?>');
            }

            // Observasi dan Tindakan Keperawatan Radiofarmaka
            let otki = $('#otki');
            otki.click(function () {
                $('#view-otki').load('<?= base_url() . 'OTKI/' . $this->uri->segment(2) ?>');
            });
            if (otki.hasClass('active')) {
                $('#view-otki').load('<?= base_url() . 'OTKI/' . $this->uri->segment(2) ?>');
            }

            // Pre IDO
            let preido = $('#preido');
            preido.click(function () {
                $('#view-preido').load('<?= base_url() . 'ido/pre/' . $this->uri->segment(2) ?>');
            });
            if (preido.hasClass('active')) {
                $('#preido-preido').load('<?= base_url() . 'ido/pre/' . $this->uri->segment(2) ?>');
            }

            // Post IDO
            let postido = $('#postido');
            postido.click(function () {
                $('#view-postido').load('<?= base_url() . 'ido/post/' . $this->uri->segment(2) ?>');
            });
            if (postido.hasClass('active')) {
                $('#view-postido').load('<?= base_url() . 'ido/post/' . $this->uri->segment(2) ?>');
            }

            //skrining perasaan tertekan
            $('.skriningPerasaanTertekan').click(function () {
                $('#view_skriningPerasaanTertekan').load('<?= base_url() ?>SkriningPerasaanTertekan/<?= $this->uri->segment(2) ?>');
            });
            if ($('.skriningPerasaanTertekan').hasClass('active')) {
                $('#view_skriningPerasaanTertekan').load('<?= base_url() ?>SkriningPerasaanTertekan/<?= $this->uri->segment(2) ?>');
            }

            // pengkajian Pra Sedasi
            $('.pengkajianPraSedasi').click(function () {
                $('#view_pengkajianPraSedasi').load('<?= base_url() ?>sedasi/PengkajianPraSedasi/index/<?= $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) ?>');
            });
            if ($('.pengkajianPraSedasi').hasClass('active')) {
                $('#view_pengkajianPraSedasi').load('<?= base_url() ?>sedasi/PengkajianPraSedasi/index/<?= $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) ?>');
            }

            // pemantauan tindakan anestesi lokal
            $('.pemTinAnesLokRjRi').click(function () {
                $('#view_pemTinAnesLokRjRi').load('<?= base_url() ?>pengkajianprorad/FormPemTinAnesLok/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.pemTinAnesLokRjRi').hasClass('active')) {
                $('#view_pemTinAnesLokRjRi').load('<?= base_url() ?>pengkajianprorad/FormPemTinAnesLok/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            // Barthel Indek
            $('.barthelIndek').click(function () {
                $('#view_barthelIndek').load('<?= base_url() ?>emr/BarthelIndek/index/<?= $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) ?>');
            });
            if ($('.barthelIndek').hasClass('active')) {
                $('#view_barthelIndek').load('<?= base_url() ?>emr/BarthelIndek/index/<?= $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) ?>');
            }

            // Gizi
            // Pengkajian Validasi Malnutrisi Dewasa
            $('.validasiMalnutrisiDewasa').click(function () {
                $('#view_validasiMalnutrisiDewasa').load('<?= base_url() ?>validasiMalnutrisiDewasa/<?= $this->uri->segment(2) ?>');
            });
            if ($('.validasiMalnutrisiDewasa').hasClass('active')) {
                $('#view_validasiMalnutrisiDewasa').load('<?= base_url() ?>validasiMalnutrisiDewasa/<?= $this->uri->segment(2) ?>');
            }
            // Pengkajian Gizi Lanjutan
            $(document).on('click', '.pengkajianGiziLanjutan', function () {
                $('#view_pengkajianGiziLanjutan').load('<?= base_url() ?>PengkajianGiziLanjutan/<?= $this->uri->segment(2) ?>');
            });

            if ($('.pengkajianGiziLanjutan').hasClass('active')) {
                $('#view_pengkajianGiziLanjutan').load('<?= base_url() ?>PengkajianGiziLanjutan/<?= $this->uri->segment(2) ?>');
            }

            // Observasi Intake Oral Pasien
            $(document).on('click', '.observasiIntakeOralPasien', function () {
                $('#view_observasiIntakeOralPasien').load('<?= base_url() ?>observasiIntakeOralPasien/<?= $this->uri->segment(2) ?>');
            });

            if ($('.observasiIntakeOralPasien').hasClass('active')) {
                $('#view_observasiIntakeOralPasien').load('<?= base_url() ?>observasiIntakeOralPasien/<?= $this->uri->segment(2) ?>');
            }
            $(document).on('click', '.editObservasiIntakeOralPasien', function () {
                $('#modal').modal('hide');
                var id = $(this).data('id');
                $('#view_observasiIntakeOralPasien').load('<?= base_url() ?>observasiIntakeOralPasien/<?= $this->uri->segment(2) ?>/' + id);
            });

            $(document).on('click', '.editpengkajianGiziLanjutan', function () {
                $('#modal').modal('hide');
                var id = $(this).data('id');
                $('#view_pengkajianGiziLanjutan').load('<?= base_url() ?>PengkajianGiziLanjutan/<?= $this->uri->segment(2) ?>/' + id);
            });

            // Penilaian Status Gizi
            $('.penilaianStatusGizi').click(function () {
                $('#view_penilaianStatusGizi').load('<?= base_url() ?>penilaianStatusGizi/<?= $this->uri->segment(2) ?>');
            });
            if ($('.penilaianStatusGizi').hasClass('active')) {
                $('#view_penilaianStatusGizi').load('<?= base_url() ?>penilaianStatusGizi/<?= $this->uri->segment(2) ?>');
            }
            $(document).on('click', '.editPenilaianStatusGizi', function () {
                $('#modal').modal('hide');
                var id = $(this).data('id');
                $('#view_penilaianStatusGizi').load('<?= base_url() ?>penilaianStatusGizi/' + id);
            });

            // Ruangan Intensif
            // CPIS
            $(document).on('click', '.cpis', function () {
                $('#view_cpis').load('<?= base_url() ?>cpis/<?= $this->uri->segment(2) ?>');
            });
            if ($('.cpis').hasClass('active')) {
                $('#view_cpis').load('<?= base_url() ?>cpis/<?= $this->uri->segment(2) ?>');
            }
            $(document).on('click', '.editCPIS', function () {
                $('#lihatHistoryCPIS').modal('hide');
                var id = $(this).data('id');
                $('#view_cpis').load('<?= base_url() ?>cpis/<?= $this->uri->segment(2) ?>/' + id);
            });

            //SOFA
            $(document).on('click', '.sofa', function () {
                $('#view_sofa').load('<?= base_url() ?>sofa/<?= $this->uri->segment(2) ?>');
            });
            if ($('.sofa').hasClass('active')) {
                $('#view_sofa').load('<?= base_url() ?>sofa/<?= $this->uri->segment(2) ?>');
            }

            // Bundle VAP
            $(document).on('click', '.bundleVAP', function () {
                $('#view_bundleVAP').load('<?= base_url() ?>bundlevap/<?= $this->uri->segment(2) ?>');
            });
            if ($('.bundleVAP').hasClass('active')) {
                $('#view_bundleVAP').load('<?= base_url() ?>bundlevap/<?= $this->uri->segment(2) ?>');
            }
            $(document).on('click', '.editBundleVAP', function () {
                $('#modal').modal('hide');
                var id = $(this).data('id');
                $('#view_bundleVAP').load('<?= base_url() ?>bundlevap/<?= $this->uri->segment(2) ?>/' + id);
            });

            // Ceklis Edukasi dan Orientasi Pasien Baru Di Ruang Perawatan Intensif
            $(document).on('click', '.ceklisEdukasiRuanganIntensif', function () {
                $('#view_ceklisEdukasiRuanganIntensif').load('<?= base_url() ?>ceri/<?= $this->uri->segment(2) ?>');
            });
            if ($('.ceklisEdukasiRuanganIntensif').hasClass('active')) {
                $('#view_ceklisEdukasiRuanganIntensif').load('<?= base_url() ?>ceri/<?= $this->uri->segment(2) ?>');
            }
            $(document).on('click', '.editCeklisEdukasiRuanganIntensif', function () {
                $('#modal').modal('hide');
                var id = $(this).data('id');
                $('#view_ceklisEdukasiRuanganIntensif').load('<?= base_url() ?>ceri/<?= $this->uri->segment(2) ?>/' + id);
            });


            // Persiapan Ekstubasi
            $(document).on('click', '.persiapanEkstubasi', function () {
                $('#view_persiapanEkstubasi').load('<?= base_url() ?>persiapanEkstubasi/<?= $this->uri->segment(2) ?>');
            });
            if ($('.persiapanEkstubasi').hasClass('active')) {
                $('#view_persiapanEkstubasi').load('<?= base_url() ?>persiapanEkstubasi/<?= $this->uri->segment(2) ?>');
            }
            $(document).on('click', '.editPersiapanEkstubasi', function () {
                $('#modal').modal('hide');
                var id = $(this).data('id');
                $('#view_persiapanEkstubasi').load('<?= base_url() ?>persiapanEkstubasi/<?= $this->uri->segment(2) ?>/' + id);
            });

            // Form Fasthug
            $(document).on('click', '.fasthug', function () {
                $('#view_fasthug').load('<?= base_url() ?>fasthug/<?= $this->uri->segment(2) ?>');
            });
            if ($('.fasthug').hasClass('active')) {
                $('#view_fasthug').load('<?= base_url() ?>fasthug/<?= $this->uri->segment(2) ?>');
            }
            $(document).on('click', '.edit_Fasthug', function () {
                $('#modal').modal('hide');
                var id = $(this).data('id');
                $('#view_fasthug').load('<?= base_url() ?>fasthug/<?= $this->uri->segment(2) ?>/' + id);
            });

            // E-resep
            $('.eresep').click(function () {
                $('#show_eresepSideBarRi').html(' ')
                $('#view_eresep').load('<?= base_url() ?>eresep/<?= $this->uri->segment(2) ?>');
            });
            if ($('.eresep').hasClass('active')) {
                $('#view_eresep').load('<?= base_url() ?>eresep/<?= $this->uri->segment(2) ?>');
            }

            // Mulai konsultasi
            // Konsultasi ke Dokter Lain
            $('#konsultasi').click(function () {
                $('#view-konsultasi').load("<?= base_url('konsultasi/Konsultasi/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN']) ?>");
            });
            if ($('#konsultasi').hasClass('active')) {
                $('#view-konsultasi').load("<?= base_url('konsultasi/Konsultasi/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN']) ?>");
            }

            // PPRA
            $('.ppra').click(function () {
                $('#view_ppra').load('<?= base_url() ?>ppra/<?= $pasien['NOKUN'] ?>');
            });
            if ($('.ppra').hasClass('active')) {
                $('#view_ppra').load('<?= base_url() ?>ppra/<?= $pasien['NOKUN'] ?>');
            }

            // // Rujukan internal
            // $('#ruj-in').click(function() {
            //     $('#view-ruj-in').load("<!?= base_url('konsultasi/RujukanInternal/index/' . $pasien['NOKUN']) ?>");
            // });
            // if ($('#ruj-in').hasClass('active')) {
            //     $('#view-ruj-in').load("<!?= base_url('konsultasi/RujukanInternal/index/' . $pasien['NOKUN']) ?>");
            // }
            // Akhir konsultasi

            // Lab Patologi Klinik
            $('.patologi_klinik').click(function () {
                $('#view_patologi_klinik').load('<?= base_url() ?>patologiKlinik/<?= $this->uri->segment(2) ?>');
            });
            if ($('.patologi_klinik').hasClass('active')) {
                $('#view_patologi_klinik').load('<?= base_url() ?>patologiKlinik/<?= $this->uri->segment(2) ?>');
            }

            // Lab Patologi Anatomi
            let labPA = $('#lab-pa');
            labPA.click(function () {
                $('#view-lab-pa').load("<?= base_url('patologiAnatomi/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN']) ?>");
            });
            if (labPA.hasClass('active')) {
                $('#view-lab-pa').load("<?= base_url('patologiAnatomi/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN']) ?>");
            }

            // Radiologi
            $('.radiologi').click(function () {
                $('#view_radiologi').load('<?= base_url() ?>radiologi/<?= $this->uri->segment(2) ?>');
            });
            if ($('.radiologi').hasClass('active')) {
                $('#view_radiologi').load('<?= base_url() ?>radiologi/<?= $this->uri->segment(2) ?>');
            }

            // Prosedur Diagnostik
            $('.prosedur').click(function () {
                $('#view_prosedur').load('<?= base_url() ?>prosedurDiagnostik/<?= $this->uri->segment(2) ?>');
            });
            if ($('.prosedur').hasClass('active')) {
                $('#view_prosedur').load('<?= base_url() ?>prosedurDiagnostik/<?= $this->uri->segment(2) ?>');
            }

            // Laporan Hasil Pemeriksaan
            $('.laporanHasilPemeriksaan').click(function () {
                $('#view_laporanHasilPemeriksaan').load('<?= base_url() ?>prosedur/LaporanHasilPemeriksaan/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });

            if ($('.laporanHasilPemeriksaan').hasClass('active')) {
                $('#view_laporanHasilPemeriksaan').load('<?= base_url() ?>prosedur/LaporanHasilPemeriksaan/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            // Laboratorium Patologi Klinik (Baru)
            let lpk = $('#lpk');
            lpk.click(function () {
                $('#view-lpk').load("<?= base_url('penunjang/PatologiKlinik/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN']) ?>");
            });
            if (lpk.hasClass('active')) {
                $('#view-lpk').load("<?= base_url('penunjang/PatologiKlinik/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN']) ?>");
            }

            // Stockart
            let stockart = $('#stockart');
            stockart.click(function () {
                $('#view-stockart').load("<?= base_url('Farmasi/Stockart/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN']) ?>");
            });
            if (stockart.hasClass('active')) {
                $('#view-stockart').load("<?= base_url('Farmasi/Stockart/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN']) ?>");
            }

            // CPPT
            $(document).on('click', '.cppt', function () {
                $('#view_cppt').load('<?= base_url() ?>cppt/<?= $this->uri->segment(2) ?>');
            });
            if ($('.cppt').hasClass('active')) {
                $('#view_cppt').load('<?= base_url() ?>cppt/<?= $this->uri->segment(2) ?>');
            }

            // Plan of care
            $('#poc, #poc-non-igd, #poc-igd').click(function () {
                $('#view-poc').load('<?= base_url() ?>rekam_medis/rawat_inap/catatanTerintegrasi/PlanOfCare/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('#poc, #poc-non-igd, #poc-igd').hasClass('active')) {
                $('#view-poc').load('<?= base_url() ?>rekam_medis/rawat_inap/catatanTerintegrasi/PlanOfCare/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            <?php if (!empty($pasien['NOKUN'])): ?>
            $.ajax({
                url: '<?= base_url() ?>rekam_medis/rawat_inap/catatanTerintegrasi/PlanOfCare/checkPocExists',
                type: 'POST',
                data: {
                    nokun: '<?= $pasien['NOKUN'] ?>'
                },
                dataType: 'json',
                success: function(response) {
                    if (!response.exists) {
                        $('#modal-warning-poc').modal('show');
                    }
                }
            });
            <?php endif; ?>
            
            // Handle "Isi" button click in warning modal
            $('#btn-isi-poc').click(function() {
                $('#modal-warning-poc').modal('hide');
                $('#poc').trigger('click');
            });

            // Pemberian Intravena
            $('.pemberianIntravena').click(function () {
                $('#view_pemberianIntravena').load('<?= base_url() ?>igd/FormulirPemberianIntravena/index/<?= $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) ?>');
            });
            if ($('.pemberianIntravena').hasClass('active')) {
                $('#view_pemberianIntravena').load('<?= base_url() ?>igd/FormulirPemberianIntravena/index/<?= $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) ?>');
            }

            //Poli Luka
            //Pengkajian Stoma
            $('.pengkajianStoma').click(function () {
                $('#view_pengkajian_stoma').load('<?= base_url() ?>luka/PengkajianStoma/index/<?= $pasien['NORM'] . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) ?>');
            });
            if ($('.pengkajianStoma').hasClass('active')) {
                $('#view_pengkajian_stoma').load('<?= base_url() ?>luka/PengkajianStoma/index/<?= $pasien['NORM'] . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) ?>');
            }

            // Pengkajian Luka
            $('.pengkajianLuka').click(function () {
                $('#view_pengkajianLuka').load('<?= base_url() ?>pengkajianLuka/index/<?= $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) ?>');
            });
            if ($('.pengkajianLuka').hasClass('active')) {
                $('#view_pengkajianLuka').load('<?= base_url() ?>pengkajianLuka/index/<?= $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) ?>');
            }

            // Transfer Ruangan
            // Permintaan Dirawat
            $('.permintaanDirawat').click(function () {
                $('#view_permintaanDirawat').load('<?= base_url() ?>transferRuangan/PermintaanDirawat/index/<?= $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) ?>');
            });
            if ($('.permintaanDirawat').hasClass('active')) {
                $('#view_permintaanDirawat').load('<?= base_url() ?>transferRuangan/PermintaanDirawat/index/<?= $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) ?>');
            }

            // Formulir Perpindahan Pasien Antar Ruang
            let formulirPindahRuangan = $('.formulir-pindah-ruangan');
            formulirPindahRuangan.click(function () {
                $('#view-formulir-pindah-ruangan').load('<?= base_url() ?>transferRuangan/FormulirPindahRuangan/index/<?= $this->uri->segment(2) ?>');
            });
            if (formulirPindahRuangan.hasClass('active')) {
                $('#view-formulir-pindah-ruangan').load('<?= base_url() ?>transferRuangan/FormulirPindahRuangan/index/<?= $this->uri->segment(2) ?>');
            }

            $('.formulirKriteriaRawatIntensif').click(function () {
                $('#view_formulirKriteriaRawatIntensif').load('<?= base_url() ?>formulirKriteriaRawatIntensif/<?= $this->uri->segment(2) ?>');
            });
            if ($('.formulirKriteriaRawatIntensif').hasClass('active')) {
                $('#view_formulirKriteriaRawatIntensif').load('<?= base_url() ?>formulirKriteriaRawatIntensif/<?= $this->uri->segment(2) ?>');
            }
            $('.formulirKriteriaRawatPicu').click(function () {
                $('#view_formulirKriteriaRawatPicu').load('<?= base_url() ?>igd/FormulirKriteriaRawatPicu/indexRawatInap/<?= $this->uri->segment(2) ?>');
            });
            if ($('.formulirKriteriaRawatPicu').hasClass('active')) {
                $('#view_formulirKriteriaRawatPicu').load('<?= base_url() ?>igd/FormulirKriteriaRawatPicu/indexRawatInap/<?= $this->uri->segment(2) ?>/');
            }

            // Ceklis Edikasi dan Orientasi Pasien Baru Rawat Inap
            $('.ceklisEdukasiOrientasiRI').click(function () {
                $('#view_ceklisEdukasiOrientasiRI').load('<?= base_url() ?>ceori/<?= $this->uri->segment(2) ?>');
            });
            if ($('.ceklisEdukasiOrientasiRI').hasClass('active')) {
                $('#view_ceklisEdukasiOrientasiRI').load('<?= base_url() ?>ceori/<?= $this->uri->segment(2) ?>/');
            }
            $(document).on('click', '.editCeklisEdukasiOrientasiRI', function () {
                $('#modal').modal('hide');
                var id = $(this).data('id');
                $('#view_ceklisEdukasiOrientasiRI').load('<?= base_url() ?>ceori/<?= $this->uri->segment(2) ?>/' + id);
            });

            // Surveilans Rumah Sakit Rawat Inap
            $('.surveilans').click(function () {
                $('#view_surveilans').load('<?= base_url() ?>surveilans/<?= $this->uri->segment(2) ?>');
            });
            if ($('.surveilans').hasClass('active')) {
                $('#view_surveilans').load('<?= base_url() ?>surveilans/<?= $this->uri->segment(2) ?>/');
            }
            $(document).on('click', '.editSurveilans', function () {
                $('#modal').modal('hide');
                var id = $(this).data('id');
                $('#view_surveilans').load('<?= base_url() ?>surveilans/<?= $this->uri->segment(2) ?>/' + id);
            });


            // Formulir Checklist Kriteria Pasien Masuk dan Keluar Ruang Isolasi Imunitas Menurun
            let riim = $('#riim');
            riim.click(function () {
                $('#view-riim').load('<?= base_url() ?>rekam_medis/rawat_inap/transferRuangan/RIIM/index/<?= $pasien['NOKUN'] ?>');
            });
            if (riim.hasClass('active')) {
                $('#view-riim').load('<?= base_url() ?>rekam_medis/rawat_inap/transferRuangan/RIIM/index/<?= $pasien['NOKUN'] ?>');
            }

            // Formulir Checklist Kriteria Pasien Masuk dan Keluar Ruang Isolasi Radioaktif (Rira)
            let rira = $('#rira');
            rira.click(function () {
                $('#view-rira').load('<?= base_url() ?>rekam_medis/rawat_inap/transferRuangan/Rira/index/<?= $pasien['NOKUN'] ?>');
            });
            if (rira.hasClass('active')) {
                $('#view-rira').load('<?= base_url() ?>rekam_medis/rawat_inap/transferRuangan/Rira/index/<?= $pasien['NOKUN'] ?>');
            }

            // Formulir Checklist Kriteria Pasien Masuk dan Keluar Ruang Isolasi Non Airbone Disease
            let rinad = $('#rinad');
            rinad.click(function () {
                $('#view-rinad').load('<?= base_url() ?>rekam_medis/rawat_inap/transferRuangan/RINAD/index/<?= $pasien['NOKUN'] ?>');
            });
            if (rinad.hasClass('active')) {
                $('#view-rinad').load('<?= base_url() ?>rekam_medis/rawat_inap/transferRuangan/RINAD/index/<?= $pasien['NOKUN'] ?>');
            }

            // reevaluasi
            $('.reevaluasi_lab').click(function () {
                $('#view_reevaluasi_lab').load('<?= base_url() ?>reevaluasiLab/<?= $this->uri->segment(2) ?>');
            });
            if ($('.reevaluasi_lab').hasClass('active')) {
                $('#view_reevaluasi_lab').load('<?= base_url() ?>reevaluasiLab/<?= $this->uri->segment(2) ?>');
            }

            // formulir skrining visual
            $('.formulirSkriningVisualRi').click(function () {
                $('#view_formulirSkriningVisualRi').load('<?= base_url() ?>igd/FormulirSkriningVisual/index/<?= $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) ?>');
            });
            if ($('.formulirSkriningVisualRi').hasClass('active')) {
                $('#view_formulirSkriningVisualRi').load('<?= base_url() ?>igd/FormulirSkriningVisual/index/<?= $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) ?>');
            }

            // Formulir Ceklis Program Pelayanan Instalasi Rawat Inap
            $('.CekPIRI').click(function () {
                $('#view_CekPIRI').load('<?= base_url() ?>rekam_medis/rawat_inap/admission/CekPIRI/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.CekPIRI').hasClass('active')) {
                $('#view_CekPIRI').load('<?= base_url() ?>rekam_medis/rawat_inap/admission/CekPIRI/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            // Pra Operasi
            $('#pengkajian-pra-operasi').click(function () {
                $('#view-pengkajian-pra-operasi').load('<?= base_url() ?>operasi/PengkajianPraOperasi/index/<?= $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) ?>');
            });
            if ($('#pengkajian-pra-operasi').hasClass('active')) {
                $('#view-pengkajian-pra-operasi').load('<?= base_url() ?>operasi/PengkajianPraOperasi/index/<?= $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) ?>');
            }

            // Operasi
            //Pendaftaran Pra Operasi
            $('#daftar-operasi').click(function () {
                $('#menu-daftar-operasi').load("<?= base_url('PengkajianDafOpe/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN']) ?>");
            });
            if ($('#daftar-operasi').hasClass('active')) {
                $('#menu-daftar-operasi').load("<?= base_url('PengkajianDafOpe/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN']) ?>");
            }

            //Pengkajian Pra Operasi
            $('.pengPraOperasi').click(function () {
                $('#view_pengPraOperasi').load('<?= base_url() ?>PengkajianPraOperasi/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.pengPraOperasi').hasClass('active')) {
                $('#view_pengPraOperasi').load('<?= base_url() ?>PengkajianPraOperasi/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            // Catatan Keperawatan Perioperatif Intra Operasi
            $('.intOpeRi').click(function () {
                $('#view_intOpeRi').load('<?= base_url() ?>rekam_medis/rawat_inap/operasi/intOpeRi/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.intOpeRi').hasClass('active')) {
                $('#view_intOpeRi').load('<?= base_url() ?>rekam_medis/rawat_inap/operasi/intOpeRi/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            // Assesmen Pra Anestesi
            $('.asPraAnes').click(function () {
                $('#view_asPraAnes').load('<?= base_url() ?>rekam_medis/rawat_inap/operasi/AsPraAnes/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.asPraAnes').hasClass('active')) {
                $('#view_asPraAnes').load('<?= base_url() ?>rekam_medis/rawat_inap/operasi/AsPraAnes/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            // Ceklis Persiapan Operasi
            $('.cpo').click(function () {
                $('#view_cpo').load('<?= base_url() ?>rekam_medis/rawat_inap/operasi/CPO/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.cpo').hasClass("active")) {
                $('#view_cpo').load('<?= base_url() ?>rekam_medis/rawat_inap/operasi/CPO/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            // Formulir Keselamatan Operasi
            $('.kesOpeRi').click(function () {
                $('#view_kesOpeRi').load('<?= base_url() ?>rekam_medis/rawat_inap/operasi/KesOpeRi/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.kesOpeRi').hasClass('active')) {
                $('#view_kesOpeRi').load('<?= base_url() ?>rekam_medis/rawat_inap/operasi/KesOpeRi/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            // Catatan Penggunaan Alat Operasi
            let alatOperasi = $('#alat-operasi');
            alatOperasi.click(function () {
                $('#view-alat-operasi').load('<?= base_url() . 'AlatOperasi/' . $this->uri->segment(2) ?>');
            });
            if (alatOperasi.hasClass('active')) {
                $('#view-alat-operasi').load('<?= base_url() . 'AlatOperasi/' . $this->uri->segment(2) ?>');
            }

            // Catatan Penggunaan Alat Operasi (Baru)
            let cpao = $('#cpao');
            cpao.click(function () {
                $('#view-cpao').load('<?= base_url() . 'CPAO/' . $this->uri->segment(2) ?>');
            });
            if (cpao.hasClass('active')) {
                $('#view-cpao').load('<?= base_url() . 'CPAO/' . $this->uri->segment(2) ?>');
            }

            // Laporan tindakan brakhiterapi nasofaring
            $('.brakNaso').click(function () {
                $('#view_brakNaso').load('<?= base_url() ?>rekam_medis/rawat_inap/brakhiterapi/BrakNaso/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.brakNaso').hasClass('active')) {
                $('#view_brakNaso').load('<?= base_url() ?>rekam_medis/rawat_inap/brakhiterapi/BrakNaso/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            //Site Marking
            $('.siteMarking').click(function () {
                $('#view_siteMarking').load('<?= base_url() ?>SiteMarking/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.siteMarking').hasClass('active')) {
                $('#view_siteMarking').load('<?= base_url() ?>SiteMarking/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            // Laporan Operasi
            let lapOperasi = $('#lap-operasi');
            lapOperasi.click(function () {
                $('#view-lap-operasi').load("<?= base_url('operasi/FormLaporanOperasi/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN']) ?>");
            });
            if (lapOperasi.hasClass('active')) {
                $('#view-lap-operasi').load("<?= base_url('operasi/FormLaporanOperasi/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN']) ?>");
            }

            // Rencana Asuhan Pasca Operasi
            let rapo = $('#rapo');
            rapo.click(function () {
                $('#view-rapo').load("<?= base_url('RAPO/' . $pasien['NOKUN']) ?>");
            });
            if (rapo.hasClass('active')) {
                $('#view-rapo').load("<?= base_url('RAPO/' . $pasien['NOKUN']) ?>");
            }

            //Pengkajian Terapi Sistemik Kanker
            $('.pengkajianTerapiSistemikRi').click(function () {
                $('#view_pengkajianTerapiSistemikRi').load('<?= base_url() ?>rekam_medis/rawat_inap/kemoterapi/PengkajianTerapiSistemik/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/00000');
            });
            if ($('.pengkajianTerapiSistemikRi').hasClass('active')) {
                $('#view_pengkajianTerapiSistemikRi').load('<?= base_url() ?>rekam_medis/rawat_inap/kemoterapi/PengkajianTerapiSistemik/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/00000');
            }

            //Pengkajian Pasien Imunitas Menurun (RIIM)
            $('.pengkajianRiim').click(function () {
                $('#view_pengkajianRiim').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/PengkajianRiim/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/00000');
            });
            if ($('.pengkajianRiim').hasClass('active')) {
                $('#view_pengkajianRiim').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/PengkajianRiim/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/00000');
            }

            //Pengkajian Pasien Ruang Isolasi Radioaktif (Rira)
            $('.pengkajianRira').click(function () {
                $('#view_pengkajianRira').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/PengkajianRira/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/00000');
            });
            if ($('.pengkajianRira').hasClass('active')) {
                $('#view_pengkajianRira').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/PengkajianRira/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/00000');
            }

            //IGD
            // Dashboard IGD
            $('#dashboard-igd').click(function () {
                $('#view-dashboard-igd').load("<?= base_url('DashboardIGD/' . $pasien['NOKUN']) ?>");
            });
            if ($('#dashboard-igd').hasClass('active')) {
                $('#view-dashboard-igd').load("<?= base_url('DashboardIGD/' . $pasien['NOKUN']) ?>");
            }

            $('.formulirTriaseRi').click(function () {
                $('#view_formulirTriaseRi').load('<?= base_url() ?>FormulirTriase/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.formulirTriaseRi').hasClass('active')) {
                $('#view_formulirTriaseRi').load('<?= base_url() ?>FormulirTriase/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            $('.surKetEm').click(function () {
                $('#view_surKetEm').load('<?= base_url() ?>SurKetEm/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.surKetEm').hasClass('active')) {
                $('#view_surKetEm').load('<?= base_url() ?>SurKetEm/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            $('.laporDPJP').click(function () {
                $('#view_laporDPJP').load('<?= base_url() ?>LaporanDPJP/<?= $this->uri->segment(2) ?>');
            });
            if ($('.laporDPJP').hasClass('active')) {
                $('#view_laporDPJP').load('<?= base_url() ?>LaporanDPJP/<?= $this->uri->segment(2) ?>');
            }

            // Farmasi

            $('.rekonsiliasiObat').click(function () {
                $('#view_rekonsiliasiObat').load('<?= base_url() ?>RekonsiliasiObat/<?= $this->uri->segment(2) ?>');
            });
            if ($('.rekonsiliasiObat').hasClass('active')) {
                $('#view_rekonsiliasiObat').load('<?= base_url() ?>RekonsiliasiObat/<?= $this->uri->segment(2) ?>');
            }

            $('.konseling').click(function () {
                $('#view_konseling').load('<?= base_url() ?>Konseling/<?= $this->uri->segment(2) ?>');
            });
            if ($('.konseling').hasClass('active')) {
                $('#view_konseling').load('<?= base_url() ?>Konseling/<?= $this->uri->segment(2) ?>');
            }
            $(document).on('click', '.editKonseling', function () {
                $('#modal').modal('hide');
                var id = $(this).data('id');
                $('#view_konseling').load('<?= base_url() ?>Konseling/<?= $this->uri->segment(2) ?>/' + id);
            });

            $('.feso').click(function () {
                $('#view_feso').load('<?= base_url() ?>rekam_medis/rawat_inap/farmasi/feso/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.feso').hasClass('active')) {
                $('#view_feso').load('<?= base_url() ?>rekam_medis/rawat_inap/farmasi/feso/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            $('.pengkajianIgdRi').click(function () {
                $('#view_pengkajianIgdRi').load('<?= base_url() ?>rekam_medis/rawat_inap/igd/PengkajianIgdRi/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/00000');
            });
            if ($('.pengkajianIgdRi').hasClass('active')) {
                $('#view_pengkajianIgdRi').load('<?= base_url() ?>rekam_medis/rawat_inap/igd/PengkajianIgdRi/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/00000');
            }

            $('.pengkajianRadioterapiRi').click(function () {
                $('#view_pengkajianRadioterapiRi').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRI/RadioterapiRi/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/00000');
            });
            if ($('.pengkajianRadioterapiRi').hasClass('active')) {
                $('#view_pengkajianRadioterapiRi').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRI/RadioterapiRi/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/00000');
            }

            //Protokol Kemoterapi
            $('.protokolKemoterapiRi').click(function () {
                $('#view_protokolKemoterapiRi').load('<?= base_url() ?>ProtokolKemo/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.protokolKemoterapiRi').hasClass('active')) {
                $('#view_protokolKemoterapiRi').load('<?= base_url() ?>ProtokolKemo/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            // Protokol Kemoterapi Anak
            let proKemAnak = $('#prokem-anak');
            proKemAnak.click(function () {
                $('#view-pro-kem-anak').load('<?= base_url() ?>ProKemAnak/<?= $pasien['NOKUN'] ?>');
            });
            if (proKemAnak.hasClass('active')) {
                $('#view-pro-kem-anak').load('<?= base_url() ?>ProKemAnak/<?= $pasien['NOKUN'] ?>');
            }

            // Protokol JPOK Kemoterapi
            let jpokem = $('#jpokem');
            jpokem.click(function () {
                $('#view-jpokem').load('<?= base_url() ?>JPOKEM/<?= $pasien['NOKUN'] ?>');
            });
            if (jpokem.hasClass('active')) {
                $('#view-jpokem').load('<?= base_url() ?>JPOKEM/<?= $pasien['NOKUN'] ?>');
            }

            //Serah Terima Pemeriksaan
            $('.serahTerimaPemRi').click(function () {
                $('#view_serahTerimaPemRi').load('<?= base_url() ?>SerahTerimaRi/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.serahTerimaPemRi').hasClass('active')) {
                $('#view_serahTerimaPemRi').load('<?= base_url() ?>SerahTerimaRi/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            //Keselamatan Tindakan Invasif
            $('.kesInvRi').click(function () {
                $('#view_kesInvRi').load('<?= base_url() ?>KesInvRi/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.kesInvRi').hasClass('active')) {
                $('#view_kesInvRi').load('<?= base_url() ?>KesInvRi/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            //Status Anestesia
            $('.sttsAnestesiaRi').click(function () {
                $('#view_sttsAnestesiaRi').load('<?= base_url() ?>Anestesia/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.sttsAnestesiaRi').hasClass('active')) {
                $('#view_sttsAnestesiaRi').load('<?= base_url() ?>Anestesia/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            //Tanda Vital Status Anestesia
            $('.tandaVitalAnestesia').click(function () {
                $('#view_tandaVitalAnestesia').load('<?= base_url() ?>TandaVitalAnes/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.tandaVitalAnestesia').hasClass('active')) {
                $('#view_tandaVitalAnestesia').load('<?= base_url() ?>TandaVitalAnes/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            // Checklist STATICS, Obat Anastesi, Sedasi, Emergensi, dan Mesin Anestesi
            let csoasema = $('#csoasema');
            csoasema.click(function () {
                $('#view-csoasema').load('<?= base_url() . 'CSOASEMA/' . $this->uri->segment(2) ?>');
            });
            if (csoasema.hasClass('active')) {
                $('#view-csoasema').load('<?= base_url() . 'CSOASEMA/' . $this->uri->segment(2) ?>');
            }

            //Kardek
            $('.kardekRi').click(function () {
                $('#view_kardekRi').load('<?= base_url() ?>rekam_medis/rawat_inap/catatanTerintegrasi/Kardek/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.kardekRi').hasClass('active')) {
                $('#view_kardekRi').load('<?= base_url() ?>rekam_medis/rawat_inap/catatanTerintegrasi/Kardek/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            // Kardek Rawat Inap
            $('.eKardekRi').click(function () {
                $('#view_eKardekRi').load('<?= base_url() ?>rekam_medis/rawat_inap/catatanTerintegrasi/KardekRi/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.eKardekRi').hasClass('active')) {
                $('#view_eKardekRi').load('<?= base_url() ?>rekam_medis/rawat_inap/catatanTerintegrasi/KardekRi/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            // Pemakaian O2
            $('.pemakaianO2Ri').click(function () {
                $('#view_pemakaianO2Ri').load('<?= base_url() ?>rekam_medis/rawat_inap/pemakaianO2/PemakaianO2/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.pemakaianO2Ri').hasClass('active')) {
                $('#view_pemakaianO2Ri').load('<?= base_url() ?>rekam_medis/rawat_inap/pemakaianO2/PemakaianO2/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            // Spinal Epidural
            $('.spinalEpi').click(function () {
                $('#view_spinalEpi').load('<?= base_url() ?>rekam_medis/rawat_inap/informedConsent/SpinalEpi/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.spinalEpi').hasClass('active')) {
                $('#view_spinalEpi').load('<?= base_url() ?>rekam_medis/rawat_inap/informedConsent/SpinalEpi/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            // PTK Bedah
            $('.PTKBedah').click(function () {
                $('#view_PTKBedah').load('<?= base_url() ?>rekam_medis/rawat_inap/informedConsent/PTKBedah/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.PTKBedah').hasClass('active')) {
                $('#view_PTKBedah').load('<?= base_url() ?>rekam_medis/rawat_inap/informedConsent/PTKBedah/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            // Radioterapi (Bekas Rawat Jalan)
            $('.radioterapiii').click(function () {
                $('#view_pengkajianRiRadio').load('<?= base_url() ?>rekam_medis/rawat_inap/radioterapiii/Radioterapiii/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.radioterapiii').hasClass('active')) {
                $('#view_pengkajianRiRadio').load('<?= base_url() ?>rekam_medis/rawat_inap/radioterapiii/Radioterapiii/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            $('.ct_Simulator').click(function () {
                $('#view_CTRiRadio').load('<?= base_url() ?>radioterapi/Ct_simulator/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.ct_Simulator').hasClass('active')) {
                $('#view_CTRiRadio').load('<?= base_url() ?>radioterapi/Ct_simulator/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            $('.simulatorInformation').click(function () {
                $('#view_simulatorRiRadio').load('<?= base_url() ?>rekam_medis/rawat_inap/radioterapiii/Radioterapiii/indexSimu/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.simulatorInformation').hasClass('active')) {
                $('#view_simulatorRiRadio').load('<?= base_url() ?>rekam_medis/rawat_inap/radioterapiii/Radioterapiii/indexSimu/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            $('.treatmentDose').click(function () {
                $('#view_doseRiRadio').load('<?= base_url() ?>radioterapi/TreatmentDose/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.treatmentDose').hasClass('active')) {
                $('#view_doseRiRadio').load('<?= base_url() ?>radioterapi/TreatmentDose/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            $('.kuotaalatradioterapi').click(function () {
                $('#view_kuotaalatradioterapi').load('<?= base_url() ?>radioterapi/TreatmentDose/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.kuotaalatradioterapi').hasClass("active")) {
                $('#view_kuotaalatradioterapi').load('<?= base_url() ?>radioterapi/TreatmentDose/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            // DNR
            $('.dnr').click(function () {
                $('#view_dnr').load('<?= base_url() ?>rekam_medis/rawat_inap/informedConsent/DNR/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.dnr').hasClass('active')) {
                $('#view_dnr').load('<?= base_url() ?>rekam_medis/rawat_inap/informedConsent/DNR/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            // Edukasi Tindakan Anastesi dan Sedasi
            $('.EdukasiTAS').click(function () {
                $('#view_EdukasiTAS').load('<?= base_url() ?>rekam_medis/rawat_inap/informedConsent/EdukasiTAS/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.EdukasiTAS').hasClass('active')) {
                $('#view_EdukasiTAS').load('<?= base_url() ?>rekam_medis/rawat_inap/informedConsent/EdukasiTAS/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            // Pemberian Informasi dan Persetujuan Tindakan Kedokteran Anestesi Umum
            $('.PIPTKAU').click(function () {
                $('#view_PIPTKAU').load('<?= base_url() ?>piptkau/<?= $this->uri->segment(2) ?>');
            });
            if ($('.PIPTKAU').hasClass('active')) {
                $('#view_PIPTKAU').load('<?= base_url() ?>piptkau/<?= $this->uri->segment(2) ?>');
            }
            $(document).on('click', '.editPIPTKAU', function () {
                $('#modal').modal('hide');
                var id = $(this).data('id');
                $('#view_PIPTKAU').load('<?= base_url() ?>piptkau/<?= $this->uri->segment(2) ?>/' + id);
            });

            // // Ringkas Medis (Summary List) Pasien Rawat Inap
            // $('#prmjri').click(function() {
            //     $('#view-prmjri').load("<!?= base_url('rekam_medis/rawat_inap/catatanTerintegrasi/PrmjRi/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN']) ?>");
            // });
            // if ($('#prmjri').hasClass('active')) {
            //     $('#view-prmjri').load("<!?= base_url('rekam_medis/rawat_inap/catatanTerintegrasi/PrmjRi/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN']) ?>");
            // }

            // Pemulangan Pasien
            $('.pemulanganPasienRi').click(function () {
                $('#view_pemulanganPasienRi').load('<?= base_url() ?>rekam_medis/rawat_inap/resume/PemulanganPasienRi/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.pemulanganPasienRi').hasClass('active')) {
                $('#view_pemulanganPasienRi').load('<?= base_url() ?>rekam_medis/rawat_inap/resume/PemulanganPasienRi/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            $(document).on('click', '.editPemulanganPasienRi', function () {
                $('#modal').modal('hide');
                var id = $(this).data('id');
                $('#view_pemulanganPasienRi').load('<?= base_url() ?>rekam_medis/rawat_inap/resume/PemulanganPasienRi/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] ?>/' + id);
            });

            // Persetujuan dan Edukasi
            // Catatan Edukasi dan Informasi Terintegrasi
            let catatanEdukasi = $('.catatan-edukasi');
            catatanEdukasi.click(function () {
                $('#view-catatan-edukasi').load('<?= base_url() . 'catatanEdukasi/index/' . $pasien['NOKUN'] ?>');
            });
            if (catatanEdukasi.hasClass('active')) {
                $('#view-catatan-edukasi').load('<?= base_url() . 'catatanEdukasi/index/' . $pasien['NOKUN'] ?>');
            }

            // Brakhiterapi
            // Pengkajian RI Brakhiterapi
            $('.pengkajianRiBrak').click(function () {
                $('#view_pengkajianRiBrak').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/PengBrak/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/00000');
            });
            if ($('.pengkajianRiBrak').hasClass('active')) {
                $('#view_pengkajianRiBrak').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/PengBrak/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/00000');
            }

            // Pengkajian RI Brakhiterapi Medis
            $('.pengkajianRiMedisBrakhiterapi').click(function () {
                $('#view_pengkajianRiMedisBrakhiterapi').load('<?= base_url() ?>pengkajianRiMedisBrakhiterapi/<?= $pasien['NOKUN'] ?>');
            });
            if ($('.pengkajianRiMedisBrakhiterapi').hasClass('active')) {
                $('#view_pengkajianRiMedisBrakhiterapi').load('<?= base_url() ?>pengkajianRiMedisBrakhiterapi/<?= $pasien['NOKUN'] ?>');
            }
            $(document).on('click', '.editPengkajianRIMedisBrakhiterapi', function () {
                var id = $(this).data('id');
                var status = $(this).data('status');
                if (status == 1) {
                    $('#modal').modal('hide');
                    $('#view_pengkajianRiMedisBrakhiterapi').load('<?= base_url() ?>pengkajianRiMedisBrakhiterapi/' + id);
                    $('.pengkajianRiMedisBrakhiterapi').trigger('click');
                }
            });

            $(document).on('click', '.verif-perawat-pasien-brakhiterapi', function () {
                var id = $(this).data('id');
                $('.pengkajianRiBrak').trigger('click');
                $('#view_pengkajianRiBrak').load('<?= base_url() ?>rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/PengBrak/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] ?>/' + id);
                $('#modal').modal('hide');
            });

            // Observasi dan Tindakan keperawatan Brakhiterapi
            let otkb = $('#otkb');
            otkb.click(function () {
                $('#view-otkb').load('<?= base_url() . 'OTKB/' . $this->uri->segment(2) ?>');
            });
            if (otkb.hasClass('active')) {
                $('#view-otkb').load('<?= base_url() . 'OTKB/' . $this->uri->segment(2) ?>');
            }

            // Laporan Tindakan Brakhiterapi Ginekologi
            $(document).on('click', '.ltbg', function () {
                $('#view_ltbg').load('<?= base_url() ?>LTBG/<?= $this->uri->segment(2) ?>');
            });
            if ($('.ltbg').hasClass('active')) {
                $('#view_ltbg').load('<?= base_url() ?>LTBG/<?= $this->uri->segment(2) ?>');
            }
            $(document).on('click', '.editLTBG', function () {
                $('#modal').modal('hide');
                var id = $(this).data('id');
                $('#view_ltbg').load('<?= base_url() ?>LTBG/<?= $this->uri->segment(2) ?>/' + id);
            });

            // Geriatri
            $('.instrumenPPPG').click(function () {
                $('#view_instrumenPPPG').load('<?= base_url() . 'geriatri/InstrumenPPPG/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.instrumenPPPG').hasClass('active')) {
                $('#view_instrumenPPPG').load('<?= base_url() . 'geriatri/InstrumenPPPG/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }
            $('.penilaianADL').click(function () {
                $('#view_penilaianADL').load('<?= base_url() . 'geriatri/Adl/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.penilaianADL').hasClass('active')) {
                $('#view_penilaianADL').load('<?= base_url() . 'geriatri/Adl/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }
            // Edit penilaianADL
            $(document).on('click', '.editPenilaianADL', function () {
                var id = $(this).data('id');
                $('#view_penilaianADL').load('<?= base_url() ?>geriatri/Adl/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/' + id + '/' + id);
            });
            // iadl
            $('.iadl').click(function () {
                $('#view_iadl').load('<?= base_url() . 'geriatri/Iadl/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.iadl').hasClass('active')) {
                $('#view_iadl').load('<?= base_url() . 'geriatri/Iadl/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }
            // Edit iadl
            $(document).on('click', '.editiadl', function () {
                var id = $(this).data('id');
                $('#view_iadl').load('<?= base_url() ?>geriatri/Iadl/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>/' + id + '/' + id);
            });
            // Instrumen Geriatric Development Scale
            $('.instrumen-gds').click(function () {
                $('#view-instrumen-gds').load('<?= base_url() ?>geriatri/InstrumenGDS/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.instrumen-gds').hasClass('active')) {
                $('#view-instrumen-gds').load('<?= base_url() ?>geriatri/InstrumenGDS/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }
            // Pemeriksaan MiniCOG
            $('.pemeriksaanMiniCOG').click(function () {
                $('#view_pemeriksaanMiniCOG').load('<?= base_url() . 'geriatri/PemeriksaanMiniCOG/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.pemeriksaanMiniCOG').hasClass('active')) {
                $('#view_pemeriksaanMiniCOG').load('<?= base_url() . 'geriatri/PemeriksaanMiniCOG/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }
            // instrumen Evalusi Mmse
            $('.instrumenMmse').click(function () {
                $('#view_instrumenMmse').load('<?= base_url() . 'geriatri/InstrumenMmse/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.instrumenMmse').hasClass('active')) {
                $('#view_instrumenMmse').load('<?= base_url() . 'geriatri/InstrumenMmse/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }
            // Formulir Abbreviated Mental Test
            $('.abbreviatedMentalTest').click(function () {
                $('#view_abbreviatedMentalTest').load('<?= base_url() . 'geriatri/AbbreviatedMentalTest/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.abbreviatedMentalTest').hasClass('active')) {
                $('#view_abbreviatedMentalTest').load('<?= base_url() . 'geriatri/AbbreviatedMentalTest/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }
            // instrumen Mna
            $('.instrumenMna').click(function () {
                $('#view_instrumenMna').load('<?= base_url() . 'geriatri/InstrumenMNA/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.instrumenMna').hasClass('active')) {
                $('#view_instrumenMna').load('<?= base_url() . 'geriatri/InstrumenMNA/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }
            // Formulir G8 Geriatri
            $('.formulirG8Geriatri').click(function () {
                $('#view_formulirG8Geriatri').load('<?= base_url() ?>formulirG8Geriatri/<?= $this->uri->segment(2) ?>');
            });
            if ($('.formulirG8Geriatri').hasClass('active')) {
                $('#view_formulirG8Geriatri').load('<?= base_url() ?>formulirG8Geriatri/<?= $this->uri->segment(2) ?>');
            }
            $(document).on('click', '.editG8Geriatri', function () {
                $('#modal').modal('hide');
                var id = $(this).data('id');
                $('#view_formulirG8Geriatri').load('<?= base_url() ?>formulirG8Geriatri/<?= $this->uri->segment(2) ?>/' + id);
            });
            // risiko jatuh pasien lanjut usia
            $('.resikojatuhlanjutusia').click(function () {
                $('#view_resikojatuhlanjutusia').load('<?= base_url() . 'geriatri/ResikoJatuhLanjutUsia/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.resikojatuhlanjutusia').hasClass('active')) {
                $('#view_resikojatuhlanjutusia').load('<?= base_url() . 'geriatri/ResikoJatuhLanjutUsia/index/' . $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            // Status Sedasi
            $('.statusSedasi').click(function () {
                $('#view_statusSedasi').load('<?= base_url() ?>statusSedasi/<?= $this->uri->segment(2) ?>');
            });
            if ($('.statusSedasi').hasClass('active')) {
                $('#view_statusSedasi').load('<?= base_url() ?>statusSedasi/<?= $this->uri->segment(2) ?>');
            }
            // Medikasi Obat
            $('.medikasiObat').click(function () {
                $('#view_medikasiObat').load('<?= base_url() ?>medikasiObat/<?= $this->uri->segment(2) ?>');
            });
            if ($('.medikasiObat').hasClass('active')) {
                $('#view_medikasiObat').load('<?= base_url() ?>medikasiObat/<?= $this->uri->segment(2) ?>');
            }
            // Medikasi Napas
            $('.medikasiNapas').click(function () {
                $('#view_medikasiNapas').load('<?= base_url() ?>medikasiNapas/<?= $this->uri->segment(2) ?>');
            });
            if ($('.medikasiNapas').hasClass('active')) {
                $('#view_medikasiNapas').load('<?= base_url() ?>medikasiNapas/<?= $this->uri->segment(2) ?>');
            }
            // Sedasi Pemulihan
            $('.sedasiPemulihan').click(function () {
                $('#view_sedasiPemulihan').load('<?= base_url() ?>sedasiPemulihan/<?= $this->uri->segment(2) ?>');
            });
            if ($('.sedasiPemulihan').hasClass('active')) {
                $('#view_sedasiPemulihan').load('<?= base_url() ?>sedasiPemulihan/<?= $this->uri->segment(2) ?>');
            }
            // Pemulihan Napas
            $('.pemulihanNapas').click(function () {
                $('#view_pemulihanNapas').load('<?= base_url() ?>pemulihanNapas/<?= $this->uri->segment(2) ?>');
            });
            if ($('.pemulihanNapas').hasClass('active')) {
                $('#view_pemulihanNapas').load('<?= base_url() ?>pemulihanNapas/<?= $this->uri->segment(2) ?>');
            }

            // Pemberian Infromasi dan Persetujuan Tindakan Kedokteran
            $('.PIPTKS').click(function () {
                $('#view_PIPTKS').load('<?= base_url() ?>piptks/<?= $this->uri->segment(2) ?>');
            });
            if ($('.PIPTKS').hasClass('active')) {
                $('#view_PIPTKS').load('<?= base_url() ?>piptks/<?= $this->uri->segment(2) ?>');
            }
            $(document).on('click', '.editPIPTKS', function () {
                $('#modal').modal('hide');
                var id = $(this).data('id');
                $('#view_PIPTKS').load('<?= base_url() ?>piptks/<?= $this->uri->segment(2) ?>/' + id);
            });

            // Persetujuan Tindakan Kedokteran
            $('.persetujuan-tindakan-kedokteran').click(function () {
                $('#view-persetujuan-tindakan-kedokteran').load('<?= base_url() ?>ptk/<?= $this->uri->segment(2) ?>');
            });
            if ($('.persetujuan-tindakan-kedokteran').hasClass('active')) {
                $('#view-persetujuan-tindakan-kedokteran').load('<?= base_url() ?>ptk/<?= $this->uri->segment(2) ?>');
            }

            // Persetujuan Tindakan Pengobatan Kemoterapi
            $('.persetujuanTindakanPengobatanKemoterapi').click(function () {
                $('#view_persetujuanTindakanPengobatanKemoterapi').load('<?= base_url() ?>ptpk/<?= $this->uri->segment(2) ?>');
            });
            if ($('.persetujuanTindakanPengobatanKemoterapi').hasClass('active')) {
                $('#view_persetujuanTindakanPengobatanKemoterapi').load('<?= base_url() ?>ptpk/<?= $this->uri->segment(2) ?>');
            }

            // Persetujuan Tindakan Kemoterapi Rhabdomiosarkoma
            $('.PTKemoRhabdomiosarkoma').click(function () {
                $('#view_PTKemoRhabdomiosarkoma').load('<?= base_url() ?>informedConsent/PTKemoRhabdomiosarkoma/index/<?= $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) ?>');
            });
            if ($('.PTKemoRhabdomiosarkoma').hasClass('active')) {
                $('#view_PTKemoRhabdomiosarkoma').load('<?= base_url() ?>informedConsent/PTKemoRhabdomiosarkoma/index/<?= $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) . '/' . $this->uri->segment(2) ?>');
            }

            // Penolakan Tindakan Kedokteran
            $('.penolakanTindakanKedokteran').click(function () {
                $('#view_penolakanTindakanKedokteran').load('<?= base_url() ?>penolakantindakankedokteran/<?= $this->uri->segment(2) ?>');
            });
            if ($('.penolakanTindakanKedokteran').hasClass('active')) {
                $('#view_penolakanTindakanKedokteran').load('<?= base_url() ?>penolakantindakankedokteran/<?= $this->uri->segment(2) ?>');
            }
            $(document).on('click', '.editPenolakanTindakanKedokteran', function () {
                $('#modal').modal('hide');
                var id = $(this).data('id');
                $('#view_penolakanTindakanKedokteran').load('<?= base_url() ?>penolakantindakankedokteran/<?= $this->uri->segment(2) ?>/' + id);
            });

            // Persetujuan Tindakan Transfusi Darah
            let pttd = $('.pttd');
            pttd.click(function () {
                $('#view-pttd').load('<?= base_url() ?>pttd/<?= $pasien['NOKUN'] ?>');
            });
            if (pttd.hasClass('active')) {
                $('#view-pttd').load('<?= base_url() ?>pttd/<?= $pasien['NOKUN'] ?>');
            }

            // FLAP
            $(document).on('click', '.flap', function () {
                $('#view_flap').load('<?= base_url() ?>Flap/<?= $this->uri->segment(2) ?>');
            });
            if ($('.flap').hasClass('active')) {
                $('#view_flap').load('<?= base_url() ?>Flap/<?= $this->uri->segment(2) ?>');
            }

            $(document).on('click', '.editFlap', function () {
                $('#modal').modal('hide');
                var id = $(this).data('id');
                $('#view_flap').load('<?= base_url() ?>Flap/<?= $this->uri->segment(2) ?>/' + id);
            });

            // FLOSHEET
            $(document).on('click', '.flosheet', function () {
                $('#view_flosheet').load('<?= base_url() ?>Flosheet/<?= $this->uri->segment(2) ?>');
            });
            if ($('.flosheet').hasClass('active')) {
                $('#view_flosheet').load('<?= base_url() ?>Flosheet/<?= $this->uri->segment(2) ?>');
            }

            // Formulir Skrining Covid-19
            $(document).on('click', '.formulirSkriningCovid19', function () {
                $('#view_formulirSkriningCovid19').load('<?= base_url() ?>SkriningCovid19/<?= $this->uri->segment(2) ?>');
            });
            if ($('.formulirSkriningCovid19').hasClass('active')) {
                $('#view_formulirSkriningCovid19').load('<?= base_url() ?>SkriningCovid19/<?= $this->uri->segment(2) ?>');
            }
            $(document).on('click', '.editSkriningCovid19', function () {
                $('#modal').modal('hide');
                var id = $(this).data('id');
                $('#view_formulirSkriningCovid19').load('<?= base_url() ?>SkriningCovid19/<?= $this->uri->segment(2) ?>/' + id);
            });

            // FORM EPID COVID-19
            $(document).on('click', '.formulirEpidemologiCovid', function () {
                $('#view_formulirEpidemologiCovid').load('<?= base_url() ?>rekam_medis/rawat_inap/covid19/FormEpidCovid/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.formulirEpidemologiCovid').hasClass('active')) {
                $('#view_formulirEpidemologiCovid').load('<?= base_url() ?>rekam_medis/rawat_inap/covid19/FormEpidCovid/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            // BANK DARAH PEMANTAUAN DARAH
            $(document).on('click', '.pemberianDanPemantauanDarah', function () {
                $('#view_pemberianDanPemantauanDarah').load('<?= base_url() ?>rekam_medis/rawat_inap/bankDarah/PemberianDanPemantauanDarah/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.pemberianDanPemantauanDarah').hasClass('active')) {
                $('#view_pemberianDanPemantauanDarah').load('<?= base_url() ?>rekam_medis/rawat_inap/bankDarah/PemberianDanPemantauanDarah/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            // SCAN
            $(document).on('click', '.Scan', function () {
                $('#view_Scan').load('<?= base_url() ?>rekam_medis/rawat_inap/scanberkas/Scan/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.Scan').hasClass('active')) {
                $('#view_Scan').load('<?= base_url() ?>rekam_medis/rawat_inap/scanberkas/Scan/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }


            // PALITIF RI
            $(document).on('click', '.pengkajianPaliatifRI', function () {
                $('#view_pengkajianPaliatifRI').load('<?= base_url() ?>rekam_medis/rawat_inap/paliatif/PaliatifRIPerawat/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.pengkajianPaliatifRI').hasClass('active')) {
                $('#view_pengkajianPaliatifRI').load('<?= base_url() ?>rekam_medis/rawat_inap/paliatif/PaliatifRIPerawat/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }
            //FAMILY MEETING
            $(document).on('click', '.ffm', function () {
                $('#view_ffm').load('<?= base_url() ?>rekam_medis/rawat_inap/paliatif/FamilyMeeting/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.ffm').hasClass('active')) {
                $('#view_ffm').load('<?= base_url() ?>rekam_medis/rawat_inap/paliatif/FamilyMeeting/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }
            //Hospital Anxiety and Depression Scale (HADS)
            $(document).on('click', '.hads', function () {
                $('#view_hads').load('<?= base_url() ?>rekam_medis/rawat_inap/paliatif/HADS/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.hads').hasClass('active')) {
                $('#view_hads').load('<?= base_url() ?>rekam_medis/rawat_inap/paliatif/HADS/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }
            //INSTRUMEN KEBUTUHAN PALIATIF
            $(document).on('click', '.iikpp', function () {
                $('#view_iikpp').load('<?= base_url() ?>rekam_medis/rawat_inap/paliatif/IIKPP/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.iikpp').hasClass('active')) {
                $('#view_iikpp').load('<?= base_url() ?>rekam_medis/rawat_inap/paliatif/IIKPP/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }
            //Kriteria Persiapan Pulang Pasien (KPPPP)
            $(document).on('click', '.kpppp', function () {
                $('#view_kpppp').load('<?= base_url() ?>rekam_medis/rawat_inap/paliatif/KPPPP/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.kpppp').hasClass('active')) {
                $('#view_kpppp').load('<?= base_url() ?>rekam_medis/rawat_inap/paliatif/KPPPP/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            // Echo EKG
            $(document).on('click', '.echoekg', function () {
                $('#view_echoekg').load('<?= base_url() ?>Echoekg/<?= $pasien['NOKUN'] ?>');
            });
            if ($('.echoekg').hasClass('active')) {
                $('#view_echoekg').load('<?= base_url() ?>Echoekg/<?= $pasien['NOKUN'] ?>');
            }

            // Input TPN
            $(document).on('click', '.inputTpn', function () {
                $('#view_inputTpn').load('<?= base_url() ?>rekam_medis/rawat_inap/Tpn/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            });
            if ($('.inputTpn').hasClass('active')) {
                $('#view_inputTpn').load('<?= base_url() ?>rekam_medis/rawat_inap/Tpn/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>');
            }

            // eTimja
            let eTimja = $('#etimja');
            eTimja.click(function (e, data) {
                // Mulai periksa jika id eTimja ada
                if (data) {
                    // console.log(data.id_etimja);
                    // $('#data-id-etimja').html(data.id_etimja);
                    // $('#view-etimja').load(
                    //     '<!?= base_url('Etimja/form/' . $pasien['NOKUN']) ?>',
                    //     {data: data.id_etimja},
                    // );
                    $.ajax({
                        url: '<?= base_url('Etimja/form/' . $pasien['NOKUN']) ?>',
                        type: 'POST',
                        data: {
                            id_etimja: data.id_etimja
                        },
                        success: function (data) {
                            $('#view-etimja').html(data);
                        },
                    });
                } else {
                    $('#view-etimja').load('<?= base_url('Etimja/form/' . $pasien['NOKUN']) ?>');
                }
                // Akhir periksa jika id eTimja ada
            });
            if (eTimja.hasClass('active')) {
                $('#view-etimja').load('<?= base_url('Etimja/form/' . $pasien['NOKUN']) ?>');
            }

            $(document).on('click', '.buka-etimja', function () {
                let id_etimja = $(this).data('id');
                // console.log(id_etimja);
                $('#modal-history-etimja-info').modal('hide');
                eTimja.trigger('click', [{
                    id_etimja: id_etimja
                }]);
            });

            // Berkas IPPJ
            let berkasPasien = $('#berkas-pasien-luar');
            berkasPasien.click(function () {
                $('#view-berkas-pasien-luar').load("<?= base_url() ?>filePendukungPasien/berkasPasien/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>");
            });
            if (berkasPasien.hasClass('active')) {
                $('#view-berkas-pasien-luar').load("<?= base_url() ?>filePendukungPasien/berkasPasien/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>");
            }

            // Berkas Scan
            let berkasScan = $('#berkas-scan');
            berkasScan.click(function () {
                $('#view-berkas-scan').load("<?= base_url() ?>rekam_medis/rawat_inap/scanberkas/Scan/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>");
            });
            if (berkasPasien.hasClass('active')) {
                $('#view-berkas-scan').load("<?= base_url() ?>rekam_medis/rawat_inap/scanberkas/Scan/index/<?= $pasien['NORM'] . '/' . $pasien['NOPEN'] . '/' . $pasien['NOKUN'] ?>");
            }

            // Resume Kematian
            $('.resumeKematian').click(function () {
                $('#view_resumeKematian').load('<?= base_url() ?>resumeKematian/<?= $this->uri->segment(2) ?>');
            });
            if ($('.resumeKematian').hasClass('active')) {
                $('#view_resumeKematian').load('<?= base_url() ?>resumeKematian/<?= $this->uri->segment(2) ?>');
            }

            // Mulai Manajemen Pelayanan Pasien
            // Mulai Skrining Pasien Membutuhkan Manajer Pelayanan Pasien
            let smpp = $('#smpp');
            smpp.click(function () {
                $('#view-smpp').load("<?= base_url('MPP/Skrining/' . $pasien['NOKUN']) ?>");
            });
            if (smpp.hasClass('active')) {
                $('#view-smpp').load("<?= base_url('MPP/Skrining/' . $pasien['NOKUN']) ?>");
            }
            // Akhir Skrining Pasien Membutuhkan Manajer Pelayanan Pasien

            // Mulai Asesmen Awal Manajer Pelayanan Pasien
            let ampp = $('#ampp');
            ampp.click(function () {
                $('#view-ampp').load("<?= base_url('MPP/Asesmen/' . $pasien['NOKUN']) ?>");
            });
            if (ampp.hasClass('active')) {
                $('#view-ampp').load("<?= base_url('MPP/Asesmen/' . $pasien['NOKUN']) ?>");
            }
            // Akhir Asesmen Awal Manajer Pelayanan Pasien

            // Mulai Implementasi
            let impp = $('#impp');
            impp.click(function () {
                $('#view-impp').load("<?= base_url('MPP/Implementasi/' . $pasien['NOKUN']) ?>");
            });
            if (impp.hasClass('active')) {
                $('#view-impp').load("<?= base_url('MPP/Implementasi/' . $pasien['NOKUN']) ?>");
            }
            // Akhir Implementasi
            // Akhir Manajemen Pelayanan Pasien

            // Mulai HIV
            // Mulai Ikhtisar Follow-up Perawatan Pasien HIV dan Terapi Antiretroviral
            let fuart = $('#fuart');
            fuart.click(function () {
                $('#view-fuart').load("<?= base_url('HIV/FollowUp/' . $pasien['NOKUN']) ?>");
            });
            if (fuart.hasClass('active')) {
                $('#view-fuart').load("<?= base_url('HIV/FollowUp/' . $pasien['NOKUN']) ?>");
            }
            // Akhir Ikhtisar Follow-up Perawatan Pasien HIV dan Terapi Antiretroviral
            // Mulai Formulir Rujukan
            let rart = $('#rart');
            rart.click(function () {
                $('#view-rart').load("<?= base_url('HIV/Rujukan/' . $pasien['NOKUN']) ?>");
            });
            if (rart.hasClass('active')) {
                $('#view-rart').load("<?= base_url('HIV/Rujukan/' . $pasien['NOKUN']) ?>");
            }
            // Akhir Formulir Rujukan
            // Akhir HIV
        });

        // Mulai history eTimja
        $('#tbl-history-etimja-info').click(function () {
            $('#lihat-history-modal-etimja-info').empty();
            let nomr = $(this).data('id');
            $.ajax({
                method: 'POST',
                url: "<?= base_url('Etimja/history') ?>",
                data: {
                    nomr: nomr,
                    nokun: "<?= $pasien['NOKUN'] ?>",
                    keterangan: 'info',
                },
                success: function (data) {
                    $('#lihat-history-modal-etimja-info').html(data);
                }
            });
        });
        // Akhir history eTimja

        // Start History Rekonsiliasi Obat
        $('#tbl-history-rekonsiliasi-obat').click(function () {
            let nomr = $(this).data('id');

            $.ajax({
                method: 'POST',
                url: "<?= base_url('igd/FormulirRekonsiliasiObat/history') ?>",
                data: {
                    nomr: nomr,
                },
                success: function (data) {
                    $('#lihat-history-modal-rekonsiliasi-obat').html(data);
                }
            });
        });

        $('#history_kenseling_menu').click(function () {
            $('#modal').modal('show');
            $('#modal .modal-title').html('History Konseling Pasien');
            $('#modal .modal-body').html('<div class="table-responsive">' +
                '<table  class="table table-bordered table-hover dt-responsive dt-responsive nowrap table-custom dataTable" id="tbl_history_kenseling" cellspacing="0" width="100%">' +
                '<thead>' +
                '<tr class="table-tr-custom">' +
                '<th>#</th>' +
                '<th>Tanggal</th>' +
                '<th>Ruangan</th>' +
                '<th>Oleh</th>' +
                '</tr>' +
                '</thead>' +
                '<tbody>' +
                '</tbody>' +
                '</table>' +
                '</div>');
            var tbl_history_kenseling = $('#tbl_history_kenseling').DataTable({
                "sPaginationType": "full_numbers",
                "responsive": true,
                "bPaginate": true,
                "lengthMenu": [
                    [1, 5, 10, 25, 50, 100, -1],
                    [1, 5, 10, 25, 50, 100, "All"]
                ],
                "processing": false,
                "serverSide": true,
                "bFilter": true,
                "bLengthChange": true,
                "iDisplayLength": 2,
                "ordering": false,
                "order": [],
                "ajax": {
                    url: '<?= base_url() ?>rekam_medis/rawat_inap/farmasi/Konseling/datatables',
                    type: "POST",
                    data: function (data) {
                        data.nomr = '<?= $getNomr['NORM'] ?>';
                    }
                }
            });
        });

        // Mulai EWS profil
        <?php if (isset($pasien['USIA'])): ?>
            let ews;
            if (<?= $pasien['USIA'] ?> === 1) {
                ews = getJSON("<?= base_url('rekam_medis/Medis/pewsDashboard') ?>", {
                    nokun: "<?= $pasien['NOKUN'] ?>",
                });
            } else {
                ews = getJSON("<?= base_url('rekam_medis/Medis/ewsDashboard') ?>", {
                    nokun: "<?= $pasien['NOKUN'] ?>",
                });
            }

            if (ews.data !== null && ews.data !== undefined) {
                if (ews.data['SKOR'] >= 6) {
                    $('#tbl-ews-profil').removeClass('d-none').attr('data-content', moment(ews.data['TANGGAL_EWS']).format('LLL')).popover('show');
                    $('#jml-ews-profil').text(ews.data['SKOR']);
                }
            }
        <?php endif ?>
        // Akhir EWS profil

        // Load History Pendaftaran
        $('#history_pendaftaran').click(function () {
            var norm = $(this).data('id');
            history(norm);
        });

        // Load List Pasien
        function loadRuangan(id, status = 1, norm = 0) {
            var listPasien = getJSON('<?= base_url() ?>rekam_medis/medis/listPasien', {
                status: status,
                ruangan: id,
                norm: norm
            });
            $('.user-list').html('');
            if (listPasien.data.length !== 0) {
                var no = 1;
                $.each(listPasien.data, function (index, element) {
                    var img = "<?= base_url('assets/admin/assets/images/users/profile.jpg') ?>";
                    var url = "<?= base_url('medis/') ?>" + element.NOKUN;
                    var dot = "<i class='fa fa-circle' style='color:#f94242'></i>";
                    if (element.STATUS_RUANGAN == 1) {
                        dot = "<i class='fa fa-circle' style='color:#25ac5c'></i>";
                    }
                    if (element.JK == 'P') {
                        img = "<?= base_url('assets/admin/assets/images/users/profile2.jpg') ?>";
                    }
                    var selected = "";
                    if (localStorage.normselected == element.NORM) {
                        selected = 'style="background-color:#629acf"';
                    }
                    var html = '<li class="list-group-item" ' + selected + '>' +
                        '<a href="' + url + '" data-norm="' + element.NORM + '" class="user-list-item list_pasien">' +
                        '<div class="avatar">' +
                        '<img src="' + img + '" alt="">' +
                        '</div>' +
                        '<div class="user-desc">' +
                        '<span class="name">' + dot + ' ' + element.NAMA_PASIEN + '</span>' +
                        '<span class="desc" style="color:#ff6348">' + element.NORM + ' [ ' + element.KAMAR + ' ]</span>' +
                        '</div>' +
                        '</a>' +
                        '</li>';
                    $('.user-list').append(html);
                });

                $('#count-list-pasien').html(listPasien.total);
                // var loadMore = '<li class="list-group-item load_more" data-page="1" data-flag="1"> <center>Load More</center>'+
                //             '</li>';
                // $('.user-list').append(loadMore);
            } else {
                $('.user-list').html('<div class="alert alert-danger">' +
                    '<strong>Tidak Ditemukan</strong> Pasien tidak berada diruangan ini/belum terdaftar.' +
                    '</div>');
                $('#count-list-pasien').html(listPasien.total);
            }
        }

        // Ruangan Select
        $('#ruangan').select2({
            placeholder: '[ Pilih Ruangan ]'
        }).on('change', function () {
            var id = $(this).val();
            localStorage.ruangan = id;
            $('.header-title').html($('#ruangan option:selected').text());
            loadRuangan(id);
            localStorage.filterstatusselected = 1;
            $('.list_pasien').click(function () {
                var norm = $(this).data('norm');
                localStorage.normselected = norm;
                localStorage.activeTab = '#dashboard';
            });
        });

        $('#rawat').select2();

        $('#filter_norm').on('keyup', function (e) {
            if (e.which == 13) {
                var ruangan = $('#ruangan').val();
                var norm = $(this).val();
                loadRuangan(ruangan, 0, norm);
            }
        });

        $(document).on('click', '.list_pasien', function () {
            var norm = $(this).data('norm');
            localStorage.normselected = norm;
            localStorage.activeTab = '#dashboard';
        });

        $('.filter_aktif').click(function () {
            var ruangan = $('#ruangan').val();
            var status = $(this).data('status');
            localStorage.filterstatusselected = status;
            loadRuangan(ruangan, status);
        });

        // document.getElementById('buka_hasil_lab').addEventListener('click', function() {
        //     document.getElementById('history_patologi_klinik').click();
        // });

        $('#buka_hasil_lab').click(function () {
            var norm = "<?= $pasien['NORM'] ?>";
            var tgls = moment("<?= $cekHasilLabPk['MASUK'] ?>").format("LLL");
            $('#modal').modal('show');
            $('#modal .modal-body').html('');
            $('#modal .modal-title').html('Hasil Lab Patologi Klinik');
            $('#modal .modal-body').append('<div class="row jarak"><div class="col-sm-3"><p class="text-white">Tanggal Lab: </p></div><div class="col-sm-3"><span class="text-white">'+tgls+'</span></div></div>');
            // $('#nokun_pk').select2({
            //     dropdownParent: $("#modal")
            // });
            // var kunjunganPK = getJSON('<?= base_url() ?>rekam_medis/medis/kunjungan_pk_baru', {
            //     norm: norm
            // });
            // if (kunjunganPK.length !== 0) {
            //     $.each(kunjunganPK, function (index, element) {
            //         var html = '<option value="' + element.ID + '">' + moment(element.DESKRIPSI).format("LLL") + ' - ' + element.TINDAKAN + '</option>';
            //         $('#nokun_pk').append(html);
            //     });
            // } else {
            //     $('#nokun_pk').html('');
            // }
            $('#modal .modal-body').append('<div class="row">' +
                '<div class="col-lg-6">' +
                '<div class="form-group">' +
                '<table class="table" id="tbl_detail_kunjungan_pk_kritis" style="background-color:#9ccbe2; color: #242a30; font-size: 13px; line-height:15px;">' +
                '</table>' +
                '</div>' +
                '</div>' +
                '<div class="col-lg-6">' +
                '<div class="form-group">' +
                '<table class="table" id="tbl_tanggal_pk_kritis" style="background-color: #f7ca71; color: #242a30; font-size: 13px; line-height:15px;">' +
                '</table>' +
                '</div>' +
                '</div>' +
                '</div>');
            $('#modal .modal-body').append('<div class="table-responsive">' +
                '<table  class="table table-bordered table-hover dt-responsive dt-responsive nowrap table-custom dataTable" id="tbl_history_patologi_klinik_kritis" cellspacing="0" width="100%">' +
                '<thead>' +
                '<tr class="table-tr-custom">' +
                '<th>No</th>' +
                '<th>Parameter</th>' +
                '<th>Hasil</th>' +
                '<th>Nilai Rujukan</th>' +
                '<th>Satuan</th>' +
                '<th>Keterangan</th>' +
                '</tr>' +
                '</thead>' +
                '<tbody>' +
                '</tbody>' +
                '</table>' +
                '</div>');
            // load_detail_pk();
            $('#nokun_pk').on('change', function () {
                tbl_history_patologi_klinik_kritis.ajax.reload(null, false);
                // load_detail_pk();
            });

            var tbl_history_patologi_klinik_kritis = $('#tbl_history_patologi_klinik_kritis').DataTable({
                "sPaginationType": "full_numbers",
                "responsive": true,
                "bPaginate": true,
                "lengthMenu": [
                    [1, 5, 10, 25, 50, 100, -1],
                    [1, 5, 10, 25, 50, 100, "All"]
                ],
                "processing": false,
                "serverSide": false,
                "bFilter": true,
                "bLengthChange": true,
                "iDisplayLength": 10,
                "ordering": false,
                "order": [],
                "ajax": {
                    url: '<?= base_url() ?>rekam_medis/penunjang/PatologiKlinik/datatablesKritis',
                    type: "POST",
                    data: function (data) {
                        data.nokun = '<?=$cekNokunHasilLabPk['NOKUN']?>';
                        data.kritis = '1';
                    }
                },
                "fnRowCallback": function (nRow, aData, iDisplayIndex, iDisplayIndexFull) {
                    if (aData[6] != "" && aData[6] != null) {
                        $('td', nRow).css('background-color', '#d9534f');
                    }
                },
                "columnDefs": [{
                    "visible": false,
                    "targets": 6
                }]
            });

            $.ajax({
                url: "<?= base_url('rekam_medis/penunjang/PatologiKlinik/simpanLogHasilLabKritis') ?>",
                method: 'POST',
                dataType: 'json',
                data: {
                    norm: '<?=$pasien['NORM'];?>',
                    nokun: '<?=$cekNokunHasilLabPk['NOKUN'];?>',
                    tgl_lab: '<?=$detailKritis['TGL'];?>',
                    tgl_sampling: '<?=$tanggalKritis['retrieved_dt'];?>',
                    tgl_hasil: '<?=$tanggalKritis['authorization_date'];?>'
                },
                success: function(resp) {
                    $("#rowKritis").hide();
                },
                error: function() {
                }
            });

        });

        // START HISTORY PATOLOGI KLINIK
        $('#history_patologi_klinik').click(function () {
            var norm = "<?= $pasien['NORM'] ?>";
            $('#modal').modal('show');
            $('#modal .modal-body').html('');
            $('#modal .modal-title').html('History Hasil Lab Patologi Klinik');
            $('#modal .modal-body').append('<div class="row jarak"><div class="col-sm-3"><p class="text-white">History Kunjungan: </p></div><div class="col-sm-9"><select name="nokun_pk" id="nokun_pk" class="form-control select2"></select></div></div>');
            $('#nokun_pk').select2({
                dropdownParent: $("#modal")
            });
            var kunjunganPK = getJSON('<?= base_url() ?>rekam_medis/medis/kunjungan_pk_baru', {
                norm: norm
            });
            if (kunjunganPK.length !== 0) {
                $.each(kunjunganPK, function (index, element) {
                    var html = '<option value="' + element.ID + '">' + moment(element.DESKRIPSI).format("LLL") + ' - ' + element.TINDAKAN + '</option>';
                    $('#nokun_pk').append(html);
                });
            } else {
                $('#nokun_pk').html('');
            }
            $('#modal .modal-body').append('<div class="row">' +
                '<div class="col-lg-6">' +
                '<div class="form-group">' +
                '<table class="table" id="tbl_detail_kunjungan_pk" style="background-color:#9ccbe2; color: #242a30; font-size: 13px; line-height:15px;">' +
                '</table>' +
                '</div>' +
                '</div>' +
                '<div class="col-lg-6">' +
                '<div class="form-group">' +
                '<table class="table" id="tbl_tanggal_pk" style="background-color: #f7ca71; color: #242a30; font-size: 13px; line-height:15px;">' +
                '</table>' +
                '</div>' +
                '</div>' +
                '</div>');
            $('#modal .modal-body').append('<div class="table-responsive">' +
                '<table  class="table table-bordered table-hover dt-responsive dt-responsive nowrap table-custom dataTable" id="tbl_history_patologi_klinik" cellspacing="0" width="100%">' +
                '<thead>' +
                '<tr class="table-tr-custom">' +
                '<th>No</th>' +
                '<th>Parameter</th>' +
                '<th>Hasil</th>' +
                '<th>Nilai Rujukan</th>' +
                '<th>Satuan</th>' +
                '<th>Keterangan</th>' +
                '</tr>' +
                '</thead>' +
                '<tbody>' +
                '</tbody>' +
                '</table>' +
                '</div>');
            load_detail_pk();
            $('#nokun_pk').on('change', function () {
                tbl_history_patologi_klinik.ajax.reload(null, false);
                load_detail_pk();
            });

            var tbl_history_patologi_klinik = $('#tbl_history_patologi_klinik').DataTable({
                "sPaginationType": "full_numbers",
                "responsive": true,
                "bPaginate": true,
                "lengthMenu": [
                    [1, 5, 10, 25, 50, 100, -1],
                    [1, 5, 10, 25, 50, 100, "All"]
                ],
                "processing": false,
                "serverSide": false,
                "bFilter": true,
                "bLengthChange": true,
                "iDisplayLength": 10,
                "ordering": false,
                "order": [],
                "ajax": {
                    url: '<?= base_url() ?>rekam_medis/penunjang/PatologiKlinik/datatables',
                    type: "POST",
                    data: function (data) {
                        data.nokun = $('#nokun_pk').val();
                    }
                },
                "fnRowCallback": function (nRow, aData, iDisplayIndex, iDisplayIndexFull) {
                    if (aData[6] != "" && aData[6] != null) {
                        $('td', nRow).css('background-color', '#d9534f');
                    }
                },
                "columnDefs": [{
                    "visible": false,
                    "targets": 6
                }]
            });
        });

        function load_detail_pk() {
            var data = getJSON('<?= base_url() ?>rekam_medis/medis/detail_kunjungan_pk', {
                nokun: $('#nokun_pk').val()
            });

            $('#tbl_detail_kunjungan_pk').html('<tr>' +
                '<td>Tanggal Lab</td>' +
                '<td>' + moment(data.detail["TGL"]).format("LLL") + '</td>' +
                '</tr>' +
                '<tr>' +
                '<td>Dokter Perujuk</td>' +
                '<td>' + text(data.detail["NMDA"]) + '</td>' +
                '</tr>' +
                '<tr>' +
                '<td>Dokter Lab</td>' +
                '<td>' + text(data.detail["NMDL"]) + '</td>' +
                '</tr>' +
                '<tr>' +
                '<td>Ruang Asal Order</td>' +
                '<td>' + text(data.detail["RUANGASAL"]) + '</td>' +
                '</tr>');
            $('#tbl_tanggal_pk').html('<tr>' +
                '<td>Tanggal Sampling</td>' +
                '<td>' + moment(data.tanggal["retrieved_dt"]).format("LLL") + '</td>' +
                '</tr>' +
                '<tr>' +
                '<td>Tanggal Hasil</td>' +
                '<td>' + moment(data.tanggal["authorization_date"]).format("LLL") + '</td>' +
                '</tr>');

        }

        // END HISTORY PATOLOGI KLINIK

        // START HISTORY RADIOLOGI
        $('#history_radiologi').click(function () {
            var norm = "<?= $pasien['NORM'] ?>";
            $('#modal').modal('show');
            $('#modal .modal-body').html('');
            $('#modal .modal-title').html('History Hasil Radiologi');
            $('#modal .modal-body').append('<div class="row jarak"><div class="col-sm-3"><p class="text-white">History Pemeriksaan: </p></div><div class="col-sm-9"><select name="nokun_rad" id="nokun_rad" class="form-control select2"></select></div></div><div class="row jarak"><div class="col-sm-3"><p class="text-white">Tindakan: </p></div><div class="col-sm-9" id="tindakan_medis_rad"></div></div><div id="view_expertise"></div>');

            $('#nokun_rad').select2();
            var pemeriksaanRad = getJSON('<?= base_url() ?>rekam_medis/medis/pemeriksaan_radiologi', {
                norm: norm
            });
            if (pemeriksaanRad.length !== 0) {
                $.each(pemeriksaanRad, function (index, element) {
                    var html = '<option value="' + element.ID + '">' + element.TINDAKAN + ' / ' + moment(element.DESKRIPSI).format("LLL") + '</option>';
                    $('#nokun_rad').append(html);
                });
                load_tindakan_radiologi();
                $('#nokun_rad').on('change', function () {
                    load_tindakan_radiologi();
                });
            } else {
                $('#nokun_rad').html('');
            }
        });

        function load_tindakan_radiologi() {
            $('#tindakan_medis_rad').html('<select name="id_tindakan_radiologi" id="id_tindakan_radiologi" class="form-control select2"></select>');
            $('#id_tindakan_radiologi').select2();
            var tindakanRad = getJSON('<?= base_url() ?>rekam_medis/medis/tindakan_radiologi', {
                nokun: $('#nokun_rad').val()
            });
            if (tindakanRad.length !== 0) {
                $.each(tindakanRad, function (index, element) {
                    var html = '<option value="' + element.ID + '">' + element.DESKRIPSI + '</option>';
                    $('#id_tindakan_radiologi').append(html);
                });
                $('#id_tindakan_radiologi').on('change', function () {
                    load_expertise();
                });
                load_expertise();
            } else {
                $('#id_tindakan_radiologi').html('');
            }
        }

        function fetchStudyData(tindaknMedis) {
            return $.ajax({
                url: `/pacs_bridging/getStudy.php?studyId=${tindaknMedis}`,
                method: 'GET'
            });
        }

        function load_expertise() {
            var norm = "<?= $pasien['NORM'] ?>";
            var expertise = getJSON('<?= base_url() ?>rekam_medis/medis/expertise', {
                id: $('#id_tindakan_radiologi').val(),
                norm: norm
            });
            var viewPacs = '<div class="btn-group">' + '<a id="viewSemuaFoto" target="_blank" class="btn btn-info btn-sm" data-nomr=' + norm + '><i class="fa fa-eye"></i> Lihat Semua Foto</a>' + '</div><br/>';
            if (expertise !== null) {
                let deadline = new Date("10/10/2021");
                let serverName = "";
                if (new Date(expertise['TANGGAL_INPUT_TINDAKAN']) < deadline) {
                    serverName = "PACSOLD";
                } else {
                    serverName = "PACS";
                }
                
                fetchStudyData($('#id_tindakan_radiologi').val()).done(function(studyData) {
                    if (studyData.status === 'success') {
                        viewPacs += `<div class="btn-group"><a href="#" class="btn btn-success btn-sm" onclick="window.open('${studyData.data.path}', '_blank', 'status=1'); return false;"><i class="fa fa-file-image-o"></i> Lihat Foto</a></div><br/>`;
                    } else if (expertise['STUDY_ID_LINK'] !== undefined) {
                        var url = "/oviyam2/viewer.html?patientID=" + norm + "&studyUID=" + expertise['STUDY_ID_LINK'] + "&serverName=" + serverName;

                        viewPacs += '<div class="btn-group">' + '<a href="' + url + '" target="_blank" class="btn btn-success btn-sm"><i class="fa  fa-file-image-o"></i> Lihat Foto</a>' + '</div><br/>';
                    }
                });
                setTimeout(function() {
                    $('#view_expertise').html('<div class="row">' +
                        '<div class="col-lg-12">' +
                        '<div class="form-group">' +
                        '<div class="row float-right">' +
                        viewPacs +
                        '</div>' +
                        '</div>' +
                        '<div class="form-group">' +
                        '<label for="klinis">KLINIS</label>' +
                        '<textarea name="txtklinis" class="form-control" rows="3" placeholder="[ Klinis ]" readonly>' + text(expertise['KLINIS']) + '</textarea>' +
                        '</div>' +
                        '<div class="form-group">' +
                        '<label for="kesan">KESAN</label>' +
                        '<textarea name="txtkesan" class="form-control" rows="12" placeholder="[ Kesan ]" readonly>' + text(expertise['KESAN']) + '</textarea>' +
                        '</div>' +
    
                        '<div class="form-group">' +
                        '<label for="usul">USUL</label>' +
                        '<textarea name="txtusul" class="form-control" rows="3" placeholder="[ Usul ]" readonly>' + text(expertise['USUL']) + '</textarea>' +
                        '</div>' +
                        '<div class="form-group">' +
                        '<label for="hasil">HASIL</label>' +
                        '<textarea name="txthasil" class="form-control" rows="12" placeholder="[ Hasil ]" readonly>' + text(expertise['HASIL']) + '</textarea>' +
                        '</div>' +
                        '</div>' +
                        '</div><br><br>' +
    
                        '<div class="row">' +
                        '<div class="col-lg-6">' +
                        '<div class="form-group">' +
                        '<label for="dokter_nuklir">DOKTER SPESIALIS NUKLIR</label>' +
                        '<input type="text" class="form-control" value="' + text(expertise['DOKTER_SATU']) + '" readonly>' +
                        '</div>' +
                        '</div>' +
    
                        '<div class="col-lg-6">' +
                        '<div class="form-group">' +
                        '<label for="dokter_radiologi">DOKTER SPESIALIS RADIOLOGI</label>' +
                        '<input type="text" class="form-control" value="' + text(expertise['DOKTER_DUA']) + '" readonly>' +
                        '</div>' +
                        '</div>' +
                        '</div>');
                }, 500);
            } else {
                $('#view_expertise').html('<div class="alert alert-danger">' + '<strong>Tidak di temukan!</strong> Hasil Pemeriksaan Belum Tersedia.' + '</div>');
            }

            $(document).on('click', '#viewSemuaFoto', function (e) {
                var nomr = $(this).data('nomr');
                $.ajax({
                    type: 'POST',
                    url: '<?= base_url() ?>pengkajianAwal/viewFoto',
                    data: {
                        nomr: nomr
                    },
                    success: function (data) {
                        $('#modal2').modal('show');
                        $('#modal2 .modal-body').html('');
                        $('#modal2 .modal-title').html('Hasil Foto Radiologi');
                        $('#modal2 .modal-body').html(data);
                    }
                });
            });

        }

        // END HISTORY RADIOLOGI

        // START HISTORY PATALOGI ANATOMI
        $('#history_patologi_anatomi').click(function () {
            var norm = "<?= $pasien['NORM'] ?>";
            $('#modal').modal('show');
            $('#modal .modal-body').html('');
            $('#modal .modal-title').html('History Hasil Patologi Anatomi');
            $('#modal .modal-body').append('<div class="row jarak"><div class="col-sm-3"><p class="text-white">Jenis Pemeriksaan: </p></div><div class="col-sm-9"><select name="jenis_pemeriksaan" id="jenis_pemeriksaan" class="form-control select2"><option value="1">Sitologi</option><option value="2">Histologi</option><option value="3">Imunohistokimia</option></select></div></div><div class="row jarak"><div class="col-sm-3"><p class="text-white">Tindakan: </p></div><div class="col-sm-9" id="pemeriksaan_patologi_anatomi"></div></div><div id="view_hasil_pa"></div>');
            $('#jenis_pemeriksaan').select2();

            loadPemeriksaan();
            $('#jenis_pemeriksaan').on('change', function () {
                loadPemeriksaan();
            });

            function loadPemeriksaan() {
                $('#pemeriksaan_patologi_anatomi').html('<select name="id_tindakan_pa" id="id_tindakan_pa" class="form-control select2"></select>');
                $('#id_tindakan_pa').select2();
                var pemeriksaanPA = getJSON('<?= base_url() ?>rekam_medis/medis/pemeriksaan_patologi_anatomi', {
                    norm: norm,
                    jenis: $('#jenis_pemeriksaan').val()
                });

                if (pemeriksaanPA.length !== 0) {
                    $.each(pemeriksaanPA, function (index, element) {
                        var html = '<option value="' + element.NOMOR_LAB + '">' + element.NOMOR_LAB + ' / ' + moment(element.TANGGAL_LAB).format("LLL") + '</option>';
                        $('#id_tindakan_pa').append(html);
                    });
                    loadHasilPemeriksaan();
                    $('#id_tindakan_pa').on('change', function () {
                        loadHasilPemeriksaan();
                    });
                } else {
                    $('#id_tindakan_pa').html('<option disabled selected>[ TIDAK ADA TINDAKAN ]</option>');
                    $('#view_hasil_pa').html('');
                }
            }

            function loadHasilPemeriksaan() {
                // var hasilPA = getJSON('<!?= base_url() ?>rekam_medis/medis/hasil_pemeriksaan_patologi_anatomi', {
                //     nolab: $('#id_tindakan_pa').val(),
                //     jenis: $('#jenis_pemeriksaan').val()
                // });
                var hasilPA = getJSON('/rekam_medis/hasil_pemeriksaan_patologi_anatomi', {
                    nolab: $('#id_tindakan_pa').val(),
                    jenis: $('#jenis_pemeriksaan').val()
                });

                if (hasilPA !== null) {
                    $('#view_hasil_pa').html('<div class="container">' +
                        '<div class="row">' +
                        '<div class="col-lg-6">' +
                        '<div class="form-group">' +
                        '<table class="table table-borderless" style="background-color:#9ccbe2; color: #242a30; font-size: 13px; line-height:15px;">' +
                        '<tr>' +
                        '<td width="35%">Tanggal Terima</td>' +
                        '<td>' + moment(text(hasilPA['TERIMA'])).format("LLL") + '</td>' +
                        '</tr>' +
                        '<tr>' +
                        '<td>Dokter Perujuk</td>' +
                        '<td>' + text(hasilPA['PENGIRIM']) + '</td>' +
                        '</tr>' +
                        '<tr>' +
                        '<td>Dokter Lab Pa</td>' +
                        '<td>' + text(hasilPA['PEMERIKSA']) + '</td>' +
                        '</tr>' +
                        '<tr>' +
                        '<td>Ruang Pengirim</td>' +
                        '<td>' + text(hasilPA['RUANGAN']) + '</td>' +
                        '</tr>' +
                        '<tr>' +
                        '<td>Penjamin</td>' +
                        '<td>' + text(hasilPA['PENJAMIN']) + '</td>' +
                        '</tr>' +
                        '</table>' +
                        '</div>' +
                        '</div>' +
                        '<div class="col-lg-6">' +
                        '<table class="table table-borderless" style="background-color:#f7ca71; color: #242a30; font-size: 13px; line-height:15px;">' +
                        '<tr>' +
                        '<td width="35%">Tanggal Sampling</td>' +
                        '<td>' + moment(text(hasilPA['TANGGAL_LAB'])).format("LLL") + '</td>' +
                        '</tr>' +
                        '<tr>' +
                        '<td>Tanggal Hasil </td>' +
                        '<td>' + moment(text(hasilPA['TANGGAL'])).format("LLL") + '</td>' +
                        '</tr>' +
                        '<tr>' +
                        '<td>Nomor Lab</td>' +
                        '<td>' + text(hasilPA['NOMOR_LAB']) + '</td>' +
                        '</tr>' +
                        '<tr>' +
                        '<td>Tindakan</td>' +
                        '<td>' + text(hasilPA['TIND']) + '</td>' +
                        '</tr>' +
                        '</table>' +
                        '</div>' +
                        '</div>' +
                        '</div>' +
                        '<hr>' +
                        '<div class="row">' +
                        '<div class="col-lg-12">' +
                        '<div class="form-group">' +
                        '<h4 style="color:#ff5e28;">Hasil Konsultasi Patologi Anatomi</h4>' +
                        '</div>' +
                        '</div>' +
                        '</div>	' +
                        '<div class="row">' +
                        '<div class="col-lg-3">' +
                        '<div class="form-group">' +
                        '<label for="lokasi">Lokasi</label>' +
                        '</div>' +
                        '</div>' +
                        '<div class="col-lg-9">' +
                        '<div class="form-group">' +
                        '<input type="text" class="form-control" placeholder="[ Lokasi ]" value="' + text(hasilPA['LOKASI']) + '" readonly>' +
                        '</div>' +
                        '</div>' +
                        '</div>' +
                        '<div class="row">' +
                        '<div class="col-lg-3">' +
                        '<div class="form-group">' +
                        '<label for="didapat_dengan">Didapat Dengan</label>' +
                        '</div>' +
                        '</div>' +
                        '<div class="col-lg-9">' +
                        '<div class="form-group">' +
                        '<input type="text" class="form-control" placeholder="[ Didapat Dengan ]" value="' + text(hasilPA['DIDAPAT_DENGAN']) + '" readonly>' +
                        '</div>' +
                        '</div>' +
                        '</div>' +
                        '<div class="row">' +
                        '<div class="col-lg-3">' +
                        '<div class="form-group">' +
                        '<label for="cairaan_fiksasi">Cairan Fiksasi</label>' +
                        '</div>' +
                        '</div>' +
                        '<div class="col-lg-9">' +
                        '<div class="form-group">' +
                        '<textarea name="" class="form-control" rows="5" placeholder="[ Cairan Fiksasi ]" readonly>' + text(hasilPA['CAIRAN_FIKSASI']) + '</textarea>' +
                        '</div>' +
                        '</div>' +
                        '</div>' +
                        '<div class="row">' +
                        '<div class="col-lg-3">' +
                        '<div class="form-group">' +
                        '<label for="diagnosa_klinik" >Diagnosa Klinik</label>' +
                        '</div>' +
                        '</div>' +
                        '<div class="col-lg-9">' +
                        '<div class="form-group">' +
                        '<textarea name="" class="form-control" rows="5" placeholder="[ Diagnosa Klinik ]" readonly>' + text(hasilPA['DIAGNOSA_KLINIK']) + '</textarea>' +
                        '</div>' +
                        '</div>' +
                        '</div>' +
                        '<div class="row">' +
                        '<div class="col-lg-3">' +
                        '<div class="form-group">' +
                        '<label for="keterangan_klinik">Keterangan Klinik</label>' +
                        '</div>' +
                        '</div>' +
                        '<div class="col-lg-9">' +
                        '<div class="form-group">' +
                        '<textarea name="" class="form-control" rows="5" placeholder="[ Keterangan Klinik ]" readonly>' + text(hasilPA['KETERANGAN_KLINIK']) + '</textarea>' +
                        '</div>' +
                        '</div>' +
                        '</div>' +
                        '<div class="row">' +
                        '<div class="col-lg-3">' +
                        '<div class="form-group">' +
                        '<label for="makroskopik">Makroskopik</label>' +
                        '</div>' +
                        '</div>' +
                        '<div class="col-lg-9">' +
                        '<div class="form-group">' +
                        '<textarea name="" class="form-control" rows="5" +placeholder="[ Makroskopik ]" readonly>' + text(hasilPA['MAKROSKOPIK']) + '</textarea>' +
                        '</div>' +
                        '</div>' +
                        '</div>' +
                        '<div class="row">' +
                        '<div class="col-lg-3">' +
                        '<div class="form-group">' +
                        '<label for="mikroskopik">Mikroskopik</label>' +
                        '</div>' +
                        '</div>' +
                        '<div class="col-lg-9">' +
                        '<div class="form-group">' +
                        '<textarea name="" class="form-control" rows="5" placeholder="[ Mikroskopik ]" readonly>' + text(hasilPA['MIKROSKOPIK']) + '</textarea>' +
                        '</div>' +
                        '</div>' +
                        '</div>' +
                        '<div class="row">' +
                        '<div class="col-lg-3">' +
                        '<div class="form-group">' +
                        '<label for="kesimpulan">Kesimpulan</label>' +
                        '</div>' +
                        '</div>' +
                        '<div class="col-lg-9">' +
                        '<div class="form-group">' +
                        '<textarea name="" class="form-control" rows="5" placeholder="[ Kesimpulan ]" readonly>' + text(hasilPA['KESIMPULAN']) + '</textarea>' +
                        '</div>' +
                        '</div>' +
                        '</div>' +
                        '<div class="row">' +
                        '<div class="col-lg-3">' +
                        '<div class="form-group">' +
                        '<label for="kesimpulan">Imuno Histokimia</label>' +
                        '</div>' +
                        '</div>' +
                        '<div class="col-lg-9">' +
                        '<div class="form-group">' +
                        '<textarea name="" class="form-control" rows="5" placeholder="[ Imuno Histokimia ]" readonly>' + text(hasilPA['IMUNO_HISTOKIMIA']) + '</textarea>' +
                        '</div>' +
                        '</div>' +
                        '</div>' +
                        '<div class="row">' +
                        '<div class="col-lg-3">' +
                        '<div class="form-group">' +
                        '<label for="kesimpulan">Reevaluasi</label>' +
                        '</div>' +
                        '</div>' +
                        '<div class="col-lg-9">' +
                        '<div class="form-group">' +
                        '<textarea name="" class="form-control" rows="5" placeholder="[ Reevaluasi ]" readonly>' + text(hasilPA['REEVALUASI']) + '</textarea>' +
                        '</div>' +
                        '</div>' +
                        '</div>');
                } else {
                    $('#view_hasil_pa').html('<div class="alert alert-danger">' + '<strong>Tidak di temukan!</strong> Hasil Pemeriksaan Belum Tersedia.' + '</div>');
                }
            }
        });

        // TOMBOL SPEECH SAMPING
        // $(document).on('click', '#viewHasilSpeechSideRi', function() {
        //     if ($('#showHasilSpeechSampingRi').hasClass('d-block')) {
        //         $('#showHasilSpeechSampingRi').fadeOut(300, function() {
        //             $('#showHasilSpeechSampingRi').addClass('d-none').removeClass('d-block').fadeOut(300);
        //         });
        //         $('#main-menu').fadeIn(300, function() {
        //             $('#main-menu').addClass('col-sm-12').removeClass('col-sm-6').fadeIn(300);
        //         });
        //         $('#mySidebarGeser').fadeIn(300, function() {
        //             $('#mySidebarGeser').fadeIn(300);
        //             $('.openbtnGeser').trigger('click');
        //         });
        //     } else {
        //         // if ($('#view-side-konsultasi').hasClass('d-block')) {
        //         //   $('#view-side-konsultasi').fadeOut(300, function() {
        //         //     $('#view-side-konsultasi').addClass('d-none').removeClass('d-block').fadeOut(300);
        //         //   });
        //         //   $('#main-menu').fadeIn(300, function() {
        //         //     $('#main-menu').addClass('col-sm-12').removeClass('col-sm-6').fadeIn(300);
        //         //   });
        //         //   $('#mySidebarGeser').fadeIn(300, function() {
        //         //     $('#mySidebarGeser').fadeIn(300);
        //         //     $('.openbtnGeser').trigger('click');
        //         //   });
        //         // }
        //         // if ($('#cppt-view-side').hasClass('d-block')) {
        //         //   $('#cppt-view-side').fadeOut(300, function() {
        //         //     $('#cppt-view-side').addClass('d-none').removeClass('d-block').fadeOut(300);
        //         //   });
        //         //   $('#main-menu').fadeIn(300, function() {
        //         //     $('#main-menu').addClass('col-sm-12').removeClass('col-sm-6').fadeIn(300);
        //         //   });
        //         //   $('#mySidebarGeser').fadeIn(300, function() {
        //         //     $('#mySidebarGeser').fadeIn(300);
        //         //     $('.openbtnGeser').trigger('click');
        //         //   });
        //         // }
        //         // if ($('#showHasilKardekSideRi').hasClass('d-block')) {
        //         //   $('#showHasilKardekSideRi').fadeOut(300, function() {
        //         //     $('#showHasilKardekSideRi').addClass('d-none').removeClass('d-block').fadeOut(300);
        //         //   });
        //         //   $('#main-menu').fadeIn(300, function() {
        //         //     $('#main-menu').addClass('col-sm-12').removeClass('col-sm-6').fadeIn(300);
        //         //   });
        //         //   $('#mySidebarGeser').fadeIn(300, function() {
        //         //     $('#mySidebarGeser').fadeIn(300);
        //         //     $('.openbtnGeser').trigger('click');
        //         //   });
        //         // }
        //         if ($('#showHasilPenunjangSampingRi').hasClass('d-block')) {
        //             $('#showHasilPenunjangSampingRi').fadeOut(300, function() {
        //                 $('#showHasilPenunjangSampingRi').addClass('d-none').removeClass('d-block').fadeOut(300);
        //             });
        //             $('#main-menu').fadeIn(300, function() {
        //                 $('#main-menu').addClass('col-sm-12').removeClass('col-sm-6').fadeIn(300);
        //             });
        //             $('#mySidebarGeser').fadeIn(300, function() {
        //                 $('#mySidebarGeser').fadeIn(300);
        //                 $('.openbtnGeser').trigger('click');
        //             });
        //         }
        //         $('#showHasilSpeechSampingRi').fadeIn(300, function() {
        //             $('#showHasilSpeechSampingRi').addClass('d-block').removeClass('d-none');
        //         });
        //         $('#main-menu').fadeOut(300, function() {
        //             $('#main-menu').addClass('col-sm-6').removeClass('col-sm-12').fadeIn(300);
        //         });
        //         $('#mySidebarGeser').fadeOut(300, function() {
        //             $('#mySidebarGeser').fadeOut(300);
        //             $('.openbtnGeser').trigger('click');
        //         });
        //     }
        // });

        // TOMBOL SAMPING HISTORY HASIL PENUNJANG DAN CPPT
        $(document).on('click', '#viewHasilPenunjangSideRi', function () {
            if ($('#showHasilPenunjangSampingRi').hasClass('d-block')) {
                $('#showHasilPenunjangSampingRi').fadeOut(300, function () {
                    $('#showHasilPenunjangSampingRi').addClass('d-none').removeClass('d-block').fadeOut(300);
                });
                $('#main-menu').fadeIn(300, function () {
                    $('#main-menu').addClass('col-sm-12').removeClass('col-sm-6').fadeIn(300);
                });
                $('#mySidebarGeser').fadeIn(300, function () {
                    $('#mySidebarGeser').fadeIn(300);
                    $('.openbtnGeser').trigger('click');

                });
            } else {
                // if ($('#showHasilSpeechSampingRi').hasClass('d-block')) {
                //     $('#showHasilSpeechSampingRi').fadeOut(300, function() {
                //         $('#showHasilSpeechSampingRi').addClass('d-none').removeClass('d-block').fadeOut(300);
                //     });
                //     $('#main-menu').fadeIn(300, function() {
                //         $('#main-menu').addClass('col-sm-12').removeClass('col-sm-6').fadeIn(300);
                //     });
                //     $('#mySidebarGeser').fadeIn(300, function() {
                //         $('#mySidebarGeser').fadeIn(300);
                //         $('.openbtnGeser').trigger('click');
                //     });
                // }
                $('#showHasilPenunjangSampingRi').fadeIn(300, function () {
                    $('#showHasilPenunjangSampingRi').addClass('d-block').removeClass('d-none');
                    $('#cpptSideBarRi-tab').trigger('click');
                });
                $('#main-menu').fadeOut(300, function () {
                    $('#main-menu').addClass('col-sm-6').removeClass('col-sm-12').fadeIn(300);
                });
                $('#mySidebarGeser').fadeOut(300, function () {
                    $('#mySidebarGeser').fadeOut(300);
                    $('.openbtnGeser').trigger('click');
                });
            }
        });
        // Akhir side pane konsultasi

        // $( window ).scroll(function() {
        //     var header = $('#cppt-view-side');
        //     var sticky = header.offset();
        //     // console.log($(window).scrollTop() +' - '+ sticky.top);
        //     if ($(window).scrollTop() > sticky.top) {
        //         header.css('position','fixed');
        //         header.css('top','0');
        //     } else {
        //         header.css('position','');
        //         header.css('top','');
        //     }
        // });

        // END HISTORY PATALOGI ANATOMI

        // $('.dropdown-menu a.dropdown-toggle').click(function(e) {
        //   if (!$(this).next().hasClass('show')) {
        //     $(this).parents('.dropdown-menu').first().find('.show').removeClass("show");
        //   }
        //   var $subMenu = $(this).next(".dropdown-menu");
        //   $subMenu.toggleClass('show');

        //   $(this).parents('li.nav-item.dropdown.show').on('hidden.bs.dropdown', function(e) {
        //     $('.dropdown-submenu .show').removeClass("show");
        //   });
        //   return false;
        // });


        // --ido modul -----------
        $(document).on('click', '.reset', function (e) {
            swal({
                title: "Anda Yakin?",
                text: `Kosongkan form`,
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: "Ya, kosongkan!",
            }).then(function () {
                resetform()
            }, function (dismiss) {
                if (dismiss === 'cancel') {

                }
            })
        });
        $(document).on('input', '.nomr', function (e) {
            var nomr = $(this).val();
            var tipe = $(this).data("tipe");
            if (nomr != '') {
                getpasien(nomr, tipe);
            }
        });
        $(document).on('click', '.idoubah', function (e) {
            var id = $(this).data('id');
            var nokun = $(".histoido").data('nokun');
            var tipe = $(this).data("tipe");
            $("#modal").modal('hide');
            $('.idoisi').load('<?= base_url() ?>' + `ido/tabel${tipe}idoshow/${nokun}_${id}`, function () {
                $(".idoisi #nomr").attr('readonly', true);
            });
        })
        $(document).on('click', '.histoido', function (e) {
            var nokun = $(this).data('nokun');
            var tipe = $(this).data("tipe");
            $('#modal').modal('show');
            $('#modal .modal-title').html(`DATA SURVEILANS PASIEN PERI-OPERATIF INFEKSI DAERAH OPERASI (IDO)`);

            $('#modal .modal-body').load(`<?= base_url() ?>` + `ido/tabel${tipe}ido/` + nokun);

        });
        $(document).on('submit', '.formido', function (e) {
            e.preventDefault();
            var data = $(this).serialize();
            var nomr = $(".idoisi #nomr").val();
            var tipe = $(this).data("tipe");
            if (nomr != "") {
                $.ajax({
                    type: 'POST',
                    url: '<?= base_url() ?>' + `ido/pos${tipe}ido/1`,
                    dataType: 'json',
                    data: data,
                    success: function (data) {
                        if (data.success == 1) {
                            resetform();
                            swal(
                                'Success',
                                `Data berhasil disimpan`,
                                'success'
                            );
                        } else if (data.success == 2) {
                            resetform();
                            swal(
                                'Success',
                                `Data berhasil diubah`,
                                'success'
                            );
                        } else {
                            swal(
                                'Informasi!',
                                `Gagal simpan`,
                                'info'
                            );
                        }
                    }
                });
            } else {
                swal(
                    'Opps!',
                    `Isi NOMR dulu yah`,
                    'error'
                );
            }
        });

        function resetform() {
            $(".formido input[type='text']:not([readonly]):not('.poske'), input[type='date']:not([readonly]), input[type='number']:not([readonly]), input[type='hidden']:not([readonly])").val("")
            $('.formido input[type="radio"]').prop('checked', false)
            $('.formido select.select2').val(null).trigger('change');
            // $(".idoisi #nomr").attr('readonly', false);
        }

        function getpasien(nomr = '', tipe = '') {
            $.ajax({
                type: 'POST',
                url: "<?= base_url('ido/gepasien/') ?>" + nomr,
                data: {
                    nomr: nomr
                },
                success: function (data) {
                    var obj = JSON.parse(data);
                    if (tipe == 'pre') {
                        $('#nama').val(obj.nama);
                        $('#diagmasuk').val(obj.diagmasuk);
                        $('#id_ruangan').val(obj.ruangan);
                        $('#umur_t').val(obj.umur_t);
                        $('#tgl_masuk').val(obj.tgl_masuk);
                        $('#tgl_lahir').val(obj.tgl_lahir);
                        $('#jk').val(obj.jk);
                        $('#nopen').val(obj.nopen);
                    } else if (tipe == 'post') {
                        $('#nama').val(obj.nama);
                        $('#umur_t').val(obj.umur_t);
                        $('#tgl_lahir').val(obj.tgl_lahir);
                        $('#telpon').val(obj.telpon);
                        $('#telpkel').val(obj.telpkel);
                        $('#namakel').val(obj.namakel);
                        $('#alamat').val(obj.alamat);
                        $('#jk').val(obj.jk);
                        $('#nopen').val(obj.nopen);
                        $('#postid').val(obj.postid);
                    }
                }
            });
        }
        // end ido modul

        // Mulai iframe iCare
        $('#buka-icare').on('click', function () {
            event.preventDefault();
            $.ajax({
                url: 'http://*************:8000/api/icare/fkrtl',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    "noKartu": "<?= isset($getNoBPJS->noKartu) ? $getNoBPJS->noKartu : 0 ?>",
                    "kodeDokter": <?= isset($getKodeDokter->HAFIS) ? $getKodeDokter->HAFIS : 0 ?>
                }),
                success: function (response) {
                    if (response.metaData.code === 200 && response.response) {
                        $('#icare-iframe').attr('src', response.response.url);
                        $('#modal-icare').modal('show');
                    } else {
                        alertify.error('Error: ' + response.metaData.message);
                    }
                },
                error: function () {
                    alertify.error('Error tidak diketahui.');
                }
            });
        });
        // Akhir iframe iCare

        $('.tombolInputDiagnosaDpjpRI').on('click', function () {
            var nopen = $(this).data('nopen');
            var nomr = $(this).data('nomr');
            var nokun = $(this).data('nokun');
            $.ajax({
                type: 'POST',
                url: "<?= base_url('DiagnosisDpjp/index') ?>",
                data: {
                    nopen: nopen,
                    nomr: nomr,
                    nokun: nokun,
                },
                success: function (data) {
                    $('#hasilInputDiagnosaDpjpRI').html(data);
                }
            });
        });
    });
</script>
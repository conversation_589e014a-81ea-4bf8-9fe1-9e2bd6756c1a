<!-- Page-Title -->
<div class="row">
  <div class="col-md-3">
    <h4 class="page-title"><PERSON><PERSON>an</h4>
  </div>
  <div class="col-md mt-4">
    <div class="btn-group">
      <button class="btn btn-warning btn-waves-effect" id="buka-side-cppt" status="aktif">Hasil Penunjang dan CPPT</button>
      <button class="btn btn-custom waves-effect" id="buka-side-konsultasi" status="aktif">Konsultasi</button>
    </div>
  </div>
  <div class="col-md-auto mr-auto mt-4">
    <a href="<?= base_url('laporan/ViewAllPengkajian') ?>" class="btn btn-primary waves-effect" role="button" data-toggle="tooltip" data-placement="left" data-original-title="Kembali ke Pencarian Pasien">
      <i class="fa fa-arrow-left"></i> Cari <PERSON>
    </a>
  </div>
</div>
<!-- end page title end breadcrumb -->
<div class="row">
  <!-- col 3 -->
  <div class="col-sm-3" id="sidebar-pasien-laporan">
    <div class="card-box stickyTerbang">
      <div id="tampil-sidebar-pasien-laporan">
        <div class="text-center">
          <?php
          if ($dataPasien['JENIS_KELAMIN'] == 1) {
            ?>
            <img src="<?= base_url('assets/admin/assets/images/users/profile.jpg') ?>" class="rounded-circle thumb-xl img-thumbnail m-b-10" alt="profile-image">
            <?php
          } else {
            ?>
            <img src="<?= base_url('assets/admin/assets/images/users/profile2.jpg') ?>" class="rounded-circle thumb-xl img-thumbnail m-b-10" alt="profile-image">
            <?php
          }
          ?>
          <hr>
          <div class="text-left">
            <table class="table table-borderless" width="100%">
              <tr>
                <td width="45%">
                  <strong class="text-muted font-13 m-b-5">
                    Nama Lengkap
                  </strong>
                </td>
                <td>
                  <p class="text-muted font-13 m-b-5">
                    <?= $dataPasien['NAMAPASIEN'] ?>
                  </p>
                </td>
              </tr>
              <tr>
                <td>
                  <strong class="text-muted font-13 m-b-5">
                    Nomor MR
                  </strong>
                </td>
                <td>
                  <p class="text-muted font-13 m-b-5">
                    <?= $dataPasien['NORM'] ?>
                  </p>
                </td>
              </tr>
              <tr>
                <td>
                  <strong class="text-muted font-13 m-b-5">
                    Tgl. Lahir/Umur
                  </strong>
                </td>
                <td>
                  <p class="text-muted font-13 m-b-5">
                    <?= date('d/m/Y', strtotime($dataPasien['TANGGAL_LAHIR'])) . ' (' . $dataPasien['UMUR'] . ')' ?>
                  </p>
                </td>
              </tr>
              <tr>
                <td>
                  <strong class="text-muted font-13 m-b-5">
                    Jenis Kelamin
                  </strong>
                </td>
                <td>
                  <p class="text-muted font-13 m-b-5">
                    <?= $dataPasien['JENIS_KELAMIN'] == 1 ? "Laki-laki" : "Perempuan" ?>
                  </p>
                </td>
              </tr>
              <tr>
                <td>
                  <strong class="text-muted font-13 m-b-5">
                    No.Telepon
                  </strong>
                </td>
                <td>
                  <p class="text-muted font-13 m-b-5">
                    <?= $dataPasien['tlpn'] ?>
                  </p>
                </td>
              </tr>
            </table>
          </div>
        </div>
      </div>
      <div id="tampil-sidebar-hasil-lab-laporan" class="d-none">
        <ul class="nav nav-tabs nav-justified">
          <li class="nav-item">
            <a href="#cppt-side-laporan" id="menu-cppt-side-laporan" data-toggle="tab" aria-expanded="true" class="nav-link">CPPT</a>
          </li>
          <li class="nav-item">
            <a href="#history-pengkajian-side-laporan" id="menu-history-pengkajian-side-laporan" data-toggle="tab" aria-expanded="true" class="nav-link">Pengkajian Awal</a>
          </li>
          <li class="nav-item">
            <a href="#lab-pk-side-laporan" data-toggle="tab" aria-expanded="false" class="nav-link active">Lab PK</a>
          </li>
          <li class="nav-item">
            <a href="#lab-pa-side-laporan" data-toggle="tab" aria-expanded="true" class="nav-link">Lab PA</a>
          </li>
          <li class="nav-item">
            <a href="#radiologi-side-laporan" data-toggle="tab" aria-expanded="true" class="nav-link">Radiologi</a>
          </li>
          <li class="nav-item">
            <a href="#resep-side-laporan" id="menu-resep-side-laporan" data-toggle="tab" aria-expanded="true" class="nav-link">Resep</a>
          </li>
        </ul>

        <div class="tab-content">
          <div role="tabpanel" class="tab-pane fade show active" id="lab-pk-side-laporan">
            <?php $this->load->view('Pengkajian/hasilLabPK') ?>
          </div>
          <div role="tabpanel" class="tab-pane fade" id="lab-pa-side-laporan">
            <?php $this->load->view('Pengkajian/hasilLabPa') ?>
          </div>
          <div role="tabpanel" class="tab-pane fade" id="radiologi-side-laporan">
            <?php $this->load->view('Pengkajian/hasilRadiologi') ?>
          </div>
          <div role="tabpanel" class="tab-pane fade" id="cppt-side-laporan" style="max-height: calc(100vh - 200px); overflow-y: auto;"></div>
          <div role="tabpanel" class="tab-pane fade" id="resep-side-laporan" style="max-height: calc(100vh - 200px); overflow-y: auto;"></div>
          <div role="tabpanel" class="tab-pane fade" id="history-pengkajian-side-laporan" style="max-height: calc(100vh - 200px); overflow-y: auto;"></div>
        </div>
      </div>
      <div id="tampil-sidebar-konsultasi-laporan"></div>
    </div>
  </div>
  <!-- End col 3 -->

  <!-- col 9 -->
  <div class="col-sm-9" id="bar-pasien-laporan">
    <div class="card-box">
      <ul class="nav nav-tabs text-white">
        <li class="nav-item">
          <a href="#LapPengkajian" data-toggle="tab" aria-expanded="false" class="nav-link active">
            Pengkajian
          </a>
        </li>
        <li class="nav-item">
          <a href="#LapCppt" data-toggle="tab" aria-expanded="false" class="nav-link">
            CPPT
          </a>
        </li>
        <li class="nav-item">
          <a href="#Eresep" data-toggle="tab" aria-expanded="false" class="nav-link">
            E-Resep
          </a>
        </li>
        <li class="nav-item">
          <a href="#LapsummaryList" data-toggle="tab" aria-expanded="false" class="nav-link">
            Summary List
          </a>
        </li>
        <li class="nav-item">
          <a href="#LapKonsul" data-toggle="tab" aria-expanded="false" class="nav-link">
            Konsul
          </a>
        </li>
        <li class="nav-item dropdown">
          <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">Status
            Lokalis
          </a>
          <div class="dropdown-menu">
            <a class="dropdown-item" data-toggle="tab" href="#LapStatusLokalis">
              Status Lokalis Pengkajian
            </a>
            <a class="dropdown-item" data-toggle="tab" href="#LapStatusLokalisCppt">
              Status Lokalis CPPT
            </a>
          </div>
        </li>
        <li class="nav-item dropdown">
          <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">Hasil
            Penunjang
          </a>
          <div class="dropdown-menu">
            <a class="dropdown-item" data-toggle="tab" href="#LapPenunjangPk">
              Lab PK
            </a>
            <a class="dropdown-item" id="lpa" data-toggle="tab" href="#menu-lpa">
              Lab PA
            </a>
            <a class="dropdown-item" data-toggle="tab" href="#LapRadiologi">
              Radiologi
            </a>
          </div>
        </li>
        <li class="nav-item dropdown">
          <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
            Transfer Ruang
          </a>
          <div class="dropdown-menu">
            <a class="dropdown-item" id="fpr" data-toggle="tab" href="#menu-fpr">
              Formulir Perpindahan Pasien Antar Ruang
            </a>
          </div>
        </li>
        <li class="nav-item dropdown">
          <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
            Operasi
          </a>
          <div class="dropdown-menu">
            <a class="dropdown-item" data-toggle="tab" href="#LapPraOperasi">
              Pra Operasi
            </a>
            <a class="dropdown-item" data-toggle="tab" href="#LapOperasi">
              Laporan Operasi
            </a>
            <a class="dropdown-item" data-toggle="tab" href="#LapSiteMarking">
              Site Marking
            </a>
          </div>
        </li>
        <li class="nav-item">
          <a href="#LapOdonTogram" data-toggle="tab" aria-expanded="false" class="nav-link">
            Odontogram
          </a>
        </li>
        <li class="nav-item dropdown">
          <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">Radio
            Terapi
          </a>
          <div class="dropdown-menu">
            <a class="dropdown-item" data-toggle="tab" href="#LapCtSimulator">
              CT Simulator
            </a>
            <a class="dropdown-item" data-toggle="tab" href="#LapSimulatorInformation">
              Simulator Konvensional
            </a>
            <a class="dropdown-item" data-toggle="tab" href="#LapTreatmentDose">
              Treatment Dose
            </a>
          </div>
        </li>
        <li class="nav-item">
          <a href="#LapPermintaanDiRawat" data-toggle="tab" aria-expanded="false" class="nav-link">
            Permintaan dirawat
          </a>
        </li>
        <li class="nav-item">
          <a href="#resume-medis" data-toggle="tab" aria-expanded="false" class="nav-link resume-medis">
            Resume Medis
          </a>
        </li>
        <li class="nav-item">
          <a href="#LberkasPasien" data-toggle="tab" aria-expanded="false" class="nav-link LberkasPasien">
            Berkas Pasien
          </a>
        </li>
      </ul>
      <div class="tab-content" style="border-color:#40739e">
        <div role="tabpanel" class="tab-pane fade show active" id="LapPengkajian">
          <div class="text-white">
            <div class="row">
              <div class="col-lg-12">
                <div class="form-group table-responsive">
                  <table id="tblLapPengkajian" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">
                    <thead>
                      <tr class="table-tr-custom">
                        <th>RUANGAN</th>
                        <th>TANGGAL KUNJUNGAN</th>
                        <!-- <th>NO KUNJUNGAN</th> -->
                        <th>DPJP</th>
                        <th>VIEW</th>
                        <th>USER PERAWAT</th>
                        <th>USER MEDIS</th>
                      </tr>
                    </thead>
                    <tbody>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div role="tabpanel" class="tab-pane fade" id="LapCppt">
          <div class="text-white">
            <div class="row">
              <div class="col-12">
                <div class="form-group table-responsive">
                  <table id="tbLapCppt" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">
                    <thead>
                      <tr class="table-tr-custom">
                        <th>NO</th>
                        <th>CETAK</th>
                        <th>TANGGAL</th>
                        <th>RUANGAN</th>
                        <th>PROFESI</th>
                        <th>USER</th>
                        <th>DPJP</th>
                      </tr>
                    </thead>
                    <tbody>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- START INC VIEW MENU ERESEP -->
        <div role="tabpanel" class="tab-pane fade" id="Eresep">
          <div class="text-white">
            <?php $this->load->view('Pengkajian/eresep/history') ?>
          </div>
        </div>
        <!-- END INC VIEW MENU ERESEP -->
        <div role="tabpanel" class="tab-pane fade" id="LapsummaryList">
          <div class="text-white">
            <div class="row">
              <div class="col-lg-12">
                <div class="form-group table-responsive">
                  <table id="tblLapSummary" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">
                    <thead>
                      <tr class="table-tr-custom">
                        <th width="5%">NO</th>
                        <th>TANGGAL</th>
                        <th>RUANG ASAL</th>
                        <th>DOKTER</th>
                        <th>VIEW</th>
                      </tr>
                    </thead>
                    <tbody>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
          <div class="modal fade" id="detailLapSummary" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog modal-lg" role="document">
              <div class="modal-content">
                <div id="datSummaryL"></div>
              </div>
            </div>
          </div>
          <div class="modal fade" id="detailLapSLP" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog modal-lg" role="document">
              <div class="modal-content">
                <div id="datSLP"></div>
              </div>
            </div>
          </div>
        </div>
        <div role="tabpanel" class="tab-pane fade" id="LapKonsul">
          <div class="text-white">
            <div class="row">
              <div class="col-lg-12">
                <div class="form-group table-responsive">
                  <table id="tblLapKonsul" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">
                    <thead>
                      <tr class="table-tr-custom">
                        <th>NO</th>
                        <th>TANGGAL</th>
                        <th>DOKTER PENGIRIM</th>
                        <th>TUJUAN</th>
                        <th>SMF</th>
                        <th>DOKTER PENERIMA</th>
                        <th>STATUS</th>
                        <th>VIEW</th>
                      </tr>
                    </thead>
                    <tbody>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div role="tabpanel" class="tab-pane fade" id="LapStatusLokalis">
          <div class="text-white">
            <div class="row">
              <div class="col-lg-12">
                <div class="form-group table-responsive">
                  <h4 class="m-t-0 header-title">History Status Lokalis Pengkajian Medis</h4><br>
                  <table id="tblLapStatusLokalis" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">
                    <thead>
                      <tr class="table-tr-custom">
                        <th width="5%">NO</th>
                        <th>TANGGAL</th>
                        <th>JUDUL</th>
                        <th>CATATAN</th>
                        <th>VIEW</th>
                      </tr>
                    </thead>
                    <tbody>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            <!-- Modal -->
            <div class="modal fade" id="detailLapStatusLokalis" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" data-backdrop="static" data-keyboard="false">
              <div class="modal-dialog modal-xl" role="document">
                <div class="modal-content">
                  <div id="datStatusLok"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div role="tabpanel" class="tab-pane fade" id="LapStatusLokalisCppt">
          <div class="text-white">
            <div class="row">
              <div class="col-lg-12">
                <div class="form-group table-responsive">
                  <h4 class="m-t-0 header-title">History Status Lokalis CPPT</h4><br>
                  <table id="tblLapStatusLokalisCppt" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">
                    <thead>
                      <tr class="table-tr-custom">
                        <th width="5%">NO</th>
                        <th>TANGGAL</th>
                        <th>JUDUL</th>
                        <th>CATATAN</th>
                        <th>VIEW</th>
                      </tr>
                    </thead>
                    <tbody>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            <!-- Modal -->
            <div class="modal fade" id="detailLapStatusLokalisCppt" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" data-backdrop="static" data-keyboard="false">
              <div class="modal-dialog modal-xl" role="document">
                <div class="modal-content">
                  <div id="datStatusLokCppt"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div role="tabpanel" class="tab-pane fade" id="LapPenunjangPk">
          <div class="text-white">
            <div class="row">
              <div class="col-lg-12">
                <div class="form-group table-responsive">
                  <h4 class="m-t-0 header-title">Laporan Penunjang Patologi Klinik</h4><br>
                  <table id="tblLapPenunjangPk" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">
                    <thead>
                      <tr class="table-tr-custom">
                        <th width="5%">NO</th>
                        <th>TANGGAL ORDER</th>
                        <th>NOKUN</th>
                        <th width="10%">VIEW</th>
                      </tr>
                    </thead>
                    <tbody>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            <!-- Modal -->
            <div class="modal fade" id="detailPenunjangPkL" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" data-backdrop="static" data-keyboard="false">
              <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                  <div class="modal-header">
                    <h4 class="card-title">Hasil Lab Pk</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                  </div>
                  <div id="datdetailPenunjangPkL"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div role="tabpanel" class="tab-pane fade" id="menu-lpa">
          <div class="text-white">
            <div id="view-lpa"></div>
          </div>
        </div>
        <div role="tabpanel" class="tab-pane fade" id="menu-fpr">
          <div class="text-white">
            <div id="view-fpr"></div>
          </div>
        </div>
        <div role="tabpanel" class="tab-pane fade" id="LapRadiologi">
          <div class="text-white">
            <div class="row">
              <div class="col-lg-12">
                <div class="form-group table-responsive">
                  <h4 class="m-t-0 header-title">History Radiologi</h4><br>
                  <table id="tblRadiologiL" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">
                    <thead>
                      <tr class="table-tr-custom">
                        <th>NO</th>
                        <th>NAMA TINDAKAN</th>
                        <th>TANGGAL KUNJUNGAN</th>
                        <th>VIEW FOTO & EXPERTISE</th>
                        <th>VIEW FOTO</th>
                        <th>VIEW EXPERTISE</th>
                      </tr>
                    </thead>
                    <tbody>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            <!-- MODAL VIEWFOTOEXPERTISE -->
            <div class="modal fade" id="viewFotoExpertise" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
              <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                  <div class="modal-header">
                    <h4 class="modal-title" id="myModalLabel">View Foto & Experise</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                      <span aria-hidden="true">&times;</span></button>
                  </div>
                  <div class="modal-body">
                    <div id="hasilViewFotoExpertise"></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- MODAL HASILFOTO -->
            <div class="modal fade" id="viewFoto" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
              <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                  <div class="modal-header">
                    <h4 class="modal-title" id="myModalLabel">Hasil Foto</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                      <span aria-hidden="true">&times;</span></button>
                  </div>
                  <div class="modal-body">
                    <div id="hasilFoto"></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- MODAL VIEWEXPERTISE -->
            <div class="modal fade" id="viewExpertise" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
              <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                  <div class="modal-header">
                    <h4 class="modal-title" id="viewExpertise">View Expertise</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                      <span aria-hidden="true">&times;</span></button>
                  </div>
                  <div class="modal-body">
                    <div id="hasilExpertise"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div role="tabpanel" class="tab-pane fade" id="LapPraOperasi">
          <div class="text-white">
            <div class="row">
              <div class="col-lg-12">
                <div class="form-group table-responsive">
                  <table id="tblLapPraOperasi" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">
                    <thead>
                      <tr class="table-tr-custom">
                        <th>NO</th>
                        <th>TANGGAL</th>
                        <th>RUANGAN</th>
                        <th>DPJP</th>
                        <th>OLEH</th>
                        <th>VIEW</th>
                      </tr>
                    </thead>
                    <tbody>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
          <!-- Modal -->
          <div class="modal fade" id="detailLapPraOperasi" role="dialog" tabindex="-1" aria-labelledby="myModalLabel">
            <div class="modal-dialog modal-lg" role="document">
              <div class="modal-content">
                <div class="modal-header">
                  <h4 class="modal-title">
                    Hasil Pra Operasi
                  </h4>
                  <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                  </button>
                </div>
                <div class="modal-body">
                  <div id="hasilLapPraOperasi"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div role="tabpanel" class="tab-pane fade" id="LapOperasi">
          <div class="text-white">
            <div class="row">
              <div class="col-lg-12">
                <div class="form-group table-responsive">
                  <table id="tblLapOperasi" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">
                    <thead>
                      <tr class="table-tr-custom">
                        <th>NO</th>
                        <th>TANGGAL</th>
                        <th>RUANGAN</th>
                        <th>DPJP</th>
                        <th>OLEH</th>
                        <th>VIEW</th>
                      </tr>
                    </thead>
                    <tbody>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
          <!-- Modal -->
          <div class="modal fade" id="detailLapOperasi" role="dialog" tabindex="-1" aria-labelledby="myModalLabel">
            <div class="modal-dialog modal-lg modal-dialog-centered">
              <div class="modal-content" id="hasilLapOperasi"></div>
            </div>
          </div>
        </div>
        <div role="tabpanel" class="tab-pane fade" id="LapSiteMarking">
          <div class="text-white">
            <div class="row">
              <div class="col-lg-12">
                <div class="form-group table-responsive">
                  <table id="tblSiteMarking" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">
                    <thead>
                      <tr class="table-tr-custom">
                        <th>NO</th>
                        <th>TANGGAL</th>
                        <th>JUDUL</th>
                        <th>OLEH</th>
                        <th>VIEW</th>
                      </tr>
                    </thead>
                    <tbody>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
          <!-- Modal -->
          <div class="modal fade" id="detailLapSiteMarking" role="dialog" tabindex="-1" aria-labelledby="myModalLabel">
            <div class="modal-dialog modal-lg" role="document">
              <div class="modal-content">
                <div class="modal-header">
                  <h4 class="modal-title">
                    Hasil Site Marking
                  </h4>
                  <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                  </button>
                </div>
                <div class="modal-body">
                  <div id="hasilSiteMarking"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div role="tabpanel" class="tab-pane fade" id="LapOdonTogram">
          <div class="text-white">
            <div class="row">
              <div class="col-lg-12">
                <div class="form-group table-responsive">
                  <table id="tblOdontogram" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">
                    <thead>
                      <tr class="table-tr-custom">
                        <th>NO</th>
                        <th>TANGGAL</th>
                        <th>RUANGAN</th>
                        <th>OLEH</th>
                        <th>DPJP</th>
                        <th>VIEW</th>
                      </tr>
                    </thead>
                    <tbody>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
          <!-- Modal -->
          <div class="modal fade" id="detailOdontogram" role="dialog" aria-labelledby="myModalLabel">
            <div class="modal-dialog modal-xl" role="document">
              <div class="modal-content">
                <div class="modal-header">
                  <h4 class="modal-title" id="myModalLabel">
                    Hasil Odontogram
                  </h4>
                  <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                  </button>
                </div>
                <div class="modal-body">
                  <div id="hasilOdontogram"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div role="tabpanel" class="tab-pane fade" id="LapCtSimulator">
          <div class="text-white">
            <div class="row">
              <div class="col-lg-12">
                <div class="form-group table-responsive">
                  <table id="tblCtSimulator" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">
                    <thead>
                      <tr class="table-tr-custom">
                        <th width="5%">No</th>
                        <th>Nokun</th>
                        <th>Form Dokter</th>
                        <th>Form Radiografer</th>
                        <th>Form Fisika Medis</th>
                        <th width="10%">Cetak</th>
                        <th width="10%">Status</th>
                      </tr>
                    </thead>
                    <tbody>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- Modal Dokter -->
        <div class="modal fade" id="modalCtDr" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
          <div class="modal-dialog modal-xl">
            <div class="modal-content">
              <div id="detailModalCtDr"></div>
            </div>
          </div>
        </div>

        <!-- Modal Radiografer -->
        <div class="modal fade" id="modalCtRad" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
          <div class="modal-content">
            <div id="detailModalCtRad"></div>
          </div>
        </div>
        </div>

        <!-- Modal Fisika Medis -->
        <div class="modal fade" id="modalCtFis" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
          <div class="modal-dialog modal-xl">
            <div class="modal-content">
              <div id="detailModalCtFis"></div>
            </div>
          </div>
        </div>
        <div role="tabpanel" class="tab-pane fade" id="LapSimulatorInformation">
          <div class="text-white">
            <div class="row">
              <div class="col-lg-12">
                <div class="form-group table-responsive">
                  <table id="tblSimulatorInformation" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">
                    <thead>
                      <tr class="table-tr-custom">
                        <th>NO</th>
                        <th>TANGGAL</th>
                        <th>DOKTER</th>
                        <th>RADIOGRAFER</th>
                        <th>FISIKA MEDIS</th>
                        <th>VIEW</th>
                      </tr>
                    </thead>
                    <tbody>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div role="tabpanel" class="tab-pane fade" id="LapTreatmentDose">
          <div class="text-white">
            <div class="row">
              <div class="col-lg-12">
                <div class="form-group table-responsive">
                  <table id="tblTreatmentDose" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">
                    <thead>
                      <tr class="table-tr-custom">
                        <th>NO</th>
                        <th>TANGGAL</th>
                        <th>DOKTER</th>
                        <th>RADIOGRAFER</th>
                        <th>FISIKA MEDIS</th>
                        <th>VIEW</th>
                      </tr>
                    </thead>
                    <tbody>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div role="tabpanel" class="tab-pane fade" id="LapPermintaanDiRawat">
          <div class="text-white">
            <div class="row">
              <div class="col-lg-12">
                <div class="form-group table-responsive">
                  <table id="tblPermintaanDiRawat" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">
                    <thead>
                      <tr class="table-tr-custom">
                        <th>NO</th>
                        <th>TANGGAL DI RAWAT</th>
                        <th>TANGGAL PERMINTAAN DI RAWAT</th>
                        <th>NOKUN</th>
                        <th>OLEH</th>
                        <th>VIEW</th>
                      </tr>
                    </thead>
                    <tbody>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
          <!-- Modal -->
          <div class="modal fade" id="detailLapPermintaanDiRawat" role="dialog" aria-labelledby="myModalLabel">
            <div class="modal-dialog modal-lg" role="document">
              <div class="modal-content">
                <div class="modal-header">
                  <h4 class="modal-title">
                    Hasil Permintaan Dirawat
                  </h4>
                  <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                  </button>
                </div>
                <div class="modal-body">
                  <div id="hasilPermintaanDiRawat"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div role="tabpanel" class="tab-pane fade" id="resume-medis">
          <div class="text-white">
            <div id="view-resume-medis"></div>
          </div>
        </div>
        <div role="tabpanel" class="tab-pane fade" id="LberkasPasien">
          <div class="text-white">
            <div class="row">
              <div class="col-lg-12">
                <div class="form-group table-responsive">
                  <table id="tblLberkasPasien" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">
                    <thead>
                      <tr class="table-tr-custom">
                        <th>NO</th>
                        <th>TANGGAL</th>
                        <th>FILE</th>
                        <th>NAMA FILE</th>
                        <th>BERKAS</th>
                        <th>VIEW</th>
                      </tr>
                    </thead>
                    <tbody>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
          <!-- Modal -->
          <div class="modal fade" id="detailLapLberkasPasien" role="dialog" aria-labelledby="myModalLabel">
            <div class="modal-dialog modal-lg" role="document">
              <div class="modal-content">
                <div class="modal-body">
                  <div id="hasilLberkasPasien"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- End col 9 -->
</div>

<script>
  $(document).ready(function () {
    // Mulai buka CPPT
    $('#buka-side-cppt').click(function () {
      let statusSideCPPT = $(this).attr('status');
      if (statusSideCPPT == 'aktif') {
        // alert('besar');
        $('#buka-side-cppt').attr('status', 'nonaktif');
        if ($('#buka-side-konsultasi').attr('status') == 'nonaktif') {
          $('#buka-side-konsultasi').attr('status', 'aktif');
          $('#tampil-sidebar-konsultasi-laporan').fadeOut(300, function () {
            $('#tampil-sidebar-konsultasi-laporan').addClass('d-none').removeClass('d-block');
          });
        } else {
          $('#sidebar-pasien-laporan').fadeOut(300, function () {
            // item.toggleClass('oldClass newClass')
            $('#sidebar-pasien-laporan').toggleClass('col-sm-3 col-sm-6').fadeIn(300);
          });
          $('#bar-pasien-laporan').fadeOut(300, function () {
            // item.toggleClass('oldClass newClass')
            $('#bar-pasien-laporan').toggleClass('col-sm-9 col-sm-6').fadeIn(300);
          });
        }
        $('#tampil-sidebar-pasien-laporan').fadeOut(300, function () {
          $('#tampil-sidebar-pasien-laporan').addClass('d-none').removeClass('d-block');
          $('#tampil-sidebar-hasil-lab-laporan').removeClass('d-none').addClass('d-block').fadeIn(300);
        });
      } else if (statusSideCPPT == 'nonaktif') {
        // alert('kecil');
        $('#buka-side-cppt').attr('status', 'aktif');
        $('#sidebar-pasien-laporan').fadeOut(300, function () {
          // item.toggleClass('oldClass newClass')
          $('#sidebar-pasien-laporan').toggleClass('col-sm-6 col-sm-3').fadeIn(300);
        });
        $('#bar-pasien-laporan').fadeOut(300, function () {
          // item.toggleClass('oldClass newClass')
          $('#bar-pasien-laporan').toggleClass('col-sm-6 col-sm-9').fadeIn(300);
        });
        $('#tampil-sidebar-hasil-lab-laporan').fadeOut(300, function () {
          $('#tampil-sidebar-hasil-lab-laporan').addClass('d-none').removeClass('d-block');
          $('#tampil-sidebar-pasien-laporan').removeClass('d-none').addClass('d-block').fadeIn(300);
        });
      }
    });
    // Akhir buka CPPT

    // Mulai list CPPT
    $('#menu-cppt-side-laporan').click(function () {
      $('#cppt-side-laporan').load("<?= base_url() ?>cpptView/<?= $dataPasien['NORM'] ?>");
      $(document).on('change', '.history_cppt_side', function () {
        var id = $(this).val();
        $('#cppt-side-laporan').load("<?= base_url() ?>cpptView/<?= $dataPasien['NORM'] ?>/" + id);
      });
    });
    // Akhir list CPPT

    // Mulai pengkajian awal
    $('#menu-history-pengkajian-side-laporan').click(function () {
      $('#history-pengkajian-side-laporan').load('<?= base_url('PengkajianAwal/historySidebar/' . $dataPasien['NORM']) ?>');
    });
    // Akhir pengkajian awal

    // Mulai resep
    $('#menu-resep-side-laporan').click(function () {
      $('#resep-side-laporan').load("<?= base_url('PengkajianAwal/hasilPenunjangResep/' . $dataPasien['NORM'] . '/' . $dataPasien['NOPEN'] . '/' . $dataPasien['NOKUN']) ?>");
    });
    // Akhir resep

    // Mulai buka konsultasi
    $('#buka-side-konsultasi').click(function () {
      let statusSideKonsultasi = $(this).attr('status');
      if (statusSideKonsultasi == 'aktif') {
        // alert('besar');
        $('#buka-side-konsultasi').attr('status', 'nonaktif');
        if ($('#buka-side-cppt').attr('status') == 'nonaktif') {
          $('#buka-side-cppt').attr('status', 'aktif');
          $('#tampil-sidebar-hasil-lab-laporan').fadeOut(300, function () {
            $('#tampil-sidebar-hasil-lab-laporan').addClass('d-none').removeClass('d-block');
          });
        } else {
          $('#sidebar-pasien-laporan').fadeOut(300, function () {
            // item.toggleClass('oldClass newClass')
            $('#sidebar-pasien-laporan').toggleClass('col-sm-3 col-sm-6').fadeIn(300);
          });
          $('#bar-pasien-laporan').fadeOut(300, function () {
            // item.toggleClass('oldClass newClass')
            $('#bar-pasien-laporan').toggleClass('col-sm-9 col-sm-6').fadeIn(300);
          });
        }

        $.ajax({
          url: "<?= base_url('konsultasi/Konsultasi/sidePane') ?>",
          type: 'POST',
          data: {
            nomr: '<?= $dataPasien['NORM'] ?>',
            ruangTujuan: '<?= $dataPasien['RUANGAN_TUJUAN'] ?>'
          },
          success: function (data) {
            $('#tampil-sidebar-pasien-laporan').addClass('d-none').removeClass('d-block').fadeOut(300);
            $('#tampil-sidebar-konsultasi-laporan').removeClass('d-none').addClass('d-block').html(data).fadeIn(300);
          }
        });
      } else if (statusSideKonsultasi == 'nonaktif') {
        // alert('kecil');
        $('#buka-side-konsultasi').attr('status', 'aktif');
        $('#sidebar-pasien-laporan').fadeOut(300, function () {
          // item.toggleClass('oldClass newClass')
          $('#sidebar-pasien-laporan').toggleClass('col-sm-6 col-sm-3').fadeIn(300);
        });
        $('#bar-pasien-laporan').fadeOut(300, function () {
          // item.toggleClass('oldClass newClass')
          $('#bar-pasien-laporan').toggleClass('col-sm-6 col-sm-9').fadeIn(300);
        });
        $('#tampil-sidebar-konsultasi-laporan').fadeOut(300, function () {
          $('#tampil-sidebar-konsultasi-laporan').addClass('d-none').removeClass('d-block');
          $('#tampil-sidebar-pasien-laporan').removeClass('d-none').addClass('d-block').fadeIn(300);
        });
      }
    });
    // Akhir buka konsultasi

    // Mulai Laboratorium Patologi Anatomi
    let lpa = $('#lpa');
    lpa.click(function () {
      $('#view-lpa').load("<?= base_url('patologiAnatomi/laporan/' . $dataPasien['NORM']) ?>");
    });
    if (lpa.hasClass('active')) {
      $('#view-lpa').load("<?= base_url('patologiAnatomi/laporan/' . $dataPasien['NORM']) ?>");
    }
    // Akhir

    // Mulai Formulir Perpindahan Pasien Antar Ruang
    $('#fpr').click(function () {
      $('#view-fpr').load("<?= base_url('transferRuangan/FormulirPindahRuangan/history/' . $dataPasien['NORM']) ?>");
    });
    if ($('#fpr').hasClass('active')) {
      $('#view-fpr').load("<?= base_url('transferRuangan/FormulirPindahRuangan/history/' . $dataPasien['NORM']) ?>");
    }
    // Akhir Formulir Perpindahan Pasien Antar Ruang
    var statusLogin = "<?= $this->session->userdata('status') ?>";
    if (statusLogin == 1 || statusLogin == 2) {
      var linkUrl = "rekam_medis/rawat_inap/pengkajian/pengkajianRI/Dewasa/datatables";
    } else {
      var linkUrl = "laporan/ViewAllPengkajian/tbLaporanPengkajianAdmin";
    }
    $('#tblLapPengkajian').DataTable({
      "sPaginationType": "full_numbers",
      "responsive": true,
      "bPaginate": true,
      "lengthMenu": [
        [1, 5, 10, 25, 50, 100, -1],
        [1, 5, 10, 25, 50, 100, "All"]
      ],
      "processing": false,
      "bFilter": true,
      "bLengthChange": true,
      "iDisplayLength": 10,
      "ordering": false,
      "order": [],
      'ajax': {
        url: '<?= base_url() ?>' + linkUrl,
        type: "POST",
        data: function (data) {
          data.nomr = '<?= $dataPasien['NORM'] ?>';
        }
      },
      "columns": [{
        "data": 2
      },
      {
        "data": 3
      },
      {
        "data": 4
      },
      {
        "data": 7
      },
      {
        "data": 9
      },
      {
        "data": 8
      }
      ]
    });
    $('#tbLapCppt').DataTable({
      "sPaginationType": "full_numbers",
      "responsive": true,
      "bPaginate": true,
      "lengthMenu": [
        [1, 5, 10, 25, 50, 100, -1],
        [1, 5, 10, 25, 50, 100, "All"]
      ],
      "processing": false,
      "serverSide": true,
      "bFilter": true,
      "bLengthChange": true,
      "iDisplayLength": 5,
      "ordering": false,
      "order": [],
      'ajax': {
        url: '<?= base_url() ?>rekam_medis/rawat_inap/catatanTerintegrasi/Cppt/datatables',
        type: "POST",
        data: function (data) {
          data.nomr = "<?= $dataPasien['NORM'] ?>";
        }
      },
      "columns": [{
        "data": 1
      },
      {
        "data": 3
      },
      {
        "data": 4
      },
      {
        "data": 5
      },
      {
        "data": 6
      },
      {
        "data": 7
      },
      {
        "data": 8
      }
      ]
    });
    $(document).on('click', '#view-cppt', function () {
      var url = $(this).data('url');
      window.open(url, '_blank')
      // $('#modal2').modal('show');
      // $('#modal2 .modal-title').html('View Cetak CPPT');
      // $('#modal2 .modal-body').html('<iframe src="'+url+'" width="100%" height="600"></iframe>');

    });
    $('#tblLapSummary').DataTable({
      'ajax': {
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/historyLapSummaryList',
        data: {
          nomr: <?= $dataPasien['NORM'] ?>
        },
        type: 'POST',
      },
    });
    $('#detailLapSummary').on('show.bs.modal', function (e) {
      var id = $(e.relatedTarget).data('id');

      $.ajax({
        type: 'POST',
        url: '<?= base_url() ?>laporan/Sumarylist/detailSummary',
        data: {
          id: id,
          jenis: 'laporan',
        },
        success: function (data) {
          $('#datSummaryL').html(data);
        }
      });
    });
    $('#detailLapSLP').on('show.bs.modal', function (e) {
      var id = $(e.relatedTarget).data('id');

      $.ajax({
        type: 'POST',
        url: '<?= base_url() ?>laporan/Sumarylist/detailSLP',
        data: {
          id: id
        },
        success: function (data) {
          $('#datSLP').html(data);
        }
      });
    });
    $('#tblLapKonsul').DataTable({
      'ajax': {
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/historyLapKonsul',
        data: {
          nomr: <?= $dataPasien['NORM'] ?>
        },
        type: 'POST',
      },
    });
    $('#tblLapStatusLokalis').DataTable({
      'ajax': {
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/historyLapStatusLokalis',
        data: {
          nomr: <?= $dataPasien['NORM'] ?>
        },
        type: 'POST',
      },
    });
    $('#detailLapStatusLokalis').on('show.bs.modal', function (e) {
      var id = $(e.relatedTarget).data('id');

      $.ajax({
        type: 'POST',
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/detailStatusLokalis',
        data: {
          id: id
        },
        success: function (data) {
          $('#datStatusLok').html(data);
        }
      });
    });
    $('#tblLapStatusLokalisCppt').DataTable({
      'ajax': {
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/historyLapStatusLokalisCppt',
        data: {
          nomr: <?= $dataPasien['NORM'] ?>
        },
        type: 'POST',
      },
    });
    $('#detailLapStatusLokalisCppt').on('show.bs.modal', function (e) {
      var id = $(e.relatedTarget).data('id');

      $.ajax({
        type: 'POST',
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/detailStatusLokalisCppt',
        data: {
          id: id
        },
        success: function (data) {
          $('#datStatusLokCppt').html(data);
        }
      });
    });
    $('#tblLapPenunjangPk').DataTable({
      'ajax': {
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/historyLapPenunjangPk',
        data: {
          nomr: <?= $dataPasien['NORM'] ?>
        },
        type: 'POST',
      },
    });
    $('#detailPenunjangPkL').on('show.bs.modal', function (e) {
      var id = $(e.relatedTarget).data('id');

      $.ajax({
        type: 'POST',
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/modaldetailPenunjangPkL',
        data: {
          id: id
        },
        success: function (data) {
          $('#datdetailPenunjangPkL').html(data);
        }
      });
    });
    $('#tblSitoL').DataTable({
      'ajax': {
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/tblSitoL',
        data: {
          nomr: <?= $dataPasien['NORM'] ?>
        },
        type: 'POST',
      },
    });
    $('#tblHistoL').DataTable({
      'ajax': {
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/tblHistoL',
        data: {
          nomr: <?= $dataPasien['NORM'] ?>
        },
        type: 'POST',
      },
    });
    $('#tblImunoL').DataTable({
      'ajax': {
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/tblImunoL',
        data: {
          nomr: <?= $dataPasien['NORM'] ?>
        },
        type: 'POST',
      },
    });
    $('#detailPenunjangPaSito').on('show.bs.modal', function (e) {
      var id = $(e.relatedTarget).data('id');

      $.ajax({
        type: 'POST',
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/modal_sito',
        data: {
          id: id
        },
        success: function (data) {
          $('#hasilLAB1').html(data);
        }
      });
    });
    $('#detailPenunjangPaHisto').on('show.bs.modal', function (e) {
      var id = $(e.relatedTarget).data('id');

      $.ajax({
        type: 'POST',
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/modal_histo',
        data: {
          id: id
        },
        success: function (data) {
          $('#hasilLAB2').html(data);
        }
      });
    });
    $('#detailPenunjangPaImuno').on('show.bs.modal', function (e) {
      var id = $(e.relatedTarget).data('id');

      $.ajax({
        type: 'POST',
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/modal_Imuno',
        data: {
          id: id
        },
        success: function (data) {
          $('#hasilLAB3').html(data);
        }
      });
    });
    $('#tblRadiologiL').DataTable({
      'ajax': {
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/tblRadiologiL',
        data: {
          nomr: <?= $dataPasien['NORM'] ?>
        },
        type: 'POST',
      },
    });
    $('#viewFotoExpertise').on('show.bs.modal', function (e) {
      var nomr = $(e.relatedTarget).data('nomr');
      var nopen = $(e.relatedTarget).data('nopen');
      var nokun = $(e.relatedTarget).data('nokun');

      $.ajax({
        type: 'POST',
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/viewFotoExpertise',
        data: {
          nomr: nomr,
          nopen: nopen,
          nokun: nokun
        },
        success: function (data) {
          $('#hasilViewFotoExpertise').html(data);
        }
      });
    });
    $('#viewFoto').on('show.bs.modal', function (e) {
      var nomr = $(e.relatedTarget).data('nomr');

      $.ajax({
        type: 'POST',
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/viewFoto',
        data: {
          nomr: nomr
        },
        success: function (data) {
          $('#hasilFoto').html(data);
        }
      });
    });
    $('#viewExpertise').on('show.bs.modal', function (e) {
      var nokun = $(e.relatedTarget).data('nokun');

      $.ajax({
        type: 'POST',
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/viewExpertise',
        data: {
          nokun: nokun
        },
        success: function (data) {
          $('#hasilExpertise').html(data);
        }
      });
    });
    $('#tblLapPraOperasi').DataTable({
      'ajax': {
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/historyLapPraOperasi',
        data: {
          nomr: <?= $dataPasien['NORM'] ?>
        },
        type: 'POST',
      }
    });
    $('#detailLapPraOperasi').on('show.bs.modal', function (e) {
      var nokun = $(e.relatedTarget).data('id');
      $.ajax({
        type: 'POST',
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/keteranganLapPraOperasi',
        data: {
          nokun: nokun
        },
        success: function (data) {
          $('#hasilLapPraOperasi').html(data);
        }
      });
    });
    $('#tblLapOperasi').DataTable({
      'ajax': {
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/historyLapOperasi',
        data: {
          nomr: <?= $dataPasien['NORM'] ?>
        },
        type: 'POST',
      }
    });
    $('#detailLapOperasi').on('show.bs.modal', function (e) {
      var id = $(e.relatedTarget).data('id');
      $.ajax({
        type: 'POST',
        url: '<?= base_url() ?>operasi/FormLaporanOperasi/index',
        data: {
          id: id,
          jenis: 'laporan',
        },
        success: function (data) {
          $('#hasilLapOperasi').html(data);
        }
      });
    });
    $('#tblSiteMarking').DataTable({
      'ajax': {
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/tblHistorySiteMarking',
        data: {
          nomr: <?= $dataPasien['NORM'] ?>
        },
        type: 'POST',
      }
    });
    $('#detailLapSiteMarking').on('show.bs.modal', function (e) {
      var id = $(e.relatedTarget).data('id');
      $.ajax({
        type: 'POST',
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/keteranganSm',
        data: {
          id: id
        },
        success: function (data) {
          $('#hasilSiteMarking').html(data);
        }
      });
    });
    $('#tblOdontogram').DataTable({
      'ajax': {
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/tblHistoryOdontogram',
        data: {
          nomr: <?= $dataPasien['NORM'] ?>
        },
        type: 'POST',
      }
    });
    $('#detailOdontogram').on('show.bs.modal', function (e) {
      var id = $(e.relatedTarget).data('id');
      $.ajax({
        type: 'POST',
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/keteranganOdontogram',
        data: {
          id: id
        },
        success: function (data) {
          $('#hasilOdontogram').html(data);
        }
      });
    });
    $('#tblCtSimulator').DataTable({
      'ajax': {
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/tblCtSimulator',
        data: {
          nomr: <?= $dataPasien['NORM'] ?>, nokun: <?= $dataPasien['NOKUN'] ?>
        },
        type: 'POST',
      }
    });
    $('#modalCtDr').on('show.bs.modal', function (e) {
      var id = $(e.relatedTarget).data('id');
      $.ajax({
        type: 'POST',
        url: '<?php echo base_url() ?>radioterapi/ct_simulator/modalCtDr',
        data: {id: id},
        success: function (data) {
          $('#detailModalCtDr').html(data);
        }
      });
    });

    $('#modalCtRad').on('show.bs.modal', function (e) {
        var button = $(e.relatedTarget); // Ambil elemen yang memicu modal
        var id = button.data('id'); // Ambil data-id
        var idctdr = button.data('idctdr'); // Ambil data-idctdr

        $.ajax({
            type: 'POST',
            url: '<?php echo base_url('radioterapi/ct_simulator/modalCtRad') ?>',
            data: {
              id: id, 
              idctdr: idctdr,
              nokun: '<?=$getNomr['NOKUN']?>'
            }, // Kirim kedua data ke backend
            success: function (data) {
                $('#detailModalCtRad').html(data);
            }
        });
    });


    $('#modalCtFis').on('show.bs.modal', function (e) {
      var button = $(e.relatedTarget); // Ambil elemen yang memicu modal
      var id = button.data('id'); // Ambil data-id
      var idctdr = button.data('idctdr');
      $.ajax({
        type: 'POST',
        url: '<?php echo base_url() ?>radioterapi/ct_simulator/modalCtFis',
        data: {
          id: id,
          idctdr: idctdr,
          nokun: '<?=$getNomr['NOKUN']?>'
        },
        success: function (data) {
          $('#detailModalCtFis').html(data);
        }
      });
    });
    
    $('#tblSimulatorInformation').DataTable({
      'ajax': {
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/tblSimulatorInformation',
        data: {
          nomr: <?= $dataPasien['NORM'] ?>
        },
        type: 'POST',
      }
    });

    $('#tblTreatmentDose').DataTable({
      'ajax': {
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/tblTreatmentDose',
        data: {
          nomr: <?= $dataPasien['NORM'] ?>
        },
        type: 'POST',
      }
    });

    $('#tblPermintaanDiRawat').DataTable({
      'ajax': {
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/historyLapPermintaanDiRawat',
        data: {
          nomr: <?= $dataPasien['NORM'] ?>
        },
        type: 'POST',
      }
    });
    $('#detailLapPermintaanDiRawat').on('show.bs.modal', function (e) {
      var id = $(e.relatedTarget).data('id');
      $.ajax({
        type: 'POST',
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/keteranganPermintaanDiRawat',
        data: {
          id: id
        },
        success: function (data) {
          $('#hasilPermintaanDiRawat').html(data);
        }
      });
    });

    $('#tblLberkasPasien').DataTable({
      'ajax': {
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/historyLapLberkasPasien',
        data: {
          nomr: <?= $dataPasien['NORM'] ?>
        },
        type: 'POST',
      }
    });
    $('#detailLapLberkasPasien').on('show.bs.modal', function (e) {
      var id = $(e.relatedTarget).data('id');
      $.ajax({
        type: 'POST',
        url: '<?= base_url() ?>laporan/ViewAllPengkajian/getFileEmr',
        data: {
          id: id
        },
        success: function (data) {
          $('#hasilLberkasPasien').html(data);
        }
      });
    });

    // Resume Medis
    $('.resume-medis').click(function () {
      $('#view-resume-medis').load('<?= base_url() ?>ResumeMedis/index/<?= $dataPasien['NORM'] ?>');
    });
    if ($('.resume-medis').hasClass('active')) {
      $('#view-resume-medis').load('<?= base_url() ?>ResumeMedis/index/<?= $dataPasien['NORM'] ?>');
    }
  });
</script>
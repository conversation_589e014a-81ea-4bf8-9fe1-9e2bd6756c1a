<?php
defined('BASEPATH') or exit('No direct script access allowed');

class IntOpeRi extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array(
      'masterModel',
      'pengkajianAwalModel',
      'rekam_medis/rawat_inap/pengkajian/pengkajianRI/DewasaModel',
      'rekam_medis/MedisModel',
      'rekam_medis/rawat_inap/operasi/IntOpeRiModel'
    ));
  }

  public function index(){
  	$norm = $this->uri->segment(6);
    $nopen = $this->uri->segment(7);
    $nokun = $this->uri->segment(8);
    $cekDataAkhir = $this->IntOpeRiModel->cekDataAkhir($nokun);
    $data = array(
      'nopen' => $nopen,
      'norm' => $norm,
      'nokun' => $nokun,
      'cekDataAkhir' => $cekDataAkhir,
      'getNomr' => $this->pengkajianAwalModel->getNomr($nokun),
      'historyIntraOperasi' => $this->IntOpeRiModel->historyIntraOperasi($norm),
      'ruangOk' => $this->masterModel->referensi(1318),
      'jenisOperasi' => $this->masterModel->referensi(1319),
      'altMedik' => $this->masterModel->referensi(1320),
      'tmptPemasangan' => $this->masterModel->referensi(1321),
      'mesinCauter' => $this->masterModel->referensi(1322),
      'monopolar' => $this->masterModel->referensi(1323),
      'negPad' => $this->masterModel->referensi(1324),
      'tmpNegPad' => $this->masterModel->referensi(1325),
      'konKul' => $this->masterModel->referensi(1326),
      'pemMes' => $this->masterModel->referensi(1327),
      'TipePem' => $this->masterModel->referensi(1328),
      'menghitungKasa' => $this->masterModel->referensi(1329),
      'mengaturPosisi' => $this->masterModel->referensi(1330),
      'memasangTali' => $this->masterModel->referensi(1331),
      'alatTurPosisi' => $this->masterModel->referensi(1332),
      'pemakaianTour' => $this->masterModel->referensi(1333),
      'lenganKananKiri' => $this->masterModel->referensi(1334),
      'kakiKananKiri' => $this->masterModel->referensi(1335),
      'pemasanganKateter' => $this->masterModel->referensi(1336),
      'diruanganDiok' => $this->masterModel->referensi(1337),
      'vaginalToilet' => $this->masterModel->referensi(1338),
      'pemasanganTampon' => $this->masterModel->referensi(1339),
      'desinfeksiKulit' => $this->masterModel->referensi(1340),
      'periksaVC' => $this->masterModel->referensi(1341),
      'periksaVCHasil' => $this->masterModel->referensi(1342),
      'pemakaianImplant' => $this->masterModel->referensi(1343),
      'pencucianLuka' => $this->masterModel->referensi(1344),
      'penggunaanInsuflator' => $this->masterModel->referensi(1346),
      'lukaOperasi' => $this->masterModel->referensi(1347),
      'drainLuka' => $this->masterModel->referensi(1348),
      'drainLukaJenis' => $this->masterModel->referensi(1349),
      'vacuum' => $this->masterModel->referensi(1350),
      'kassaDanJarum' => $this->masterModel->referensi(1351),
      'tindakanRontgen' => $this->masterModel->referensi(1352),
      'balutanLuka' => $this->masterModel->referensi(1354),
      'balutanLukaJenis' => $this->masterModel->referensi(1355),
      'padPostop' => $this->masterModel->referensi(1356),
      'periksaSpesimen' => $this->masterModel->referensi(1357),
      'periksaSpesimenShow' => $this->masterModel->referensi(1358),
      'ruangKe' => $this->masterModel->referensi(1359),
      'masKepIntraOperasi' => $this->masterModel->referensi(1706),
      'listPerawat' => $this->masterModel->listPerawat(),
    );
    $this->load->view('rekam_medis/rawat_inap/operasi/intraOperasi/intOpeView', $data);
  }

  ///////////// Modal view hasil intra operasi ////////////////
  public function viewEditIntraOperasi()
  {
    $idintr = $this->input->post('idintr');
    $getIntraOperasi = $this->IntOpeRiModel->getIntraOperasi($idintr);
    $getMasKep = $this->IntOpeRiModel->getMasKep($idintr);

    $data = array(
      'idintr' => $idintr,
      'getIntraOperasi' => $getIntraOperasi,
      'getMasKep' => $getMasKep,
      'ruangOk' => $this->masterModel->referensi(1318),
      'jenisOperasi' => $this->masterModel->referensi(1319),
      'altMedik' => $this->masterModel->referensi(1320),
      'tmptPemasangan' => $this->masterModel->referensi(1321),
      'mesinCauter' => $this->masterModel->referensi(1322),
      'monopolar' => $this->masterModel->referensi(1323),
      'negPad' => $this->masterModel->referensi(1324),
      'tmpNegPad' => $this->masterModel->referensi(1325),
      'konKul' => $this->masterModel->referensi(1326),
      'pemMes' => $this->masterModel->referensi(1327),
      'TipePem' => $this->masterModel->referensi(1328),
      'menghitungKasa' => $this->masterModel->referensi(1329),
      'mengaturPosisi' => $this->masterModel->referensi(1330),
      'memasangTali' => $this->masterModel->referensi(1331),
      'masKepIntraOperasi' => $this->masterModel->referensi(1706),
      'alatTurPosisi' => $this->masterModel->referensi(1332),
      'pemakaianTour' => $this->masterModel->referensi(1333),
      'lenganKananKiri' => $this->masterModel->referensi(1334),
      'kakiKananKiri' => $this->masterModel->referensi(1335),
      'pemasanganKateter' => $this->masterModel->referensi(1336),
      'diruanganDiok' => $this->masterModel->referensi(1337),
      'vaginalToilet' => $this->masterModel->referensi(1338),
      'pemasanganTampon' => $this->masterModel->referensi(1339),
      'desinfeksiKulit' => $this->masterModel->referensi(1340),
      'periksaVC' => $this->masterModel->referensi(1341),
      'periksaVCHasil' => $this->masterModel->referensi(1342),
      'pemakaianImplant' => $this->masterModel->referensi(1343),
      'pencucianLuka' => $this->masterModel->referensi(1344),
      'penggunaanInsuflator' => $this->masterModel->referensi(1346),
      'lukaOperasi' => $this->masterModel->referensi(1347),
      'drainLuka' => $this->masterModel->referensi(1348),
      'drainLukaJenis' => $this->masterModel->referensi(1349),
      'vacuum' => $this->masterModel->referensi(1350),
      'kassaDanJarum' => $this->masterModel->referensi(1351),
      'tindakanRontgen' => $this->masterModel->referensi(1352),
      'balutanLuka' => $this->masterModel->referensi(1354),
      'balutanLukaJenis' => $this->masterModel->referensi(1355),
      'padPostop' => $this->masterModel->referensi(1356),
      'periksaSpesimen' => $this->masterModel->referensi(1357),
      'periksaSpesimenShow' => $this->masterModel->referensi(1358),
      'ruangKe' => $this->masterModel->referensi(1359),
      'listPerawat' => $this->masterModel->listPerawat(),
    );

    $this->load->view('rekam_medis/rawat_inap/operasi/intraOperasi/editIntOpeView', $data);
  }

  ///////////// Modal pemantauan kondisi fisiologis ////////////////
  public function viewPemKonIntraOperasi()
  {
    $idintr = $this->input->post('idintr');
    $nokun = $this->input->post('nokun');
    $historyPemantauanIntraOperasi = $this->IntOpeRiModel->historyPemantauanIntraOperasi($idintr);

    $data = array(
      'idintr' => $idintr,
      'nokun' => $nokun,
      'historyPemantauanIntraOperasi' => $historyPemantauanIntraOperasi,
    );

    $this->load->view('rekam_medis/rawat_inap/operasi/intraOperasi/pemantauanKondisi', $data);
  }

  public function simpanPemantauanIntraOperasi()
  {
    $post = $this->input->post();

    $data = array(
      'nokun' => isset($post['nokun']) ? $post['nokun'] : "",
      'id_anestesi_lokal' => isset($post['id_intra']) ? $post['id_intra'] : "",
      'pukul' => isset($post['jmPemantauanIntraOperasi']) ? $post['jmPemantauanIntraOperasi'] : "",
      'td_1' => isset($post['tekanan_darah_1']) ? $post['tekanan_darah_1'] : "",
      'td_2' => isset($post['tekanan_darah_2']) ? $post['tekanan_darah_2'] : "",
      'n' => isset($post['nadi']) ? $post['nadi'] : "",
      'rr' => isset($post['rr']) ? $post['rr'] : "",
      'sp' => isset($post['spo2']) ? $post['spo2'] : "",
      'ekg' => isset($post['ekg']) ? $post['ekg'] : "",
      'oleh' => isset($post['oleh']) ? $post['oleh'] : "",
    );

    $this->db->insert('keperawatan.tb_intraoperasi_pemantauan', $data);
  }

  public function batalPemantauan()
  {
    $post = $this->input->post();
    $idpemantauan = $this->input->post('idpemantauan');

    $data = array(
      'status' => 0
    );

    $this->db->where('id', $idpemantauan);
    $this->db->update('keperawatan.tb_intraoperasi_pemantauan', $data);
  }

  public function simpanIntOperasi($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        $post = $this->input->post();

        $dataIntraOperasi = array(         
          'nokun' => isset($post['nokun']) ? $post['nokun'] : "",
          'tanggal_operasi' => date('Y-m-d',strtotime($post['tglOperasiIntraOperasiLm'])),
          'masuk_ok_jam' => isset($post['mskJamIntraOperasiLm']) ? $post['mskJamIntraOperasiLm'] : "",
          'ruang_ok' => isset($post['ruangOkIntraOperasiLm']) ? $post['ruangOkIntraOperasiLm'] : "",
          'desk_ruang_ok' => isset($post['deskRuangOkIntraOperasiLm']) ? $post['deskRuangOkIntraOperasiLm'] : "",
          'jenis_operasi' => isset($post['jenisOperasiIntraOperasiLm']) ? $post['jenisOperasiIntraOperasiLm'] : "",
          'cek_set_instrumen' => isset($post['setInsIntraOperasiLm']) ? 1 : 0,
          'sebutkan_set_instrumen' => isset($post['setInsYaIntraOperasiLm']) ? $post['setInsYaIntraOperasiLm'] : "",
          'tanggal_set_instrumen' => date('Y-m-d',strtotime($post['tglExpIntraOperasiLm'])),
          'alat_medik_set_instrumen' => isset($post['altMedikIntraOperasiLm']) ? json_encode($post['altMedikIntraOperasiLm']) : "",
          'desk_alat_medik_set_instrumen' => isset($post['altMedLainIntraOperasiLm']) ? $post['altMedLainIntraOperasiLm'] : "",
          'infus_iv' => isset($post['tmptPemasanganIntraOperasiLm']) ? json_encode($post['tmptPemasanganIntraOperasiLm']) : "",
          'desk_infus_iv' => isset($post['deskTmpPmsIntraOperasiLm']) ? $post['deskTmpPmsIntraOperasiLm'] : "",
          'mesin_cauter' => isset($post['mesinCauterIntraOperasiLm']) ? $post['mesinCauterIntraOperasiLm'] : "",
          'merek_mesin' => isset($post['merekIntraOperasiLm']) ? $post['merekIntraOperasiLm'] : "",
          'monopolar_bipolar' => isset($post['monopolarBipIntraOperasiLm']) ? json_encode($post['monopolarBipIntraOperasiLm']) : "",
          'negative_pad_dipakai' => isset($post['negPadIntraOperasiLm']) ? $post['negPadIntraOperasiLm'] : "",
          'tempat_negative_pad' => isset($post['tmpNegPadIntraOperasiLm']) ? json_encode($post['tmpNegPadIntraOperasiLm']) : "",
          'desk_tempat_negative_pad' => isset($post['deskTmpNegPadIntraOperasiLm']) ? $post['deskTmpNegPadIntraOperasiLm'] : "",
          'kondisi_kulit_pre_operasi' => isset($post['konKulIntraOperasiLm']) ? $post['konKulIntraOperasiLm'] : "",
          'pemakaian_mesin_penghangat' => isset($post['pemMesIntraOperasiLm']) ? $post['pemMesIntraOperasiLm'] : "",
          'merek_pemakaian_mesin' => isset($post['deskMerekPemMesinIntraOperasiLm']) ? $post['deskMerekPemMesinIntraOperasiLm'] : "",
          'setting_pemakaian_meisn' => isset($post['deskSuhuPemMesinIntraOperasiLm']) ? $post['deskSuhuPemMesinIntraOperasiLm'] : "",
          'tipe_pembiusan' => isset($post['tipePemIntraOperasiLm']) ? json_encode($post['tipePemIntraOperasiLm']) : "",
          'jam_mulai_induksi' => isset($post['jmInduksiIntraOperasiLm']) ? $post['jmInduksiIntraOperasiLm'] : "",
          'menghitung_jumlah_pemakaian' => isset($post['menghitungKasaIntraOperasiLm']) ? $post['menghitungKasaIntraOperasiLm'] : "",
          'mengatur_posisi_pasien' => isset($post['mengaturPosisiIntraOperasiLm']) ? json_encode($post['mengaturPosisiIntraOperasiLm']) : "",
          'desk_mengatur_posisi_pasien' => isset($post['deskMengaturPosisiIntraOperasiLm']) ? $post['deskMengaturPosisiIntraOperasiLm'] : "",
          'memasang_tali_pengaman' => isset($post['memasangTaliIntraOperasiLm']) ? $post['memasangTaliIntraOperasiLm'] : "",
          'alat_pengatur_posisi' => isset($post['alatTurPosisiIntraOperasiLm']) ? json_encode($post['alatTurPosisiIntraOperasiLm']) : "",
          'desk_alat_pengatur_posisi' => isset($post['deskAlatTurPosisiIntraOperasiLm']) ? $post['deskAlatTurPosisiIntraOperasiLm'] : "",
          'pemakaian_tourniquet' => isset($post['pemakaianTourIntraOperasiLm']) ? $post['pemakaianTourIntraOperasiLm'] : "",
          'lokasi_lengan' => isset($post['lenganKananKiriIntraOperasiLm']) ? $post['lenganKananKiriIntraOperasiLm'] : "",
          'lokasi_kaki' => isset($post['kakiKananKiriIntraOperasiLm']) ? $post['kakiKananKiriIntraOperasiLm'] : "",
          'tekanan_lengan' => isset($post['deskTekananLenganIntraOperasiLm']) ? $post['deskTekananLenganIntraOperasiLm'] : "",
          'tekanan_kaki' => isset($post['deskTekananKakiIntraOperasiLm']) ? $post['deskTekananKakiIntraOperasiLm'] : "",
          'jam_mulai_lengan' => isset($post['jmMulaiLenganIntraOperasiLm']) ? $post['jmMulaiLenganIntraOperasiLm'] : "",
          'jam_mulai_kaki' => isset($post['jmMulaiKakiIntraOperasiLm']) ? $post['jmMulaiKakiIntraOperasiLm'] : "",
          'jam_pengempesan_lengan' => isset($post['jmPengemLenganIntraOperasiLm']) ? $post['jmPengemLenganIntraOperasiLm'] : "",
          'jam_pengempesan_kaki' => isset($post['jmPengemKakiIntraOperasiLm']) ? $post['jmPengemKakiIntraOperasiLm'] : "",
          'pemasangan_kateter' => isset($post['pemasanganKateterIntraOperasiLm']) ? $post['pemasanganKateterIntraOperasiLm'] : "",
          'ruang_pemasangan_kateter' => isset($post['diruanganDiokIntraOperasiLm']) ? $post['diruanganDiokIntraOperasiLm'] : "",
          'no_kateter' => isset($post['noKateterIntraOperasiLm']) ? $post['noKateterIntraOperasiLm'] : "",
          'isi_balon' => isset($post['isiBalonIntraOperasiLm']) ? $post['isiBalonIntraOperasiLm'] : "",
          'vaginal_toilet' => isset($post['vaginalToiletIntraOperasiLm']) ? $post['vaginalToiletIntraOperasiLm'] : "",
          'pemasangan_tampon' => isset($post['pemasanganTamponIntraOperasiLm']) ? $post['pemasanganTamponIntraOperasiLm'] : "",
          'desinfeksi_kulit_area' => isset($post['desinfeksiKulitIntraOperasiLm']) ? json_encode($post['desinfeksiKulitIntraOperasiLm']) : "",
          'desk_desinfeksi_kulit_area' => isset($post['deskDesinfeksiKulitIntraOperasiLm']) ? $post['deskDesinfeksiKulitIntraOperasiLm'] : "",
          // 'cek_jam_bius' => isset($post['cekJamBiusIntraOperasiLm']) ? 1 : 0,
          // 'jam_bius' => isset($post['jmBiusIntraOperasiLm']) ? $post['jmBiusIntraOperasiLm'] : "",
          'jam_incisi' => isset($post['jmIncisiIntraOperasiLm']) ? $post['jmIncisiIntraOperasiLm'] : "",
          'pemeriksaan_vc' => isset($post['periksaVCIntraOperasiLm']) ? $post['periksaVCIntraOperasiLm'] : "",
          'jaringan_pemeriksaan_vc' => isset($post['deskPeriksaVCIntraOperasiLm']) ? $post['deskPeriksaVCIntraOperasiLm'] : "",
          'jam_pemeriksaan_vc' => isset($post['jmPeriksaVCIntraOperasiLm']) ? $post['jmPeriksaVCIntraOperasiLm'] : "",
          'hasil_pemeriksaan_vc' => isset($post['periksaVCHasilIntraOperasiLm']) ? $post['periksaVCHasilIntraOperasiLm'] : "",
          'hasil_pemeriksaan_vc_desk' => isset($post['deskPeriksaVCHasilIntraOperasiLm']) ? $post['deskPeriksaVCHasilIntraOperasiLm'] : "",
          'pemakaian_implant' => isset($post['pemakaianImplantIntraOperasiLm']) ? $post['pemakaianImplantIntraOperasiLm'] : "",
          'jenis_pemakaian_implant' => isset($post['deskPemakaianImplantIntraOperasiLm']) ? $post['deskPemakaianImplantIntraOperasiLm'] : "",
          'pencucian_luka_operasi' => isset($post['pencucianLukaIntraOperasiLm']) ? json_encode($post['pencucianLukaIntraOperasiLm']) : "",
          'desk_pencucian_luka_operasi' => isset($post['deskPencucianLukaIntraOperasiLm']) ? $post['deskPencucianLukaIntraOperasiLm'] : "",
          'cek_irigasi_cairan' => isset($post['cekIrigasiIntraOperasiLm']) ? 1 : 0,
          'nacl_irigasi_cairan' => isset($post['deskNaclIntraOperasiLm']) ? $post['deskNaclIntraOperasiLm'] : "",
          'aquabidest_irigasi_cairan' => isset($post['deskAquaIntraOperasiLm']) ? $post['deskAquaIntraOperasiLm'] : "",
          'tekanan_irigasi_cairan' => isset($post['deskTekananIntraOperasiLm']) ? $post['deskTekananIntraOperasiLm'] : "",
          'lainnya_irigasi_cairan' => isset($post['deskLainnyaIriIntraOperasiLm']) ? $post['deskLainnyaIriIntraOperasiLm'] : "",
          'penggunaan_insuflator' => isset($post['penggunaanInsuflatorIntraOperasiLm']) ? $post['penggunaanInsuflatorIntraOperasiLm'] : "",
          'tekanan_penggunaan_insuflator' => isset($post['deskPengTekananIntraOperasiLm']) ? $post['deskPengTekananIntraOperasiLm'] : "",
          'jumlah_penggunaan_insuflator' => isset($post['deskJmlGasIntraOperasiLm']) ? $post['deskJmlGasIntraOperasiLm'] : "",
          'letak_luka_operasi' => isset($post['letakLukaIntraOperasiLm']) ? $post['letakLukaIntraOperasiLm'] : "",
          'panjang_luka_operasi' => isset($post['panjangLukaIntraOperasiLm']) ? $post['panjangLukaIntraOperasiLm'] : "",
          'jahitan_luka_operasi' => isset($post['jahitanKulitIntraOperasiLm']) ? $post['jahitanKulitIntraOperasiLm'] : "",
          'kondisi_luka_operasi' => isset($post['lukaOperasiIntraOperasiLm']) ? $post['lukaOperasiIntraOperasiLm'] : "",
          'drain_luka' => isset($post['drainLukaIntraOperasiLm']) ? $post['drainLukaIntraOperasiLm'] : "",
          'jenis_drain_luka' => isset($post['drainLukaJenisIntraOperasiLm']) ? json_encode($post['drainLukaJenisIntraOperasiLm']) : "",
          'ngt_drain_luka' => isset($post['deskCekNgtIntraOperasiLm']) ? $post['deskCekNgtIntraOperasiLm'] : "",
          'vacum_drain_luka' => isset($post['vacuumIntraOperasiLm']) ? $post['vacuumIntraOperasiLm'] : "",
          'haemovack_drain_luka' => isset($post['deskCekHaemovackIntraOperasiLm']) ? $post['deskCekHaemovackIntraOperasiLm'] : "",
          'wsd_drain_luka' => isset($post['deskCekWSDIntraOperasiLm']) ? $post['deskCekWSDIntraOperasiLm'] : "",
          'lainnya_drain_luka' => isset($post['deskCekLainnyaIntraOperasiLm']) ? $post['deskCekLainnyaIntraOperasiLm'] : "",
          'perhitungan_lengkap' => isset($post['kassaDanJarumIntraOperasiLm']) ? $post['kassaDanJarumIntraOperasiLm'] : "",
          'tindakan_perhitungan_lengkap' => isset($post['tindakanRontgenIntraOperasiLm']) ? $post['tindakanRontgenIntraOperasiLm'] : "",
          'masalah_kep_intraoperasi' => isset($post['masKepIntraOperasiLm']) ? $post['masKepIntraOperasiLm'] : "",
          'operasi_selesai_jam' => isset($post['jmSelesaiOperasiIntraOperasiLm']) ? $post['jmSelesaiOperasiIntraOperasiLm'] : "",
          'balutan_luka' => isset($post['balutanLukaIntraOperasiLm']) ? $post['balutanLukaIntraOperasiLm'] : "",
          'jenis_balutan_luka' => isset($post['balutanLukaJenisIntraOperasiLm']) ? $post['balutanLukaJenisIntraOperasiLm'] : "",
          'lainnya_balutan_luka' => isset($post['deskJenisBalutanLukaIntraOperasiLm']) ? $post['deskJenisBalutanLukaIntraOperasiLm'] : "",
          'jumlah_perdarahan' => isset($post['perdarahanJumlahIntraOperasiLm']) ? $post['perdarahanJumlahIntraOperasiLm'] : "",
          'jumlah_urine' => isset($post['urineJumlahIntraOperasiLm']) ? $post['urineJumlahIntraOperasiLm'] : "",
          'jumlah_ascites' => isset($post['ascitesJumlahIntraOperasiLm']) ? $post['ascitesJumlahIntraOperasiLm'] : "",
          'jumlah_lainnya' => isset($post['lainnyaJumlahIntraOperasiLm']) ? $post['lainnyaJumlahIntraOperasiLm'] : "",
          'kondisi_kulit_tempat' => isset($post['padPostopIntraOperasiLm']) ? $post['padPostopIntraOperasiLm'] : "",
          'pemeriksaan_spesimen' => isset($post['periksaSpesimenIntraOperasiLm']) ? $post['periksaSpesimenIntraOperasiLm'] : "",
          'pilih_pemeriksaan_spesimen' => isset($post['periksaSpesimenShowIntraOperasiLm']) ? json_encode($post['periksaSpesimenShowIntraOperasiLm']) : "",
          'pilih_jaringan' => isset($post['deskCekPAIntraOperasiLm']) ? $post['deskCekPAIntraOperasiLm'] : "",
          'pilih_sistologi' => isset($post['deskCekSistologiIntraOperasiLm']) ? $post['deskCekSistologiIntraOperasiLm'] : "",
          'pilih_kultur' => isset($post['deskCekKulturIntraOperasiLm']) ? $post['deskCekKulturIntraOperasiLm'] : "",
          'pilih_lainnya' => isset($post['deskCekLainnyaSpesimenIntraOperasiLm']) ? $post['deskCekLainnyaSpesimenIntraOperasiLm'] : "",
          'pilih_hasil_vc' => isset($post['deskCekVcSpesimenIntraOperasiLm']) ? $post['deskCekVcSpesimenIntraOperasiLm'] : "",
          'jam_post_operasi' => isset($post['jmPostOperasiIntraOperasiLm']) ? $post['jmPostOperasiIntraOperasiLm'] : "",
          'ke_post_operasi' => isset($post['ruangKeIntraOperasiLm']) ? $post['ruangKeIntraOperasiLm'] : "",
          'lainnya_post_operasi' => isset($post['deskRuangKeIntraOperasiLm']) ? $post['deskRuangKeIntraOperasiLm'] : "",
          'rencana_post_operasi' => isset($post['rencanaPerawatanIntraOperasiLm']) ? $post['rencanaPerawatanIntraOperasiLm'] : "",
          'perawat_instrumentator_1' => isset($post['perawatIns1IntraOperasiLm']) ? $post['perawatIns1IntraOperasiLm'] : "",
          'perawat_instrumentator_2' => isset($post['perawatIns2IntraOperasiLm']) ? $post['perawatIns2IntraOperasiLm'] : "",
          'perawat_instrumentator_3' => isset($post['perawatIns3IntraOperasiLm']) ? $post['perawatIns3IntraOperasiLm'] : "",
          'perawat_instrumentator_4' => isset($post['perawatIns4IntraOperasiLm']) ? $post['perawatIns4IntraOperasiLm'] : "",
          'perawat_sirkuler_1' => isset($post['perawatSir1IntraOperasiLm']) ? $post['perawatSir1IntraOperasiLm'] : "",
          'perawat_sirkuler_2' => isset($post['perawatSir2IntraOperasiLm']) ? $post['perawatSir2IntraOperasiLm'] : "",
          'oleh' => isset($post['oleh']) ? $post['oleh'] : "",
        );

        // echo "<pre>"; print_r($dataIntraOperasi); echo "</pre>"; exit();

        if (!empty($post['id_intra'])) {
          $this->db->where('tb_intra_operasi.id', $post['id_intra']);
          $this->db->update('keperawatan.tb_intra_operasi', $dataIntraOperasi);

          $dataMasKep = array();
          $indexMasKep = 0;
          if (isset($post['waktuMasIntOpeHasilIntraOperasiLm'])) {
            foreach ($post['waktuMasIntOpeHasilIntraOperasiLm'] as $input) {
              if ($post['waktuMasIntOpeHasilIntraOperasiLm'][$indexMasKep] != "") {
                array_push(
                  $dataMasKep, array(
                    'nokun' => $post['nokun'],
                    'id_intra_operasi' => $post['id_intra'],
                    'waktu_kejadian' => $post['waktuMasIntOpeHasilIntraOperasiLm'][$indexMasKep],
                    'kejadian_masalah' => $post['kejadianMasIntOpeHasilIntraOperasiLm'][$indexMasKep],
                    'tindakan_masalah' => $post['tindakanMasIntOpeHasilIntraOperasiLm'][$indexMasKep],
                    'oleh' => $post['oleh'],
                  )
                );
              }
              $indexMasKep++;
            }
          }
        }else{
          $getIdIntra = $this->IntOpeRiModel->insertIntraOperasi($dataIntraOperasi);

          $dataMasKep = array();
          $indexMasKep = 0;
          if (isset($post['waktuMasIntOpeHasilIntraOperasiLm'])) {
            foreach ($post['waktuMasIntOpeHasilIntraOperasiLm'] as $input) {
              if ($post['waktuMasIntOpeHasilIntraOperasiLm'][$indexMasKep] != "") {
                array_push(
                  $dataMasKep, array(
                    'nokun' => $post['nokun'],
                    'id_intra_operasi' => $getIdIntra,
                    'waktu_kejadian' => $post['waktuMasIntOpeHasilIntraOperasiLm'][$indexMasKep],
                    'kejadian_masalah' => $post['kejadianMasIntOpeHasilIntraOperasiLm'][$indexMasKep],
                    'tindakan_masalah' => $post['tindakanMasIntOpeHasilIntraOperasiLm'][$indexMasKep],
                    'oleh' => $post['oleh'],
                  )
                );
              }
              $indexMasKep++;
            }
          }
        }

        $this->db->trans_begin();
        if (!empty($post['id_intra'])) {
          $this->db->delete('keperawatan.tb_intraoperasi_masalahkeperawatan', array('id_intra_operasi' => $post['id_intra']));
          foreach ($dataMasKep as $key => $value) {
            $this->db->replace('keperawatan.tb_intraoperasi_masalahkeperawatan', $value, 'nokun');
          }
        } else {
          $result = array('status' => 'failed');
          if (isset($post['waktuMasIntOpeHasilIntraOperasiLm'])) {
            $this->db->insert_batch('keperawatan.tb_intraoperasi_masalahkeperawatan', $dataMasKep);
          }
        }

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }

        echo json_encode($result);
      }
    }
  }

}
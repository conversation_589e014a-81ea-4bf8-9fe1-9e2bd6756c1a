<h3><center>PEMANTAUAN ANASTESI LOKAL TINDAKAN GIGI</center></h3><hr>

<ul class="nav nav-tabs nav-justified" style="color:white;">
  <li class="nav-item">
    <a href="#PAG" data-toggle="tab" aria-expanded="false" class="nav-link active" data-target="#tabPAG">
      Form
    </a>
  </li>
  <li class="nav-item">
    <a href="#PAG" data-toggle="tab" aria-expanded="true" class="nav-link" data-target="#tabhistoryPAG">
      History
    </a>
  </li>
</ul>

<div class="tab-content">
  <div role="tabpanel" id="tabPAG" class="tab-pane fade show active">
    <form id="formPAG">
      <input type="hidden" name="PAR" value="save" readonly>
      <input type="hidden" name="norm" value="<?=$getNomr['NORM'];?>">
      <input type="hidden" name="nokun" value="<?=$getNomr['NOKUN'];?>">
        <div class="row jarak">
            <div class="col-md-3">
              <span>1. Tanggal Tindakan</span>
            </div>
            <div class="col-md-9">
                <input type="date" class="form-control" id="tglAnalok" name="tglAnalok">
            </div>
        </div>
        <div class="row jarak">
             <div class="col-md-3">
                 <span>2. Tindakan Operasi</span>
            </div>
            <div class="col-md-9">
                <input type="text" class="form-control" id="tndAnalok" name="tndAnalok">
            </div>
        </div>
        <div class="row jarak">
            <div class="col-md-3">
                <span>3. Keadaan umum pasien pr tindakan:</span>
            </div>
        </div>
        <div class="row jarak">
            <div class="col-md-3">
                <span class="ml-3">Status Mental:</span>
            </div>
            <div class="col-md-9 form-group">
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="mentalAnalok" id="mentalAnalok1" value="1">
                    <label class="form-check-label" for="mentalAnalok1">Komposmetis</label>
                </div>
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="mentalAnalok" id="mentalAnalok2" value="2">
                    <label class="form-check-label" for="mentalAnalok2">Cemas</label>
                </div>
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="mentalAnalok" id="mentalAnalok3" value="3">
                    <label class="form-check-label" for="mentalAnalok3">Agitasi</label>
                </div>
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="mentalAnalok" id="mentalAnalok4" value="4">
                    <label class="form-check-label" for="mentalAnalok4">Mengantuk</label>
                </div>
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="mentalAnalok" id="mentalAnalok5" value="5">
                    <label class="form-check-label" for="mentalAnalok5">Koma</label>
                </div>
                <div class="radio radio-primary form-check-inline" style="width: 50%;">
                    <input class="form-check-input" type="radio" name="mentalAnalok" id="mentalAnalok6" value="6">
                    <label class="form-check-label" for="mentalAnalok6" style="width: 16%">Sebutkan</label>
                    <input class="form-control" type="text" name="mentalAnalokDll" id="mentalAnalokDll" style="display:none;width: 100%">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3"></div>
            <div class="col-md-9">
                <div class="row">
                    <div class="col-md-4">
                        <div class="input-group mb-3">
                            <div class="input-group-prepend">
                                <span class="input-group-text">BB</span>
                            </div>
                            <input type="text" class="form-control" name="bbAnalok">
                            <div class="input-group-append">
                                <span class="input-group-text">kg</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="input-group mb-3">
                            <div class="input-group-prepend">
                                <span class="input-group-text">TB</span>
                            </div>
                            <input type="text" class="form-control" name="TBAnalok">
                            <div class="input-group-append">
                                <span class="input-group-text">cm</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="input-group mb-3">
                            <div class="input-group-prepend">
                                <span class="input-group-text">TD</span>
                            </div>
                            <input type="text" class="form-control" name="tdAnalok">
                            <div class="input-group-append">
                                <span class="input-group-text">mmHg</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="input-group mb-3">
                            <div class="input-group-prepend">
                                <span class="input-group-text">RR</span>
                            </div>
                            <input type="text" class="form-control" name="rrAnalok">
                            <div class="input-group-append">
                                <span class="input-group-text">x/mnt</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="input-group mb-3">
                            <div class="input-group-prepend">
                                <span class="input-group-text">N</span>
                            </div>
                            <input type="text" class="form-control" name="nAnalok">
                            <div class="input-group-append">
                                <span class="input-group-text">x/mnt</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="input-group mb-3">
                            <div class="input-group-prepend">
                                <span class="input-group-text">S</span>
                            </div>
                            <input type="text" class="form-control" name="sAnalok">
                            <div class="input-group-append">
                                <span class="input-group-text">C</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row jarak">
             <div class="col-md-3">
                 <span>4. Laboratorium</span>
            </div>
            <div class="col-md-9">
                <div class="row">
                    <div class="col-md-4">
                        <div class="input-group mb-3">
                            <div class="input-group-prepend">
                                <span class="input-group-text">HB</span>
                            </div>
                            <input type="text" class="form-control" name="hbAnalok">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="input-group mb-3">
                            <div class="input-group-prepend">
                                <span class="input-group-text">L</span>
                            </div>
                            <input type="text" class="form-control" name="lAnalok">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="input-group mb-3">
                            <div class="input-group-prepend">
                                <span class="input-group-text">tr</span>
                            </div>
                            <input type="text" class="form-control" name="trAnalok">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row jarak">
            <div class="col-md-3">
                <span>5. Riwayat alergi:</span>
            </div>
            <div class="col-md-9 form-group">
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="riwayatAnalok" id="riwayatAnalok1" value="1">
                    <label class="form-check-label" for="riwayatAnalok1">Tidak</label>
                </div>
                <div class="radio radio-primary form-check-inline" style="width: 91%;">
                    <input class="form-check-input" type="radio" name="riwayatAnalok" id="riwayatAnalok3" value="2">
                    <label class="form-check-label mr-2" for="riwayatAnalok3" style="width:9%">Ya, yaitu</label>
                    <input class="form-control" type="text" name="riwayatAnalokDll" id="riwayatAnalokDll" style="display:none;width: 100%;">
                </div>
            </div>
        </div>  
        <div class="row jarak">
            <div class="col-md-3">
                <span>6. Obat anastesi yang digunakan:</span>
            </div>
            <div class="col-md-9 form-group">
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="obat" id="obat1" value="1">
                    <label class="form-check-label" for="obat1">Lidocain</label>
                </div>
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="obat" id="obat2" value="2">
                    <label class="form-check-label" for="obat2">Pehacain</label>
                </div>
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="obat" id="obat3" value="3">
                    <label class="form-check-label" for="obat3">Articaine</label>
                </div>
            </div>
        </div>  
        <div class="row jarak">
            <div class="col-md-3">
                <span class="ml-3">Dosis atau jumlah yang digunakan</span>
            </div>
            <div class="col-md-9">
                <input type="text" class="form-control" id="dosisAnalok" name="dosisAnalok">
            </div>
        </div>
        <div class="row jarak">
            <div class="col-md-3">
                <span class="ml-3">Diencerkan:</span>
            </div>
            <div class="col-md-9 form-group">
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="encerAnalok" id="encerAnalok1" value="1">
                    <label class="form-check-label" for="encerAnalok1">Tidak</label>
                </div>
                <div class="radio radio-primary form-check-inline" style="width: 91%;">
                    <input class="form-check-input" type="radio" name="encerAnalok" id="encerAnalok3" value="2">
                    <label class="form-check-label mr-2" for="encerAnalok3" style="width:9%">Ya, dosis</label>
                    <input class="form-control" type="text" name="encerAnalokDll" id="encerAnalokDll" style="display:none;width: 100%;">
                </div>
            </div>
        </div> 
        <div class="row jarak">
             <div class="col-md-3">
                 <span class="ml-3">Lokasi Pemberian</span>
            </div>
            <div class="col-md-3">
                <input type="text" class="form-control" id="lokasiAnalok" name="lokasiAnalok">
            </div>
            <div class="col-md-1">
                 <span>Jam</span>
            </div>
            <div class="col-md-3">
                <input type="time" class="form-control" id="timelokasiAnalok" name="timelokasiAnalok">
            </div>
        </div>

        <div class="row jarak">
             <div class="col-md-3">
                 <span>7. Pemantauan status fsikologi selama tindakan (minimal tiap 5 menit):</span>
            </div>
            <div class="col-md-9">
                <table class="table table-bordered">
                    <tbody>
                        <tr>
                            <td style="width:10%">Jam</td>
                            <?php for($i = 1; $i <= 7; $i++): ?>
                                <td><input type="time" class="form-control" id="pantauJamAnalok<?=$i;?>" name="pantauJamAnalok[]"></td>
                            <?php endfor; ?>
                        </tr>
                        <tr>
                            <td style="width:10%">TD (mmHg)</td>
                            <?php for($i = 1; $i <= 7; $i++): ?>
                                <td><input type="text" class="form-control" id="pantauTdAnalok<?=$i;?>" name="pantauTdAnalok[]"></td>
                            <?php endfor; ?>
                        </tr>
                        <tr>
                            <td style="width:10%">N (x/mnt)</td>
                            <?php for($i = 1; $i <= 7; $i++): ?>
                                <td><input type="text" class="form-control" id="pantauNAnalok<?=$i;?>" name="pantauNAnalok[]"></td>
                            <?php endfor; ?>
                        </tr>
                        <tr>
                            <td style="width:10%">RR (x/mnt)</td>
                            <?php for($i = 1; $i <= 7; $i++): ?>
                                <td><input type="text" class="form-control" id="pantauRRAnalok<?=$i;?>" name="pantauRRAnalok[]"></td>
                            <?php endfor; ?>    
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="row jarak">
            <div class="col-md-3">
                <span>8. Kejadian konversianastesi selama tindakan:</span>
            </div>
            <div class="col-md-9 form-group">
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="kejadianAnalok" id="kejadianAnalok1" value="1">
                    <label class="form-check-label" for="kejadianAnalok1">Tidak</label>
                </div>
                <div class="radio radio-primary form-check-inline" style="width: 30%;">
                    <input class="form-check-input" type="radio" name="kejadianAnalok" id="kejadianAnalok3" value="2">
                    <label class="form-check-label mr-2" for="kejadianAnalok3" style="width:50%">Ya, jenis anastesi</label>
                </div>
            </div>
        </div> 
        <div class="row">
            <div class="col-md-3">
                <span>9. Selesai Tindakan</span>
            </div>
            <div class="col-md-9">
                <div class="input-group mb-3">
                    <div class="input-group-prepend">
                        <span class="input-group-text">Jam</span>
                    </div>
                    <input type="time" class="form-control" name="jamAnalok">
                    <div class="input-group-append">
                        <span class="input-group-text">wib</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="row jarak">
            <div class="col-md-3">
                <span>10. Kondisi pasien saat tindakan:</span>
            </div>
        </div>
        <div class="row jarak">
            <div class="col-md-3">
                <span class="ml-4">Status Mental:</span>
            </div>
            <div class="col-md-9 form-group">
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="kondisiAnalok" id="kondisiAnalok1" value="1">
                    <label class="form-check-label" for="kondisiAnalok1">Komposmetis</label>
                </div>
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="kondisiAnalok" id="kondisiAnalok2" value="2">
                    <label class="form-check-label" for="kondisiAnalok2">Cemas</label>
                </div>
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="kondisiAnalok" id="kondisiAnalok3" value="3">
                    <label class="form-check-label" for="kondisiAnalok3">Agitasi</label>
                </div>
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="kondisiAnalok" id="kondisiAnalok4" value="4">
                    <label class="form-check-label" for="kondisiAnalok4">Mengantuk</label>
                </div>
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="kondisiAnalok" id="kondisiAnalok5" value="5">
                    <label class="form-check-label" for="kondisiAnalok5">Koma</label>
                </div>
                <div class="radio radio-primary form-check-inline" style="width: 50%;">
                    <input class="form-check-input" type="radio" name="kondisiAnalok" id="kondisiAnalok6" value="6">
                    <label class="form-check-label" for="kondisiAnalok6" style="width: 16%">Lainnya</label>
                </div>
            </div>
            <div class="col-md-3">
                <span class="ml-4">Tanda Toksisitas:</span>
            </div>
            <div class="col-md-9 form-group">
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="toksikAnalok" id="toksikAnalok1" value="1">
                    <label class="form-check-label" for="toksikAnalok1">Tidak Ada</label>
                </div>
                <div class="radio radio-primary form-check-inline" style="width: 50%;">
                    <input class="form-check-input" type="radio" name="toksikAnalok" id="toksikAnalok3" value="2">
                    <label class="form-check-label" for="toksikAnalok3" style="width: 16%">Ada</label>
                    <input class="form-control" type="text" name="toksikAnalokDll" id="toksikAnalokDll" style="display:none;width: 100%;">
                </div>
            </div>
            <div class="col-md-3">
                <span class="ml-4">Mual Muntah:</span>
            </div>
            <div class="col-md-9 form-group">
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="muntahAnalok" id="muntahAnalok1" value="1">
                    <label class="form-check-label" for="muntahAnalok1">Tidak Ada</label>
                </div>
                <div class="radio radio-primary form-check-inline" style="width: 50%;">
                    <input class="form-check-input" type="radio" name="muntahAnalok" id="muntahAnalok3" value="2">
                    <label class="form-check-label" for="muntahAnalok3" style="width: 16%">Ada</label>
                </div>
            </div>
            <div class="col-md-3">
                <span class="ml-4">Keluhan Nyeri:</span>
            </div>
            <div class="col-md-9 form-group">
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="nyeriAnalok" id="nyeriAnalok1" value="1">
                    <label class="form-check-label" for="nyeriAnalok1">Tidak Ada</label>
                </div>
                <div class="radio radio-primary form-check-inline" style="width: 50%;">
                    <input class="form-check-input" type="radio" name="nyeriAnalok" id="nyeriAnalok3" value="2">
                    <label class="form-check-label" for="nyeriAnalok3" style="width: 16%">Ada</label>
                    <input class="form-control" type="text" name="nyeriAnalokDll" id="nyeriAnalokDll" style="display:none;width: 100%;">
                </div>
            </div>
            <div class="col-md-3">
                <span class="ml-4">Perdarahan:</span>
            </div>
            <div class="col-md-9 form-group">
                <div class="input-group mb-3">
                    <input type="text" class="form-control" name="perdarahanAnalok" id="perdarahanAnalok">
                    <div class="input-group-append">
                        <span class="input-group-text" id="basic-addon2">CC</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row jarak">
             <div class="col-md-3">
                 <span>11. Rencana asuhan pasca tindakan</span>
            </div>
        </div>
        <div class="row jarak">
             <div class="col-md-3">
                 <span class="ml-4">Observasi</span>
            </div>
            <div class="col-md-9">
                <input type="text" class="form-control" id="observasiAnalok" name="observasiAnalok">
            </div>
        </div>
        <div class="row jarak">
             <div class="col-md-3">
                 <span class="ml-4">Edukasi dan follow up</span>
            </div>
            <div class="col-md-9">
                <input type="text" class="form-control" id="eduAnalok" name="eduAnalok">
            </div>
        </div>

         <div class="row jarak">
             <div class="col-md-3">
                 <span>12. Pasien pindah jam:</span>
            </div>
            <div class="col-md-9">
                <div class="input-group mb-3" style="min-width: 100%;">
                    <input type="time" class="form-control" name="pindahAnalok" id="pindahAnalok" style="max-width: 10%;">
                    <div class="input-group-append">
                        <span class="input-group-text" id="basic-addon2">wib, ke</span>
                    </div>
                    <div class="radio radio-primary form-check-inline ml-2">
                        <input class="form-check-input" type="radio" name="manaAnalok" id="manaAnalok1" value="1">
                        <label class="form-check-label" for="manaAnalok1">Ruang rawat</label>
                    </div>
                    <div class="radio radio-primary form-check-inline">
                        <input class="form-check-input" type="radio" name="manaAnalok" id="manaAnalok2" value="2">
                        <label class="form-check-label" for="manaAnalok2">Pulang</label>
                    </div>
                    <div class="radio radio-primary form-check-inline" style="width: 50%;">
                        <input class="form-check-input" type="radio" name="manaAnalok" id="manaAnalok3" value="3">
                        <label class="form-check-label" for="manaAnalok3">Lainnya</label>
                        <input class="form-control" type="text" name="manaAnalokDll" id="manaAnalokDll" style="display:none;width: 100%;">
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="offset-10 col-lg-2">
                <div class="form-group">
                    <div class="pull-right">
                    <button type="button" class="btn btn-primary btn-block" id="simpan-PAG"><i class="fa fa-save"></i> Simpan</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
  </div>

  <div role="tabpanel" id="tabhistoryPAG" class="tab-pane fade">
    <div class="table-responsive">
      <table id="tblPAG" class="table table-bordered table-hover dt-responsive table-custom no-footer dtr-inline" style="width: 100%;">
        <thead class="table-tr-custom">
          <tr>
            <th width="5%">No</th>
            <th>Nokun</th>
            <th>DPJP</th>
            <th>Oleh</th>
            <th>Tanggal</th>
            <!-- <th>Status</th> -->
            <th width="10%">#</th>
          </tr>
        </thead>
        <tbody></tbody>
      </table>
    </div>
  </div>
</div>

<div class="modal fade" id="modalPemantauanAnastesi" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl">
    <div class="modal-content" style="width: 1300px;">
      <div class="modal-header">
        <h4 class="modal-title mt-0" id="mySmallModalLabel">Edit Pemantauan Anestesi</h4>
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
      </div>
      <div class="modal-body">
        <div id="detailModalPemantauanAnastesi"></div>
      </div>
    </div>
  </div>
</div>


<script>
  $(document).ready(function(){
    $("input[name='mentalAnalok']").click(function() {
        if($(this).val()== 6){
            $("#mentalAnalokDll").show();
        } else{
            $("#mentalAnalokDll").hide();
        }
    });
    $("input[name='riwayatAnalok']").click(function() {
        if($(this).val()== 2){
            $("#riwayatAnalokDll").show();
        } else{
            $("#riwayatAnalokDll").hide();
        }
    });
    $("input[name='encerAnalok']").click(function() {
        if($(this).val()== 2){
            $("#encerAnalokDll").show();
        } else{
            $("#encerAnalokDll").hide();
        }
    });
    $("input[name='toksikAnalok']").click(function() {
        if($(this).val()== 2){
            $("#toksikAnalokDll").show();
        } else{
            $("#toksikAnalokDll").hide();
        }
    });
    $("input[name='nyeriAnalok']").click(function() {
        if($(this).val()== 2){
            $("#nyeriAnalokDll").show();
        } else{
            $("#nyeriAnalokDll").hide();
        }
    });
    $("input[name='manaAnalok']").click(function() {
        if($(this).val()== 3){
            $("#manaAnalokDll").show();
        } else{
            $("#manaAnalokDll").hide();
        }
    });

    $("#simpan-PAG").on('click', function (event) {
        event.preventDefault();
        alertify.confirm('Konfirmasi', 'Pilih Ok, jika setuju untuk Simpan',
          function(){
            var formPAG = $("#formPAG").serializeArray();
            $.ajax({
              dataType:'json',
              url: "<?php echo base_url('gigi/PemantauanAnastesiGigi/simpan') ?>",
              method: "POST",
              data: formPAG,
              success: function(data) {
                if(data.status == 'success'){
                  alertify.success('Data Tersimpan');
                    location.reload();
                  }else{
                    alertify.warning('Internal Server Error');
                  }
                }
              });
          }, function(){ 
             alertify.error('Batal')
          });
      });

    $('#tblPAG').DataTable({
        "responsive": true,
        "pageLength" : 10,
        "processing": true,
        "serverSide": true,
        "bLengthChange": true,
        "ordering": false,
        "order": [],
        "language": {
            "processing": 'Memuat Data...',
            "zeroRecords": "Data Tidak Ditemukan",
            "emptyTable": "Data Tidak Tersedia",
            "loadingRecords": "Harap Tunggu...",
            "paginate": {
                "next":       "Selanjutnya",
                "previous":   "Sebelumnya"
            },
            "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ Data",
            "infoEmpty": "Menampilkan 0 sampai 0 dari 0 Data",
            "search": "Cari:",
            "lengthMenu": "Tampilkan: _MENU_ Data",
            "infoFiltered": "(Disaring dari _MAX_ jumlah Data)",
        },
        
        lengthMenu: [[10, 20, 30, 40, 50], [10, 20, 30, 40, 50]],
        ajax: {
            url: '<?php echo base_url('gigi/PemantauanAnastesiGigi/get_data_history')?>',
            type: 'POST',
            data: {norm: '<?=$getNomr['NORM']?>'}
        },
    });

    $('#modalPemantauanAnastesi').on('show.bs.modal', function (e) {
        var id = $(e.relatedTarget).data('id');
        $.ajax({
            type: 'POST',
            url: '<?php echo base_url() ?>gigi/PemantauanAnastesiGigi/modalPemantauanAnastesi',
            data: {id: id},
            success: function (data) {
            $('#detailModalPemantauanAnastesi').html(data);
            }
        });
    });
  });
</script>

<!-- JPOKEM - Form, History, Detail Tabs (nav-justified style) -->
<div class="row">
    <div class="col-sm-12">
        <h4 class="page-title">JPOK Kemoterapi</h4>
    </div>
</div>
<ul class="nav nav-tabs nav-justified mt-3" id="jpokemTab" role="tablist">
    <li class="nav-item">
        <a class="nav-link active" id="form-tab" data-toggle="tab" href="#tab-form" role="tab" aria-controls="tab-form" aria-selected="true">Form</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" id="history-tab" data-toggle="tab" href="#tab-history" role="tab" aria-controls="tab-history" aria-selected="false">History</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" id="detail-tab" data-toggle="tab" href="#tab-detail" role="tab" aria-controls="tab-detail" aria-selected="false">Detail</a>
    </li>
</ul>
<div class="tab-content" id="jpokemTabContent">
    <!-- Tab Form -->
    <div class="tab-pane fade show active pt-3" id="tab-form" role="tabpanel" aria-labelledby="form-tab">
        <form id="form-jpokem" autocomplete="off">
            <input type="hidden" name="nokun" value="<?= $nokun ?>">
            <div class="form-group row">
                <label for="jumlah_siklus" class="col-sm-2 col-form-label">Jumlah Siklus</label>
                <div class="col-sm-4">
                    <input type="number" min="1" class="form-control" id="jumlah_siklus" name="jumlah_siklus" placeholder="Jumlah Siklus">
                </div>
            </div>
            <div class="form-group row">
                <label for="lini" class="col-sm-2 col-form-label">Lini</label>
                <div class="col-sm-4">
                    <input type="text" class="form-control" id="lini" name="lini" placeholder="Lini">
                </div>
            </div>
            <div class="form-group row" id="menopause-group" style="display:none;">
                <label class="col-sm-2 col-form-label">Menopause</label>
                <div class="col-sm-4 pt-2">
                    <div class="form-check radio radio-primary jarak2" style="margin-right: 20px;">
                        <input type="radio" class="form-check-input" name="menopause" value="Ya" id="menopause_ya">
                        <label for="menopause_ya" class="form-check-label">Ya</label>
                    </div>
                    <div class="form-check radio radio-primary jarak2">
                        <input type="radio" class="form-check-input" name="menopause" value="Belum" id="menopause_belum">
                        <label for="menopause_belum" class="form-check-label">Belum</label>
                    </div>
                </div>
            </div>
            <div class="form-group row mt-2">
                <label for="protokol_kemo" class="col-sm-2 col-form-label">Protokol Kemoterapi</label>
                <div class="col-sm-6">
                    <select id="protokol_kemo" name="protokol_kemo" class="form-control" style="width:100%"></select>
                </div>
            </div>
        </form>
        <div id="tabel-detail-jpokem"></div>
    </div>
    <!-- Tab History -->
    <div class="tab-pane fade pt-3" id="tab-history" role="tabpanel" aria-labelledby="history-tab">
        <div id="history-jpok-container">
            <div class="alert alert-info">Loading history JPOK...</div>
        </div>
    </div>
    <!-- Tab Detail -->
    <div class="tab-pane fade pt-3" id="tab-detail" role="tabpanel" aria-labelledby="detail-tab">
        <div id="detail-jpok-container">
            <div class="row mb-3">
                <div class="col-md-6">
                    <select id="select-jpok" class="form-control" style="width:100%">
                        <option value="">Pilih JPOK...</option>
                    </select>
                </div>
            </div>
            <div class="alert alert-info">Pilih JPOK dari dropdown untuk melihat detail pemberian.</div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Inisialisasi select2
    $('#protokol_kemo').select2({
        placeholder: '[ Pilih Protokol Kemoterapi ]',
        allowClear: true,
        ajax: {
            url: '<?= base_url('kemoterapi/JPOKEM/get_protokol_kemo') ?>',
            dataType: 'json',
            delay: 250,
            data: function(params) {
                return { q: params.term };
            },
            processResults: function(data) {
                return data;
            },
            cache: true
        }
    });

    // Load tabel detail saat select2 dipilih
    $('#protokol_kemo').on('change', function() {
        var id = $(this).val();
        if (id) {
            $.ajax({
                url: '<?= base_url('kemoterapi/JPOKEM/get_protokol_kemo_detail') ?>',
                type: 'POST',
                data: { id: id },
                beforeSend: function() {
                    $('#tabel-detail-jpokem').html('<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Loading...</div>');
                },
                success: function(html) {
                    $('#tabel-detail-jpokem').html(html);
                }
            });
        } else {
            $('#tabel-detail-jpokem').html('');
        }
    });

    // Simpan data JPOK
    $(document).on('click', '#btn-simpan-jpokem', function() {
        var formData = $('#form-jpokem').serialize();
        var detailData = $('#tabel-detail-jpokem').find('input').serialize();
        var allData = formData + '&' + detailData + '&nokun=<?= $nokun ?>';

        $.ajax({
            url: '<?= base_url('kemoterapi/JPOKEM/simpan_jpok') ?>',
            type: 'POST',
            data: allData,
            dataType: 'json',
            beforeSend: function() {
                $('#btn-simpan-jpokem').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Menyimpan...');
            },
            success: function(response) {
                if (response.status === 'success') {
                    alertify.success(response.message);
                    // Reset form atau redirect sesuai kebutuhan
                    $('#form-jpokem')[0].reset();
                    $('#protokol_kemo').val(null).trigger('change');
                    $('#tabel-detail-jpokem').html('');
                } else {
                    alertify.error(response.message);
                }
            },
            error: function() {
                alertify.error('Terjadi kesalahan saat menyimpan data');
            },
            complete: function() {
                $('#btn-simpan-jpokem').prop('disabled', false).html('<i class="fa fa-save"></i> Simpan');
            }
        });
    });

    // Cek jenis kelamin pasien untuk Menopause
    <?php if (!empty($nokun)): ?>
    $.ajax({
        url: '<?= base_url('kemoterapi/JPOKEM/getJenisKelaminPasien') ?>',
        type: 'POST',
        data: { nokun: '<?= $nokun ?>' },
        dataType: 'json',
        success: function(resp) {
            if (resp && resp.jenkel && resp.jenkel === 'Perempuan') {
                $('#menopause-group').show();
            } else {
                $('#menopause-group').hide();
            }
        }
    });
    <?php endif; ?>

    // Event handler untuk tab Detail
    $('#detail-tab').on('shown.bs.tab', function() {
        initSelect2ForDetail();
    });

    // Event listener untuk tab history
    $('#history-tab').on('shown.bs.tab', function() {
        loadHistoryJpok();
    });

    function loadDetailJpok(idJpok) {
        // Default ke 0 jika tidak ada ID
        idJpok = idJpok || 0;

        // Jika ID = 0, tidak perlu load via AJAX, select2 sudah ada di DOM
        if (idJpok === 0) {
            return;
        }

        // Load detail JPOK yang dipilih
        $.ajax({
            url: '<?= base_url('kemoterapi/JPOKEM/get_jpok_detail') ?>',
            type: 'POST',
            data: {
                id_jpok: idJpok,
                nokun: '<?= $nokun ?>'
            },
            beforeSend: function() {
                // Tampilkan loading di bagian alert-info saja
                $('#detail-jpok-container .alert-info').html('<i class="fa fa-spinner fa-spin"></i> Loading detail JPOK...');
            },
            success: function(html) {
                // Replace konten setelah select2, biarkan select2 tetap ada
                var $container = $('#detail-jpok-container');
                var $selectRow = $container.find('.row.mb-3').first(); // Row yang berisi select2

                // Hapus konten lama setelah select2
                $selectRow.nextAll().remove();

                // Tambahkan konten baru setelah select2
                $selectRow.after(html);
            },
            error: function() {
                $('#detail-jpok-container .alert-info').html('<div class="alert alert-danger">Gagal memuat data detail.</div>');
            }
        });
    }



    // Function untuk inisialisasi select2 di tab detail
    function initSelect2ForDetail() {
        

        var $selectJpok = $('#select-jpok');

        if ($selectJpok.length > 0) {
            

            // Destroy existing select2 if any
            if ($selectJpok.hasClass('select2-hidden-accessible')) {
                
                $selectJpok.select2('destroy');
            }

            // Initialize select2
            $selectJpok.select2({
                placeholder: 'Pilih JPOK...',
                allowClear: true,
                ajax: {
                    url: '<?= base_url('kemoterapi/JPOKEM/get_jpok_list') ?>',
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        return {
                            q: params.term,
                            nokun: '<?= $nokun ?>'
                        };
                    },
                    processResults: function(data) {
                        return data;
                    },
                    cache: true
                }
            });

            // Event handler untuk select2
            $selectJpok.on('select2:select', function(e) {
                var selectedData = e.params.data;
                // Load detail JPOK yang dipilih
                if (selectedData.id) {
                    loadDetailJpok(selectedData.id);
                }
            });

            
        }
    }

    function loadHistoryJpok() {
        // Load history JPOK dari database

        $.ajax({
            url: '<?= base_url('kemoterapi/JPOKEM/get_history') ?>',
            type: 'POST',
            data: { nokun: '<?= $nokun ?>' },
            beforeSend: function() {
                $('#history-jpok-container').html('<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Loading history...</div>');
            },
            success: function(html) {
                $('#history-jpok-container').html(html);
            },
            error: function() {
                $('#history-jpok-container').html('<div class="alert alert-danger">Gagal memuat history JPOK.</div>');
            }
        });
    }

    function loadTabelDetail() {
        // Reload tabel detail setelah simpan
        var protokolId = $('#protokol_kemo').val();

        if (protokolId) {
            $.ajax({
                url: '<?= base_url('kemoterapi/JPOKEM/get_detail_protokol') ?>',
                type: 'POST',
                data: { id_protokol: protokolId },
                beforeSend: function() {
                    $('#tabel-detail-jpokem').html('<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Loading...</div>');
                },
                success: function(html) {
                    $('#tabel-detail-jpokem').html(html);
                },
                error: function() {
                    $('#tabel-detail-jpokem').html('<div class="alert alert-danger">Gagal memuat tabel detail.</div>');
                }
            });
        }
    }

    // Buat function global agar bisa diakses dari tabelDetail.php
    window.loadTabelDetail = loadTabelDetail;
});
</script>

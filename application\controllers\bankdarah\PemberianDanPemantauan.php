<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PemberianDanPemantauan extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('bankdarah/PemberianDanPemantauanModel','bankdarah/KesesuaianKantongModel','bankdarah/ReaksiTransfusiModel','bankdarah/PemantauanPemberianDarahModel'));
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah'){
    			$rules = $this->PemberianDanPemantauanModel->rules;
                $this->form_validation->set_rules($rules);

    			if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $dataPemberianDanPemantauan = array(
                        'kunjungan' => $post['nokun'],
                        'rawat' => $post['ruangan'],
                        'ruangan' => $post['ruangan'] == 1 ? $post['pemberian_jalan_ruangan'] : $post['pemberian_inap_ruangan'],
                        'surat_ijin' => $post['surat_ijin'],
                        'kesesuaian_intruksi' => $post['kesesuaian_intruksi'],
                        'jenis_darah' => $post['jenis_darah'],
                        'jenis_darah_lain' => $post['jenis_darah'] == '3057' ? $post['jenis_darah_lainnya'] : null,

                        'volume' => $post['volume'],
                        'jenis_darah_sesuai' => $post['jenis_darah_sesuai'],
                        'golongan_darah' => $post['golongan_darah_pemberian'],
                        'golongan_darah_sesuai' => $post['golongan_darah_sesuai'],
                        'rhesus' => $post['rhesus'],
                        'rhesus_sesuai' => $post['rhesus_sesuai'],
                        'formulir_pmi' => $post['formulir_pmi'],
                        'petugas_bank_darah' => $post['petugas_bank_darah'],
                        'petugas_ruangan' => $post['petugas_ruangan'],
                        'oleh' => $this->session->userdata("id"),
                    );

                    if (!empty($post['id'])) {
                        $this->PemberianDanPemantauanModel->update($dataPemberianDanPemantauan,array('id' => $post['id']));
                    }else{
                        $this->PemberianDanPemantauanModel->insert($dataPemberianDanPemantauan);
                    }


                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
    			}else{
    				$result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
    			}
    			echo json_encode($result);
            }else if($param == 'tambahKesesuaianNomor'){
    			$rules = $this->KesesuaianKantongModel->rules;
                $this->form_validation->set_rules($rules);

    			if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $dataPemberianDanPemantauan = array(
                        'kunjungan' => $post['nokun'],
                        'nomor_kantong' => $post['nomor_kantong'],
                        'golongan_darah' => $post['golongan_darah_kesesuaian'],
                        'label_darah' => $post['label_darah'],
                        'identitas_pasien' => $post['identitas_pasien'],
                        'perawat_1' => $post['perawat1'],
                        'perawat_2' => $post['perawat2'],
                        'oleh' => $this->session->userdata("id"),
                    );

                    if (!empty($post['id'])) {
                        $this->KesesuaianKantongModel->update($dataPemberianDanPemantauan,array('id' => $post['id']));
                    }else{
                        $this->KesesuaianKantongModel->insert($dataPemberianDanPemantauan);
                    }


                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
    			}else{
    				$result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
    			}
    			echo json_encode($result);
            }else if($param == 'tambahReakasiTransfusi'){
    			$rules = $this->ReaksiTransfusiModel->rules;
                $this->form_validation->set_rules($rules);

    			if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $dataReaksiTransfusi = array(
                        'kunjungan' => $post['nokun'],
                        'nomor_kantong' => $post['nomor_kantong'],
                        'reaksi_tindakan' => $post['reaksi_tindakan'],
                        'tindakan' => $post['tindakan'],
                        'oleh' => $this->session->userdata("id"),
                    );

                    if (!empty($post['id'])) {
                        $this->ReaksiTransfusiModel->update($dataReaksiTransfusi,array('id' => $post['id']));
                    }else{
                        $this->ReaksiTransfusiModel->insert($dataReaksiTransfusi);
                    }

                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
    			}else{
    				$result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
    			}
    			echo json_encode($result);
            }else if($param == 'tambahDaftarPemantauan'){
    			$rules = $this->PemantauanPemberianDarahModel->rules;
                $this->form_validation->set_rules($rules);

    			if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $dataPemantauanDarah = array(
                        'kunjungan' => $post['nokun'],
                        'bag_ke' => $post['bag_ke'],
                        'tanggal' => $post['tanggal'],
                        'jam' => $post['jam'],
                        'sistolik' => $post['sistolik'],
                        'diastolik' => $post['diastolik'],
                        'n' => $post['n'],
                        'rr' => $post['rr'],
                        's' => $post['s'],

                        'sistolik15' => $post['sistolik15'],
                        'diastolik15' => $post['diastolik15'],
                        'n15' => $post['n15'],
                        'rr15' => $post['rr15'],
                        's15' => $post['s15'],

                        'sistolik30' => $post['sistolik30'],
                        'diastolik30' => $post['diastolik30'],
                        'n30' => $post['n30'],
                        'rr30' => $post['rr30'],
                        's30' => $post['s30'],
                        'oleh' => $this->session->userdata("id"),
                    );

                    if (!empty($post['id'])) {
                        $this->PemantauanPemberianDarahModel->update($dataPemantauanDarah,array('id' => $post['id']));
                    }else{
                        $this->PemantauanPemberianDarahModel->insert($dataPemantauanDarah);
                    }


                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
    			}else{
    				$result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
    			}
    			echo json_encode($result);
            }else if($param == 'ambilKesesuaianFormulir'){
    			$post = $this->input->post(NULL,TRUE);
                $dataPemberianDanPemantauan = $this->PemberianDanPemantauanModel->get($post['id'], true);
                echo json_encode(array(
                    'status' => 'success',
                    'data' => $dataPemberianDanPemantauan
                ));
            }else if($param == 'count'){
                $result = $this->PemberianDanPemantauanModel->get_count();;
                echo json_encode($result);
            } else if ($param == 'hapusKesesuaianNomor') {
                $post = $this->input->post(NULL, TRUE);
                if (!empty($post['id'])) {
                    $data = array(
                        'status' => '0',
                        'deleted_at' => date('Y-m-d H:i:s'),
                        'deleted_by' => $this->session->userdata('id'),
                    );
                    $this->KesesuaianKantongModel->update($data, array('id' => $post['id']));
                    $result = array('status' => 'success');
                }

                echo json_encode($result);
            } else if ($param == 'hapusPemantauanDarah') {
                $post = $this->input->post(NULL, TRUE);
                if (!empty($post['id'])) {
                    $data = array(
                        'status' => '0',
                        'deleted_at' => date('Y-m-d H:i:s'),
                        'deleted_by' => $this->session->userdata('id'),
                    );
                    $this->PemantauanPemberianDarahModel->update($data, array('id' => $post['id']));
                    $result = array('status' => 'success');
                }

                echo json_encode($result);
            } else if ($param == 'hapusReaksiTranfusi') {
                $post = $this->input->post(NULL, TRUE);
                if (!empty($post['id'])) {
                    $data = array(
                        'status' => '0',
                        'deleted_at' => date('Y-m-d H:i:s'),
                        'deleted_by' => $this->session->userdata('id'),
                    );
                    $this->ReaksiTransfusiModel->update($data, array('id' => $post['id']));
                    $result = array('status' => 'success');
                }

                echo json_encode($result);
            }
    	}
    }

    public function datatables(){
        $result = $this->PemberianDanPemantauanModel->datatables();

        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm history_kesesuaian_form" data-id="'.$row -> ID.'"><i class="fa fa-eye"></i> Lihat</a>';
            $sub_array[] = $row -> TANGGAL;
            $sub_array[] = $row -> RUANGAN_KUNJUNGAN;      
            $sub_array[] = $row -> DPJP;
            $sub_array[] = $row -> USER;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->PemberianDanPemantauanModel->total_count(),
            "recordsFiltered"   => $this->PemberianDanPemantauanModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }

    public function datatablesKesesuaianKantong(){
        $result = $this->KesesuaianKantongModel->datatables();

        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-danger btn-block btn-sm hapus_kesesuaian_kantong" data-id="'.$row -> id.'"><i class="fa fa-times"></i> Hapus</a>';
            $sub_array[] = $row -> nomor_kantong;
            $sub_array[] = $row -> golongan_darah;      
            $sub_array[] = $row -> label_darah;
            $sub_array[] = $row -> identitas_pasien;
            $sub_array[] = $row -> perawat_1;
            $sub_array[] = $row -> perawat_2;


            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->KesesuaianKantongModel->total_count(),
            "recordsFiltered"   => $this->KesesuaianKantongModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }

    public function datatablesReaksiTransfusi(){
        $result = $this->ReaksiTransfusiModel->datatables();

        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-danger btn-block btn-sm hapus_reaksi_tranfusi" data-id="'.$row -> id.'"><i class="fa fa-times"></i> Hapus</a>';
            $sub_array[] = $row -> nomor_kantong;
            $sub_array[] = $row -> reaksi_tindakan;      
            $sub_array[] = $row -> tindakan;
            $sub_array[] = $row -> oleh;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->ReaksiTransfusiModel->total_count(),
            "recordsFiltered"   => $this->ReaksiTransfusiModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }

    public function datatablesPemantauanPemberianDarah(){
        $result = $this->PemantauanPemberianDarahModel->datatables();

        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-danger btn-block btn-sm hapus_pemantauan_darah" data-id="'.$row -> id.'"><i class="fa fa-times"></i> Hapus</a>';
            $sub_array[] = $row -> bag_ke;
            $sub_array[] = $row -> tanggal;      
            $sub_array[] = $row -> jam;
            $sub_array[] = 'Sebelum : '.$row->sistolik.'<br/>'.'15 menit : '.$row->sistolik15.'<br/>'.'Sesudah : '.$row->sistolik30.'<br/>';
            $sub_array[] = 'Sebelum : '.$row->diastolik.'<br/>'.'15 menit : '.$row->diastolik15.'<br/>'.'Sesudah : '.$row->diastolik30.'<br/>';
            $sub_array[] = 'Sebelum : '.$row->n.'<br/>'.'15 menit : '.$row->n15.'<br/>'.'Sesudah : '.$row->n30.'<br/>';
            $sub_array[] = 'Sebelum : '.$row->rr.'<br/>'.'15 menit : '.$row->rr15.'<br/>'.'Sesudah : '.$row->rr30.'<br/>';
            $sub_array[] = 'Sebelum : '.$row->s.'<br/>'.'15 menit : '.$row->s15.'<br/>'.'Sesudah : '.$row->s30.'<br/>';
            $sub_array[] = $row -> oleh;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->PemantauanPemberianDarahModel->total_count(),
            "recordsFiltered"   => $this->PemantauanPemberianDarahModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}
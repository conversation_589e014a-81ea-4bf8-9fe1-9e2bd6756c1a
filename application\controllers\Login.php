<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Login extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    // $this->load->model('login_model');
    $this->load->model(array('login_model','whatsapp/WhatsappPasienModel'));
    // if ($this->session->userdata('logged_in') == TRUE) {
    //   if ($this->session->userdata('logged_fp') == FALSE) {
    //     redirect($this->session->userdata('link'));
    //   }else{
    //     unset($_SESSION);
    //     $this->session->sess_destroy();
    //   }
    // }
    date_default_timezone_set('Asia/Jakarta');
    $this->load->library('whatsapp');
  }

  public function index()
  {
    if ($this->session->userdata('logged_in') == TRUE) {
      redirect($this->session->userdata('link'));
    }else{
      $this->load->view('login');
    }
  }

  // public function fingerprint()
  // {
  //   shell_exec('start C:\fp_fc_name\FPSIMRSKD.appref-ms');
  //   echo '';
  // }

  public function getOTP(){

    $json = file_get_contents("php://input");
    $post = json_decode($json, true); // Decode ke array

    if (!$post) {
      echo json_encode(["status" => "error", "message" => "Invalid JSON"]);
      return;
    }

    $nomor = '+62'.substr(trim($post['nomor']), 1) ?? ''; 
    $kodeOTP = $post['kode'] ?? '';
    $jenis = "kode OTP Anda ";
    $selamat = (date("H") >= '05' && date("H") < "10" ? "Pagi" : (date("H") >= '10' && date("H") < "15" ? "Siang" : (date("H") >= '15' && date("H") < "19" ? "Sore" : "Malam")));

    $res = $this->whatsapp->send($nomor, array($selamat,$jenis,$kodeOTP . ' JANGAN BERIKAN KODE INI KEPADA SIAPA PUN'));

    echo json_encode(["status" => "success", "message" => $res]);
  }

  public function checkSession($aes_username=NULL, $aes_fp=0, $aes_password=NULL){
    // unset($_SESSION);
		// $this->session->sess_destroy();
    // $session = $_SESSION;
    $host = $_SERVER['HTTP_HOST'];
    $host_diizinkan = array('************', '127.0.0.1', 'localhost', 'simrskd.test', '*************', '*************','*************');
    if(in_array($host, $host_diizinkan)){
      $keyUsername = '2129264869259147';
      $key1 = '6992825730419199';
      $keyPass = '9359886494143302';
      $username = $this->decryptAES($aes_username, $keyUsername);
      $fp = $this->decryptAES($aes_fp, $key1);
      $password = $this->decryptAES($aes_password, $keyPass);
      $tgl = date('Y-m-d H');
      // echo json_encode($session);
      if($password == $tgl){
        if($this->session->userdata('logged_in') == TRUE){
          if($this->session->userdata('logged_fp') == TRUE){
            // echo "sesi dari fp";
            if($username == $this->session->userdata('username')){
              redirect($this->session->userdata('link'));
            }else{
              $this->sign_out();
              $this->sign_in($username, $fp, $password);
            }
          }elseif($this->session->userdata('logged_fp') == FALSE){
            // echo "sesi dari login";
            if($fp!=0){
              $this->sign_out();
              $this->sign_in($username, $fp, $password);
            }else{
              redirect($this->session->userdata('link'));
            }
            
          }
        }elseif($this->session->userdata('logged_in') == FALSE){
          // echo "tidak ada sesi";
          $this->sign_in($username, $fp, $password);
        }
      }else{
        $result = array('status' => 204, 'message' => 'Akses ditolak');
        echo json_encode($result);
      }
    }else{
      $result = array('status' => 204, 'message' => 'Akses ditolak');
      echo json_encode($result);
    }
    
  }

  function encryptAES($plainText, $key) {
    $iv = str_repeat("\0", 16); // IV harus 16 byte (semua nol)
    $encrypted = openssl_encrypt($plainText, 'AES-128-CBC', $key, OPENSSL_RAW_DATA, $iv);
    return bin2hex($encrypted); // Konversi hasil enkripsi ke Hexadecimal
  }

  function decryptAES($encryptedHex, $key) {
    $iv = str_repeat("\0", 16); // IV harus 16 byte (semua nol)
    $encryptedBytes = hex2bin($encryptedHex); // Konversi dari Hexadecimal ke byte array
    return openssl_decrypt($encryptedBytes, 'AES-128-CBC', $key, OPENSSL_RAW_DATA, $iv);
  }

  public function sign_out()
	{
		unset($_SESSION);
		$this->session->sess_destroy();
	}

  public function sign_in($username=NULL, $fp=0, $password=NULL)
  {
    if($fp == 0){
      $username = $this->input->post('username');
      $password = $this->input->post('password');
    }elseif($fp == 2){
      $username == $username;
      $password == $password;
    }
    // Mulai periksa host
    $host = $_SERVER['HTTP_HOST'];
    $host_diizinkan = array('************', '127.0.0.1', 'localhost', 'simrskd.test', '*************', '*************','*************');
    $user_diizinkan = array('doksirs', 'elfira', 'resti', 'sriagustini', 'sulistyani', 'perawatsimrs', 'psikologsimrs', 'doksirs2','doksirs3','fismedsimrs','rttsimrs');
    // echo '<pre>';print_r($host);exit();

    if (in_array($host, $host_diizinkan)) { // Login lokal
      $data = $this->login_model->login($username, $password);
      // echo '<pre>';print_r($result);exit();
      $this->proses_sign_in($data, $password, $fp);
    } else {
      if (in_array($username, $user_diizinkan)) {
        $data = $this->login_model->login($username, $password);
        // echo '<pre>';print_r($result);exit();
        $this->proses_sign_in($data, $password, $fp);
      } else {
        $result = array('status' => 204, 'message' => 'Akses ditolak');
        echo json_encode($result);
      }
    }
    // Akhir periksa host
    if($fp != 0){
      // echo '<script type="text/javascript">window.close();</script>';
      echo '<script type="text/javascript">location.reload();</script>';
    }
  }

  function proses_sign_in($data, $password, $fp)
  {
    if ($data == '') {
      $result = array('status' => 204, 'message' => 'Username dan password tidak sesuai', 'fp' => $fp);
    } else {
      $private_key = 'KDFLDMSTHBWWSGCBH';
      $hashed_password = $data->PASSWORD;
      $id = $data->ID;
      $username = $data->LOGIN;
      $nama = $data->NAMA;
      $akses = $data->AKSES;
      $akses_ruangan = $data->AKSES_RUANGAN;
      $link = $data->LINK;
      $smf = $data->SMF;
      $smf_desc = $data->SMF_DESC;
      $profesi = $data->PROFESI_ID;
      $iddokter = $data->iddokter;
      $statusPengguna = $this->login_model->statusPengguna($data->NIP);
      $config['cppt'] = $data->PROFESI_ID == 5 || $data->PROFESI_ID == 9 || $data->PROFESI_ID == 13 || $data->PROFESI_ID == 15 || $data->PROFESI_ID == 16 || $this->login_model->statusPengguna($data->NIP) == 1 ? 1 : 2;
      $passwordMD5 = MD5($private_key . MD5($password) . $private_key);
      // $fp == $data->ST_FP;
      if ((hash_equals($hashed_password, $passwordMD5) || $data->PASSWORD == $data->PASS || $fp == 1)) {
        if ($link == null) {
          $result = array('status' => 204, 'message' => 'Akses belum diberikan hubungi SIMRS');
        } else {
          // Mulai data session
          $session = array(
            'id' => $id,
            'username' => $username,
            'nama' => $nama,
            'smf' => $smf,
            'smf_desc' => $smf_desc,
            'profesi' => $profesi,
            'iddokter' => $iddokter,
            'config' => $config,
            // 'akses' => 1,
            'akses' => explode(',', $akses),
            'akses_ruangan' => explode(',', $akses_ruangan),
            // 'link' => $link,
            'link' => 'profile/index',
            'logged_in' => TRUE,
            'status' => $statusPengguna,
            'logged_fp'  => $fp == 0 ? FALSE : TRUE,
          );
          $this->session->set_userdata($session);
          // Akhir session

          // Mulai data result
          unset($session['status']);
          $session['akses'] = $akses;
          $session['akses_ruangan'] = $akses_ruangan;
          $session['link'] = $link;
          $session['statusPengguna'] = $statusPengguna;
          $result = array('status' => 200, 'message' => 'Berhasil masuk', 'data' => $session);
          // Akhir data result
        }
      } else {
        $result = array('status' => 204, 'message' => 'Username dan password tidak sesuai');
      }
    }
    // echo '<pre>';print_r($result);exit();
    echo $fp == 0 || $fp == 2 ? json_encode($result) : md5(json_encode($result));
  }
}

/* End of file Login.php */
/* Location: ./application/controllers/Login.php */
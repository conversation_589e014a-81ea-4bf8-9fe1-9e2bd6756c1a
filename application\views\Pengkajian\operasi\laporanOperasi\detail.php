<!-- Mulai form laporan operasi -->
<form id="form-ubah-lap-operasi" autocomplete="off">
    <div class="modal-header">
        <h4 class="modal-title mt-0" id="mySmallModalLabel">
            <?php if ($jenis == 'detail') { ?>
                Detail Laporan Operasi
            <?php } elseif ($jenis == 'laporan') { ?>
                <PERSON><PERSON> Operasi
            <?php } ?>
        </h4>
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
    </div>
    <div class="modal-body" style="max-height: calc(100vh - 200px); overflow-y: auto;">
        <?= $jenis == 'laporan' ? "<fieldset disabled='disabled'>" : null ?>
        <input type="hidden" name="id" id="id-ubah-lap-operasi" value="<?= $id ?>">
        <input type="hidden" name="nokun" id="nokun-ubah-lap-operasi" value="<?= $detail['nokun'] ?>">
        <input type="hidden" name="id_waiting_list_lama" id="id-waiting-list-lama-ubah-lap-operasi" value="<?= $detail['id_wlo'] ?? null ?>">
        <!-- Mulai dokter operator bedah -->
        <div class="form-group">
            <label for="dok-operator-bedah-ubah-lap-operasi">
                Dokter operator bedah
            </label>
            <select name="dokter_bedah[]" id="dok-operator-bedah-ubah-lap-operasi" class="form-control" multiple>
                <?php foreach ($listDr as $ld): ?>
                    <option id="dok-operator-bedah-ubah-lap-operasi-<?= $ld['ID_DOKTER'] ?>" value="<?= $ld['ID_DOKTER'] ?>">
                        <?= $ld['DOKTER'] . ' - ' . $ld['SMF'] ?>
                    </option>
                <?php endforeach ?>
            </select>
        </div>
        <!-- Akhir dokter operator bedah -->
        <!-- Mulai asisten operator -->
        <div class="form-group">
            <label for="ass-operator-ubah-lap-operasi">
                Asisten operator
            </label>
            <select name="asisten_operator[]" id="ass-operator-ubah-lap-operasi" class="form-control" multiple>
                <option value="">Kosong</option>
                <?php foreach ($listDr as $ld): ?>
                    <option id="ass-operator-ubah-lap-operasi-<?= $ld['ID_DOKTER'] ?>" value="<?= $ld['ID_DOKTER'] ?>">
                        <?= $ld['DOKTER'] . ' - ' . $ld['SMF'] ?>
                    </option>
                <?php endforeach ?>
            </select>
        </div>
        <!-- Akhir asisten operator -->
        <!-- Mulai asisten operator lainnya -->
        <div class="form-group">
            <label for="ass-operator-lainnya-lap-operasi">
                Asisten operator lainnya
            </label>
            <input type="text" class="form-control" id="ass-operator-lainnya-lap-operasi" placeholder="[ Sebutkan Asisten Operator Lainnya ]" name="asisten_operator_lainnya" value="<?= $detail['asisten_operator_lainnya'] ?>">
        </div>
        <!-- Akhir asisten operator lainnya -->
        <!-- Mulai perawat instrumentator -->
        <div class="form-group">
            <label for="instrumentator-ubah-lap-operasi">
                Perawat instrumentator
            </label>
            <select name="perawat_instrumentator[]" id="instrumentator-ubah-lap-operasi" class="form-control" multiple>
                <option value="">Kosong</option>
                <?php foreach ($listPerawat as $lp): ?>
                    <option id="instrumentator-ubah-lap-operasi-<?= $lp['NIP'] ?>" value="<?= $lp['NIP'] ?>">
                        <?= $lp['NAMA'] ?>
                    </option>
                <?php endforeach ?>
            </select>
        </div>
        <!-- Akhir perawat instrumentator -->
        <!-- Mulai perawat sirkuler -->
        <div class="form-group">
            <label for="sirkuler-ubah-lap-operasi">
                Perawat sirkuler
            </label>
            <select name="perawat_sirkuler" id="sirkuler-ubah-lap-operasi" class="form-control">
                <option value="">Kosong</option>
                <?php foreach ($listPerawat as $lp): ?>
                    <option id="sirkuler-ubah-lap-operasi-<?= $lp['NIP'] ?>" value="<?= $lp['NIP'] ?>" <?= $lp['NIP'] == $detail['perawat_sirkuler'] ? 'selected' : null ?>>
                        <?= $lp['NAMA'] ?>
                    </option>
                <?php endforeach ?>
            </select>
        </div>
        <!-- Akhir perawat sirkuler -->
        <!-- Mulai dokter anestesi -->
        <div class="form-group">
            <label for="dok-anestesi-ubah-lap-operasi">
                Dokter anestesi
            </label>
            <select name="dokter_anestesi[]" id="dok-anestesi-ubah-lap-operasi" class="form-control" multiple>
                <option value="">Kosong</option>
                <?php foreach ($listDrAnestesi as $ld): ?>
                    <option id="dok-anestesi-ubah-lap-operasi-<?= $ld['ID_DOKTER'] ?>" value="<?= $ld['ID_DOKTER'] ?>">
                        <?= $ld['DOKTER'] . ' - ' . $ld['SMF'] ?>
                    </option>
                <?php endforeach ?>
            </select>
        </div>
        <!-- Akhir dokter anestesi -->
        <!-- Mulai jenis anestesi -->
        <div class="form-group">
            <label for="jenis-anestesi-ubah-lap-operasi">
                Jenis anestesi
            </label>
            <div class="row px-1">
                <?php foreach ($jenisAnestesi as $ja): ?>
                    <div class="col-md form-check form-check-inline">
                        <div class="radio radio-primary">
                            <input type="radio" class="jenis-anestesi-ubah-lap-operasi" name="jenis_anestesi" id="jenis-anestesi-ubah-lap-operasi-<?= $ja['id_variabel'] ?>" value="<?= $ja['id_variabel'] ?>" <?= $ja['id_variabel'] == $detail['jenis_anestesi'] ? 'checked' : null ?>>
                            <label for="jenis-anestesi-ubah-lap-operasi-<?= $ja['id_variabel'] ?>" class="form-check-label">
                                <?= $ja['variabel'] ?>
                            </label>
                        </div>
                    </div>
                <?php endforeach ?>
            </div>
        </div>
        <!-- Akhir jenis anestesi -->
        <!-- Mulai keterangan anestesi lokal -->
        <div class="form-group d-none" id="ket-anestesi-lokal-ubah-lap-operasi">
            <div class="card new-card">
                <div class="card-header new-card-header">Keterangan Anestesi Lokal</div>
                <div class="card-body">
                    <!-- Mulai teknik anestesi lokal -->
                    <div class="form-group">
                        <label for="teknik-anestesi-lokal-ubah-lap-operasi">
                            Teknik anestesi lokal
                        </label>
                        <div class="row px-1">
                            <?php foreach ($teknikAnestesiLokal as $tal): ?>
                                <div class="col-md form-check form-check-inline">
                                    <div class="radio radio-primary">
                                        <input type="radio" class="teknik-anestesi-lokal-ubah-lap-operasi" name="teknik_anestesi_lokal" id="teknik-anestesi-lokal-ubah-lap-operasi-<?= $tal['id_variabel'] ?>" value="<?= $tal['id_variabel'] ?>" <?= $tal['id_variabel'] == $detail['teknik_anestesi_lokal'] ? 'checked' : null ?>>
                                        <label for="teknik-anestesi-lokal-ubah-lap-operasi-<?= $tal['id_variabel'] ?>" class="form-check-label">
                                            <?= $tal['variabel'] ?>
                                        </label>
                                    </div>
                                </div>
                            <?php endforeach ?>
                        </div>
                    </div>
                    <!-- Akhir teknik anestesi lokal -->
                    <!-- Mulai sebutkan teknik anestesi lokal -->
                    <div class="form-group d-none" id="form-teknik-ubah-lap-operasi">
                        <label for="ket-teknik-ubah-lap-operasi">
                            Keterangan teknik anestesi
                        </label>
                        <input type="text" class="form-control" id="ket-teknik-ubah-lap-operasi" placeholder="[ Sebutkan Teknik ]" name="keterangan_teknik" value="<?= $detail['keterangan_teknik'] ?>">
                    </div>
                    <!-- Akhir sebutkan teknik anestesi lokal -->
                    <!-- Mulai lokasi anestesi -->
                    <div class="form-group">
                        <label for="lokasi-ubah-lap-operasi">
                            Lokasi anestesi
                        </label>
                        <input type="text" class="form-control" id="lokasi-ubah-lap-operasi" placeholder="[ Lokasi Anestesi ]" name="lokasi" value="<?= $detail['lokasi'] ?>">
                    </div>
                    <!-- Akhir lokasi anestesi -->
                    <!-- Mulai obat-obat anestesi -->
                    <div class="form-group">
                        <label for="obat-anestesi-ubah-lap-operasi">
                            Obat-obat anestesi
                        </label>
                        <input type="text" class="form-control" id="obat-anestesi-ubah-lap-operasi" placeholder="[ Sebutkan Obat-obatnya ]" name="obat_anestesi" value="<?= $detail['obat_anestesi'] ?>">
                    </div>
                    <!-- Akhir obat-obat anestesi -->
                    <!-- Mulai respon hipersensitivitas -->
                    <div class="form-group">
                        <label for="respon-hipersensitivitas-ubah-lap-operasi">
                            Respon hipersensitivitas
                        </label>
                        <div class="row px-1">
                            <?php foreach ($responHipersensitivitas as $rh): ?>
                                <div class="col-md form-check form-check-inline">
                                    <div class="radio radio-primary">
                                        <input type="radio" class="respon-hipersensitivitas-ubah-lap-operasi" name="respon_hipersensitivitas" id="respon-hipersensitivitas-ubah-lap-operasi-<?= $rh['id_variabel'] ?>" value="<?= $rh['id_variabel'] ?>" <?= $rh['id_variabel'] == $detail['respon_hipersensitivitas'] ? 'checked' : null ?>>
                                        <label for="respon-hipersensitivitas-ubah-lap-operasi-<?= $rh['id_variabel'] ?>" class="form-check-label">
                                            <?= $rh['variabel'] ?>
                                        </label>
                                    </div>
                                </div>
                            <?php endforeach ?>
                        </div>
                    </div>
                    <!-- Akhir respon hipersensitivitas -->
                    <!-- Mulai keterangan respon hipersensitivitas -->
                    <div class="form-group d-none" id="form-respon-ubah-lap-operasi">
                        <label for="ket-respon-ubah-lap-operasi">
                            Keterangan respon
                        </label>
                        <input type="text" class="form-control" id="ket-respon-ubah-lap-operasi" placeholder="[ Keterangan Respon ]" name="isi_respon_hipersensitivitas" value="<?= $detail['isi_respon_hipersensitivitas'] ?>">
                    </div>
                    <!-- Akhir keterangan respon hipersensitivitas -->
                    <!-- Mulai kejadian toksikasi -->
                    <div class="form-group">
                        <label for="kejadian-toksikasi-ubah-lap-operasi">
                            Kejadian toksikasi
                        </label>
                        <div class="row px-1">
                            <?php foreach ($kejadianToksikasi as $kt): ?>
                                <div class="col-md form-check form-check-inline">
                                    <div class="radio radio-primary">
                                        <input type="radio" class="kejadian-toksikasi-ubah-lap-operasi" name="kejadian_toksikasi" id="kejadian-toksikasi-ubah-lap-operasi-<?= $kt['id_variabel'] ?>" value="<?= $kt['id_variabel'] ?>" <?= $kt['id_variabel'] == $detail['kejadian_toksikasi'] ? 'checked' : null ?>>
                                        <label for="kejadian-toksikasi-ubah-lap-operasi-<?= $kt['id_variabel'] ?>" class="form-check-label">
                                            <?= $kt['variabel'] ?>
                                        </label>
                                    </div>
                                </div>
                            <?php endforeach ?>
                        </div>
                    </div>
                    <!-- Akhir kejadian toksikasi -->
                    <!-- Mulai keterangan kejadian toksikasi -->
                    <div class="form-group d-none" id="form-toksikasi-ubah-lap-operasi">
                        <label for="sebutkan-toksikasi-ubah-lap-operasi">
                            Keterangan kejadian
                        </label>
                        <input type="text" class="form-control" id="sebutkan-toksikasi-ubah-lap-operasi" placeholder="[ Keterangan Kejadian ]" name="isi_kejadian_toksikasi" value="<?= $detail['isi_kejadian_toksikasi'] ?>">
                    </div>
                    <!-- Akhir keterangan kejadian toksikasi -->
                </div>
            </div>
        </div>
        <!-- Akhir keterangan anestesi lokal -->
        <!-- Mulai kategori operasi -->
        <div class="form-group">
            <label for="kat-operasi-ubah-lap-operasi">
                Kategori operasi
            </label>
            <div class="row px-1">
                <?php foreach ($kategoriOperasi as $ko): ?>
                    <div class="col-md form-check form-check-inline">
                        <div class="radio radio-primary">
                            <input type="radio" class="kat-operasi-ubah-lap-operasi" name="kategori_operasi" id="kat-operasi-ubah-lap-operasi-<?= $ko['id_variabel'] ?>" value="<?= $ko['id_variabel'] ?>" <?= $ko['id_variabel'] == $detail['kategori_operasi'] ? 'checked' : null ?>>
                            <label for="kat-operasi-ubah-lap-operasi-<?= $ko['id_variabel'] ?>" class="form-check-label">
                                <?= $ko['variabel'] ?>
                            </label>
                        </div>
                    </div>
                <?php endforeach ?>
            </div>
        </div>
        <!-- Akhir kategori operasi -->
        <!-- Mulai lokasi pengambilan sampel -->
        <div class="form-group">
            <label for="lok-pengambilan-sampel-ubah-lap-operasi">
                Lokasi pengambilan sampel
            </label>
            <input type="text" class="form-control" id="lok-pengambilan-sampel-ubah-lap-operasi" placeholder="[ Lokasi Pengambilan Sampel ]" name="lok_pengambilan_sampel" value="<?= $detail['lok_pengambilan_sampel'] ?>">
        </div>
        <!-- Akhir lokasi pengambilan sampel -->
        <!-- Mulai diagnosis pra bedah -->
        <div class="form-group">
            <label for="diagnosis-pra-bedah-ubah-lap-operasi">
                Diagnosis pra bedah
            </label>
            <div class="row px-1">
                <div class="col-md form-check form-check-inline">
                    <div class="radio radio-primary">
                        <input type="radio" class="diagnosis-pra-bedah-ubah-lap-operasi" name="radio_diagnosis_pra_bedah" id="diagnosis-pra-bedah-ubah-lap-operasi-pilihan" value="pilihan" <?= !empty($detail['id_pra_bedah']) || !empty($detail['diagnosis_pra_bedah_multiple']) ? 'checked' : null ?>>
                        <label for="diagnosis-pra-bedah-ubah-lap-operasi-pilihan" class="form-check-label">
                            ICD 10
                        </label>
                    </div>
                </div>
                <div class="col-md form-check form-check-inline">
                    <div class="radio radio-primary">
                        <input type="radio" class="diagnosis-pra-bedah-ubah-lap-operasi" name="radio_diagnosis_pra_bedah" id="diagnosis-pra-bedah-ubah-lap-operasi-isian" value="isian" <?= !empty($detail['diagnosis_pra_bedah_lainnya']) ? 'checked' : null ?>>
                        <label for="diagnosis-pra-bedah-ubah-lap-operasi-isian" class="form-check-label">
                            Teks
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <!-- Akhir diagnosis pra bedah -->
        <!-- Mulai pilihan diagnosis pra bedah -->
        <div class="form-group d-none" id="form-pilihan-diagnosis-pra-bedah-ubah-lap-operasi">
            <label for="pilihan-diagnosis-pra-bedah-ubah-lap-operasi">
                Pilihan diagnosis pra bedah
            </label>
            <select name="diagnosis_pra_bedah[]" id="pilihan-diagnosis-pra-bedah-ubah-lap-operasi" class="form-control" multiple required>
                <option value="">Kosong</option>
                <?php if (!empty($getSur['diagnosa_utama'])): ?>
                    <option id="pilihan-diagnosis-pra-bedah-ubah-lap-operasi-<?= $getSur['diagnosa_utama'] ?>" value="<?= $getSur['diagnosa_utama'] ?>">
                        <?= $getSur['diagnosa_utama'] ?>
                    </option>
                <?php endif ?>
            </select>
        </div>
        <!-- Akhir pilihan diagnosis pra bedah -->
        <!-- Mulai diagnosis pra bedah lainnya -->
        <div class="form-group d-none" id="form-diagnosis-pra-bedah-lainnya-ubah-lap-operasi">
            <label for="diagnosis-pra-bedah-lainnya-ubah-lap-operasi">
                Diagnosis pra bedah lainnya
            </label>
            <input type="text" class="form-control" placeholder="[ Diagnosis Pra Bedah Lainnya ]" id="diagnosis-pra-bedah-lainnya-ubah-lap-operasi" name="diagnosis_pra_bedah_lainnya" value="<?= $detail['diagnosis_pra_bedah_lainnya'] ?>">
        </div>
        <!-- Akhir diagnosis pra bedah lainnya -->
        <!-- Mulai tanggal operasi -->
        <div class="form-group">
            <label for="tgl-operasi-ubah-lap-operasi">
                Tanggal operasi
            </label>
            <div class="input-group">
                <input type="date" class="form-control" placeholder="[ Tanggal Operasi ]" id="tgl-operasi-ubah-lap-operasi" name="tgl_operasi" value="<?= $detail['tgl_operasi'] == '0000-00-00' ? null : $detail['tgl_operasi'] ?>">
                <div class="input-group-append">
                    <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                </div>
            </div>
        </div>
        <!-- Akhir tanggal operasi -->
        <!-- Mulai jam mulai insisi -->
        <div class="form-group">
            <label for="mulai-insisi-ubah-lap-operasi">
                Jam mulai insisi
            </label>
            <div class="input-group">
                <input type="time" class="form-control" placeholder="[ Jam Mulai Insisi ]" id="mulai-insisi-ubah-lap-operasi" name="jam_mulai" value="<?= $detail['jam_mulai'] ?>">
                <div class="input-group-append">
                    <span class="input-group-text">WIB</span>
                </div>
            </div>
        </div>
        <!-- Akhir jam mulai insisi -->
        <!-- Mulai jam selesai insisi -->
        <div class="form-group">
            <label for="selesai-insisi-ubah-lap-operasi">
                Jam selesai insisi
            </label>
            <div class="input-group">
                <input type="time" class="form-control" placeholder="[ Jam Selesai Insisi ]" id="selesai-insisi-ubah-lap-operasi" name="jam_selesai" value="<?= $detail['jam_selesai'] ?>">
                <div class="input-group-append">
                    <span class="input-group-text">WIB</span>
                </div>
            </div>
        </div>
        <!-- Akhir jam selesai insisi -->
        <!-- Mulai diagnosis pasca bedah -->
        <div class="form-group">
            <label for="diagnosis-pasca-bedah-ubah-lap-operasi">
                Diagnosis pasca bedah
            </label>
            <div class="row px-1">
                <div class="col-md form-check form-check-inline">
                    <div class="radio radio-primary">
                        <input type="radio" class="diagnosis-pasca-bedah-ubah-lap-operasi" name="radio_diagnosis_pasca_bedah" id="diagnosis-pasca-bedah-ubah-lap-operasi-pilihan" value="pilihan" <?= !empty($detail['id_pasca_bedah']) || !empty($detail['diagnosis_pasca_bedah_multiple']) ? 'checked' : null ?>>
                        <label for="diagnosis-pasca-bedah-ubah-lap-operasi-pilihan" class="form-check-label">
                            ICD 10
                        </label>
                    </div>
                </div>
                <div class="col-md form-check form-check-inline">
                    <div class="radio radio-primary">
                        <input type="radio" class="diagnosis-pasca-bedah-ubah-lap-operasi" name="radio_diagnosis_pasca_bedah" id="diagnosis-pasca-bedah-ubah-lap-operasi-isian" value="isian" <?= !empty($detail['diagnosis_pasca_bedah_lainnya']) ? 'checked' : null ?>>
                        <label for="diagnosis-pasca-bedah-ubah-lap-operasi-isian" class="form-check-label">
                            Teks
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <!-- Akhir diagnosis pasca bedah -->
        <!-- Mulai pilihan diagnosis pasca bedah -->
        <div class="form-group d-none" id="form-pilihan-diagnosis-pasca-bedah-ubah-lap-operasi">
            <label for="pilihan-diagnosis-pasca-bedah-ubah-lap-operasi">
                Pilihan diagnosis pasca bedah
            </label>
            <select name="diagnosis_pasca_bedah[]" id="pilihan-diagnosis-pasca-bedah-ubah-lap-operasi" class="form-control" multiple required>
                <option value="">Kosong</option>
                <?php if (!empty($getSur['diagnosa_utama'])): ?>
                    <option id="pilihan-diagnosis-pasca-bedah-ubah-lap-operasi-<?= $getSur['diagnosa_utama'] ?>" value="<?= $getSur['diagnosa_utama'] ?>">
                        <?= $getSur['diagnosa_utama'] ?>
                    </option>
                <?php endif ?>
            </select>
        </div>
        <!-- Akhir pilihan diagnosis pasca bedah -->
        <!-- Mulai diagnosis pasca bedah lainnya -->
        <div class="form-group d-none" id="form-diagnosis-pasca-bedah-lainnya-ubah-lap-operasi">
            <label for="diagnosis-pasca-bedah-lainnya-ubah-lap-operasi">
                Diagnosis pasca bedah lainnya
            </label>
            <input type="text" class="form-control" placeholder="[ Diagnosis Pasca Bedah Lainnya ]" id="diagnosis-pasca-bedah-lainnya-ubah-lap-operasi" name="diagnosis_pasca_bedah_lainnya" value="<?= $detail['diagnosis_pasca_bedah_lainnya'] ?>">
        </div>
        <!-- Akhir diagnosis pasca bedah lainnya -->
        <!-- Mulai letak tumor primer -->
        <div class="form-group">
            <label for="letak-tumor-primer-ubah-lap-operasi">
                Letak tumor primer
            </label>
            <textarea name="letak_tumor_primer" id="letak-tumor-primer-ubah-lap-operasi" class="form-control" placeholder="[ Sebutkan Letak Tumor Primer ]"><?= $detail['letak_tumor_primer'] ?></textarea>
        </div>
        <!-- Akhir letak tumor primer -->
        <!-- Mulai sifat operasi -->
        <div class="form-group">
            <label for="sifat-operasi-ubah-lap-operasi">
                Sifat operasi
            </label>
            <div class="row px-1">
                <?php foreach ($sifatOperasi as $so): ?>
                    <div class="col-md form-check form-check-inline">
                        <div class="radio radio-primary">
                            <input type="radio" class="sifat-operasi-ubah-lap-operasi" name="sifat_operasi" id="sifat-operasi-ubah-lap-operasi-<?= $so['id_variabel'] ?>" value="<?= $so['id_variabel'] ?>" <?= $so['id_variabel'] == $detail['sifat_operasi'] ? 'checked' : null ?>>
                            <label for="sifat-operasi-ubah-lap-operasi-<?= $so['id_variabel'] ?>" class="form-check-label">
                                <?= $so['variabel'] ?>
                            </label>
                        </div>
                    </div>
                <?php endforeach ?>
            </div>
        </div>
        <!-- Akhir sifat operasi -->
        <!-- Mulai tujuan operasi -->
        <div class="form-group">
            <label for="tujuan-operasi-ubah-lap-operasi">
                Tujuan operasi
            </label>
            <div class="row px-1">
                <?php foreach ($tujuanOperasi as $to): ?>
                    <div class="col-md form-check form-check-inline">
                        <div class="radio radio-primary">
                            <input type="radio" class="tujuan-operasi-ubah-lap-operasi" name="tujuan_operasi" id="tujuan-operasi-ubah-lap-operasi-<?= $to['id_variabel'] ?>" value="<?= $to['id_variabel'] ?>" <?= $to['id_variabel'] == $detail['tujuan_operasi'] ? 'checked' : null ?>>
                            <label for="tujuan-operasi-ubah-lap-operasi-<?= $to['id_variabel'] ?>" class="form-check-label">
                                <?= $to['variabel'] ?>
                            </label>
                        </div>
                    </div>
                <?php endforeach ?>
            </div>
        </div>
        <!-- Akhir tujuan operasi -->
        <!-- Mulai jenis pembedahan -->
        <div class="form-group">
            <label for="jenis-pembedahan-ubah-lap-operasi">
                Jenis pembedahan
            </label>
            <div class="row px-1">
                <?php foreach ($jenisPembedahan as $jp): ?>
                    <div class="col-md form-check form-check-inline">
                        <div class="radio radio-primary">
                            <input type="radio" class="jenis-pembedahan-ubah-lap-operasi" name="jenis_pembedahan" id="jenis-pembedahan-ubah-lap-operasi-<?= $jp['id_variabel'] ?>" value="<?= $jp['id_variabel'] ?>" <?= $jp['id_variabel'] == $detail['jenis_pembedahan'] ? 'checked' : null ?>>
                            <label for="jenis-pembedahan-ubah-lap-operasi-<?= $jp['id_variabel'] ?>" class="form-check-label">
                                <?= $jp['variabel'] ?>
                            </label>
                        </div>
                    </div>
                <?php endforeach ?>
            </div>
        </div>
        <!-- Akhir jenis pembedahan -->
        <!-- Mulai antibiotik propilaksis -->
        <div class="form-group">
            <label for="ab-propilaksis-ubah-lap-operasi">
                Antibiotik propilaksis
            </label>
            <div class="row px-1">
                <?php foreach ($antibiotikPropilaksis as $ap): ?>
                    <div class="col-md form-check form-check-inline">
                        <div class="radio radio-primary">
                            <input type="radio" class="ab-propilaksis-ubah-lap-operasi" name="antibiotik_propilaksis" id="ab-propilaksis-ubah-lap-operasi-<?= $ap['id_variabel'] ?>" value="<?= $ap['id_variabel'] ?>" <?= $ap['id_variabel'] == $detail['antibiotik_propilaksis'] ? 'checked' : null ?>>
                            <label for="ab-propilaksis-ubah-lap-operasi-<?= $ap['id_variabel'] ?>" class="form-check-label">
                                <?= $ap['variabel'] ?>
                            </label>
                        </div>
                    </div>
                <?php endforeach ?>
            </div>
        </div>
        <!-- Akhir antibiotik propilaksis -->
        <!-- Mulai keterangan antibiotik propilaksis -->
        <div class="form-group d-none" id="ket-ab-ubah-lap-operasi">
            <div class="card new-card">
                <div class="card-header new-card-header">Keterangan Antibiotik Propilaksis</div>
                <div class="card-body">
                    <!-- Mulai jenis antibiotik propilaksis -->
                    <div class="form-group">
                        <label for="jenis-ab-ubah-lap-operasi">
                            Jenis antibiotik propilaksis
                        </label>
                        <input type="text" class="form-control" id="jenis-ab-ubah-lap-operasi" placeholder="[ Sebutkan Jenis ]" name="jenis_antibiotik_propilaksis" value="<?= $detail['jenis_antibiotik_propilaksis'] ?>">
                    </div>
                    <!-- Akhir jenis antibiotik propilaksis -->
                    <!-- Mulai waktu pemberian antibiotik propilaksis -->
                    <div class="form-group">
                        <label for="waktu-ab-ubah-lap-operasi">
                            Waktu pemberian
                        </label>
                        <div class="input-group">
                            <input type="number" class="form-control" placeholder="[ Waktu Pemberian ]" id="waktu-ab-ubah-lap-operasi" name="waktu_antibiotik_propilaksis" value="<?= floatval($detail['waktu_antibiotik_propilaksis']) ?>" min="0" step="0.01">
                            <div class="input-group-append">
                                <span class="input-group-text">menit Sebelum Insisi</span>
                            </div>
                        </div>
                    </div>
                    <!-- Akhir waktu pemberian antibiotik propilaksis -->
                </div>
            </div>
        </div>
        <!-- Akhir keterangan antibiotik propilaksis -->
        <!-- Mulai tindakan operasi yang dilakukan -->
        <div class="form-group">
            <label for="tindakan-operasi-ubah-lap-operasi">
                Tindakan operasi yang dilakukan
            </label>
            <div class="row">
                <div class="col-md form-check form-check-inline">
                    <div class="checkbox checkbox-primary px-0">
                        <input type="checkbox" class="tindakan-operasi-ubah-lap-operasi" id="tindakan-operasi-ubah-lap-operasi-pilihan" name="jenis_tindakan" value="pilihan" <?= !empty($detail['tindakan_operasi']) ? 'checked' : null ?>>
                        <label for="tindakan-operasi-ubah-lap-operasi-pilihan" class="form-check-label">
                            ICD 9
                        </label>
                    </div>
                </div>
                <div class="col-md form-check form-check-inline">
                    <div class="checkbox checkbox-primary px-0">
                        <input type="checkbox" class="tindakan-operasi-ubah-lap-operasi" id="tindakan-operasi-ubah-lap-operasi-isian" name="jenis_tindakan" value="isian" <?= !empty($detail['tindakan_operasi_lainnya']) ? 'checked' : null ?>>
                        <label for="tindakan-operasi-ubah-lap-operasi-isian" class="form-check-label">
                            Teks
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <!-- Akhir tindakan operasi yang dilakukan -->
        <!-- Mulai pilihan tindakan operasi yang dilakukan -->
        <div class="form-group d-none" id="form-pilihan-tindakan-operasi-yang-dilakukan-ubah-lap-operasi">
            <label for="pilihan-tindakan-operasi-ubah-lap-operasi">
                Pilihan tindakan operasi yang dilakukan
            </label>
            <select name="tindakan_operasi[]" id="pilihan-tindakan-operasi-ubah-lap-operasi" class="form-control" multiple>
                <?php
                if (!empty($getSur['tindakan_procedure'])):
                    $dataTindakan = json_decode($getSur['tindakan_procedure']);
                    for ($i = 0; $i < count($dataTindakan); $i++):
                        ?>
                        <option id="tindakan-operasi-ubah-lap-operasi-<?= $dataTindakan[$i] ?>" value="<?= $dataTindakan[$i] ?>">
                            <?= $dataTindakan[$i] ?>
                        </option>
                        <?php
                    endfor;
                endif;
                ?>
            </select>
        </div>
        <!-- Akhir pilihan tindakan operasi yang dilakukan -->
        <!-- Mulai tindakan operasi lainnya yang dilakukan -->
        <div class="form-group d-none" id="form-tindakan-operasi-lainnya-ubah-lap-operasi">
            <label for="tindakan-operasi-lainnya-ubah-lap-operasi">
                Tindakan operasi lainnya yang dilakukan
            </label>
            <input type="text" class="form-control" placeholder="[ Tindakan Operasi Lainnya yang Dilakukan ]" id="tindakan-operasi-lainnya-ubah-lap-operasi" name="tindakan_operasi_lainnya" value="<?= $detail['tindakan_operasi_lainnya'] ?>">
        </div>
        <!-- Akhir tindakan operasi lainnya yang dilakukan -->
        <!-- Mulai deskripsi atau uraian operasi dokter 1 -->
        <div class="form-group" id="form-deskripsi-operasi-1-ubah-lap-operasi">
            <label for="deskripsi-operasi-1-ubah-lap-operasi">
                Deskripsi atau uraian operasi dokter 1
            </label>
            <textarea name="deskripsi_operasi_1" id="deskripsi-operasi-1-ubah-lap-operasi" class="form-control" placeholder="[ Sebutkan Deskripsi atau uraian Operasi Dokter 1 ]" required><?= $detail['deskripsi_operasi_1'] ?></textarea>
        </div>
        <!-- Akhir deskripsi atau uraian operasi dokter 1 -->
        <!-- Mulai deskripsi atau uraian operasi dokter 2 -->
        <div class="form-group d-none" id="form-deskripsi-operasi-2-ubah-lap-operasi">
            <label for="deskripsi-operasi-2-ubah-lap-operasi">
                Deskripsi atau uraian operasi dokter 2
            </label>
            <textarea name="deskripsi_operasi_2" id="deskripsi-operasi-2-ubah-lap-operasi" class="form-control" placeholder="[ Sebutkan Deskripsi atau uraian Operasi Dokter 2 ]"><?= $detail['deskripsi_operasi_2'] ?></textarea>
        </div>
        <!-- Akhir deskripsi atau uraian operasi dokter 2 -->
        <!-- Mulai deskripsi atau uraian operasi dokter 3 -->
        <div class="form-group d-none" id="form-deskripsi-operasi-3-ubah-lap-operasi">
            <label for="deskripsi-operasi-3-ubah-lap-operasi">
                Deskripsi atau uraian operasi dokter 3
            </label>
            <textarea name="deskripsi_operasi_3" id="deskripsi-operasi-3-ubah-lap-operasi" class="form-control" placeholder="[ Sebutkan Deskripsi atau uraian Operasi Dokter 3 ]"><?= $detail['deskripsi_operasi_3'] ?></textarea>
        </div>
        <!-- Akhir deskripsi atau uraian operasi dokter 3 -->
        <!-- Mulai komplikasi -->
        <div class="form-group">
            <label for="komplikasi-ubah-lap-operasi">
                Komplikasi
            </label>
            <div class="row px-1">
                <?php foreach ($komplikasi as $k): ?>
                    <div class="col-md form-check form-check-inline">
                        <div class="radio radio-primary">
                            <input type="radio" class="komplikasi-ubah-lap-operasi" name="komplikasi" id="komplikasi-ubah-lap-operasi-<?= $k['id_variabel'] ?>" value="<?= $k['id_variabel'] ?>" <?= $k['id_variabel'] == $detail['komplikasi'] ? 'checked' : null ?>>
                            <label for="komplikasi-ubah-lap-operasi-<?= $k['id_variabel'] ?>" class="form-check-label">
                                <?= $k['variabel'] ?>
                            </label>
                        </div>
                    </div>
                <?php endforeach ?>
            </div>
        </div>
        <!-- Akhir komplikasi -->
        <!-- Mulai sebutkan komplikasi -->
        <div class="form-group d-none" id="form-komplikasi-ubah-lap-operasi">
            <label for="ket-komplikasi-ubah-lap-operasi">
                Keterangan komplikasi
            </label>
            <input type="text" class="form-control" id="ket-komplikasi-ubah-lap-operasi" placeholder="[ Keterangan Komplikasi ]" name="isi_komplikasi" value="<?= $detail['isi_komplikasi'] ?>">
        </div>
        <!-- Akhir sebutkan komplikasi -->
        <!-- Mulai jumlah kehilangan darah -->
        <div class="form-group">
            <label for="jml-hlg-drh-ubah-lap-operasi">
                Jumlah kehilangan darah
            </label>
            <div class="input-group">
                <div class="input-group-prepend">
                    <span class="input-group-text">±</span>
                </div>
                <input type="number" class="form-control" id="jml-hlg-drh-ubah-lap-operasi" placeholder="[ Sebutkan Jumlah Kehilangan Darah ]" name="jml_kehilangan_darah" value="<?= floatval($detail['jml_kehilangan_darah']) ?>" min="0" step="0.01">
                <div class="input-group-append">
                    <span class="input-group-text">cc</span>
                </div>
            </div>
        </div>
        <!-- Akhir jumlah kehilangan darah -->
        <?php if ($jenis == 'detail'): ?>
            <!-- Mulai transfusi -->
            <div class="form-group" id="form-transfusi-ubah-lap-operasi">
                <label for="transfusi-ubah-lap-operasi">
                    Transfusi darah
                </label>
                <div class="input-group">
                    <div class="input-group-prepend">
                        <label for="jenis-transfusi-ubah-lap-operasi" class="input-group-text">Jenis</label>
                    </div>
                    <input type="text" class="form-control" id="jenis-transfusi-ubah-lap-operasi" placeholder="[ Sebutkan Jenisnya ]">
                    <div class="input-group-prepend">
                        <label for="volume-transfusi-ubah-lap-operasi" class="input-group-text">Volume</label>
                    </div>
                    <input type="number" class="form-control" id="volume-transfusi-ubah-lap-operasi" placeholder="[ Sebutkan Volumenya ]" step="0.01">
                    <div class="input-group-append">
                        <span class="input-group-text">cc</span>
                    </div>
                </div>
            </div>
            <!-- Akhir transfusi -->
            <!-- Mulai aksi transfusi -->
            <div class="row form-group">
                <div class="col-sm-6">
                    <button type="button" class="btn btn-outline-danger btn-block waves-effect" id="hapus-transfusi-ubah-lap-operasi">
                        Hapus transfusi
                    </button>
                </div>
                <div class="col-sm-6">
                    <button type="button" class="btn btn-outline-success btn-block waves-effect" id="tambah-transfusi-ubah-lap-operasi">
                        Tambah transfusi
                    </button>
                </div>
            </div>
            <!-- Akhir aksi transfusi -->
        <?php endif ?>
        <!-- Mulai tabel transfusi -->
        <div class="form-group" style="max-height: 200px; overflow-y: auto;">
            <div class="table-responsive">
                <table class="table table-bordered table-hover table-custom" cellspacing="0" width="100%">
                    <thead>
                        <tr class="table-tr-custom">
                            <th>#</th>
                            <th>Jenis Transfusi</th>
                            <th>Volume</th>
                        </tr>
                    </thead>
                    <tbody id="list-transfusi-ubah-lap-operasi">
                        <?php foreach ($historyTransfusi as $ht): ?>
                            <tr>
                                <td>
                                    <input type="checkbox" class="checkbox pilih-transfusi-ubah-lap-operasi" name="pilih_transfusi">
                                </td>
                                <td>
                                    <input type="text" class="form-control isi-jenis-transfusi-ubah-lap-operasi" name="jenis_transfusi[]" value="<?= $ht['jenis_transfusi'] ?>" readonly>
                                </td>
                                <td>
                                    <input type="number" class="form-control isi-volume-transfusi-ubah-lap-operasi" name="volume_transfusi[]" value="<?= floatval($ht['volume_transfusi']) ?>" step="0.01" readonly>
                                </td>
                            </tr>
                        <?php endforeach ?>
                    </tbody>
                </table>
            </div>
        </div>
        <!-- Akhir tabel transfusi -->
        <!-- Mulai spesimen -->
        <div class="form-group">
            <label for="spesimen-ubah-lap-operasi">
                Spesimen
            </label>
            <div class="row px-1">
                <?php foreach ($spesimen as $s): ?>
                    <div class="col-md form-check form-check-inline">
                        <div class="radio radio-primary">
                            <input type="radio" class="spesimen-ubah-lap-operasi" name="spesimen" id="spesimen-ubah-lap-operasi-<?= $s['id_variabel'] ?>" value="<?= $s['id_variabel'] ?>" <?= $s['id_variabel'] == $detail['spesimen'] ? 'checked' : null ?>>
                            <label for="spesimen-ubah-lap-operasi-<?= $s['id_variabel'] ?>" class="form-check-label">
                                <?= $s['variabel'] ?>
                            </label>
                        </div>
                    </div>
                <?php endforeach ?>
            </div>
        </div>
        <!-- Akhir spesimen -->
        <!-- Mulai sebutkan spesimen -->
        <div id="form-spesimen-ubah-lap-operasi" class="form-group d-none">
            <label for="sebutkan-spesimen-ubah-lap-operasi">
                Sebutkan spesimen
            </label>
            <textarea name="isi_spesimen" class="form-control" id="sebutkan-spesimen-ubah-lap-operasi" placeholder="[ Sebutkan Spesimen ]"><?= $detail['isi_spesimen'] ?></textarea>
        </div>
        <!-- Akhir sebutkan spesimen -->
        <!-- Mulai pemasangan implan -->
        <div class="form-group">
            <label for="pemasangan-implan-ubah-lap-operasi">
                Pemasangan implan
            </label>
            <div class="row px-1">
                <?php foreach ($pemasanganImplan as $pi): ?>
                    <div class="col-md form-check form-check-inline">
                        <div class="radio radio-primary">
                            <input type="radio" class="pemasangan-implan-ubah-lap-operasi" name="pemasangan_implan" id="pemasangan-implan-ubah-lap-operasi-<?= $pi['id_variabel'] ?>" value="<?= $pi['id_variabel'] ?>" <?= $pi['id_variabel'] == $detail['pemasangan_implan'] ? 'checked' : null ?>>
                            <label for="pemasangan-implan-ubah-lap-operasi-<?= $pi['id_variabel'] ?>" class="form-check-label">
                                <?= $pi['variabel'] ?>
                            </label>
                        </div>
                    </div>
                <?php endforeach ?>
            </div>
        </div>
        <!-- Akhir pemasangan implan -->
        <!-- Mulai keterangan pemasangan implan -->
        <div class="form-group d-none" id="form-pemasangan-implan-ubah-lap-operasi">
            <div class="card new-card">
                <div class="card-header new-card-header">Keterangan Pemasangan Implan</div>
                <div class="card-body">
                    <!-- Mulai nomor seri implan -->
                    <div class="form-group">
                        <label for="seri-implan-ubah-lap-operasi">
                            Nomor seri implan
                        </label>
                        <input type="text" class="form-control" id="seri-implan-ubah-lap-operasi" placeholder="[ Nomor Seri Implan ]" name="seri_implan" value="<?= $detail['seri_implan'] ?>">
                    </div>
                    <!-- Akhir nomor seri implan -->
                    <!-- Mulai nama implan -->
                    <div class="form-group">
                        <label for="nama-implan-ubah-lap-operasi">
                            Nama implan
                        </label>
                        <input type="text" class="form-control" id="nama-implan-ubah-lap-operasi" placeholder="[ Nama Implan ]" name="nama_implan" value="<?= $detail['nama_implan'] ?>">
                    </div>
                    <!-- Akhir nama implan -->
                </div>
            </div>
        </div>
        <!-- Akhir keterangan pemasangan implan -->
        <!-- Mulai riwayat penyakit sekarang -->
        <div class="form-group">
            <label for="riwayat-penyakit-sekarang-ubah-lap-operasi">
                Data riwayat penyakit sekarang
            </label>
            <div class="row px-1">
                <?php foreach ($pilihanCPPT as $pc): ?>
                    <div class="col-md form-check form-check-inline">
                        <div class="radio radio-primary">
                            <input type="radio" name="riwayat_penyakit_sekarang" id="riwayat-penyakit-sekarang-ubah-lap-operasi-<?= $pc['id_variabel'] ?>" value="<?= $pc['id_variabel'] ?>" class="riwayat-penyakit-sekarang-ubah-lap-operasi" required>
                            <label for="riwayat-penyakit-sekarang-ubah-lap-operasi-<?= $pc['id_variabel'] ?>" class="form-check-label">
                                <?= $pc['variabel'] ?>
                            </label>
                        </div>
                    </div>
                <?php endforeach ?>
            </div>
        </div>
        <!-- Akhir riwayat penyakit sekarang -->
        <!-- Mulai keterangan riwayat penyakit sekarang -->
        <div class="form-group alert alert-info text-justify d-none" id="info-riwayat-penyakit-sekarang-ubah-lap-operasi"></div>
        <div class="form-group <?= isset($detail['ket_riwayat_penyakit_sekarang']) ? null : 'd-none' ?>" id="form-riwayat-penyakit-sekarang-ubah-lap-operasi">
            <label for="ket-riwayat-penyakit-sekarang-ubah-lap-operasi">
                Riwayat penyakit sekarang
            </label>
            <textarea name="ket_riwayat_penyakit_sekarang" class="form-control" id="ket-riwayat-penyakit-sekarang-ubah-lap-operasi" placeholder="[ Riwayat Penyakit Sekarang ]"><?= $detail['ket_riwayat_penyakit_sekarang'] ?></textarea>
        </div>
        <!-- Akhir keterangan riwayat penyakit sekarang -->
         <!-- Mulai Formulir Pendaftaran Operasi -->
        <div class="form-group">
            <label for="daftar-tunggu-ubah-lap-operasi">
                Formulir Pendaftaran Operasi
            </label>
            <select name="id_waiting_list" id="daftar-tunggu-ubah-lap-operasi" class="form-control">
                <option value="">Kosong</option>
                <?php foreach ($daftarTunggu as $dt): ?>
                    <option id="daftar-tunggu-ubah-lap-operasi-<?= $dt['id'] ?>" value="<?= $dt['id'] ?>">
                        <?= date('d/m/Y', strtotime($dt['tanggal'])) . ' - ' . $dt['dokter'] ?>
                    </option>
                <?php endforeach ?>
            </select>
        </div>
        <!-- Akhir Formulir Pendaftaran Operasi -->
        <?= $jenis == 'laporan' ? "</fieldset>" : null ?>
    </div>
    <?php if ($jenis == 'detail'): ?>
        <div class="modal-footer">
            <button class="btn btn-primary waves-effect" type="submit" id="ubah-ubah-lap-operasi">
                <i class="fa fa-save"></i> Simpan perubahan
            </button>
        </div>
    <?php endif ?>
</form>
<!-- Akhir form laporan operasi -->

<script>
    $(document).ready(function () {
        // Mulai variabel
        let dokOperatorBedah = $('#dok-operator-bedah-ubah-lap-operasi');
        let assOperator = $('#ass-operator-ubah-lap-operasi');
        let instrumentator = $('#instrumentator-ubah-lap-operasi');
        let dokAnestesi = $('#dok-anestesi-ubah-lap-operasi');
        // Akhir variabel

        // mulai daftar tunggu
        $('#daftar-tunggu-ubah-lap-operasi').select2({
            allowClear: true,
            placeholder: '[ Pilih Formulir Pendaftaran Operasi ]'
        }).val(<?= $detail['id_wlo'] ?? 0 ?>).trigger('change');
        // akhir daftar tunggu

        // Mulai dokter bedah
        <?php if (isset($detail['dokter_bedah'])) { ?>
            dokOperatorBedah.select2({
                maximumSelectionLength: 3, // Ambil jumlah dokter
                placeholder: '[ Pilih dokter operator bedah ]',
            }).val(<?= $detail['dokter_bedah'] ?>).trigger('change');
        <?php } else { ?>
            dokOperatorBedah.select2({
                maximumSelectionLength: 3, // Ambil jumlah dokter
                placeholder: '[ Pilih dokter operator bedah ]',
            });
            <?php
        }
        if ($jenis == 'laporan') {
            ?>
            dokOperatorBedah.select2({
                disabled: true,
            });
        <?php } ?>
        // Akhir dokter bedah

        // Mulai asisten operator
        <?php if (isset($detail['asisten_operator'])) { ?>
            assOperator.select2({
                placeholder: '[ Pilih asisten operator bedah ]',
            }).val(<?= $detail['asisten_operator'] ?>).trigger('change');
        <?php } else { ?>
            assOperator.select2({
                placeholder: '[ Pilih asisten operator bedah ]',
            });
            <?php
        }
        if ($jenis == 'laporan') {
            ?>
            assOperator.select2({
                disabled: true,
            });
        <?php } ?>
        // Akhir asisten operator

        // Mulai perawat instrumentator
        <?php if (isset($detail['perawat_instrumentator'])) { ?>
            instrumentator.select2({
                placeholder: '[ Pilih perawat instrumentator ]',
            }).val(<?= $detail['perawat_instrumentator'] ?>).trigger('change');
        <?php } else { ?>
            instrumentator.select2({
                placeholder: '[ Pilih perawat instrumentator ]',
            });
            <?php
        }
        if ($jenis == 'laporan') {
            ?>
            instrumentator.select2({
                disabled: true,
            });
        <?php } ?>
        // Akhir perawat instrumentator

        // Mulai perawat sirkuler
$('#sirkuler-ubah-lap-operasi').select2({
    placeholder: '[ Pilih perawat sirkuler ]',
    dropdownParent: $('#sirkuler-ubah-lap-operasi').closest('.modal')
});
        <?php if ($jenis == 'laporan') { ?>
$('#sirkuler-ubah-lap-operasi').select2({
    disabled: true,
    dropdownParent: $('#sirkuler-ubah-lap-operasi').closest('.modal')
});
        <?php } ?>
        // Akhir perawat sirkuler

        // Mulai dokter anestesi
        <?php if (isset($detail['dokter_anestesi'])) { ?>
            dokAnestesi.select2({
                placeholder: '[ Pilih dokter anestesi ]',
            }).val(<?= $detail['dokter_anestesi'] ?>).trigger('change');
        <?php } else { ?>
            dokAnestesi.select2({
                placeholder: '[ Pilih dokter anestesi ]',
            });
            <?php
        }
        if ($jenis == 'laporan') {
            ?>
            dokAnestesi.select2({
                disabled: true,
            });
        <?php } ?>
        // Akhir dokter anestesi

        // Mulai isi diagnosis pra bedah
        $('#pilihan-diagnosis-pra-bedah-ubah-lap-operasi').select2(
            <?php if ($jenis == 'laporan') { ?> {
                disabled: true,
            }
        <?php } ?>
        );
        // Akhir isi diagnosis pra bedah

        // Mulai pilihan diagnosis pra bedah
        let pilihanDiagnosisPraBedah = $('#pilihan-diagnosis-pra-bedah-ubah-lap-operasi');
        pilihanDiagnosisPraBedah.select2({
            placeholder: '[ Pilih diagnosis pra bedah ]',
            ajax: {
                url: "<?= base_url('operasi/FormLaporanOperasi/icd10') ?>",
                dataType: 'json',
                delay: 250,
                processResults: function (data) {
                    return {
                        results: data
                    };
                },
                cache: true
            }
        });
        <?php
        // Mulai isi diagnosis pra bedah
        if (isset($isiPraBedah)) {
            foreach ($isiPraBedah as $ipb) {
                ?>
                $('#pilihan-diagnosis-pra-bedah-ubah-lap-operasi').append("<option value='<?= $ipb['id_pra_bedah'] ?>' selected><?= $ipb['id_pra_bedah'] . ' - ' . $ipb['pra_bedah'] ?></option>");
                <?php
            }
        }
        // Akhir isi diagnosis pra bedah
        if ($jenis == 'laporan') {
            ?>
            pilihanDiagnosisPraBedah.select2({
                disabled: true,
            });
        <?php } ?>
        // Akhir pilihan diagnosis pra bedah

        // Mulai isi diagnosis pasca bedah
        $('#pilihan-diagnosis-pasca-bedah-ubah-lap-operasi').select2(
            <?php if ($jenis == 'laporan') { ?> {
                disabled: true,
            }
        <?php } ?>
        );
        // Akhir isi diagnosis pasca bedah

        // Mulai pilihan diagnosis pasca bedah
        let pilihanDiagnosisPascaBedah = $('#pilihan-diagnosis-pasca-bedah-ubah-lap-operasi');
        pilihanDiagnosisPascaBedah.select2({
            placeholder: '[ Pilih diagnosis pasca bedah ]',
            ajax: {
                url: "<?= base_url('operasi/FormLaporanOperasi/icd10') ?>",
                dataType: 'json',
                delay: 250,
                processResults: function (data) {
                    return {
                        results: data
                    };
                },
                cache: true
            }
        });
        <?php
        // Mulai isi diagnosis pasca bedah
        if (isset($isiPascaBedah)) {
            foreach ($isiPascaBedah as $ipb) {
                ?>
                $('#pilihan-diagnosis-pasca-bedah-ubah-lap-operasi').append("<option value='<?= $ipb['id_pasca_bedah'] ?>' selected><?= $ipb['id_pasca_bedah'] . ' - ' . $ipb['pasca_bedah'] ?></option>");
                <?php
            }
        }
        // Akhir isi diagnosis pasca bedah
        if ($jenis == 'laporan') {
            ?>
            pilihanDiagnosisPascaBedah.select2({
                disabled: true,
            });
        <?php } ?>
        // Akhir pilihan diagnosis pasca bedah

        // Mulai isi tindakan operasi yang dilakukan
        $('#pilihan-tindakan-operasi-ubah-lap-operasi').select2(
            <?php if ($jenis == 'laporan') { ?> {
                disabled: true,
            }
        <?php } ?>
        );
        // Akhir isi tindakan operasi yang dilakukan

        // Mulai pilihan tindakan operasi yang dilakukan
        let pilihanTindakanOperasi = $('#pilihan-tindakan-operasi-ubah-lap-operasi');
        pilihanTindakanOperasi.select2({
            placeholder: '[ Pilih tindakan operasi yang dilakukan ]',
            ajax: {
                url: "<?= base_url('operasi/FormLaporanOperasi/icd9') ?>",
                dataType: 'json',
                delay: 250,
                processResults: function (data) {
                    return {
                        results: data
                    };
                },
                cache: true
            }
        });
        <?php
        // Mulai isi tindakan
        if (isset($isiTindakan)) {
            foreach ($isiTindakan as $it) {
                ?>
                $('#pilihan-tindakan-operasi-ubah-lap-operasi').append("<option value='<?= $it['id_tindakan'] ?>' selected><?= $it['id_tindakan'] . ' - ' . $it['tindakan'] ?></option>");
                <?php
            }
        }
        // Akhir isi tindakan

        if ($jenis == 'laporan') {
            ?>
            pilihanTindakanOperasi.select2({
                disabled: true,
            });
        <?php } ?>
        // Akhir pilihan tindakan operasi yang dilakukan

        // Mulai deskripsi atau uraian operasi
        dokOperatorBedah.change(function () {
            let isi = [];
            let data = dokOperatorBedah.find(':selected');
            isi.push(data);
            if (data.length === 1) {
                $('#form-deskripsi-operasi-2-ubah-lap-operasi, #form-deskripsi-operasi-3-ubah-lap-operasi').addClass('d-none');
                $('#deskripsi-operasi-2-ubah-lap-operasi, #deskripsi-operasi-3-ubah-lap-operasi').removeAttr('required', null).val();
            } else if (data.length === 2) {
                $('#form-deskripsi-operasi-2-ubah-lap-operasi').removeClass('d-none');
                $('#deskripsi-operasi-2-ubah-lap-operasi').attr('required', null);
                $('#form-deskripsi-operasi-3-ubah-lap-operasi').addClass('d-none');
                $('#deskripsi-operasi-3-ubah-lap-operasi').removeAttr('required', null).val();
            } else if (data.length === 3) {
                $('#form-deskripsi-operasi-2-ubah-lap-operasi, #form-deskripsi-operasi-3-ubah-lap-operasi').removeClass('d-none');
                $('#deskripsi-operasi-2-ubah-lap-operasi, #deskripsi-operasi-3-ubah-lap-operasi').attr('required', null);
            }
        });

        if ($('#deskripsi-operasi-2-ubah-lap-operasi').val()) {
            $('#form-deskripsi-operasi-2-ubah-lap-operasi').removeClass('d-none');
        } else {
            $('#form-deskripsi-operasi-2-ubah-lap-operasi').addClass('d-none');
        }

        if ($('#deskripsi-operasi-3-ubah-lap-operasi').val()) {
            $('#form-deskripsi-operasi-3-ubah-lap-operasi').removeClass('d-none');
        } else {
            $('#form-deskripsi-operasi-3-ubah-lap-operasi').addClass('d-none');
        }
        // Akhir deskripsi atau uraian operasi

        // Mulai jenis anestesi
        $('.jenis-anestesi-ubah-lap-operasi').change(function () {
            let id = $(this).val();
            if (id === '1982') {
                $('#ket-anestesi-lokal-ubah-lap-operasi').removeClass('d-none');
            } else {
                $('#ket-anestesi-lokal-ubah-lap-operasi').addClass('d-none');
                $('.teknik-anestesi-lokal-ubah-lap-operasi').prop('checked', false);
                $('#lokasi-ubah-lap-operasi').val(null);
                $('#obat-anestesi-ubah-lap-operasi').val(null);
                $('.respon-hipersensitivitas-ubah-lap-operasi').prop('checked', false);
                $('.kejadian-toksikasi-ubah-lap-operasi').prop('checked', false);
                $('#form-teknik-ubah-lap-operasi').addClass('d-none');
                $('#ket-teknik-ubah-lap-operasi').val(null);
                $('#waktu-ab-ubah-lap-operasi').val(null);
                $('#form-respon-ubah-lap-operasi').addClass('d-none');
                $('#ket-respon-ubah-lap-operasi').val(null);
                $('#form-toksikasi-ubah-lap-operasi').addClass('d-none');
                $('#sebutkan-toksikasi-ubah-lap-operasi').val(null);
            }
        });
        if ($('.jenis-anestesi-ubah-lap-operasi:checked').val() === '1982') {
            $('#ket-anestesi-lokal-ubah-lap-operasi').removeClass('d-none');
        }
        // Akhir jenis anestesi

        // Mulai diagnosis pra bedah
        $('.diagnosis-pra-bedah-ubah-lap-operasi').change(function () {
            let id = $(this).val();
            if (id === 'pilihan') {
                $('#form-isi-diagnosis-pra-bedah-ubah-lap-operasi, #form-pilihan-diagnosis-pra-bedah-ubah-lap-operasi').removeClass('d-none');
                pilihanDiagnosisPraBedah.attr('required', null);
                $('#form-diagnosis-pra-bedah-lainnya-ubah-lap-operasi').addClass('d-none');
                $('#diagnosis-pra-bedah-lainnya-ubah-lap-operasi').removeAttr('required', null).val(null);
            } else if (id === 'isian') {
                $('#form-isi-diagnosis-pra-bedah-ubah-lap-operasi, #form-pilihan-diagnosis-pra-bedah-ubah-lap-operasi').addClass('d-none');
                pilihanDiagnosisPraBedah.removeAttr('required', null).val(null).trigger('change');
                $('#form-diagnosis-pra-bedah-lainnya-ubah-lap-operasi').removeClass('d-none');
                $('#diagnosis-pra-bedah-lainnya-ubah-lap-operasi').attr('required', null);
            }
        });
        let diagnosisPraBedah = $('.diagnosis-pra-bedah-ubah-lap-operasi:checked');
        if (diagnosisPraBedah.val() === 'pilihan') {
            $('#form-isi-diagnosis-pra-bedah-ubah-lap-operasi, #form-pilihan-diagnosis-pra-bedah-ubah-lap-operasi').removeClass('d-none');
            pilihanDiagnosisPraBedah.attr('required', null);
            $('#form-diagnosis-pra-bedah-lainnya-ubah-lap-operasi').addClass('d-none');
            $('#diagnosis-pra-bedah-lainnya-ubah-lap-operasi').removeAttr('required', null).val(null);
        } else if (diagnosisPraBedah.val() === 'isian') {
            $('#form-isi-diagnosis-pra-bedah-ubah-lap-operasi, #form-pilihan-diagnosis-pra-bedah-ubah-lap-operasi').addClass('d-none');
            pilihanDiagnosisPraBedah.removeAttr('required', null).val(null).trigger('change');
            $('#form-diagnosis-pra-bedah-lainnya-ubah-lap-operasi').removeClass('d-none');
            $('#diagnosis-pra-bedah-lainnya-ubah-lap-operasi').attr('required', null);
        }
        // Akhir diagnosis pra bedah

        // Mulai diagnosis pasca bedah
        $('.diagnosis-pasca-bedah-ubah-lap-operasi').change(function () {
            let id = $(this).val();
            if (id === 'pilihan') {
                $('#form-isi-diagnosis-pasca-bedah-ubah-lap-operasi, #form-pilihan-diagnosis-pasca-bedah-ubah-lap-operasi').removeClass('d-none');
                pilihanDiagnosisPascaBedah.attr('required', null);
                $('#form-diagnosis-pasca-bedah-lainnya-ubah-lap-operasi').addClass('d-none');
                $('#diagnosis-pasca-bedah-lainnya-ubah-lap-operasi').removeAttr('required', null).val(null);
            } else if (id === 'isian') {
                $('#form-pilihan-diagnosis-pasca-bedah-ubah-lap-operasi').addClass('d-none');
                pilihanDiagnosisPascaBedah.removeAttr('required', null).val(null).trigger('change');
                $('#form-diagnosis-pasca-bedah-lainnya-ubah-lap-operasi').removeClass('d-none');
                $('#diagnosis-pasca-bedah-lainnya-ubah-lap-operasi').attr('required', null);
            }
        });
        let diagnosisPascaBedah = $('.diagnosis-pasca-bedah-ubah-lap-operasi:checked');
        if (diagnosisPascaBedah.val() === 'pilihan') {
            $('#form-isi-diagnosis-pasca-bedah-ubah-lap-operasi, #form-pilihan-diagnosis-pasca-bedah-ubah-lap-operasi').removeClass('d-none');
            pilihanDiagnosisPascaBedah.attr('required', null);
            $('#form-diagnosis-pasca-bedah-lainnya-ubah-lap-operasi').addClass('d-none');
            $('#diagnosis-pasca-bedah-lainnya-ubah-lap-operasi').removeAttr('required', null).val(null);
        } else if (diagnosisPascaBedah.val() === 'isian') {
            $('#form-isi-diagnosis-pasca-bedah-ubah-lap-operasi, #form-pilihan-diagnosis-pasca-bedah-ubah-lap-operasi').addClass('d-none');
            pilihanDiagnosisPascaBedah.removeAttr('required', null).val(null).trigger('change');
            $('#form-diagnosis-pasca-bedah-lainnya-ubah-lap-operasi').removeClass('d-none');
            $('#diagnosis-pasca-bedah-lainnya-ubah-lap-operasi').attr('required', null);
        }
        // Akhir diagnosis pasca bedah

        // Mulai tindakan operasi yang dilakukan
        let checkPilihanTindakanOperasi = $('#tindakan-operasi-ubah-lap-operasi-pilihan');
        let checkIsianTindakanOperasi = $('#tindakan-operasi-ubah-lap-operasi-isian');
        checkPilihanTindakanOperasi.change(function () {
            if (checkPilihanTindakanOperasi.prop('checked')) {
                $('#form-pilihan-tindakan-operasi-yang-dilakukan-ubah-lap-operasi').removeClass('d-none');
                pilihanTindakanOperasi.attr('required', null);
            } else if (checkPilihanTindakanOperasi.prop('checked', false)) {
                $('#form-pilihan-tindakan-operasi-yang-dilakukan-ubah-lap-operasi').addClass('d-none');
                pilihanTindakanOperasi.removeAttr('required', null).val(null).trigger('change');
            }
        });
        checkIsianTindakanOperasi.change(function () {
            if (checkIsianTindakanOperasi.prop('checked')) {
                $('#form-tindakan-operasi-lainnya-ubah-lap-operasi').removeClass('d-none');
                $('#tindakan-operasi-lainnya-ubah-lap-operasi').attr('required', null);
            } else if (checkIsianTindakanOperasi.prop('checked', false)) {
                $('#form-tindakan-operasi-lainnya-ubah-lap-operasi').addClass('d-none');
                $('#tindakan-operasi-lainnya-ubah-lap-operasi').removeAttr('required', null).val(null);
            }
        });
        if (checkPilihanTindakanOperasi.prop('checked')) {
            $('#form-pilihan-tindakan-operasi-yang-dilakukan-ubah-lap-operasi').removeClass('d-none');
            pilihanTindakanOperasi.attr('required', null);
        } else if (checkPilihanTindakanOperasi.prop('checked', false)) {
            $('#form-pilihan-tindakan-operasi-yang-dilakukan-ubah-lap-operasi').addClass('d-none');
            pilihanTindakanOperasi.removeAttr('required', null).val(null).trigger('change');
        }
        if (checkIsianTindakanOperasi.prop('checked')) {
            $('#form-tindakan-operasi-lainnya-ubah-lap-operasi').removeClass('d-none');
            $('#tindakan-operasi-lainnya-ubah-lap-operasi').attr('required', null);
        } else if (checkIsianTindakanOperasi.prop('checked', false)) {
            $('#form-tindakan-operasi-lainnya-ubah-lap-operasi').addClass('d-none');
            $('#tindakan-operasi-lainnya-ubah-lap-operasi').removeAttr('required', null).val(null);
        }
        // Akhir tindakan operasi yang dilakukan

        // Mulai antibiotik propilaksis
        $('.ab-propilaksis-ubah-lap-operasi').change(function () {
            let id = $(this).val();
            if (id === '1978') {
                $('#ket-ab-ubah-lap-operasi').removeClass('d-none');
            } else {
                $('#ket-ab-ubah-lap-operasi').addClass('d-none');
                $('#jenis-ab-ubah-lap-operasi').val(null);
            }
        });
        if ($('.ab-propilaksis-ubah-lap-operasi:checked').val() === '1978') {
            $('#ket-ab-ubah-lap-operasi').removeClass('d-none');
        }
        // Akhir antibiotik propilaksis

        // Mulai teknik anestesi lokal
        $('.teknik-anestesi-lokal-ubah-lap-operasi').change(function () {
            let id = $(this).val();
            if (id === '2149') {
                $('#form-teknik-ubah-lap-operasi').removeClass('d-none');
            } else {
                $('#form-teknik-ubah-lap-operasi').addClass('d-none');
                $('#ket-teknik-ubah-lap-operasi').val(null);
                $('#waktu-ab-ubah-lap-operasi').val(null);
            }
        });
        if ($('.teknik-anestesi-lokal-ubah-lap-operasi:checked').val() === '2149') {
            $('#form-teknik-ubah-lap-operasi').removeClass('d-none');
        }
        // Akhir teknik anestesi lokal

        // Mulai respon hipersensitivitas
        $('.respon-hipersensitivitas-ubah-lap-operasi').change(function () {
            let id = $(this).val();
            if (id === '2151') {
                $('#form-respon-ubah-lap-operasi').removeClass('d-none');
            } else {
                $('#form-respon-ubah-lap-operasi').addClass('d-none');
                $('#ket-respon-ubah-lap-operasi').val(null);
            }
        });
        if ($('.respon-hipersensitivitas-ubah-lap-operasi:checked').val() === '2151') {
            $('#form-respon-ubah-lap-operasi').removeClass('d-none');
        }
        // Akhir respon hipersensitivitas

        // Mulai kejadian toksikasi
        $('.kejadian-toksikasi-ubah-lap-operasi').change(function () {
            let id = $(this).val();
            if (id === '2153') {
                $('#form-toksikasi-ubah-lap-operasi').removeClass('d-none');
            } else {
                $('#form-toksikasi-ubah-lap-operasi').addClass('d-none');
                $('#sebutkan-toksikasi-ubah-lap-operasi').val(null);
            }
        });
        if ($('.kejadian-toksikasi-ubah-lap-operasi:checked').val() === '2153') {
            $('#form-toksikasi-ubah-lap-operasi').removeClass('d-none');
        }
        // Akhir kejadian toksikasi

        // Mulai komplikasi
        $('.komplikasi-ubah-lap-operasi').change(function () {
            let id = $(this).val();
            if (id === '2155') {
                $('#form-komplikasi-ubah-lap-operasi').removeClass('d-none');
            } else {
                $('#form-komplikasi-ubah-lap-operasi').addClass('d-none');
                $('#ket-komplikasi-ubah-lap-operasi').val(null);
            }
        });
        if ($('.komplikasi-ubah-lap-operasi:checked').val() === '2155') {
            $('#form-komplikasi-ubah-lap-operasi').removeClass('d-none');
        }
        // Akhir komplikasi

        <?php if ($jenis == 'detail') { ?>
            // Mulai tambah transfusi darah
            $('#tambah-transfusi-ubah-lap-operasi').click(function () {
                let jenis = $('#jenis-transfusi-ubah-lap-operasi');
                let volume = $('#volume-transfusi-ubah-lap-operasi');
                let isi =
                    "<tr>" +
                    "<td><input type='checkbox' id='pilih-transfusi-ubah-lap-operasi'></td>" +
                    "<td><input type='text' class='form-control isi-jenis-transfusi-ubah-lap-operasi' name='jenis_transfusi[]' value='" + jenis.val() + "' readonly></td>" +
                    "<td><input type='text' class='form-control isi-volume-transfusi-ubah-lap-operasi' name='volume_transfusi[]' value='" + volume.val() + "' readonly></td>" +
                    "</tr>";
                if (jenis.val() && volume.val()) {
                    $(isi).hide().appendTo('table tbody#list-transfusi-ubah-lap-operasi').fadeIn(1000);
                }
                // Akhir tambah transfusi darah

                // Bersihkan Form
                jenis.val(null);
                volume.val(null);
            });


            // Mulai hapus transfusi darah
            $('#hapus-transfusi-ubah-lap-operasi').click(function () {
                $('table tbody#list-transfusi-ubah-lap-operasi').find('.pilih-transfusi-ubah-lap-operasi').each(function () {
                    if ($(this).is(':checked')) {
                        $(this).parents('tr').fadeOut(500, function () {
                            $(this).remove();
                        });
                    }
                });
            });
            // Akhir hapus transfusi darah
        <?php } ?>

        // Mulai spesimen
        $('.spesimen-ubah-lap-operasi').change(function () {
            let id = $(this).val();
            if (id === '2161') {
                $('#form-spesimen-ubah-lap-operasi').removeClass('d-none');
            } else {
                $('#form-spesimen-ubah-lap-operasi').addClass('d-none');
                $('#sebutkan-spesimen-ubah-lap-operasi').val(null);
            }
        });
        if ($('.spesimen-ubah-lap-operasi:checked').val() === '2161') {
            $('#form-spesimen-ubah-lap-operasi').removeClass('d-none');
        }
        // Akhir spesimen

        // Mulai pemasangan implan
        $('.pemasangan-implan-ubah-lap-operasi').change(function () {
            let id = $(this).val();
            if (id === '2163') {
                $('#form-pemasangan-implan-ubah-lap-operasi').removeClass('d-none');
            } else {
                $('#form-pemasangan-implan-ubah-lap-operasi').addClass('d-none');
                $('#nama-implan-ubah-lap-operasi, #seri-implan-ubah-lap-operasi').val(null);
            }
        });
        if ($('.pemasangan-implan-ubah-lap-operasi:checked').val() === '2163') {
            $('#form-pemasangan-implan-ubah-lap-operasi').removeClass('d-none');
        }
        // Akhir pemasangan implan

        // Mulai riwayat penyakit sekarang
        let checkRiwayatPenyakitSekarang = $('.riwayat-penyakit-sekarang-ubah-lap-operasi');
        checkRiwayatPenyakitSekarang.click(function () {
            let id = $(this).val();
            if (id === '4661') {
                $('#info-riwayat-penyakit-sekarang-ubah-lap-operasi').addClass('d-none');
                $('#form-riwayat-penyakit-sekarang-ubah-lap-operasi').removeClass('d-none');
                $('#ket-riwayat-penyakit-sekarang-ubah-lap-operasi').attr('required', null).removeAttr('readonly', null);
            } else if (id === '4662') {
                riwayatPenyakitSekarang('<?= $detail['nokun'] ?>');
                $('#form-riwayat-penyakit-sekarang-ubah-lap-operasi').removeClass('d-none');
                $('#ket-riwayat-penyakit-sekarang-ubah-lap-operasi').attr('readonly', 'required');
            } else if (id === '4663') {
                $('#info-riwayat-penyakit-sekarang-ubah-lap-operasi, #form-riwayat-penyakit-sekarang-ubah-lap-operasi').addClass('d-none');
                $('#ket-riwayat-penyakit-sekarang-ubah-lap-operasi').removeAttr('required', null).val(null);
            }
        });

        function riwayatPenyakitSekarang(id, status = 0) {
            let riwayat_penyakit_sekarang;
            if (status === 1) {
                riwayat_penyakit_sekarang = getJSON("<?= base_url('rekam_medis/Medis/ambilRiwayatPenyakitSekarang') ?>", {
                    id: id
                });
            } else {
                riwayat_penyakit_sekarang = getJSON("<?= base_url('rekam_medis/Medis/ambilRiwayatPenyakitSekarang') ?>", {
                    nokun: id
                });
            }

            if (riwayat_penyakit_sekarang.data !== null) {
                $('#info-riwayat-penyakit-sekarang-ubah-lap-operasi').html('Dibuat oleh <b>' + riwayat_penyakit_sekarang.data['oleh_desc'] + '</b> pada ' + moment(riwayat_penyakit_sekarang.data['created_at']).format('LLL') + null).removeClass('d-none');
                $('#ket-riwayat-penyakit-sekarang-ubah-lap-operasi').val(riwayat_penyakit_sekarang.data['riwayat_sakit_sekarang']);
            } else {
                toastr.warning('Data riwayat penyakit sekarang tidak tersedia');
            }
        }
        // Akhir riwayat penyakit sekarang

        // Mulai ubah
        $('#form-ubah-lap-operasi').submit(function (event) {
            event.preventDefault();
            alertify.confirm('Data akan diubah', 'Pilih OK jika setuju mengubah', function () {
                let form = $('#form-ubah-lap-operasi').serialize();

                $.ajax({
                    url: "<?= base_url('operasi/FormLaporanOperasi/aksi/simpan') ?>",
                    data: form,
                    method: 'POST',
                    dataType: 'json',
                    success: function (data) {
                        if (data.status === 'success') {
                            toastr.success('Berhasil diubah');
                            $('#modal-history-lap-operasi, #modal-detail-lap-operasi').modal('toggle');
                        } else {
                            $.each(data.errors, function (index, element) {
                                toastr.warning(element);
                            });
                        }
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        toastr.error('Internal Server Error!');
                    }
                });
            }, function () {
                alertify.error('Batal')
            });
        });
        // Akhir ubah
    });
</script>

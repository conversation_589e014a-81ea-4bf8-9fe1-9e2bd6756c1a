<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PengkajianPraOperasiModel extends MY_Model
{
    protected $_table = 'medis.tb_pengkajian_operasi';
    protected $_primary_key = 'id';
    protected $_order_by = 'id';
    protected $_order_by_type = 'DESC';

    public function __construct()
    {
        parent::__construct();
    }
    public function rules()
    {
        return [
            [
                'field' => 'keluhan_utama',
                'label' => 'Keluhan utama',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'riwayat_penyakit_sekarang',
                'label' => 'Riwayat penyakit sekarang',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'riwayat_guna_obat',
                'label' => 'Riwayat penggunaan obat',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'alergi',
                'label' => 'Riwayat alergi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'psikologis',
                'label' => 'Psikologis',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'hubungan_anggota_keluarga',
                'label' => 'Hubungan dengan anggota keluarga',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'pencari_nafkah_utama',
                'label' => 'Pencari nafkah utama',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'tinggal_serumah_dengan',
                'label' => 'Tinggal serumah dengan',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'kesadaran',
                'label' => 'Kesadaran',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'desk_pemeriksaan_fisik',
                'label' => 'Pemeriksaan fisik',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'desk_pemeriksaan_penunjang',
                'label' => 'Pemeriksaan penunjang/diagnostik',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'diagnosis_pra_operasi',
                'label' => 'Diagnosis pra operasi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'indikasi_operasi',
                'label' => 'Indikasi operasi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'rencana_tindakan_operasi',
                'label' => 'Rencana tindakan operasi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'perkiraan_lama_operasi',
                'label' => 'Perkiraan lama operasi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'persiapan_darah',
                'label' => 'Persiapan darah',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'persiapan_alatkhusus',
                'label' => 'Persiapan alat khusus',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
        ];
    }

    public function rulesSimpan()
    {
        return [
            [
                'field' => 'nokun',
                'label' => 'Nomor kunjungan',
                'rules' => 'trim|numeric|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'numeric' => '%s wajib angka',
                ]
            ],
            [
                'field' => 'ruang_tujuan',
                'label' => 'Ruang tujuan',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ]
        ];
    }

    public function rulesPenggunaanObat()
    {
        return [
            [
                'field' => 'desk_riwayat_guna_obat',
                'label' => 'Keterangan riwayat penggunaan obat',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ]
        ];
    }

    public function rulesAlergi()
    {
        return [
            [
                'field' => 'isi_alergi[]',
                'label' => 'Sebutkan riwayat alergi',
                'rules' => 'trim|required|max_length[50]',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'max_length' => '%s maksimal 50 karakter',
                ]
            ],
            [
                'field' => 'reaksi_alergi',
                'label' => 'Reaksi alergi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ]
        ];
    }

    public function rulesPsikologis()
    {
        return [
            [
                'field' => 'desk_psikologis',
                'label' => 'Sebutkan psikologis lainnya',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ]
        ];
    }

    public function rulesPersiapanDarah()
    {
        return [
            [
                'field' => 'ket_persiapan_darah',
                'label' => 'Keterangan persiapan darah',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ]
        ];
    }

    public function rulesPersiapanAlatKhusus()
    {
        return [
            [
                'field' => 'ket_persiapan_alatkhusus',
                'label' => 'Keterangan persiapan alat khusus',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ]
        ];
    }

    public function simpan($data)
    {
        $this->db->insert($this->_table, $data);
    }

    public function ubah($id, $data)
    {
        $this->db->where('medis.tb_pengkajian_operasi.id', $id);
        $this->db->update($this->_table, $data);
    }

    public function ambil($nokun)
    {
        $this->db->select('diagnosis_pra_operasi, rencana_tindakan_operasi, perkiraan_lama_operasi');
        $this->db->from($this->_table);
        $this->db->where('nokun', $nokun);
        $this->db->order_by('id', 'desc');
        $query = $this->db->get();
        return $query->row_array();
    }
}

/* End of file PengkajianPraOperasiModel.php */
/* Location: ./application/models/operasi/PengkajianPraOperasiModel.php */
<?php
defined('BASEPATH') or exit('No direct script access allowed');

class CeklisTindakanAferesis extends CI_Controller{

    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }
    
        if (!in_array(8, $this->session->userdata('akses'))) {
            redirect('login');
        }
    
        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('masterModel', 'pengkajianAwalModel'));
    }


    public function simpanFormCeklisTindakanAferesis()
    {
        $kunjungan = $this->input->post("nokun");
        $pengguna = $this->input->post("pengguna");
        $jenismesineferesis = $this->input->post("jenismesineferesis");
        $namadonor = $this->input->post("namadonor");
        $formcalondonor = $this->input->post("formcalondonor");
        $tanggal1 = $this->input->post("tanggal1");
        $jam1 = $this->input->post("jam1");
        $identifikasidonor = $this->input->post("identifikasidonor");
        $tanggal2 = $this->input->post("tanggal2");
        $jam2 = $this->input->post("jam2");
        $penyakitdonor = $this->input->post("penyakitdonor");
        $tanggal3 = $this->input->post("tanggal3");
        $jam3 = $this->input->post("jam3");
        $keadaanumumdonor = $this->input->post("keadaanumumdonor");
        $tanggal4 = $this->input->post("tanggal4");
        $jam4 = $this->input->post("jam4");
        $periksagolongandarah = $this->input->post("periksagolongandarah");
        $tanggal5 = $this->input->post("tanggal5");
        $jam5 = $this->input->post("jam5");
        $periksalabpk = $this->input->post("periksalabpk");
        $tanggal6 = $this->input->post("tanggal6");
        $jam6 = $this->input->post("jam6");
        $hasilgolongandarah = $this->input->post("hasilgolongandarah");
        $tanggal7 = $this->input->post("tanggal7");
        $jam7 = $this->input->post("jam7");
        $hasillaboratorium = $this->input->post("hasillaboratorium");
        $tanggal8 = $this->input->post("tanggal8");
        $jam8 = $this->input->post("jam8");
        $donordatang = $this->input->post("donordatang");
        $tanggal9 = $this->input->post("tanggal9");
        $jam9 = $this->input->post("jam9");
        $suratpersetujuan = $this->input->post("suratpersetujuan");
        $tanggal10 = $this->input->post("tanggal10");
        $jam10 = $this->input->post("jam10");
        $mesineferesis = $this->input->post("mesineferesis");
        $mesinreferensisebutkan = $this->input->post("mesinreferensisebutkan");
        $tanggal11 = $this->input->post("tanggal11");
        $jam11 = $this->input->post("jam11");
        $protocolcard = $this->input->post("protocolcard");
        $tanggal12 = $this->input->post("tanggal12");
        $jam12 = $this->input->post("jam12");
        $paketkiteferesis = $this->input->post("paketkiteferesis");
        $tanggal13 = $this->input->post("tanggal13");
        $jam13 = $this->input->post("jam13");
        $larutanacd = $this->input->post("larutanacd");
        $tanggal14 = $this->input->post("tanggal14");
        $jam14 = $this->input->post("jam14");
        $peralatanantiseptik = $this->input->post("peralatanantiseptik");
        $tanggal15 = $this->input->post("tanggal15");
        $jam15 = $this->input->post("jam15");
        $bahanantiseptik = $this->input->post("bahanantiseptik");
        $tanggal16 = $this->input->post("tanggal16");
        $jam16 = $this->input->post("jam16");
        $calciumgluconas = $this->input->post("calciumgluconas");
        $tanggal17 = $this->input->post("tanggal17");
        $jam17 = $this->input->post("jam17");
        $adrenalin = $this->input->post("adrenalin");
        $tanggal18 = $this->input->post("tanggal18");
        $jam18 = $this->input->post("jam18");
        $antihistamin = $this->input->post("antihistamin");
        $tanggal19 = $this->input->post("tanggal19");
        $jam19 = $this->input->post("jam19");
        $corticosteroid = $this->input->post("corticosteroid");
        $tanggal20 = $this->input->post("tanggal20");
        $jam20 = $this->input->post("jam20");
        $disposiblesyringe = $this->input->post("disposiblesyringe");
        $tanggal21 = $this->input->post("tanggal21");
        $jam21 = $this->input->post("jam21");
        $lkbibd = $this->input->post("lkbibd");
        $tanggal22 = $this->input->post("tanggal22");
        $jam22 = $this->input->post("jam22");
        $prosedurdimulai = $this->input->post("prosedurdimulai");
        $tanggal23 = $this->input->post("tanggal23");
        $jam23 = $this->input->post("jam23");
        $prosedurberakhir = $this->input->post("prosedurberakhir");
        $tanggal24 = $this->input->post("tanggal24");
        $jam24 = $this->input->post("jam24");
        $donormeninggalkanruang = $this->input->post("donormeninggalkanruang");
        $tanggal25 = $this->input->post("tanggal25");
        $jam25 = $this->input->post("jam25");
        $keterangan = $this->input->post("keterangan");

        $data = array(
            'nokun' => $kunjungan,
            'jenis_mesin_aferesis' => $jenismesineferesis,
            'nama_donor' => $namadonor,
            'perawat_aferesis' => $pengguna,
            'form_calon_donor' => $formcalondonor,
            'form_calon_donor_tanggal' => $tanggal1,
            'form_calon_donor_jam' => $jam1,
            'identifikasi_donor' => $identifikasidonor,
            'identifikasi_donor_tanggal' => $tanggal2,
            'identifikasi_donor_jam' => $jam2,
            'penyakit_donor' => $penyakitdonor,
            'penyakit_donor_tanggal' => $tanggal3,
            'penyakit_donor_jam' => $jam3,
            'keadaan_umum_donor' => $keadaanumumdonor,
            'keadaan_umum_donor_tanggal' => $tanggal4,
            'keadaan_umum_donor_jam' => $jam4,
            'periksa_golongan_darah' => $periksagolongandarah,
            'periksa_golongan_darah_tanggal' => $tanggal5,
            'periksa_golongan_darah_jam' => $jam5,
            'periksa_labpk' => $periksalabpk,
            'periksa_labpk_tanggal' => $tanggal6,
            'periksa_labpk_jam' => $jam6,
            'hasil_golongan_darah' => $hasilgolongandarah,
            'hasil_golongan_darah_tanggal' => $tanggal7,
            'hasil_golongan_darah_jam' => $jam7,
            'hasil_laboratorium' => $hasillaboratorium,
            'hasil_laboratorium_tanggal' => $tanggal8,
            'hasil_laboratorium_jam' => $jam8,
            'donor_datang' => $donordatang,
            'donor_datang_tanggal' => $tanggal9,
            'donor_datang_jam' => $jam9,
            'surat_persetujuan' => $suratpersetujuan,
            'surat_persetujuan_tanggal' => $tanggal10,
            'surat_persetujuan_jam' => $jam10,
            'mesin_eferesis' => $mesineferesis,
            'mesin_eferesis_sebutkan' => $mesinreferensisebutkan,
            'mesin_eferesis_tanggal' => $tanggal11,
            'mesin_eferesis_jam' => $jam11,
            'protocol_card' => $protocolcard,
            'protocol_card_tanggal' => $tanggal12,
            'protocol_card_jam' => $jam12,
            'paket_kit_eferesis' => $paketkiteferesis,
            'paket_kit_eferesis_tanggal' => $tanggal13,
            'paket_kit_eferesis_jam' => $jam13,
            'larutan_acd' => $larutanacd,
            'larutan_acd_tanggal' => $tanggal14,
            'larutan_acd_jam' => $jam14,
            'peralatan_antiseptik' => $peralatanantiseptik,
            'peralatan_antiseptik_tanggal' => $tanggal15,
            'peralatan_antiseptik_jam' => $jam15,
            'bahan_antiseptik' => $bahanantiseptik,
            'bahan_antiseptik_tanggal' => $tanggal16,
            'bahan_antiseptik_jam' => $jam16,
            'calcium_gluconas' => $calciumgluconas,
            'calcium_gluconas_tanggal' => $tanggal17,
            'calcium_gluconas_jam' => $jam17,
            'adrenalin' => $adrenalin,
            'adrenalin_tanggal' => $tanggal18,
            'adrenalin_jam' => $jam18,
            'antihistamin' => $antihistamin,
            'antihistamin_tanggal' => $tanggal19,
            'antihistamin_jam' => $jam19,
            'corticosteroid' => $corticosteroid,
            'corticosteroid_tanggal' => $tanggal20,
            'corticosteroid_jam' => $jam20,
            'disposiblesyringe' => $disposiblesyringe,
            'disposiblesyringe_tanggal' =>$tanggal21,
            'disposiblesyringe_jam' => $jam21,
            'lkbibd' => $lkbibd,
            'lkbibd_tanggal' => $tanggal22,
            'lkbibd_jam' => $jam22,
            'prosedur_dimulai' => $prosedurdimulai,
            'prosedur_dimulai_tanggal' => $tanggal23,
            'prosedur_dimulai_jam' => $jam23,
            'prosedur_berakhir' => $prosedurberakhir,
            'prosedur_berakhir_tanggal' => $tanggal24,
            'prosedur_berakhir_jam' => $jam24,
            'donormeninggalkanruang' => $donormeninggalkanruang,
            'donormeninggalkanruang_tanggal' => $tanggal25,
            'donormeninggalkanruang_jam' => $jam25,
            'keterangan' => $keterangan,
        );

        $ceklistindakanaferesis = $this->pengkajianAwalModel->insertFormCeklisTindakanAferesis($data);
    }

    public function lihatHistoryCeklisTindakanAferesis()
    {
        $id = $this->input->post('id');
        $HistoryCeklisTindakanAferesis = $this->pengkajianAwalModel->historyDetailCeklisTindakanAferesis($id);
        $listFormCalonDonor = $this->masterModel->referensi(776);
        $listIdentifikasiDonor = $this->masterModel->referensi(778);
        $listRiwayatPenyakitDonor = $this->masterModel->referensi(779);
        $listKeadaanUmumDonor = $this->masterModel->referensi(780);
        $listPeriksaGolonganDarah = $this->masterModel->referensi(781);
        $listPeriksaLabPK = $this->masterModel->referensi(782);
        $listHasilGolonganDarah = $this->masterModel->referensi(783);
        $listHasilLaboratorium = $this->masterModel->referensi(784);
        $listDonorDatang = $this->masterModel->referensi(785);
        $listSuratPersetujuan = $this->masterModel->referensi(786);
        $listMesinEferesis = $this->masterModel->referensi(787);
        $listProtocolCard = $this->masterModel->referensi(788);
        $listPaketKitEferesis = $this->masterModel->referensi(789);
        $listLarutanACD = $this->masterModel->referensi(790);
        $listPeralatanAntiseptik = $this->masterModel->referensi(791);
        $listBahanAntiseptik = $this->masterModel->referensi(792);
        $listCalciumGluconas = $this->masterModel->referensi(793);
        $listAdrenalin = $this->masterModel->referensi(794);
        $listAntiHistamin = $this->masterModel->referensi(795);
        $listCorticoSteroid = $this->masterModel->referensi(796);
        $listDisposibleSyringe = $this->masterModel->referensi(797);
        $listLKBIBD = $this->masterModel->referensi(798);
        $listProsedurDimulai = $this->masterModel->referensi(799);
        $listProsedurBerakhir = $this->masterModel->referensi(800);
        $listDonorMeninggalkanRuang = $this->masterModel->referensi(801);

        foreach ($HistoryCeklisTindakanAferesis as $ceklisaferesis):
            
    echo '  <ul class="nav nav-tabs">
                <li class="nav-item col-sm-12 col-md-12">
                    <div class="row form-group">
                        <div class="col-md-4">
                            <label for="jenismesineferesis">Jenis mesin eferesis yang dipakai</label>
                        </div>
                        <div class="col-md-8">
                            <input type="text" class="form-control" placeholder="[ Jenis mesin ]" value="'.$ceklisaferesis['jenis_mesin_aferesis'].'" name="jenismesineferesis" autocomplete="off">
                        </div>
                    </div>
                    <div class="row form-group">
                        <div class="col-md-4">
                            <label for="namadonor">Nama donor</label>
                        </div>
                        <div class="col-md-8">
                            <input type="text" class="form-control" placeholder="[ Nama donor ]" value="'.$ceklisaferesis['nama_donor'].'" name="namadonor" autocomplete="off">
                        </div>
                    </div>

                    <a href="" data-toggle="tab" aria-expanded="false" class="nav-link active">
                        1. Form Permintaan Komponen Aferesis
                    </a>
                    <a href="" data-toggle="tab" aria-expanded="false" class="nav-link active">
                        2. Data Donor
                    </a>
                    </br>
                    <div class="row form-group" style="margin-left:2px;">
                        <label class="col-md-6">Form calon donor komponen dengan aferesis FRM.IBD.018</label>';
                            foreach ($listFormCalonDonor as $lfcd): 
    echo '                  <div class="col-md-2 form-check">
                                <div class="radio radio-primary form-check-input jarak2">
                                    <input type="radio" name="formcalondonor" value="'.$lfcd['id_variabel'].'" class="formcalondonor" id="formcalondonor'.$lfcd['id_variabel'].'" ';
                                    if($ceklisaferesis['form_calon_donor'] == $lfcd['id_variabel'])
                                    {
                                        echo "checked";
                                    }else{
                                        echo "";
                                    }
    echo '                          >
                                    <label for="formcalondonor'.$lfcd['id_variabel'].'" class="form-check-label">'.$lfcd['variabel'].'</label>
                                </div>
                            </div> ';
                            endforeach;
    echo '          </div>
                    <div id="idwaktu1">
                        <div class="row form-group" style="margin-left:380px;">
                            <div class="col-md-6">
                                <input type="text" class="form-control waktu1" placeholder="[ Tanggal ]" value="'.$ceklisaferesis['form_calon_donor_tanggal'].'" name="tanggal1" autocomplete="off">
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control jam1" placeholder="[ 00:00 ]" value="'.$ceklisaferesis['form_calon_donor_jam'].'" name="jam1" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row form-group" style="margin-left:2px;">
                        <label class="col-md-6">Identifikasi Donor</label>';
                        foreach ($listIdentifikasiDonor as $lid): 
    echo '                  <div class="col-md-2 form-check">
                                <div class="radio radio-primary form-check-input jarak2">
                                    <input type="radio" name="identifikasidonor" value="'.$lid['id_variabel'].'" class="identifikasidonor" id="identifikasidonor'.$lid['id_variabel'].'" ';
                                    if($ceklisaferesis['identifikasi_donor'] == $lid['id_variabel'])
                                    {
                                        echo "checked";
                                    }else{
                                        echo "";
                                    }
    echo '                          >
                                    <label for="identifikasidonor'.$lid['id_variabel'].'" class="form-check-label">'.$lid['variabel'].'</label>
                                </div>
                            </div> ';
                        endforeach;
    echo '          </div>
                    <div id="idwaktu2">
                        <div class="row form-group" style="margin-left:380px;">
                            <div class="col-md-6">
                                <input type="text" class="form-control waktu2" placeholder="[ Tanggal ]" name="tanggal2" value="'.$ceklisaferesis['identifikasi_donor_tanggal'].'" autocomplete="off">
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control jam2" placeholder="[ 00:00 ]" name="jam2" value="'.$ceklisaferesis['identifikasi_donor_jam'].'" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="row form-group" style="margin-left:2px;">
                        <label class="col-md-6">Riwayat penyakit donor</label>';
                            foreach ($listRiwayatPenyakitDonor as $lid): 
    echo '                  <div class="col-md-2 form-check">
                                <div class="radio radio-primary form-check-input jarak2">
                                    <input type="radio" name="penyakitdonor" value="'.$lid['id_variabel'].'" class="penyakitdonor" id="penyakitdonor'.$lid['id_variabel'].'" ';
                                    if($ceklisaferesis['penyakit_donor'] == $lid['id_variabel'])
                                    {
                                        echo "checked";
                                    }else{
                                        echo "";
                                    }
    echo '                          >
                                    <label for="penyakitdonor'.$lid['id_variabel'].'" class="form-check-label">'.$lid['variabel'].'</label>
                                </div>
                            </div> ';
                            endforeach;
    echo '      </div>
                    <div id="idwaktu3">
                        <div class="row form-group" style="margin-left:380px;">
                            <div class="col-md-6">
                                <input type="text" class="form-control waktu3" placeholder="[ Tanggal ]" name="tanggal3" value="'.$ceklisaferesis['penyakit_donor_tanggal'].'" autocomplete="off">
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control jam3" placeholder="[ 00:00 ]" name="jam3" value="'.$ceklisaferesis['penyakit_donor_jam'].'" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="row form-group" style="margin-left:2px;">
                        <label class="col-md-6">Keadaan umum donor</label>';
                            foreach ($listKeadaanUmumDonor as $lkd): 
    echo '                  <div class="col-md-2 form-check">
                                <div class="radio radio-primary form-check-input jarak2">
                                    <input type="radio" name="keadaanumumdonor" value="'.$lkd['id_variabel'].'" class="keadaanumumdonor" id="keadaanumumdonor'.$lkd['id_variabel'].'" ';
                                    if($ceklisaferesis['keadaan_umum_donor'] == $lkd['id_variabel'])
                                    {
                                        echo "checked";
                                    }else{
                                        echo "";
                                    }
    echo '                          >
                                    <label for="keadaanumumdonor'.$lkd['id_variabel'].'" class="form-check-label">'.$lkd['variabel'].'</label>
                                </div>
                            </div> ';
                            endforeach; 
    echo '          </div>
                    <div id="idwaktu4">
                        <div class="row form-group" style="margin-left:380px;">
                            <div class="col-md-6">
                                <input type="text" class="form-control waktu4" placeholder="[ Tanggal ]"  name="tanggal4" value="'.$ceklisaferesis['keadaan_umum_donor_tanggal'].'" autocomplete="off">
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control jam4" placeholder="[ 00:00 ]" name="jam4" value="'.$ceklisaferesis['keadaan_umum_donor_jam'].'" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="row form-group" style="margin-left:2px;">
                        <label class="col-md-6">Dikirim periksa golongan darah</label>';
                            foreach ($listPeriksaGolonganDarah as $lpg):
    echo '                  <div class="col-md-2 form-check">
                                <div class="radio radio-primary form-check-input jarak2">
                                    <input type="radio" name="periksagolongandarah" value="'.$lpg['id_variabel'].'" class="periksagolongandarah" id="periksagolongandarah'.$lpg['id_variabel'].'" ';
                                    if($ceklisaferesis['periksa_golongan_darah'] == $lpg['id_variabel'])
                                    {
                                        echo "checked";
                                    }else{
                                        echo "";
                                    }
    echo '                          >
                                    <label for="periksagolongandarah'.$lpg['id_variabel'].'" class="form-check-label">'.$lpg['variabel'].'</label>
                                </div>
                            </div> ';
                            endforeach;
    echo '          </div>
                    <div id="idwaktu5">
                        <div class="row form-group" style="margin-left:380px;">
                            <div class="col-md-6">
                                <input type="text" class="form-control waktu5" placeholder="[ Tanggal ]" name="tanggal5" value="'.$ceklisaferesis['periksa_golongan_darah_tanggal'].'" autocomplete="off">
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control jam5" placeholder="[ 00:00 ]" name="jam5" value="'.$ceklisaferesis['periksa_golongan_darah_jam'].'" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="row form-group" style="margin-left:2px;">
                        <label class="col-md-6">Dikirim periksa ke lab patologi klinik</label>';
                            foreach ($listPeriksaLabPK as $lpk): 
    echo '                  <div class="col-md-2 form-check">
                                <div class="radio radio-primary form-check-input jarak2">
                                    <input type="radio" name="periksalabpk" value="'.$lpk['id_variabel'].'" class="periksalabpk" id="periksalabpk'.$lpk['id_variabel'].'" ';
                                    if($ceklisaferesis['periksa_labpk'] == $lpk['id_variabel'])
                                    {
                                        echo "checked";
                                    }else{
                                        echo "";
                                    }
    echo '                          >
                                    <label for="periksalabpk'.$lpk['id_variabel'].'" class="form-check-label">'.$lpk['variabel'].'</label>
                                </div>
                            </div> ';
                            endforeach; 
    echo '          </div>
                    <div id="idwaktu6">
                        <div class="row form-group" style="margin-left:380px;">
                            <div class="col-md-6">
                                <input type="text" class="form-control waktu6" placeholder="[ Tanggal ]" name="tanggal6" value="'.$ceklisaferesis['periksa_labpk_tanggal'].'" autocomplete="off">
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control jam6" placeholder="[ 00:00 ]" name="jam6" value="'.$ceklisaferesis['periksa_labpk_jam'].'" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="row form-group" style="margin-left:2px;">
                        <label class="col-md-6">Hasil pemeriksaan golongan darah</label>';
                            foreach ($listHasilGolonganDarah as $lhg): 
    echo '                  <div class="col-md-2 form-check">
                                <div class="radio radio-primary form-check-input jarak2">
                                    <input type="radio" name="hasilgolongandarah" value="'.$lhg['id_variabel'].'" class="hasilgolongandarah" id="hasilgolongandarah'.$lhg['id_variabel'].'" ';
                                    if($ceklisaferesis['hasil_golongan_darah'] == $lhg['id_variabel'])
                                    {
                                        echo "checked";
                                    }else{
                                        echo "";
                                    }
    echo '                          >
                                    <label for="hasilgolongandarah'.$lhg['id_variabel'].'" class="form-check-label">'.$lhg['variabel'].'</label>
                                </div>
                            </div> ';
                            endforeach; 
    echo '          </div>
                    <div id="idwaktu7">
                        <div class="row form-group" style="margin-left:380px;">
                            <div class="col-md-6">
                                <input type="text" class="form-control waktu7" placeholder="[ Tanggal ]" name="tanggal7" value="'.$ceklisaferesis['hasil_golongan_darah_tanggal'].'" autocomplete="off">
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control jam7" placeholder="[ 00:00 ]" name="jam7" value="'.$ceklisaferesis['hasil_golongan_darah_jam'].'" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="row form-group" style="margin-left:2px;">
                        <label class="col-md-6">Hasil pemeriksaan laboratorium</label>';
                            foreach ($listHasilLaboratorium as $lhl): 
    echo '                  <div class="col-md-2 form-check">
                                <div class="radio radio-primary form-check-input jarak2">
                                    <input type="radio" name="hasillaboratorium" value="'.$lhl['id_variabel'].'" class="hasillaboratorium" id="hasillaboratorium'.$lhl['id_variabel'].'" ';
                                    if($ceklisaferesis['hasil_laboratorium'] == $lhl['id_variabel'])
                                    {
                                        echo "checked";
                                    }else{
                                        echo "";
                                    }
    echo '                          >
                                    <label for="hasillaboratorium'.$lhl['id_variabel'].'" class="form-check-label">'.$lhl['variabel'].'</label>
                                </div>
                            </div> ';
                            endforeach;
    echo '          </div>
                    <div id="idwaktu8">
                        <div class="row form-group" style="margin-left:380px;">
                            <div class="col-md-6">
                                <input type="text" class="form-control waktu8" placeholder="[ Tanggal ]"   name="tanggal8" value="'.$ceklisaferesis['hasil_laboratorium_tanggal'].'" autocomplete="off">
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control jam8" placeholder="[ 00:00 ]" name="jam8" value="'.$ceklisaferesis['hasil_laboratorium_jam'].'" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="row form-group" style="margin-left:2px;">
                        <label class="col-md-6">Donor datang dan siap</label>';
                            foreach ($listDonorDatang as $ldd): 
    echo '                  <div class="col-md-2 form-check">
                                <div class="radio radio-primary form-check-input jarak2">
                                    <input type="radio" name="donordatang" value="'.$ldd['id_variabel'].'" class="donordatang" id="donordatang'. $ldd['id_variabel'].'" ';
                                    if($ceklisaferesis['donor_datang'] == $ldd['id_variabel'])
                                    {
                                        echo "checked";
                                    }else{
                                        echo "";
                                    }
    echo '                          >
                                    <label for="donordatang'.$ldd['id_variabel'].'" class="form-check-label">'.$ldd['variabel'].'</label>
                                </div>
                            </div> ';
                            endforeach; 
    echo '          </div>
                    <div id="idwaktu9">
                        <div class="row form-group" style="margin-left:380px;">
                            <div class="col-md-6">
                                <input type="text" class="form-control waktu9" placeholder="[ Tanggal ]" name="tanggal9" value="'.$ceklisaferesis['donor_datang_tanggal'].'" autocomplete="off">
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control jam9" placeholder="[ 00:00 ]" name="jam9" value="'.$ceklisaferesis['donor_datang_jam'].'"autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="row form-group" style="margin-left:2px;">
                        <label class="col-md-6">Surat persetujuan tindakan</label>';
                            foreach ($listSuratPersetujuan as $lsp): 
    echo '                  <div class="col-md-2 form-check">
                                <div class="radio radio-primary form-check-input jarak2">
                                    <input type="radio" name="suratpersetujuan" value="'.$lsp['id_variabel'].'" class="suratpersetujuan" id="suratpersetujuan'.$lsp['id_variabel'].'" ';
                                    if($ceklisaferesis['surat_persetujuan'] == $lsp['id_variabel'])
                                    {
                                        echo "checked";
                                    }else{
                                        echo "";
                                    }
    echo '                          >
                                    <label for="suratpersetujuan'.$lsp['id_variabel'].'" class="form-check-label">'.$lsp['variabel'].'</label>
                                </div>
                            </div> ';
                            endforeach;
    echo '          </div>
                    <div id="idwaktu10">
                        <div class="row form-group" style="margin-left:380px;">
                            <div class="col-md-6">
                                <input type="text" class="form-control waktu10" placeholder="[ Tanggal ]" name="tanggal10" value="'.$ceklisaferesis['surat_persetujuan_tanggal'].'" autocomplete="off">
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control jam10" placeholder="[ 00:00 ]" name="jam10" value="'.$ceklisaferesis['surat_persetujuan_jam'].'" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <a href="" data-toggle="tab" aria-expanded="false" class="nav-link active">
                        3. Peralatan & Bahan
                    </a>
                    <div class="row form-group" style="margin-left:2px;">
                        <label class="col-md-6">Mesin eferesis</label>';
                            foreach ($listMesinEferesis as $lme):
    echo '                  <div class="col-md-2 form-check">
                                <div class="radio radio-primary form-check-input jarak2">
                                    <input type="radio" name="mesineferesis" value="'.$lme['id_variabel'].'" class="mesineferesis" id="mesineferesis'.$lme['id_variabel'].'" ';
                                    if($ceklisaferesis['mesin_eferesis'] == $lme['id_variabel'])
                                    {
                                        echo "checked";
                                    }else{
                                        echo "";
                                    }
    echo '                          >
                                    <label for="mesineferesis'.$lme['id_variabel'].'" class="form-check-label">'.$lme['variabel'].'</label>
                                </div>
                            </div> ';
                            endforeach;
    echo '          </div>
                    <div id="idwaktu11">
                        <div class="row form-group" style="margin-left:380px;">
                            <div class="col-md-10">
                                <input type="text" class="form-control" placeholder="[ Sebutkan ]" name="mesinreferensisebutkan" value="'.$ceklisaferesis['mesin_eferesis_sebutkan'].'" autocomplete="off">
                            </div>
                            <div class="col-md-6" style="margin-top:2px;">
                                <input type="text" class="form-control waktu11" placeholder="[ Tanggal ]" name="tanggal11" value="'.$ceklisaferesis['mesin_eferesis_tanggal'].'" autocomplete="off">
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control jam11" placeholder="[ 00:00 ]" name="jam11" value="'.$ceklisaferesis['mesin_eferesis_jam'].'" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="row form-group" style="margin-left:2px;">
                        <label class="col-md-6">Protocol card yang sesuai (untuk MCS+)</label>';
                            foreach ($listProtocolCard as $lpc): 
    echo '                  <div class="col-md-2 form-check">
                                <div class="radio radio-primary form-check-input jarak2">
                                    <input type="radio" name="protocolcard" value="'.$lpc['id_variabel'].'" class="protocolcard" id="protocolcard'.$lpc['id_variabel'].'" ';
                                    if($ceklisaferesis['protocol_card'] == $lpc['id_variabel'])
                                    {
                                        echo "checked";
                                    }else{
                                        echo "";
                                    }
    echo '                          >
                                    <label for="protocolcard'.$lpc['id_variabel'].'" class="form-check-label">'.$lpc['variabel'].'</label>
                                </div>
                            </div> ';
                            endforeach; 
    echo '          </div>
                    <div id="idwaktu12">
                        <div class="row form-group" style="margin-left:380px;">
                            <div class="col-md-6">
                                <input type="text" class="form-control waktu12" placeholder="[ Tanggal ]" name="tanggal12" value="'.$ceklisaferesis['protocol_card_tanggal'].'" autocomplete="off">
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control jam12" placeholder="[ 00:00 ]" name="jam12" value="'.$ceklisaferesis['protocol_card_jam'].'" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="row form-group" style="margin-left:2px;">
                        <label class="col-md-6">Paket kit aferesis yang sesuai</label>';
                            foreach ($listPaketKitEferesis as $lpe): 
    echo '                 <div class="col-md-2 form-check">
                                <div class="radio radio-primary form-check-input jarak2">
                                    <input type="radio" name="paketkiteferesis" value="'.$lpe['id_variabel'].'" class="paketkiteferesis" id="paketkiteferesis'.$lpe['id_variabel'].'" ';
                                    if($ceklisaferesis['paket_kit_eferesis'] == $lpe['id_variabel'])
                                    {
                                        echo "checked";
                                    }else{
                                        echo "";
                                    }
    echo '                          >
                                    <label for="paketkiteferesis'.$lpe['id_variabel'].'" class="form-check-label">'.$lpe['variabel'].'</label>
                                </div>
                            </div> ';
                            endforeach;
    echo '          </div>
                    <div id="idwaktu13">
                        <div class="row form-group" style="margin-left:380px;">
                            <div class="col-md-6">
                                <input type="text" class="form-control waktu13" placeholder="[ Tanggal ]" name="tanggal13" value="'.$ceklisaferesis['paket_kit_eferesis_tanggal'].'" autocomplete="off">
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control jam13" placeholder="[ 00:00 ]" name="jam13" value="'.$ceklisaferesis['paket_kit_eferesis_jam'].'" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="row form-group" style="margin-left:2px;">
                        <label class="col-md-6">Larutan ACD-A</label>';
                            foreach ($listLarutanACD as $lacd):
    echo '                  <div class="col-md-2 form-check">
                                <div class="radio radio-primary form-check-input jarak2">
                                    <input type="radio" name="larutanacd" value="'.$lacd['id_variabel'].'" class="larutanacd" id="larutanacd'.$lacd['id_variabel'].'" ';
                                    if($ceklisaferesis['larutan_acd'] == $lacd['id_variabel'])
                                    {
                                        echo "checked";
                                    }else{
                                        echo "";
                                    }
    echo '                          >
                                    <label for="larutanacd'.$lacd['id_variabel'].'" class="form-check-label">'.$lacd['variabel'].'</label>
                                </div>
                            </div> ';
                            endforeach;
    echo '          </div>
                    <div id="idwaktu14">
                        <div class="row form-group" style="margin-left:380px;">
                            <div class="col-md-6">
                                <input type="text" class="form-control waktu14" placeholder="[ Tanggal ]" name="tanggal14" value="'.$ceklisaferesis['larutan_acd_tanggal'].'" autocomplete="off">
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control jam14" placeholder="[ 00:00 ]" name="jam14" value="'.$ceklisaferesis['larutan_acd_jam'].'" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="row form-group" style="margin-left:2px;">
                        <label class="col-md-6">Peralatan antiseptik</label>';
                            foreach ($listPeralatanAntiseptik as $pas):
    echo '                       <div class="col-md-2 form-check">
                                <div class="radio radio-primary form-check-input jarak2">
                                    <input type="radio" name="peralatanantiseptik" value="'.$pas['id_variabel'].'" class="peralatanantiseptik" id="peralatanantiseptik'.$pas['id_variabel'].'" ';
                                    if($ceklisaferesis['peralatan_antiseptik'] == $pas['id_variabel'])
                                    {
                                        echo "checked";
                                    }else{
                                        echo "";
                                    }
    echo '                          >
                                    <label for="peralatanantiseptik'.$pas['id_variabel'].'" class="form-check-label">'.$pas['variabel'].'</label>
                                </div>
                            </div>'; 
                            endforeach;
    echo '          </div>
                    <div id="idwaktu15">
                        <div class="row form-group" style="margin-left:380px;">
                            <div class="col-md-6">
                                <input type="text" class="form-control waktu15" placeholder="[ Tanggal ]" name="tanggal15" value="'.$ceklisaferesis['peralatan_antiseptik_tanggal'].'" autocomplete="off">
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control jam15" placeholder="[ 00:00 ]" name="jam15" value="'.$ceklisaferesis['peralatan_antiseptik_jam'].'" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="row form-group" style="margin-left:2px;">
                        <label class="col-md-6">Bahan antiseptik : 2 macam</label>';
                            foreach ($listBahanAntiseptik as $bas):
    echo '                  <div class="col-md-2 form-check">
                                <div class="radio radio-primary form-check-input jarak2">
                                    <input type="radio" name="bahanantiseptik" value="'.$bas['id_variabel'].'" class="bahanantiseptik" id="bahanantiseptik'.$bas['id_variabel'].'" ';
                                    if($ceklisaferesis['bahan_antiseptik'] == $bas['id_variabel'])
                                    {
                                        echo "checked";
                                    }else{
                                        echo "";
                                    }
    echo '                          >
                                    <label for="bahanantiseptik'.$bas['id_variabel'].'" class="form-check-label">'.$bas['variabel'].'</label>
                                </div>
                            </div> ';
                            endforeach;
    echo '          </div>
                    <div id="idwaktu16">
                        <div class="row form-group" style="margin-left:380px;">
                            <div class="col-md-6">
                                <input type="text" class="form-control waktu16" placeholder="[ Tanggal ]"  name="tanggal16" value="'.$ceklisaferesis['bahan_antiseptik_tanggal'].'" autocomplete="off">
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control jam16" placeholder="[ 00:00 ]" name="jam16" value="'.$ceklisaferesis['bahan_antiseptik_jam'].'" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <a href="" data-toggle="tab" aria-expanded="false" class="nav-link active">
                        4. Alat & Bahan Kedaruratan
                    </a>
                    <div class="row form-group" style="margin-left:2px;">
                        <label class="col-md-6">Calcium gluconas inj. 10 ml</label>';
                            foreach ($listCalciumGluconas as $lcg): 
    echo '                 <div class="col-md-2 form-check">
                                <div class="radio radio-primary form-check-input jarak2">
                                    <input type="radio" name="calciumgluconas" value="'.$lcg['id_variabel'].'" class="calciumgluconas" id="calciumgluconas'.$lcg['id_variabel'].'" ';
                                    if($ceklisaferesis['calcium_gluconas'] == $lcg['id_variabel'])
                                    {
                                        echo "checked";
                                    }else{
                                        echo "";
                                    }
    echo '                          >
                                    <label for="calciumgluconas'.$lcg['id_variabel'].'" class="form-check-label">'.$lcg['variabel'].'</label>
                                </div>
                            </div> ';
                            endforeach;
    echo '          </div>
                    <div id="idwaktu17">
                        <div class="row form-group" style="margin-left:380px;">
                            <div class="col-md-6">
                                <input type="text" class="form-control waktu17" placeholder="[ Tanggal ]" name="tanggal17" value="'.$ceklisaferesis['calcium_gluconas_tanggal'].'" autocomplete="off">
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control jam17" placeholder="[ 00:00 ]" name="jam17" value="'.$ceklisaferesis['calcium_gluconas_jam'].'" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="row form-group" style="margin-left:2px;">
                        <label class="col-md-6">Adrenalin</label>';
                            foreach ($listAdrenalin as $la):
    echo '                  <div class="col-md-2 form-check">
                                <div class="radio radio-primary form-check-input jarak2">
                                    <input type="radio" name="adrenalin" value="'.$la['id_variabel'].'" class="adrenalin" id="adrenalin'.$la['id_variabel'].'" ';
                                    if($ceklisaferesis['adrenalin'] == $la['id_variabel'])
                                    {
                                        echo "checked";
                                    }else{
                                        echo "";
                                    }
    echo '                          >
                                    <label for="adrenalin'.$la['id_variabel'].'" class="form-check-label">'.$la['variabel'].'</label>
                                </div>
                            </div> ';
                            endforeach;
    echo '          </div>
                    <div id="idwaktu18">
                        <div class="row form-group" style="margin-left:380px;">
                            <div class="col-md-6">
                                <input type="text" class="form-control waktu18" placeholder="[ Tanggal ]" name="tanggal18" value="'.$ceklisaferesis['adrenalin_tanggal'].'" autocomplete="off">
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control jam18" placeholder="[ 00:00 ]" name="jam18" value="'.$ceklisaferesis['adrenalin_jam'].'" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="row form-group" style="margin-left:2px;">
                        <label class="col-md-6">Anti histamin</label>';
                            foreach ($listAntiHistamin as $lah): 
    echo '                  <div class="col-md-2 form-check">
                                <div class="radio radio-primary form-check-input jarak2">
                                    <input type="radio" name="antihistamin" value="'.$lah['id_variabel'].'" class="antihistamin" id="antihistamin'.$lah['id_variabel'].'" ';
                                    if($ceklisaferesis['antihistamin'] == $lah['id_variabel'])
                                    {
                                        echo "checked";
                                    }else{
                                        echo "";
                                    }
    echo '                          >
                                    <label for="antihistamin'.$lah['id_variabel'].'" class="form-check-label">'.$lah['variabel'].'</label>
                                </div>
                            </div> ';
                            endforeach;
    echo '          </div>
                    <div style="display:none;" id="idwaktu19">
                        <div class="row form-group" style="margin-left:380px;">
                            <div class="col-md-6">
                                <input type="text" class="form-control waktu19" placeholder="[ Tanggal ]" name="tanggal19" value="'.$ceklisaferesis['antihistamin_tanggal'].'" autocomplete="off">
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control jam19" placeholder="[ 00:00 ]" name="jam19" value="'.$ceklisaferesis['antihistamin_jam'].'" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="row form-group" style="margin-left:2px;">
                        <label class="col-md-6">Cortico steroid</label>';
                            foreach ($listCorticoSteroid as $lcs):
    echo '                  <div class="col-md-2 form-check">
                                <div class="radio radio-primary form-check-input jarak2">
                                    <input type="radio" name="corticosteroid" value="'.$lcs['id_variabel'].'" class="corticosteroid" id="corticosteroid'.$lcs['id_variabel'].'" ';
                                    if($ceklisaferesis['corticosteroid'] == $lcs['id_variabel'])
                                    {
                                        echo "checked";
                                    }else{
                                        echo "";
                                    }
    echo '                          >
                                    <label for="corticosteroid'.$lcs['id_variabel'].'" class="form-check-label">'.$lcs['variabel'].'</label>
                                </div>
                            </div> ';
                            endforeach;
    echo '          </div>
                    <div id="idwaktu20">
                        <div class="row form-group" style="margin-left:380px;">
                            <div class="col-md-6">
                                <input type="text" class="form-control waktu20" placeholder="[ Tanggal ]" name="tanggal20" value="'.$ceklisaferesis['corticosteroid_tanggal'].'" autocomplete="off">
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control jam20" placeholder="[ 00:00 ]" name="jam20" value="'.$ceklisaferesis['corticosteroid_jam'].'" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="row form-group" style="margin-left:2px;">
                        <label class="col-md-6">Disposible syringe 10 ml, 5 ml</label>';
                            foreach ($listDisposibleSyringe as $lds):
    echo '                  <div class="col-md-2 form-check">
                                <div class="radio radio-primary form-check-input jarak2">
                                    <input type="radio" name="disposiblesyringe" value="'.$lds['id_variabel'].'" class="disposiblesyringe" id="disposiblesyringe'.$lds['id_variabel'].'" ';
                                    if($ceklisaferesis['disposiblesyringe'] == $lds['id_variabel'])
                                    {
                                        echo "checked";
                                    }else{
                                        echo "";
                                    }
    echo '                          >
                                    <label for="disposiblesyringe'.$lds['id_variabel'].'" class="form-check-label">'.$lds['variabel'].'</label>
                                </div>
                            </div> ';
                            endforeach;
    echo '          </div>
                    <div style="display:none;" id="idwaktu21">
                        <div class="row form-group" style="margin-left:380px;">
                            <div class="col-md-6">
                                <input type="text" class="form-control waktu21" placeholder="[ Tanggal ]" name="tanggal21" value="'.$ceklisaferesis['disposiblesyringe_tanggal'].'" autocomplete="off">
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control jam21" placeholder="[ 00:00 ]" name="jam21" value="'.$ceklisaferesis['disposiblesyringe_jam'].'" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <a href="" data-toggle="tab" aria-expanded="false" class="nav-link active">
                        5. Lembar Kerja
                    </a>
                    <div class="row form-group" style="margin-left:2px;">
                        <label class="col-md-6">LKB. IBD.008 a / b / c yang sesuai</label>';
                            foreach ($listLKBIBD as $lk):
    echo '                  <div class="col-md-2 form-check">
                                <div class="radio radio-primary form-check-input jarak2">
                                    <input type="radio" name="lkbibd" value="'.$lk['id_variabel'].'" class="lkbibd" id="lkbibd'.$lk['id_variabel'].'" ';
                                    if($ceklisaferesis['lkbibd'] == $lk['id_variabel'])
                                    {
                                        echo "checked";
                                    }else{
                                        echo "";
                                    }
    echo '                          >
                                    <label for="lkbibd'.$lk['id_variabel'].'" class="form-check-label">'.$lk['variabel'].'</label>
                                </div>
                            </div> ';
                            endforeach;
    echo '          </div>
                    <div id="idwaktu22">
                        <div class="row form-group" style="margin-left:380px;">
                            <div class="col-md-6">
                                <input type="text" class="form-control waktu22" placeholder="[ Tanggal ]" name="tanggal22" value="'.$ceklisaferesis['lkbibd_tanggal'].'" autocomplete="off">
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control jam22" placeholder="[ 00:00 ]" name="jam22" value="'.$ceklisaferesis['lkbibd_jam'].'" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <a href="" data-toggle="tab" aria-expanded="false" class="nav-link active">
                        6. Prosedur Aferesis
                    </a>
                    <div class="row form-group" style="margin-left:2px;">
                        <label class="col-md-6">Prosedur dimulai</label>';
                            foreach ($listProsedurDimulai as $lpd):
    echo '                  <div class="col-md-2 form-check">
                                <div class="radio radio-primary form-check-input jarak2">
                                    <input type="radio" name="prosedurdimulai" value="'.$lpd['id_variabel'].'" class="prosedurdimulai" id="prosedurdimulai'.$lpd['id_variabel'].'" ';
                                    if($ceklisaferesis['prosedur_dimulai'] == $lpd['id_variabel'])
                                    {
                                        echo "checked";
                                    }else{
                                        echo "";
                                    }
    echo '                          >
                                    <label for="prosedurdimulai'.$lpd['id_variabel'].'" class="form-check-label">'.$lpd['variabel'].'</label>
                                </div>
                            </div> ';
                            endforeach;
    echo '          </div>
                    <div id="idwaktu23">
                        <div class="row form-group" style="margin-left:380px;">
                            <div class="col-md-6">
                                <input type="text" class="form-control waktu23" placeholder="[ Tanggal ]" name="tanggal23" value="'.$ceklisaferesis['prosedur_dimulai_tanggal'].'" autocomplete="off">
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control jam23" placeholder="[ 00:00 ]" name="jam23" value="'.$ceklisaferesis['prosedur_dimulai_jam'].'" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="row form-group" style="margin-left:2px;">
                        <label class="col-md-6">Prosedur berakhir</label>';
                            foreach ($listProsedurBerakhir as $lpb):
    echo '                  <div class="col-md-2 form-check">
                                <div class="radio radio-primary form-check-input jarak2">
                                    <input type="radio" name="prosedurberakhir" value="'.$lpb['id_variabel'].'" class="prosedurberakhir" id="prosedurberakhir'.$lpb['id_variabel'].'" ';
                                    if($ceklisaferesis['prosedur_berakhir'] == $lpb['id_variabel'])
                                    {
                                        echo "checked";
                                    }else{
                                        echo "";
                                    }
    echo '                          >
                                    <label for="prosedurberakhir'.$lpb['id_variabel'].'" class="form-check-label">'.$lpb['variabel'].'</label>
                                </div>
                            </div> ';
                            endforeach;
    echo '          </div>
                    <div id="idwaktu24">
                        <div class="row form-group" style="margin-left:380px;">
                            <div class="col-md-6">
                                <input type="text" class="form-control waktu24" placeholder="[ Tanggal ]" name="tanggal24" value="'.$ceklisaferesis['prosedur_berakhir_tanggal'].'" autocomplete="off">
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control jam24" placeholder="[ 00:00 ]" name="jam24" value="'.$ceklisaferesis['prosedur_berakhir_jam'].'" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="row form-group" style="margin-left:2px;">
                        <label class="col-md-6">Donor meninggalkan ruang aferesis</label>';
                            foreach ($listDonorMeninggalkanRuang as $dmr):
    echo '                  <div class="col-md-2 form-check">
                                <div class="radio radio-primary form-check-input jarak2">
                                    <input type="radio" name="donormeninggalkanruang" value="'.$dmr['id_variabel'].'" class="donormeninggalkanruang" id="donormeninggalkanruang'.$dmr['id_variabel'].'" ';
                                    if($ceklisaferesis['donormeninggalkanruang'] == $lpb['id_variabel'])
                                    {
                                        echo "checked";
                                    }else{
                                        echo "";
                                    }
    echo '                          >
                                    <label for="donormeninggalkanruang'.$dmr['id_variabel'].'" class="form-check-label">'.$dmr['variabel'].'</label>
                                </div>
                            </div> ';
                            endforeach;
    echo '          </div>
                    <div id="idwaktu25">
                        <div class="row form-group" style="margin-left:380px;">
                            <div class="col-md-6">
                                <input type="text" class="form-control waktu25" placeholder="[ Tanggal ]" name="tanggal25" value="'.$ceklisaferesis['donormeninggalkanruang_tanggal'].'" autocomplete="off">
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control jam25" placeholder="[ 00:00 ]" name="jam25" value="'.$ceklisaferesis['donormeninggalkanruang_jam'].'" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="row form-group" style="margin-left:2px;">
                        <div class="col-md-2">
                            <label for="keterangan">Keterangan</label>
                        </div>
                        <div class="col-md-8">
                            <textarea class="form-control" placeholder="[ Keterangan ]" name="keterangan" autocomplete="off">'.$ceklisaferesis['keterangan'].'</textarea>
                        </div>
                    </div>
                </li>
            </ul>
            ';
        endforeach;
    }

}

?>
<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PlanOfCareModel extends MY_Model
{
    protected $_table_name = 'db_layanan.tb_planofcare';
    protected $_primary_key = 'id';
    protected $_order_by = 'id';
    protected $_order_by_type = 'DESC';

    public $rules = array(


        'daftar_masalah' => array(
            'field' => 'daftar_masalah',
            'label' => 'Daftar masalah',
            'rules' => 'trim|required',
            'errors' => array(
                'required' => '%s Wajib <PERSON>.'
            ),
        ),

        'planofcare' => array(
            'field' => 'planofcare',
            'label' => 'Planofcare',
            'rules' => 'trim|required',
            'errors' => array(
                'required' => '%s Wajib Diisi.'
            ),
        ),

        'target_terukur' => array(
            'field' => 'target_terukur',
            'label' => 'Target terukur',
            'rules' => 'trim|required',
            'errors' => array(
                'required' => '%s Wajib <PERSON>.'
            ),
        ),
    );


    function __construct()
    {
        parent::__construct();
    }

    // public function insertPlanOfCare($data)
    // {
    //     $this->db->insert('db_layanan.tb_planofcare', $data);
    //     return $this->db->insert_id();
    // }

    function table_query()
    {
        $this->db->select('po.id ID,po.nokun NOKUN, po.tanggal TANGGAL
        , master.getNamaLengkapPegawai(peng.NIP) USER
        , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
        , rk.DESKRIPSI RUANGAN_KUNJUNGAN
        , p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN');
        $this->db->from('db_layanan.tb_planofcare po');
        $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = po.nokun', 'LEFT');
        $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = pk.NOPEN', 'LEFT');
        $this->db->join('pendaftaran.tujuan_pasien tp', 'tp.NOPEN = p.NOMOR', 'LEFT');
        $this->db->join('pendaftaran.penjamin pj', 'pj.NOPEN = p.NOMOR', 'LEFT');
        $this->db->join('master.diagnosa_masuk dm', 'dm.ID = p.DIAGNOSA_MASUK', 'LEFT');
        $this->db->join('master.dokter dpjp', 'dpjp.ID = tp.DOKTER', 'LEFT');
        $this->db->join('master.ruangan rk', 'rk.ID = pk.RUANGAN', 'LEFT');
        $this->db->join('aplikasi.pengguna peng', 'peng.ID = po.oleh', 'LEFT');

        $this->db->where('po.STATUS !=', '0');
        $this->db->where('p.NORM', $this->input->post('nomr'));
        $this->db->order_by('po.id', 'DESC');
    }

    function get_table($single = TRUE)
    {
        $this->table_query();
        $query = $this->db->get();
        if ($single == TRUE) {
            $method = 'row';
        } else {
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count()
    {
        $this->table_query();
        return $this->db->count_all_results();
    }

    // List Seluruh Pegawai SELAIN DOKTER DAN PERAWAT
    public function listPg()
    {
        $q = $this->input->get('q');
        $query = $this->db->query("SELECT NIP, CONCAT(IF(GELAR_DEPAN='' OR GELAR_DEPAN IS NULL,'',CONCAT(GELAR_DEPAN,'. ')),UPPER(NAMA),IF(GELAR_BELAKANG='' OR GELAR_BELAKANG IS NULL,'',CONCAT(', ',GELAR_BELAKANG))) NAMA 
        FROM master.pegawai WHERE PROFESI NOT IN(6,11) AND NAMA LIKE '%%' GROUP BY NIP ORDER BY NAMA ASC");

        return $query->result();
    }

    public function listDr()
    {
        $q = $this->input->get('q');
        $query = $this->db->query("SELECT NIP,CONCAT(IF(GELAR_DEPAN='' OR GELAR_DEPAN IS NULL,'',CONCAT(GELAR_DEPAN,'. ')),UPPER(NAMA),IF(GELAR_BELAKANG='' OR GELAR_BELAKANG IS NULL,'',CONCAT(', ',GELAR_BELAKANG))) NAMA
        FROM master.pegawai 
        WHERE PROFESI=11 AND NAMA LIKE '%" . $q . "%'
        GROUP BY NIP ORDER BY NAMA ASC");

        return $query->result();
    }

    public function listPr()
    {
        $q = $this->input->get('q');
        $query = $this->db->query("SELECT NIP, CONCAT(IF(GELAR_DEPAN='' OR GELAR_DEPAN IS NULL,'',CONCAT(GELAR_DEPAN,'. ')),UPPER(NAMA),IF(GELAR_BELAKANG='' OR GELAR_BELAKANG IS NULL,'',CONCAT(', ',GELAR_BELAKANG))) NAMA
        FROM master.pegawai
        WHERE PROFESI=6 AND NAMA LIKE '%" . $q . "%'
        GROUP BY NIP ORDER BY NAMA ASC");

        return $query->result();
    }


    public function historyPOC($norm)
    {
        $query = $this->db->query('SELECT poc.id ID, p.NOMOR NOPEN
        , poc.nokun NOKUN, poc.created_at TANGGAL
        , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
        , rk.DESKRIPSI RUANGAN_KUNJUNGAN
        , p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN
        , poc.`*`,  master.getNamaLengkapPegawai(peng.NIP) USER

        FROM db_layanan.tb_planofcare poc
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = poc.nokun
        LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
        LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
        LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
        LEFT JOIN master.dokter dpjp ON dpjp.ID = tp.DOKTER
        left join master.pegawai peg ON peg.NIP=poc.dpjp_utama
        LEFT JOIN master.ruangan rk ON rk.ID = pk.RUANGAN
        LEFT JOIN aplikasi.pengguna peng ON peng.ID = poc.oleh
        LEFT JOIN db_pasien.tb_tanda_vital tv ON tv.data_source = 24 AND tv.ref = poc.id
        WHERE p.NORM="' . $norm . '" AND poc.`status`=1');
        return $query->result_array();
    }

    public function getDataPoc($id)
    {
        $query = $this->db->query('SELECT poc.id ID, p.NOMOR NOPEN
        , poc.nokun NOKUN, poc.created_at TANGGAL
        , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
        , rk.DESKRIPSI RUANGAN_KUNJUNGAN
        , p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN
        , poc.`*`

        FROM db_layanan.tb_planofcare poc
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = poc.nokun
        LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
        LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
        LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
        LEFT JOIN master.dokter dpjp ON dpjp.ID = tp.DOKTER
        LEFT JOIN master.ruangan rk ON rk.ID = pk.RUANGAN
        LEFT JOIN aplikasi.pengguna peng ON peng.ID = poc.oleh
        LEFT JOIN db_pasien.tb_tanda_vital tv ON tv.data_source = 24 AND tv.ref = poc.id
        WHERE poc.id="' . $id . '"');
        return $query->row_array();
    }

    public function getDataPlanOfCare()
    {
        $this->db->select('poc.planofcare, poc.target_terukur, poc.tanggal_rencana, poc.created_at, `master`.getNamaLengkapPegawai(ap.NIP) oleh');
        $this->db->from('db_layanan.tb_planofcare poc ');
        $this->db->join('pendaftaran.kunjungan pk', 'poc.nokun = pk.NOMOR','left');
        $this->db->join('aplikasi.pengguna ap', 'poc.oleh = ap.ID','left');
        $this->db->order_by('poc.created_at','DESC');
        if($this->input->post('nopen')){
            $this->db->where('pk.NOPEN', $this->input->post('nopen'));
            $this->db->limit(1);
        }
        $query = $this->db->get();
        return $query->row();
    }

    public function checkPocExists($nokun)
    {
        $this->db->select('1');
        $this->db->from('db_layanan.tb_planofcare');
        $this->db->where('nokun', $nokun);
        $this->db->where('status', 1);
        $query = $this->db->get();
        return $query->num_rows() > 0;
    }

}

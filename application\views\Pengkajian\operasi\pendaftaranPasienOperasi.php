<ul class="nav nav-tabs nav-justified">
    <li class="nav-item">
        <a href="#tab-form-pendaftaran-pra-operasi" data-toggle="tab" aria-expanded="false" class="nav-link active">
            Form Pendaftaran Pasien Operasi
        </a>
    </li>
    <li class="nav-item">
        <a href="#tab-history-pendaftaran-pra-operasi" data-toggle="tab" aria-expanded="true" class="nav-link">
            History Pendaftaran Pasien Operasi
        </a>
    </li>
</ul>

<div class="tab-content">
    <!-- <PERSON><PERSON> form -->
    <div role="tabpanel" class="tab-pane fade show active" id="tab-form-pendaftaran-pra-operasi">
        <form id="form-pendaftaran-pra-operasi" autocomplete="off">
            <input type="hidden" name="norm" value="<?= $getNomr['NORM'] ?>">
            <input type="hidden" name="dpjp" value="<?= $getNomr['ID_DOKTER'] ?>">
            <input type="hidden" name="nopen" value="<?= $getNomr['NOPEN'] ?>">
            <input type="hidden" name="nokun" value="<?= $getNomr['NOKUN'] ?>">
            <input type="hidden" name="usia" value="<?= $getNomr['USIA'] ?>">
            <input type="hidden" name="ruang_tujuan" value="<?= $getNomr['ID_RUANGAN'] ?>">
            <input type="hidden" name="jenis_kunjungan" value="<?= $getNomr['JENIS_KUNJUNGAN'] ?>">
            <input type="hidden" name="idemr" value="<?= $this->uri->segment(7) ?>">
            <?php if (isset($dataSebelumnya['created_at'])): ?>
                <input type="hidden" name="id_waiting_list" id="id-wl-pendaftaran-pra-operasi" value="<?= $dataSebelumnya['id_waiting_list'] ?? 0 ?>">
            <?php endif ?>
            <!-- Mulai dokter bedah -->
            <div class="row form-group">
                <label for="dokter-bedah-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Dokter Bedah Utama
                </label>
                <div class="col-md-8">
                    <select name="dokter_bedah[]" id="dokter-bedah-pendaftaran-pra-operasi" class="form-control">
                        <option value=""></option>
                        <?php foreach ($listDr as $ld): ?>
                            <option id="dokter-bedah-pendaftaran-pra-operasi-<?= $ld['ID_DOKTER'] ?>" value="<?= $ld['ID_DOKTER'] ?>" data-smf="<?= $ld['ID_SMF'] ?>">
                                <?= $ld['DOKTER'] . ' - ' . $ld['SMF'] ?>
                            </option>
                        <?php endforeach ?>
                    </select>
                </div>
            </div>
            <!-- Akhir dokter bedah -->
            <!-- Mulai rencana tindakan operasi -->
            <div class="row form-group">
                <label for="rencana-tindakan-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Rencana Tindakan Operasi <br>
                    <small>(diisi lengkap dengan bagian atau organ yang akan dioperasi)</small>
                </label>
                <div class="col-md-8">
                    <textarea id="rencana-tindakan-pendaftaran-pra-operasi" name="rencana_tindakan_operasi[]" class="form-control" placeholder="[ Tuliskan Rencana Tindakan ]"><?= $dataPengkajianPraOperasi['rencana_tindakan_operasi'] ?? null ?></textarea>
                </div>
            </div>
            <!-- Akhir rencana tindakan operasi -->
            <!-- Mulai join operasi -->
            <div class="row form-group">
                <label for="join-operasi-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    <em>Join</em> Operasi
                </label>
                <div class="col-md-8">
                    <div class="row pl-1">
                        <?php foreach ($joinOperasi as $jo): ?>
                            <div class="col-md form-check form-check-inline">
                                <div class="radio radio-primary">
                                    <input type="radio" name="join_operasi" id="join-operasi-pendaftaran-pra-operasi<?= $jo['id_variabel'] ?>" class="join-operasi-pendaftaran-pra-operasi" value="<?= $jo['id_variabel'] ?>" <?= $jo['status_checked'] == 1 ? 'checked' : null ?>>
                                    <label for="join-operasi-pendaftaran-pra-operasi<?= $jo['id_variabel'] ?>">
                                        <?= $jo['variabel'] ?>
                                    </label>
                                </div>
                            </div>
                        <?php endforeach ?>
                    </div>
                </div>
            </div>
            <!-- Akhir join operasi -->
            <!-- Mulai form dokter bedah lain -->
            <div class="card new-card form-group" id="form-dokter-bedah-lain-pendaftaran-pra-operasi">
                <div class="card-header new-card-header">Dokter Bedah Lain dan Tindakannya</div>
                <div class="card-body">
                    <!-- Mulai dokter bedah lain -->
                    <div class="row form-group">
                        <label for="dokter-lain-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                            Dokter Bedah Lain
                        </label>
                        <div class="col-md-8">
                            <select id="dokter-lain-pendaftaran-pra-operasi" class="form-control">
                                <option value=""></option>
                                <?php foreach ($listDr as $ld): ?>
                                    <option id="dokter-lain-pendaftaran-pra-operasi-<?= $ld['ID_DOKTER'] ?>" value="<?= $ld['ID_DOKTER'] ?>">
                                        <?= $ld['DOKTER'] . ' - ' . $ld['SMF'] ?>
                                    </option>
                                <?php endforeach ?>
                            </select>
                        </div>
                    </div>
                    <!-- Akhir dokter bedah lain -->
                    <!-- Mulai rencana tindakan dokter bedah lain -->
                    <div class="row form-group">
                        <label for="rencana-tindakan-lain-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                            Rencana Tindakan Operasi untuk Dokter Lain <br>
                            <small>(diisi lengkap dengan bagian atau organ yang akan dioperasi)</small>
                        </label>
                        <div class="col-md-8">
                            <textarea id="rencana-tindakan-lain-pendaftaran-pra-operasi" class="form-control" placeholder="[ Tuliskan Rencana Tindakan untuk Dokter Lain ]"></textarea>
                        </div>
                    </div>
                    <!-- Akhir rencana tindakan dokter bedah lain -->
                    <!-- Mulai aksi dokter bedah lain -->
                    <div class="row form-group">
                        <!-- <div class="col-sm-6">
                            <button type="button" class="btn btn-outline-danger btn-block waves-effect" id="hapus-dokter-bedah-lain-pendaftaran-pra-operasi">
                                Hapus
                            </button>
                        </div> -->
                        <div class="col-sm-12">
                            <button type="button" class="btn btn-outline-success btn-block waves-effect" id="tambah-dokter-bedah-lain-pendaftaran-pra-operasi">
                                Tambah
                            </button>
                        </div>
                    </div>
                    <!-- Akhir aksi dokter bedah lain -->
                    <!-- Mulai tabel dokter bedah lain -->
                    <div class="table-responsive overflow-auto">
                        <table class="table table-bordered table-hover table-custom" cellspacing="0" width="100%">
                            <thead>
                                <tr class="table-tr-custom">
                                    <th>#</th>
                                    <th>Dokter Bedah dan Rencana Tindakan Lain</th>
                                </tr>
                            </thead>
                            <tbody id="list-dokter-lain-pendaftaran-pra-operasi"></tbody>
                        </table>
                    </div>
                    <!-- Akhir tabel dokter bedah lain -->
                </div>
            </div>
            <!-- Akhir form dokter bedah lain -->
            <!-- Mulai diagnosis medis -->
            <div class="row form-group">
                <label for="diagnosa-medis-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Diagnosis Medis
                </label>
                <div class="col-md-8">
                    <input type="text" id="diagnosa-medis-pendaftaran-pra-operasi" name="diagnosa_medis" class="form-control" placeholder="[ Diagnosis Medis ]" value="<?= $dataPengkajianPraOperasi['diagnosis_pra_operasi'] ?? null ?>">
                </div>
            </div>
            <!-- Akhir diagnosis medis -->
            <!-- Mulai perkiraan lama operasi -->
            <div class="row form-group">
                <label for="lama-operasi-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Perkiraan Lama Operasi
                </label>
                <div class="col-md-8">
                    <div class="input-group">
                        <input type="number" class="form-control" id="lama-operasi-pendaftaran-pra-operasi" name="perkiraan_lama_operasi" placeholder="[ Lama Operasi dalam Menit ]" value="<?= $dataPengkajianPraOperasi['perkiraan_lama_operasi'] ?? null ?>">
                        <div class="input-group-append">
                            <span class="input-group-text">menit</span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Akhir perkiraan lama operasi -->
            <!-- Mulai tanggal -->
            <div class="row form-group">
                <label for="tanggal-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Tanggal
                </label>
                <div class="col-md-8">
                    <div class="input-group">
                        <input type="date" class="form-control" id="tanggal-pendaftaran-pra-operasi" name="tanggal_operasi">
                        <div class="input-group-append">
                            <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Akhir tanggal -->
            <!-- Mulai waktu -->
            <div class="row form-group">
                <label for="waktu-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Waktu
                </label>
                <div class="col-md-8">
                    <div class="input-group">
                        <input type="time" class="form-control" id="waktu-pendaftaran-pra-operasi" name="jam_operasi">
                        <div class="input-group-append">
                            <span class="input-group-text"><i class="fa fa-clock"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Akhir waktu -->
            <!-- Mulai penjamin/pembiayaan operasi -->
            <div class="row form-group">
                <label for="ruangan-operasi-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Penjamin/Pembiayaan Operasi <span class="text-danger">*</span>
                </label>
                <div class="col-md-8">
                    <select name="ruang_operasi" id="ruangan-operasi-pendaftaran-pra-operasi" class="form-control" required>
                        <option value=""></option>
                        <?php
                        $penjaminOperasi = $this->masterModel->referensiSimpel(81);
                        foreach ($penjaminOperasi as $po):
                            if ($po['ID'] == 2 || $po['ID'] == 16):
                        ?>
                            <option value="<?= $po['ID'] ?>"><?= $po['DESKRIPSI'] ?></option>
                        <?php
                            endif;
                        endforeach;
                        ?>
                    </select>
                </div>
            </div>
            <!-- Akhir penjamin/pembiayaan operasi -->
            <!-- Mulai tujuan operasi -->
            <div class="row form-group">
                <label for="tujuanOperasiDafOpe" class="col-form-label col-md-4">
                    Tujuan Operasi
                </label>
                <div class="col-md-8">
                    <div class="row pl-1">
                        <?php foreach ($tujuanOperasi as $to): ?>
                            <div class="col-md form-check form-check-inline">
                                <div class="radio radio-primary">
                                    <input type="radio" name="tujuan_operasi" id="tujuanOperasiDafOpe<?= $to['id_variabel'] ?>" class="tujuanOperasiDafOpe" value="<?= $to['id_variabel'] ?>" <?= $to['status_checked'] == 1 ? 'checked' : null ?>>
                                    <label for="tujuanOperasiDafOpe<?= $to['id_variabel'] ?>">
                                        <?= $to['variabel'] ?>
                                    </label>
                                </div>
                            </div>
                        <?php endforeach ?>
                    </div>
                </div>
            </div>
            <!-- Akhir tujuan operasi -->
            <!-- Mulai sifat operasi -->
            <div class="row form-group">
                <label for="sifatOperasiDafOpe" class="col-form-label col-md-4">
                    Sifat Operasi
                </label>
                <div class="col-md-8">
                    <div class="row pl-1">
                        <?php foreach ($sifatOperasi as $so): ?>
                            <div class="col-md-6 form-check">
                                <div class="radio radio-primary">
                                    <input type="radio" name="sifat_operasi" id="sifatOperasiDafOpe<?= $so['id_variabel'] ?>" class="sifatOperasiDafOpe" value="<?= $so['id_variabel'] ?>" <?= $so['status_checked'] == 1 ? 'checked' : null ?>>
                                    <label for="sifatOperasiDafOpe<?= $so['id_variabel'] ?>">
                                        <?= $so['variabel'] ?>
                                    </label>
                                </div>
                            </div>
                        <?php endforeach ?>
                    </div>
                </div>
            </div>
            <!-- Akhir sifat operasi -->
            <!-- Mulai alasan CITO -->
            <div class="row form-group d-none" id="form-cito-pendaftaran-pra-operasi">
                <label for="alasan-cito-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Alasan CITO
                </label>
                <div class="col-md-8">
                    <select name="sifat_operasi_lain" id="alasan-cito-pendaftaran-pra-operasi" class="form-controller">
                        <option value=""></option>
                    </select>
                </div>
            </div>
            <!-- Akhir alasan CITO -->
            <!-- Mulai alasan urgent -->
            <div class="row form-group d-none" id="form-urgent-pendaftaran-pra-operasi">
                <label for="alasan-urgent-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Alasan <em>Urgent</em>
                </label>
                <div class="col-md-8">
                    <select name="alasan_urgent" id="alasan-urgent-pendaftaran-pra-operasi" class="form-controller">
                        <option value=""></option>
                    </select>
                </div>
            </div>
            <!-- Akhir alasan urgent -->
            <!-- Mulai alasan prioritas -->
            <div class="row form-group d-none" id="form-prioritas-pendaftaran-pra-operasi">
                <label for="alasan-prioritas-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Alasan Prioritas
                </label>
                <div class="col-md-8">
                    <select name="alasan_prioritas" id="alasan-prioritas-pendaftaran-pra-operasi" class="form-controller">
                        <option value=""></option>
                    </select>
                </div>
            </div>
            <!-- Akhir alasan prioritas -->
            <!-- Mulai rencana jenis pembiusan -->
            <div class="row form-group">
                <label for="rencanaJenisPembiusanDafOpe" class="col-form-label col-md-4">
                    Rencana Jenis Pembiusan
                </label>
                <div class="col-md-8">
                    <div class="row pl-1">
                        <?php foreach ($rencanaJenisPembiusan as $renJenPem): ?>
                            <div class="col-md-6 form-check">
                                <div class="radio radio-primary">
                                    <input type="radio" name="rencana_jenis_pembiusan" id="rencanaJenisPembiusanDafOpe<?= $renJenPem['id_variabel'] ?>" class="rencanaJenisPembiusanDafOpe" value="<?= $renJenPem['id_variabel'] ?>" <?= $renJenPem['status_checked'] == 1 ? 'checked' : null ?>>
                                    <label for="rencanaJenisPembiusanDafOpe<?= $renJenPem['id_variabel'] ?>">
                                        <?= $renJenPem['variabel'] ?>
                                    </label>
                                </div>
                            </div>
                        <?php endforeach ?>
                    </div>
                </div>
            </div>
            <!-- Akhir rencana jenis pembiusan -->
            <!-- Mulai rencana jenis pembiusan lain -->
            <div class="row form-group d-none" id="ShowrencanaJenisPembiusan">
                <label for="rencanaJenisPembiusan_lainnya" class="col-form-label col-md-4">
                    Jenis Pembiusan Lainnya
                </label>
                <div class="col-md-8">
                    <textarea id="rencanaJenisPembiusan_lainnya" name="rencana_jenis_pembiusan_lain" class="form-control" placeholder="[ Jelaskan Lainnya ]"></textarea>
                </div>
            </div>
            <!-- Akhir rencana jenis pembiusan lain -->
            <!-- Mulai potong beku -->
            <div class="row form-group">
                <label for="potong-beku-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Potong Beku
                </label>
                <div class="col-md-8">
                    <div class="row pl-1">
                        <?php foreach ($potongBeku as $pb): ?>
                            <div class="col-md form-check form-check-inline">
                                <div class="radio radio-primary">
                                    <input type="radio" name="potong_beku" id="potong-beku-pendaftaran-pra-operasi<?= $pb['id_variabel'] ?>" class="potong-beku-pendaftaran-pra-operasi" value="<?= $pb['id_variabel'] ?>" <?= $pb['status_checked'] == 1 ? 'checked' : null ?>>
                                    <label for="potong-beku-pendaftaran-pra-operasi<?= $pb['id_variabel'] ?>">
                                        <?= $pb['variabel'] ?>
                                    </label>
                                </div>
                            </div>
                        <?php endforeach ?>
                    </div>
                </div>
            </div>
            <!-- Akhir potong beku -->
            <!-- Mulai catatan khusus -->
            <div class="row form-group">
                <label for="catatan-khusus-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Catatan Khusus
                </label>
                <div class="col-md-8">
                    <textarea id="catatan-khusus-pendaftaran-pra-operasi" name="catatan_khusus" class="form-control" placeholder="[ Tuliskan catatan khusus]"></textarea>
                </div>
            </div>
            <!-- Akhir catatan khusus -->
            <div class="row">
                <div class="col-lg-12">
                    <div class="pull-right">
                        <button type="submit" class="btn btn-primary waves-effect" id="simpan-pendaftaran-pra-operasi">
                            <i class="fa fa-save"></i> Simpan
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <!-- Akhir form -->
    <!-- Mulai tabel -->
    <div role="tabpanel" class="tab-pane fade" id="tab-history-pendaftaran-pra-operasi">
        <div class="row">
            <div class="col-12">
                <div class="table-responsive">
                    <table id="tabel-pendaftaran-pra-operasi" class="table table-bordered table-hover dt-responsive table-custom" cellspacing="0" width="100%">
                        <thead>
                            <tr class="table-tr-custom">
                                <th>No.</th>
                                <th>Waktu Operasi</th>
                                <th>Jadwal Operasi (OK)</th>
                                <th>Ruang</th>
                                <th>Dokter Bedah Utama</th>
                                <th>Oleh</th>
                                <th>Tanggal Daftar</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $no = 1;
                            foreach ($historydaftaroperasi as $hprop):
                            ?>
                                <tr>
                                    <td><?= $no ?>.</td>
                                    <td><?= $hprop['TANGGAL_JAM_OPERASI'] != '0000-00-00, 00:00:00' ? date('d/m/Y, H.i.s', strtotime($hprop['TANGGAL_JAM_OPERASI'])) : '-' ?></td>
                                    <td>
                                        <?php
                                            if (!empty($hprop['TANGGAL_PERJANJIAN']) && $hprop['TANGGAL_PERJANJIAN'] != '0000-00-00 00:00:00') {
                                                echo date('d/m/Y, H:i', strtotime($hprop['TANGGAL_PERJANJIAN']));
                                            } else {
                                                echo '-';
                                            }
                                        ?>
                                    </td>
                                    <td><?= $hprop['RUANGAN'] ?></td>
                                    <td><?= $hprop['DPJP'] ?></td>
                                    <td><?= $hprop['USER'] ?></td>
                                    <td><?= $hprop['TANGGAL_DAFTAR'] ?></td>
                                    <td>
                                        <a href="#ubah-pendaftaran-pra-operasi" data-toggle="modal" data-backdrop="static" data-id="<?= $hprop['id'] ?>" data-nokun="<?= $hprop['NOKUN'] ?>" class="btn btn-primary btn-block waves-effect">
                                            <i class="fas fa-edit"></i> Ubah
                                        </a>
                                    </td>
                                </tr>
                            <?php
                                $no++;
                            endforeach;
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <!-- Akhir tabel -->
</div>

<!-- Mulai modal ubah daftar pra operasi -->
<div aria-hidden="true" aria-labelledby="mySmallModalLabel" class="modal fade" id="ubah-pendaftaran-pra-operasi" role="dialog" style="display: none;">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content" id="hasilDaftarPraOperasi"></div>
    </div>
</div>
<!-- Akhir modal ubah daftar pra operasi -->

<script>
    $(document).ready(function() {
        // Mulai history
        $('#tabel-pendaftaran-pra-operasi').DataTable({
            responsive: true,
            language: {
                'sEmptyTable': 'Maaf, tidak ada data yang tersedia',
                'sInfo': 'Menampilkan _START_ sampai _END_ dari _TOTAL_ data',
                'sInfoEmpty': 'Menampilkan 0 sampai 0 dari 0 data',
                'sInfoFiltered': '(Pencarian dari _MAX_ total data)',
                'sInfoPostFix': '',
                'sInfoThousands': ',',
                'sLengthMenu': 'Menampilkan _MENU_ data',
                'sLoadingRecords': 'Harap tunggu...',
                'sProcessing': 'Sedang memproses...',
                'sSearch': 'Pencarian:',
                'sZeroRecords': 'Data tidak ditemukan',
                'oPaginate': {
                    'sFirst': 'Pertama',
                    'sLast': 'Terakhir',
                    'sNext': 'Selanjutnya',
                    'sPrevious': 'Sebelumnya'
                }
            },
        });
        // Akhir history

        // Mulai dokter bedah
        let smf = $('#dokter-bedah-pendaftaran-pra-operasi').find(':selected').data('smf');
        $('#dokter-bedah-pendaftaran-pra-operasi').select2({
            placeholder: '[ Pilih ]'
        }).val("<?= $getNomr['ID_DOKTER'] ?? 0 ?>").trigger('change');

        $('#dokter-bedah-pendaftaran-pra-operasi').change(function() {
            smf = $('#dokter-bedah-pendaftaran-pra-operasi').find(':selected').data('smf');
        });
        // Akhir dokter bedah

        // Mulai dokter bedah lain
        $('#dokter-lain-pendaftaran-pra-operasi').select2({
            placeholder: '[ Pilih dokter bedah lain ]'
        });
        // Akhir dokter bedah lain

        // Mulai penjamin/pembiayaan operasi
        $('#ruangan-operasi-pendaftaran-pra-operasi').select2({
            placeholder: '[ Pilih penjamin/pembiayaan ]'
        });

        // Auto-select penjamin/pembiayaan operasi berdasarkan RUANGAN/GEDUNG pasien
        var id_ruangan_pasien = "<?= $getNomr['ID_RUANGAN'] ?>";

        // Jika ID_RUANGAN ada
        if (id_ruangan_pasien) {
            // Panggil AJAX untuk mendapatkan detail ruangan
            $.ajax({
                url: "<?= base_url('operasi/PengkajianDafOpe/cekRuangan') ?>",
                method: 'POST',
                dataType: 'json',
                data: {
                    id_ruangan: id_ruangan_pasien
                },
                success: function(response) {
                    if (response) {
                        // Jika GEDUNG is NULL, pilih Operasi Reguler (ID=2)
                        if (response.GEDUNG === null) {
                            $('#ruangan-operasi-pendaftaran-pra-operasi').val('2').trigger('change');
                        }
                        // Jika GEDUNG == 1, pilih Operasi Swasta (Gedung C) (ID=16)
                        else if (response.GEDUNG == 1) {
                            $('#ruangan-operasi-pendaftaran-pra-operasi').val('16').trigger('change');
                        }
                    }
                }
            });
        }
        // Akhir ruangan operasi

        // Fungsi untuk menambah dokter bedah lain
        function tambahDokterBedahLain() {
            let jumlah = $('.hapus-item-dokter-lain').length;
            let id_dokter = $('#dokter-lain-pendaftaran-pra-operasi').val();
            let dokter = $('#dokter-lain-pendaftaran-pra-operasi option:selected').text().trim();
            let tindakan = $('#rencana-tindakan-lain-pendaftaran-pra-operasi').val();
            let isi = null;

            // Mulai periksa
            if (jumlah <= 4) { // Periksa jumlah apakah sudah ada 5
                if (id_dokter !== '' && tindakan !== '') { // Periksa apakah sudah diisi
                    // Mulai isi
                    isi = '<tr>' +
                        '<td class="text-center">' +
                        "<button type='button' class='btn btn-danger btn-sm hapus-item-dokter-lain' title='Hapus'><i class='fa fa-xmark'></i></button>" +
                        "<input type='hidden' class='isi-id-dokter-lain-pendaftaran-pra-operasi' name='dokter_bedah[]' value='" + id_dokter + "'>" +
                        '</td>' +
                        '<td>' +
                        "<div class='form-group'><input type='text' class='form-control isi-dokter-lain-pendaftaran-pra-operasi' value='" + dokter + "' aria-label='Dokter Bedah Lain' readonly></div>" +
                        "<div><textarea class='form-control isi-rencana-tindakan-pendaftaran-pra-operasi' name='rencana_tindakan_operasi[]' aria-label='Rencana Tindakan Operasi untuk Dokter Lain' readonly>" + tindakan + "</textarea></div>" +
                        '</td>' +
                        '</tr>';
                    $(isi).hide().appendTo('#list-dokter-lain-pendaftaran-pra-operasi').fadeIn(1000);
                    // Akhir isi

                    // Mulai bersihkan form`
                    $('#dokter-lain-pendaftaran-pra-operasi').val(0).trigger('change');
                    $('#rencana-tindakan-lain-pendaftaran-pra-operasi').val(null);
                    // Akhri bersihkan form
                    return true;
                } else {
                    return false;
                }
            } else {
                alertify.warning('Sudah ada 5 dokter bedah lain');
                return false;
            }
            // Akhir periksa
        }

        // Mulai tambah dokter bedah lain (manual button click)
        $('#tambah-dokter-bedah-lain-pendaftaran-pra-operasi').click(function() {
            if (!tambahDokterBedahLain()) {
                alertify.warning('Mohon isi data dokter bedah lain dan tindakannya lebih dulu');
            }
        });
        // Akhir tambah dokter bedah lain

        // Auto-add yang lebih user-friendly - dipicu ketika user pindah ke field lain atau melakukan action
        // HANYA akan auto-add jika KEDUA field sudah terisi (dokter DAN rencana tindakan)
        function checkAutoAdd() {
            let id_dokter = $('#dokter-lain-pendaftaran-pra-operasi').val();
            let tindakan = $('#rencana-tindakan-lain-pendaftaran-pra-operasi').val().trim();
            
            // Validasi ketat: KEDUA field harus terisi
            if (id_dokter !== '' && id_dokter !== null && tindakan !== '' && tindakan.length > 0) {
                tambahDokterBedahLain();
            }
        }

        // Event listener untuk auto-add - hanya pada action tertentu, bukan saat mengetik
        $('#rencana-tindakan-lain-pendaftaran-pra-operasi').on('blur', function() {
            // Auto-add ketika user keluar dari textarea (blur) - tapi hanya jika dokter juga sudah dipilih
            let id_dokter = $('#dokter-lain-pendaftaran-pra-operasi').val();
            if (id_dokter !== '' && id_dokter !== null) {
                checkAutoAdd();
            }
        });

        // Auto-add ketika user mengubah pilihan dokter - tapi hanya jika rencana tindakan juga sudah diisi
        $('#dokter-lain-pendaftaran-pra-operasi').on('change', function() {
            let tindakan = $('#rencana-tindakan-lain-pendaftaran-pra-operasi').val().trim();
            if (tindakan !== '' && tindakan.length > 0) {
                checkAutoAdd();
            }
        });

        // Auto-add ketika user klik field lain setelah mengisi KEDUA field
        $('#diagnosa-medis-pendaftaran-pra-operasi, #lama-operasi-pendaftaran-pra-operasi, #tanggal-pendaftaran-pra-operasi, #waktu-pendaftaran-pra-operasi, #ruangan-operasi-pendaftaran-pra-operasi').on('focus', function() {
            // Auto-add ketika user klik field lain - tapi hanya jika KEDUA field sudah terisi
            checkAutoAdd();
        });

        // Auto-add ketika user klik radio button tujuan operasi, sifat operasi, dll
        $('.tujuanOperasiDafOpe, .sifatOperasiDafOpe, .rencanaJenisPembiusanDafOpe, .potong-beku-pendaftaran-pra-operasi').on('click', function() {
            // Auto-add ketika user klik radio button lain - tapi hanya jika KEDUA field sudah terisi
            checkAutoAdd();
        });

        // Event listener untuk hapus individual dengan icon trash
        $(document).on('click', '.hapus-item-dokter-lain', function() {
            $(this).closest('tr').fadeOut(500, function() {
                $(this).remove();
            });
        });

        // Hapus bulk (tidak digunakan lagi, tapi tetap dipertahankan untuk kompatibilitas)
        $('#hapus-dokter-bedah-lain-pendaftaran-pra-operasi').click(function() {
            $('#list-dokter-lain-pendaftaran-pra-operasi').find($('.pilih-dokter-lain-pendaftaran-pra-operasi')).each(function() {
                if ($(this).is(':checked')) {
                    $(this).parents('tr').fadeOut(500, function() {
                        $(this).remove();
                    });
                }
            });
        });
        // Akhir hapus dokter bedah lain

        // Mulai sifat operasi
        $('.sifatOperasiDafOpe').click(function() {
            let id = $(this).val();
            if (id === '2131') { // CITO
                $('#form-cito-pendaftaran-pra-operasi').removeClass('d-none');
                $('#form-urgent-pendaftaran-pra-operasi, #form-prioritas-pendaftaran-pra-operasi').addClass('d-none');
                $('#alasan-urgent-pendaftaran-pra-operasi, #alasan-prioritas-pendaftaran-pra-operasi').val(null).trigger('change');
            } else if (id === '6080') { // Urgent
                $('#form-cito-pendaftaran-pra-operasi, #form-prioritas-pendaftaran-pra-operasi').addClass('d-none');
                $('#form-urgent-pendaftaran-pra-operasi').removeClass('d-none');
                $('#alasan-cito-pendaftaran-pra-operasi, #alasan-prioritas-pendaftaran-pra-operasi').val(null).trigger('change');
            } else if (id === '6125') { // Prioritas
                $('#form-cito-pendaftaran-pra-operasi, #form-urgent-pendaftaran-pra-operasi').addClass('d-none');
                $('#alasan-cito-pendaftaran-pra-operasi, #alasan-urgent-pendaftaran-pra-operasi').val(null).trigger('change');
                $('#form-prioritas-pendaftaran-pra-operasi').removeClass('d-none');
            } else { // Elektif
                $('#form-cito-pendaftaran-pra-operasi, #form-urgent-pendaftaran-pra-operasi, #form-prioritas-pendaftaran-pra-operasi').addClass('d-none');
                $('#alasan-cito-pendaftaran-pra-operasi, #alasan-urgent-pendaftaran-pra-operasi, #alasan-prioritas-pendaftaran-pra-operasi').val(null).trigger('change');
            }

            // Mulai alasan sifat operasi
            $('#alasan-cito-pendaftaran-pra-operasi, #alasan-urgent-pendaftaran-pra-operasi, #alasan-prioritas-pendaftaran-pra-operasi').select2({
                placeholder: '[ Pilih alasan ]',
                ajax: {
                    url: "<?= base_url('operasi/PengkajianDafOpe/alasan') ?>",
                    dataType: 'json',
                    delay: 250,
                    method: 'POST',
                    data: {
                        sifatOperasi: id,
                        smf: $('#dokter-bedah-pendaftaran-pra-operasi').find(':selected').data('smf')
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                }
            }).val(0).trigger('change');
            // Akhir alasan sifat operasi
        });
        // Akhir sifat operasi

        // Mulai rencana jenis pembiusan
        $('.rencanaJenisPembiusanDafOpe').click(function() {
            let id = $(this).val();
            if (id === '2138') {
                $('#ShowrencanaJenisPembiusan').removeClass('d-none');
            } else {
                $('#ShowrencanaJenisPembiusan').addClass('d-none');
                $('#rencanaJenisPembiusan_lainnya').val(null);
            }
        });

        if ($('.rencanaJenisPembiusanDafOpe:checked').val() === '2138') {
            $('#ShowrencanaJenisPembiusan').removeClass('d-none');
        }
        // Akhir rencana jenis pembiusan

        // Mulai join operasi
        $('.join-operasi-pendaftaran-pra-operasi').click(function() {
            let joinOperasiValue = $(this).val();
            if (joinOperasiValue === '6249') { // Ya
                $('#form-dokter-bedah-lain-pendaftaran-pra-operasi').show();
            } else { // Tidak
                $('#form-dokter-bedah-lain-pendaftaran-pra-operasi').hide();
            }
        });

        // Inisialisasi visibility form dokter bedah lain berdasarkan nilai yang sudah dipilih
        let selectedJoinOperasi = $('.join-operasi-pendaftaran-pra-operasi:checked').val();
        if (selectedJoinOperasi === '6249') {
            $('#form-dokter-bedah-lain-pendaftaran-pra-operasi').show();
        } else {
            $('#form-dokter-bedah-lain-pendaftaran-pra-operasi').hide();
        }
        // Akhir join operasi

        // Mulai simpan daftar operasi dengan validasi
        $('#form-pendaftaran-pra-operasi').submit(function(event) {
            event.preventDefault();
            
            // Validasi join operasi
            let joinOperasiValue = $('.join-operasi-pendaftaran-pra-operasi:checked').val();
            let jumlahDokterLain = $('.hapus-item-dokter-lain').length;
            
            // Jika pilih "Ya" tapi list dokter bedah lain kosong
            if (joinOperasiValue === '6249' && jumlahDokterLain === 0) {
                alertify.warning('Dokter Bedah Lain dan Tindakannya harus diisi karena Anda memilih "Ya" pada Join Operasi');
                return false;
            }
            
            let dataDAF_OPERASI = $(this).serializeArray();

            $.ajax({
                dataType: 'json',
                url: "<?= base_url('operasi/PengkajianDafOpe/action_dafoperasi/tambah') ?>",
                method: 'POST',
                data: dataDAF_OPERASI,
                success: function(data) {
                    if (data.status === 'success') {
                        alertify.success('Data tersimpan');
                        location.reload();
                    } else {
                        $.each(data.errors, function(index, element) {
                            alertify.warning(element);
                        });
                    }
                }
            });
        });
        // Akhir simpan daftar operasi

        // Mulai tampil daftar pra operasi
        $('#ubah-pendaftaran-pra-operasi').on('show.bs.modal', function(e) {
            let id = $(e.relatedTarget).data('id');
            $.ajax({
                type: 'POST',
                url: "<?= base_url('operasi/PengkajianDafOpe/viewDaftarPraOperasi') ?>",
                data: {
                    id: id
                },
                success: function(data) {
                    $('#hasilDaftarPraOperasi').html(data);
                }
            });
            // e.preventDefault();
        });
        // Akhir tampil daftar pra operasi
    });
</script>

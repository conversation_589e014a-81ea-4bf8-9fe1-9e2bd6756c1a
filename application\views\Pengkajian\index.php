<style>
    .tooltip-container {
        position: relative;
        display: inline-block;
        /*  border-bottom: 1px dotted black;*/
        width: 100%;
    }

    .tooltip-container .tooltip-text {
        visibility: hidden;
        width: 120px;
        background-color: black;
        color: #fff;
        text-align: center;
        border-radius: 6px;
        padding: 5px 0;
        position: absolute;
        z-index: 1;
        top: 150%;
        left: 50%;
        margin-left: -60px;
    }

    .tooltip-container .tooltip-text::after {
        content: "";
        position: absolute;
        bottom: 100%;
        left: 50%;
        margin-left: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: transparent transparent black transparent;
    }

    .tooltip-container:hover .tooltip-text {
        visibility: visible;
    }

    .pad3px {
        padding: 3px;
        width: 35px;
    }
</style>


<?php if ($jumlahNotif > 0): ?>
    <!-- Mulai info konsultasi -->
    <div class="form-group alert alert-info text-justify mt-3">
        <i class="fa fa-info-circle"></i> Ada <strong><?= $jumlahNotif ?> konsultasi untuk Anda yang belum dijawab</strong> di pasien ini
    </div>
    <!-- Akhir info konsultasi -->
<?php endif ?>

<!-- Page-Title -->
<div class="row">
    <div class="col-md-3">
        <h4 class="page-title">Detail Kunjungan</h4>
    </div>
    <div class="col-md mt-4">
        <div class="btn-group">
            <button class="btn btn-warning btn-waves-effect" id="buka-side-cppt" status="aktif">Hasil Penunjang dan CPPT</button>
            <button class="btn btn-custom waves-effect" id="buka-side-konsultasi" status="aktif">Konsultasi</button>
        </div>
    </div>
    <div class="col-md-auto mr-auto mt-4">
        <a href="<?= base_url() . 'pengkajianAwal/listpasien/' . $id_ruangan ?>" class="btn btn-primary waves-effect" role="button" data-toggle="tooltip" data-placement="left" data-original-title="Kembali ke List Pasien">
            <i class="fa fa-arrow-left"></i> <em>List</em> Pasien
        </a>
    </div>
</div>
<!-- end page title end breadcrumb -->

<div class="row">
    <!-- col 3 -->
    <div class="col-sm-3" id="sidebar-pasien-rj">
        <div class="card-box stickyTerbang">
            <div id="tampil-sidebar-pasien-rj" class="d-block">
                <div class="dropdown pull-right">
                    <!-- <a href="#" class="dropdown-toggle arrow-none card-drop" data-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-ellipsis-v"></i>
                    </a> -->
                    <div class="dropdown-menu dropdown-menu-right">
                        <!-- item-->
                        <!-- <a href="#takePhoto" data-toggle="modal" class="dropdown-item">
                            <i class="fa fa-camera"></i> Take Photo
                        </a> -->
                        <!-- item-->
                        <a href="#uploadPhoto" data-toggle="modal" class="dropdown-item">
                            <i class="fa fa-upload"></i> Unggah Foto
                        </a>
                    </div>
                </div>
                <div class="row form-group">
                    <div class="col-md-4">
                        <?php
                        if (empty($getNomr['NAME_PIC']) && isset($getNomr['ID_JK'])) {
                            if ($getNomr['ID_JK'] == 1) {
                                ?>
                                <img src="<?= base_url('assets/admin/assets/images/users/profile.jpg') ?>" class="rounded-circle img-thumbnail" alt="profile-image">
                                <?php
                            } else {
                                ?>
                                <img src="<?= base_url('assets/admin/assets/images/users/profile2.jpg') ?>" class="rounded-circle img-thumbnail" alt="profile-image">
                                <?php
                            }
                        } elseif (isset($getNomr['NAME_PIC'])) {
                            ?>
                            <img src="<?= base_url('assets/admin/assets/images/users/foto/') . $getNomr['NAME_PIC'] ?>" class="rounded-circle img-thumbnail" alt="profile-image">
                            <?php
                        }
                        ?>
                    </div>
                    <div class="col-md-5">
                        <?php if (isset($getNomr['ASN_TGL_INPUT'])): ?>
                            <div class="row">
                                <div class="tooltip-container">
                                    <span class="badge badge-warning">
                                        <img src="<?= base_url('assets/admin/assets/images/favicon.png') ?>" style="max-height: 20px;">
                                        ASN Kemenkes
                                    </span>
                                </div>
                            </div>
                        <?php endif ?>
                        <?php if ($infoconsenBgsi >= 0 || $statusBgsi >= 0): ?>
                            <!-- START INFO BGSI -->
                            <div class="row">
                                <div class="tooltip-container">
                                    <span class="tooltip-text">
                                        <?php if ($infoconsenBgsi == 0): ?>
                                            <i class="fa fa-info-circle"></i> Pasien belum inform concern BGSi</b>
                                            <br>
                                        <?php endif ?>
                                        <?php if ($infoconsenBgsi > 0): ?>
                                            <i class="fa fa-info-circle"></i> Pasien sudah inform concern BGSi</b>
                                            <br>
                                        <?php endif ?>
                                        <?php if ($statusBgsi == 0): ?>
                                            <i class="fa fa-info-circle"></i> Pasien belum disampel BGSi</b>
                                            <br>
                                        <?php endif ?>
                                        <?php if ($statusBgsi > 0): ?>
                                            <i class="fa fa-info-circle"></i> Pasien sudah disampel BGSi</b>
                                            <br>
                                        <?php endif ?>
                                    </span>
                                    <span class="badge badge-info text-black"><i class="fas fa-dna"></i> Info BGSi</span>
                                </div>
                            </div>
                            <!-- END INFO BGSI -->

                            <!-- START INFO INPUT DIAGNOSIS -->
                            <div class="row">
                                <div class="tooltip-container">
                                    <span class="tooltip-text" id="tooltipTombolInputDiagnosisDpjp">
                                        Input diagnosis <?= $cekMrDiagnosisDpjp <= 0 ? 'belum diisi' : null ?>
                                    </span>
                                    <a href="#viewInputDiagnosisDpjp" class="btn btn-<?= $cekMrDiagnosisDpjp <= 0 ? 'warning' : 'success' ?> btn-sm waves-effect" id="tombolInputDiagnosisDpjp" data-toggle="modal">
                                        <i class="fa fa-stethoscope"></i> <span id="isiTombolInputDiagnosisDpjp"><?= $cekMrDiagnosisDpjp ?></span>
                                    </a>
                                </div>
                            </div>
                            <!-- END INFO INPUT DIAGNOSIS -->

                            <!-- START HIV PALIATIF DNR -->
                            <?php if (isset($dataHIV['STATUS_HIV']) && $dataHIV['STATUS_HIV'] != "") { ?>
                                <div class="row">
                                    <div class="tooltip-container">
                                        <span class="badge badge-danger">HIV</span>
                                    </div>
                                </div>
                                <?php
                            }
                            if (isset($dataDNR['NORM'])) {
                                ?>
                                <div class="row">
                                    <div class="tooltip-container">
                                        <span class="badge badge-danger">DNR</span>
                                    </div>
                                </div>
                                <?php
                            }
                            if ($infoPaliatif['paliatif'] == 1) {
                                ?>
                                <div class="row">
                                    <div class="tooltip-container">
                                        <span class="badge badge-warning text-black">Paliatif</span>
                                    </div>
                                </div>
                            <?php } else if ($infoPaliatif['paliatif'] == 2) { ?>
                                    <div class="row">
                                        <div class="tooltip-container">
                                            <span class="badge badge-danger" style="color:#7d5bff;background-color:rgba(118, 80, 240, 0.2);border-color:rgba(118, 80, 240, 0.5)">Paliatif</span>
                                        </div>
                                    </div>
                            <?php } ?>
                            <!-- END HIV PALIATIF DNR -->
                        <?php endif ?>
                    </div>
                    <div class="col-md-3">
                        <div class="row">
                            <div class="col-md-6 tooltip-container mt-1">
                                <span class="tooltip-text">
                                    History eTimja
                                </span>
                                <a href="#modal-history-etimja-info" class="btn btn-info pad3px" id="tbl-history-etimja-info" data-toggle="modal" data-id="<?= $getNomr['NOKUN'] ?? null ?>" style="color: black;">
                                    <i class="fas fa-users"></i>
                                    <!--?php //if (isset($getNomr['JUMLAH_ETIMJA']) && $getNomr['JUMLAH_ETIMJA'] >= 1) : ?-->
                                    <!-- <span class="badge badge-danger"><!?php //$getNomr['JUMLAH_ETIMJA'] ?></span> -->
                                    <!--?php //endif ?-->
                                </a>
                            </div>

                            <?php if (isset($getNomr['IDPENJAMIN']) && $getNomr['IDPENJAMIN'] == '2'): ?>
                                <!-- Mulai cetak SEP -->
                                <div class="col-md-6 tooltip-container mt-1">
                                    <span class="tooltip-text">
                                        Cetak SEP
                                    </span>
                                    <a href="/reports/sep/cetak_sep_cppt.php?format=pdf&PSEP=<?= isset($getNomr['NO_SEP']) ? $getNomr['NO_SEP'] : $getNomr['NO_SEP_ALT'] ?>" class="btn btn-warning pad3px" target="_blank" style="color: black;">
                                        <i class="fa fa-print"></i>
                                    </a>
                                </div>
                                <!-- Akhir cetak SEP -->
                            <?php endif ?>

                            <!-- Mulai REGKAN -->
                            <div class="col-md-6 tooltip-container mt-1">
                                <span class="tooltip-text">
                                    Pelayanan Kanker
                                </span>
                                <a href="#viewModalREGKAN" class="btn btn-success viewModalRegkan pad3px" data-toggle="modal" data-nokun="<?= $getNomr['NOKUN'] ?? null ?>" data-nomr="<?= $getNomr['NORM'] ?? null ?>" data-nopen="<?= $getNomr['NOPEN'] ?? null ?>">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </div>
                            <!-- Akhir REGKAN -->

                            <?php if (isset($getNomr['IDPENJAMIN']) && $getNomr['IDPENJAMIN'] == '2'): ?>
                                <!-- Mulai iCARE -->
                                <div class="col-md-6 tooltip-container mt-1">
                                    <span class="tooltip-text">
                                        i-Care
                                    </span>
                                    <a href="#" class="btn btn-success pad3px" id="buka-icare">
                                        <i class="fa fa-hand-holding-medical"></i>
                                    </a>
                                </div>
                                <!-- Akhir iCARE -->
                            <?php endif ?>

                            <?php if ($this->session->userdata('status') == 1): ?>
                                <!-- START Diskon -->
                                <div class="col-md-6 tooltip-container mt-1">
                                    <span class="tooltip-text">
                                        Diskon Dokter
                                    </span>
                                    <a href="#viewDiskonDokter" class="btn btn-primary tombolDiskonDokter pad3px" data-nopen="<?= $getNomr['NOPEN'] ?>" data-nomr="<?= $getNomr['NORM'] ?>" data-toggle="modal">
                                        <i class="fa fa-percent"></i>
                                    </a>
                                </div>
                                <!-- END Diskon -->
                            <?php endif ?>

                            <?php if (isset($getNokunTerbaru['NOKUN']) && $getNokunTerbaru['NOKUN'] == $nokun): ?>
                                <!-- START P3G -->
                                <div class="col-md-6 tooltip-container mt-1">
                                    <span class="tooltip-text">
                                        Instrumen (P3G)
                                    </span>
                                    <a class="btn btn-primary tombolViewP3g pad3px" data-toggle="modal" data-target="#modalViewP3g" data-idgeriatri="<?= $getNokunTerbaru['ID_GERIATRI'] ?>" data-nokun="<?= $getNokunTerbaru['NOKUN'] ?>">P3G</a>
                                </div>
                                <!-- END P3G -->
                            <?php endif ?>

                            <!-- START Tanda Vital -->
                            <div class="col-md-6 tooltip-container mt-1">
                                <span class="tooltip-text">
                                    Tanda Vital
                                </span>
                                <a class="btn btn-danger tombolTandaVitalMon pad3px" data-toggle="modal" data-target="#viewTandaVitalMon" data-nomr="<?= $nomr ?>" data-nokun="<?= $nokun ?>" id="tandaVitalMon"><i class="fa fa-heartbeat"></i></a>
                            </div>
                            <!-- END Tanda Vital -->
                        </div>
                    </div>
                </div>

                <?php if ($jmlHasiLabKritis > 0): ?>
                    <?php if ($ceklogKritis == 0): ?>
                        <div class="row" id="rowKritis">
                            <div class="col-md-12">
                                <div class="alert alert-danger text-justify" style="margin-left:-10px">
                                    <i class="fa fa-info-circle"></i> Nilai Kritis Hasil Lab: <strong>Kritis.</strong>
                                    <a class="text-white" id="buka_hasil_lab" href="#">Cek Hasil Lab</a>
                                </div>
                            </div>
                        </div>
                    <?php endif ?>
                    <?php if ($ceklogKritis > 0 && $cekHasilLabPk['NOKUN'] > $logKritis['nokun']): ?>
                        <div class="row" id="rowKritis">
                            <div class="col-md-12">
                                <div class="alert alert-warning text-justify" style="margin-left:-10px">
                                    <i class="fa fa-info-circle"></i> Nilai Kritis Hasil Lab: <strong>Kritis.</strong>
                                    <a class="text-white" id="buka_hasil_lab" href="#">Cek Hasil Lab</a>
                                </div>
                            </div>
                        </div>
                    <?php endif ?>
                <?php endif ?>

                <div class="row form-group alert alert-warning <?= $cekMrDiagnosisDpjp <= 0 ? null : 'd-none' ?>" id="infoInputDiagnosisDpjp">
                    Diagnosis DPJP belum diisi. <a href="#viewInputDiagnosisDpjp" class="text-pink text-decoration-none" id="tautanInputDiagnosisDpjp" data-toggle="modal">Silakan isi di sini.</a>
                </div>

                <?php
                if (isset($getNomr['UMUR'])) {
                    $arrayUmur = explode(' ', $getNomr['UMUR']);
                    $umur = $arrayUmur[0];
                    if ($umur >= 60) { // Periksa umur
                        if (isset($getGeriatri3Bulan['id'])) {
                            if (isset($getGeriatriTerakhir['total'])) {
                                if ($getGeriatriTerakhir['total'] <= 12) {
                                    ?>
                                    <div class="alert alert-warning" style="margin-bottom: 0;">Hasil Skrining G8 pada tanggal <?= date('d/m/Y', strtotime($getGeriatriTerakhir['created_at'])) ?> adalah <?= $getGeriatriTerakhir['total'] ?>. Perlu dilanjutkan penilaian paripurna pasien geriatri (P3G) di Poli Geriatri</div>
                                    <?php
                                    if ($getp3gTerakhirNums > 0) {
                                        ?>
                                        <div class="mt-1" style="background-color:#fab157; color: black;">Hasil P3G pada tanggal <?= date('d/m/Y', strtotime($getp3gTerakhir['created_at'])) ?>.</br>Kesimpulan: <?= $getp3gTerakhir['rekomendasi_pilih'] ?></br>Rekomendasi: <?= $getp3gTerakhir['rekomendasi'] ?></div>
                                        <?php
                                    }
                                } else {
                                    ?>
                                    <div style="background-color:yellow; color: black;">Hasil Skrining G8 belum ada</div>
                                    <?php
                                }
                            }
                        } else {
                            ?>
                            <div style="background-color:yellow; color: black;">Belum ada skrining G8 dalam 3 bulan terakhir</div>
                            <?php
                        }
                    }
                }
                ?>

                <h6 class="notifviraload d-none border border-danger bg-danger text-white p-1"></h6>
                <hr>
                <div class="text-center">
                    <!--                     <div class="row">
                        <div class="col-md-12">
                            <span class="d-flex justify-content-start">Status Pasien: </span>
                        </div>
                    </div> -->

                    <div class="clearfix"></div>

                    <div class="divmulailayani d-none mt-2">
                        <div class="border border-warning">
                            <a class="btn btn-primary btn-block btn-sm waves-effect mulailayanipasien" target="_blank">
                                <i class="fa fa-stethoscope"></i> Mulai Layani Pasien
                            </a>
                        </div>
                        <div class="border border-warning p-2 badge badge-pill d-none mr-2 lbmulailayani">mulai dilayani 2023-02-22 10:32:04</div>
                    </div>
                    <div class="divviralload mt-2 d-none">
                        <div class="border border-warning">
                            <a class="btn btn-primary btn-block btn-sm waves-effect viralloadpasien" target="_blank">
                                <i class="fa fa-calendar"></i> Tanggal Pemberian obat HIV pertama
                            </a>
                        </div>
                    </div>
                    <div class="text-left">
                        <table class="table-borderless" width="100%">
                            <tr>
                                <td class="align-baseline" width="45%"><strong class="text-muted font-13">Nama Lengkap</strong></td>
                                <td>
                                    <span class="text-muted font-13">
                                        <?= $getNomr['NAMA_PASIEN'] ?? '-' ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="align-baseline"><strong class="text-muted font-13">Nomor MR</strong></td>
                                <td>
                                    <span class="text-muted font-13">
                                        <?= $getNomr['NORM'] ?? '-' ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="align-baseline"><strong class="text-muted font-13">Tgl. Lahir/Umur</strong></td>
                                <td>
                                    <span class="text-muted font-13">
                                        <?= isset($getNomr['TANGGAL_LAHIR']) && isset($getNomr['UMUR']) ? date('d/m/Y', strtotime($getNomr['TANGGAL_LAHIR'])) . ', ' . $getNomr['UMUR'] : '-' ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="align-baseline"><strong class="text-muted font-13">Jenis Kelamin</strong></td>
                                <td>
                                    <span class="text-muted font-13">
                                        <?= $getNomr['JK'] ?? '-' ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="align-baseline"><strong class="text-muted font-13">Tanggal Daftar</strong></td>
                                <td>
                                    <span class="text-muted font-13">
                                        <?= isset($getNomr['TANGGAL_DAFTAR']) ? date('d/m/Y, H.i.s', strtotime($getNomr['TANGGAL_DAFTAR'])) : '-' ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="align-baseline"><strong class="text-muted font-13">Ruang Asal</strong></td>
                                <td>
                                    <span class="text-muted font-13">
                                        <?= $getNomr['RUANGAN_TUJUAN'] ?? '-' ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="align-baseline"><strong class="text-muted font-13">DPJP</strong></td>
                                <td>
                                    <span class="text-muted font-13">
                                        <?= $getNomr['DOKTER_TUJUAN'] ?? '-' ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="align-baseline"><strong class="text-muted font-13">Penjamin</strong></td>
                                <td>
                                    <span class="text-muted font-13">
                                        <?php if (isset($getNomr['PENJAMIN']) && $getNomr['STATUS_PENJAMIN_COB'] == 1) { ?>
                                            <span style="color: #F1CF5A;"><b>COB</b></span> - <?= isset($getNomr['PENJAMIN']) ? $getNomr['PENJAMIN'] : null ?>
                                        <?php } elseif (isset($getNomr['PENJAMIN']) && $getNomr['STATUS_PENJAMIN_COB'] == 0) { ?>
                                            <span><?= isset($getNomr['PENJAMIN']) ? $getNomr['PENJAMIN'] : null ?></span>
                                        <?php } ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="align-baseline"><strong class="text-muted font-13">Diagnosis Pasien</strong></td>
                                <td>
                                    <span class="text-muted font-13">
                                        <?= isset($dataDiagnosisDpjp->DIAGNOSA) ? $dataDiagnosisDpjp->DIAGNOSA : 'Belum ada diagnosis' ?>
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                <?php if (isset($getPengkajianMedis['isi_alergi'])): ?>
                    <div class="text-center card-box">
                        <strong class="text-muted font-13 m-b-5">Riwayat Alergi</strong>
                        <hr>
                        <div class="text-left">
                            <table class="table table-borderless" width="100%">
                                <tr>
                                    <td>
                                        <p class="text-muted font-13 m-b-5">
                                            <select multiple data-role="tagsinput" name="riwayat_alergi_desk[]" class="riwayat_alergi_desk_medis" disabled="true">
                                                <?php
                                                if (!empty($getPengkajianMedis['isi_alergi'])) {
                                                    $dataalergi = json_decode($getPengkajianMedis['isi_alergi']);
                                                    for ($i = 0; $i < count($dataalergi); $i++) {
                                                        echo "<option value='$dataalergi[$i]'>$dataalergi[$i]</option>";
                                                    }
                                                }
                                                ?>
                                            </select>
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                <?php endif ?>
            </div>

            <?php if (isset($infoPermintaanDarah) && isset($getNomr['TANGGAL_DAFTAR']) && $infoPermintaanDarah['tgl_minta'] >= $getNomr['TANGGAL_DAFTAR']): ?>
                <!-- Mulai info permintaan darah -->
                <hr>
                <div id="tampil-sidebar-permintaan-darah-rj" class="d-block">
                    <h4 class="text-center">Info Permintaan Darah</h4>
                    <div class="row">
                        <div class="col-sm">
                            <strong>Tgl. Permintaan</strong>
                        </div>
                        <div class="col-sm"><?= date('d/m/Y, H.i.s', strtotime($infoPermintaanDarah['tgl_minta'])) ?></div>
                    </div>
                    <div class="row">
                        <div class="col-sm">
                            <strong>Tgl. Diperlukan</strong>
                        </div>
                        <div class="col-sm"><?= date('d/m/Y', strtotime($infoPermintaanDarah['tgl_diperlukan'])) ?></div>
                    </div>
                    <div class="row">
                        <div class="col-sm">
                            <strong>Alasan</strong>
                        </div>
                        <div class="col-sm"><?= !empty($infoPermintaanDarah['alasan']) ? $infoPermintaanDarah['alasan'] : '-' ?></div>
                    </div>
                    <div class="row">
                        <div class="col-sm">
                            <strong>Status</strong>
                        </div>
                        <div class="col-sm"><?= $infoPermintaanDarah['status'] ?></div>
                    </div>
                </div>
                <!-- Akhir info permintaan darah -->
            <?php endif ?>

            <!-- Mulai sidebar rawat jalan -->
            <div id="tampil-sidebar-hasil-lab-rj" class="d-none">
                <ul class="nav nav-tabs nav-justified">
                    <li class="nav-item">
                        <a href="#cppt-side-rj" id="menu-cppt-side-rj" data-toggle="tab" aria-expanded="true" class="nav-link active cppt-side-rj">CPPT</a>
                    </li>
                    <li class="nav-item">
                        <a href="#lab-pk-side-rj" id="menu-lab-pk-side-rj" data-toggle="tab" aria-expanded="false" class="nav-link lab-pk-side-rj">Lab PK</a>
                    </li>
                    <li class="nav-item">
                        <a href="#lab-pa-side-rj" id="menu-lab-pa-side-rj" data-toggle="tab" aria-expanded="true" class="nav-link lab-pa-side-rj">Lab PA</a>
                    </li>
                    <li class="nav-item">
                        <a href="#lab-pt-side-rj" id="menu-lab-pt-side-rj" data-toggle="tab" aria-expanded="true" class="nav-link lab-pt-side-rj">Lab Patmol</a>
                    </li>
                    <li class="nav-item">
                        <a href="#radiologi-side-rj" id="menu-radiologi-side-rj" data-toggle="tab" aria-expanded="true" class="nav-link radiologi-side-rj">Radiologi</a>
                    </li>
                    <li class="nav-item">
                        <a href="#resep-side-rj" id="menu-resep-side-rj" data-toggle="tab" aria-expanded="true" class="nav-link">Resep</a>
                    </li>
                    <li class="nav-item">
                        <a href="#e-resep-side-rj" id="menu-e-resep-side-rj" data-toggle="tab" aria-expanded="true" class="nav-link">E-Resep</a>
                    </li>
                    <li class="nav-item">
                        <a href="#history-pengkajian-side-rj" id="menu-history-pengkajian-side-rj" data-toggle="tab" aria-expanded="true" class="nav-link">History Pengkajian</a>
                    </li>
                    <li class="nav-item">
                        <a href="#listobat-side" id="menu-listobat-side" data-toggle="tab" aria-expanded="true" class="nav-link">Daftar Obat Formularium</a>
                    </li>
                    <li class="nav-item">
                        <a href="#tindakanBilling-side" id="menu-tindakanBilling-side" data-toggle="tab" aria-expanded="true" class="nav-link">Tindakan Billing</a>
                    </li>
                </ul>
                <div class="tab-content">
                    <div role="tabpanel" class="tab-pane fade" id="tindakanBilling-side" style="max-height: calc(100vh - 200px); overflow-y: auto;"></div>
                    <div role="tabpanel" class="tab-pane fade" id="listobat-side" style="max-height: calc(100vh - 200px); overflow-y: auto;"></div>
                    <div role="tabpanel" class="tab-pane fade show active" id="cppt-side-rj" style="max-height: calc(100vh - 200px); overflow-y: auto;"></div>
                    <div role="tabpanel" class="tab-pane fade" id="history-pengkajian-side-rj" style="max-height: calc(100vh - 200px); overflow-y: auto;"></div>
                    <div role="tabpanel" class="tab-pane fade" id="lab-pk-side-rj"></div>
                    <div role="tabpanel" class="tab-pane fade" id="lab-pa-side-rj"></div>
                    <div role="tabpanel" class="tab-pane fade" id="lab-pt-side-rj"></div>
                    <div role="tabpanel" class="tab-pane fade" id="radiologi-side-rj"></div>
                    <div role="tabpanel" class="tab-pane fade" id="resep-side-rj" style="max-height: calc(100vh - 200px); overflow-y: auto;"></div>
                    <!-- START INC VIEW MENU ERESEP -->
                    <div role="tabpanel" class="tab-pane fade" id="e-resep-side-rj">
                        <div class="text-white" id="view-eresep-side-rj" style="max-height: calc(100vh - 200px); overflow-y: auto;"></div>
                    </div>
                    <!-- END INC VIEW MENU ERESEP -->
                </div>
            </div>
            <div id="tampil-sidebar-konsultasi-rj"></div>
            <!-- Akhir sidebar rawat jalan -->
        </div>
    </div>
    <!-- End col 3 -->
    <!-- col 9 -->
    <div class="col-sm-9" id="bar-pasien-rj">
        <div class="card-box">
            <ul class="nav nav-tabs" id="myTab">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" data-toggle="dropdown" aria-expanded="false" href="#" role="button">
                        EMR
                    </a>
                    <div class="dropdown-menu pre-scrollable">
                        <?php
                        if (isset($getNomr['USIA']) && $id_ruangan) {
                            $pengkajian = ($getNomr['USIA'] == 2 or ($getNomr['USIA'] == 1 && in_array($id_ruangan, array('105140101', '105120101', '105020101', '105020901', '105100101')))) && $_SESSION['status'] == 2 && !in_array($id_ruangan, array('105130101', '105020401', '105130103')) ? "pengkajianKeperawatanDewasa" : ($getNomr['USIA'] == 1 && $_SESSION['status'] == 2 && !in_array($id_ruangan, array('105020401')) ? "pengkajianKeperawatanAnak" : (($getNomr['USIA'] == 2 or ($getNomr['USIA'] == 1 && in_array($id_ruangan, array('105140101', '105120101', '105020101', '105020901', '105100101')))) && $_SESSION['status'] == 1 && !in_array($id_ruangan, array('105130101', '105020401', '105130103')) ? "pengkajianMedisDewasa" : ($getNomr['USIA'] == 1 && $_SESSION['status'] == 1 && !in_array($id_ruangan, array('105020401')) ? "pengkajianMedisAnak" : ($id_ruangan == 105130101 && $_SESSION['status'] == 2 || $id_ruangan == 105130103 && $_SESSION['status'] == 2 ? "pengkajianKeperawatanDeteksiDini" : ($id_ruangan == 105130101 && $_SESSION['status'] == 1 || $id_ruangan == 105130103 && $_SESSION['status'] == 1 ? "pengkajianMedisDeteksiDini" : ($id_ruangan == 105020401 && $_SESSION['status'] == 2 ? "paliatifKeperawatan" : ($id_ruangan == 105020401 && $_SESSION['status'] == 1 ? "paliatifMedis" : "")))))));
                            if ($id_ruangan != 105100101) {
                                ?>
                                <a class="dropdown-item PengkajianAwalKeperawatan " data-toggle="tab" href="#<?= $pengkajian ?>">
                                    Pengkajian Awal
                                </a>
                                <?php
                            }
                        }
                        ?>
                        <a class="dropdown-item cppt" data-toggle="tab" href="#cppt">
                            CPPT
                        </a>
                        <a class="dropdown-item ewsRawatJalan" data-toggle="tab" href="#ews">
                            EWS
                        </a>
                        <a href="#catatan-edukasi" class="dropdown-item catatan-edukasi" data-toggle="tab">
                            Catatan Edukasi dan Informasi Terintegrasi
                        </a>
                        <a href="#formulirSkriningVisual" class="dropdown-item" data-toggle="tab">
                            Formulir Skrining Visual
                        </a>
                        <a href="#gizi" class="dropdown-item" data-toggle="tab">
                            Gizi
                        </a>
                        <a href="#pengkajianPraSedasi" class="dropdown-item pengkajianPraSedasi" data-toggle="tab">
                            Pengkajian Pra Anastesi/Sedasi
                        </a>
                        <a href="#barthelIndek" class="dropdown-item barthelIndek" data-toggle="tab">
                            Barthel Indek
                        </a>
                        <a href="#summaryListPe" class="dropdown-item summaryListPe" data-toggle="tab">
                            Profil Ringkas Medis Rawat Jalan
                        </a>
                        <a href="#buktiPelayananRJ" class="dropdown-item buktiPelayananRJ" data-toggle="tab">
                            Bukti Pelayanan Rawat Jalan
                        </a>
                        <a href="#echoekg" class="dropdown-item" data-toggle="tab">
                            <em>Echo</em> EKG
                        </a>
                        <a class="dropdown-item laporanHasilPemeriksaan" data-toggle="tab" href="#laporanHasilPemeriksaan">
                            <?= ($statusPengguna == 1 ? '(Dokter) ' : ($statusPengguna == 2 ? '(Perawat) ' : '')) ?>Laporan Hasil Pemeriksaan
                        </a>
                        <!--?php // $pengkajianPaliatif = $_SESSION['status'] == 2 ? "paliatifKeperawatan" : ($_SESSION['status'] == 1 ? "paliatifMedis" : "") ?>
                        <a class="dropdown-item" data-toggle="tab" href="#<!?= $pengkajianPaliatif ?>">
                            Pengkajian Awal Pasien Paliatif
                        </a>-->
                    </div>
                </li>
                <!-- START CPPT LIST -->
                <li class="nav-item">
                    <a href="#cpptList" data-toggle="tab" aria-expanded="false" class="nav-link cpptList">
                        CPPT <em>List</em>
                    </a>
                </li>
                <!-- END CPPT LIST -->
                <!-- START PENGKAJIAN LIST -->
                <li class="nav-item">
                    <a href="#menu-pengkajian-list" data-toggle="tab" aria-expanded="false" class="nav-link" id="pengkajian-list">Pengkajian <em>List</em></a>
                </li>
                <!-- END PENGKAJIAN LIST -->
                <!-- START Berkas LIST -->
                <!-- <li class="nav-item">
                    <a href="#menu-berkas-list" data-toggle="tab" aria-expanded="false" class="nav-link" id="berkas-list">Berkas Pasien</a>
                </li> -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                        Berkas <em>Scan</em>
                    </a>
                    <div class="dropdown-menu">
                        <a class="dropdown-item" href="#menu-berkas-list" data-toggle="tab" aria-expanded="false" id="berkas-list">
                            Berkas <em>Scan</em> Rekam Medis
                        </a>
                        <a class="dropdown-item" href="#menu-berkas-pasien-luar" data-toggle="tab" aria-expanded="false" id="berkas-pasien-luar">
                            Berkas <em>Scan</em> Pasien Luar RSKD
                        </a>
                        <a class="dropdown-item" href="#menu-berkas-scan" data-toggle="tab" aria-expanded="false" id="berkas-scan">
                            Berkas <em>Scan</em> Lainnya
                        </a>
                    </div>
                </li>
                <li class="nav-item">
                    <a href="#menu-faktor-resiko" data-toggle="tab" aria-expanded="false" class="nav-link" id="faktor-resiko">Faktor Resiko</a>
                </li>
                <!-- END Berkas LIST -->
                <!-- START MENU PROSEDUR DIAGNOSTIK -->
                <?php if ($id_ruangan == 105060101): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle menuProsedurDiagnostik" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Prosedur Diagnostik
                        </a>
                        <div class="dropdown-menu">
                            <a class="dropdown-item tindakan_invasif" data-toggle="tab" href="#pengkajianTindakanInvasif">
                                Pengkajian Awal Tindakan Invasif
                            </a>
                            <a class="dropdown-item persiapan_Bronkoskopi" data-toggle="tab" href="#persiapanBronkoskopi">
                                Persiapan Bronkoskopi
                            </a>
                            <a class="dropdown-item" data-toggle="tab" href="#persiapanGastroskopi">
                                Persiapan Gastroskopi
                            </a>
                            <a class="dropdown-item" data-toggle="tab" href="#pemeriksaanSpirometri">
                                Pemeriksaan Spirometri
                            </a>
                            <a class="dropdown-item laporanHasilPemeriksaan" data-toggle="tab" href="#laporanHasilPemeriksaan">
                                Laporan Hasil Pemeriksaan
                            </a>
                            <a class="dropdown-item scanHasilPemeriksaan" data-toggle="tab" href="#scanHasilPemeriksaan">
                                Scan Hasil Pemeriksaan
                            </a>
                            <a class="dropdown-item serah_terima" data-toggle="tab" href="#serahterima">
                                Serah Terima Pemeriksaan
                            </a>
                            <a class="dropdown-item keselamatan_TindakanInvasif" data-toggle="tab" href="#keselamatanTindakanInvasif">
                                Keselamatan Tindakan Invasif
                            </a>
                            <a class="dropdown-item" data-toggle="tab" href="#echoekg">
                                <em>Echo</em> EKG
                            </a>
                            <a href="#pengkajianPraSedasi" class="dropdown-item pengkajianPraSedasi" data-toggle="tab">
                                Pengkajian Pra Anastesi/Sedasi
                            </a>
                            <a class="dropdown-item pemantauan_AnestesiLokal" data-toggle="tab" href="#pemantauanAnestesiLokal">
                                Pemantauan Anestesi Lokal
                            </a>
                            <a class="dropdown-item status_anestesia" data-toggle="tab" aria-expanded="false" href="#statusanestesia">
                                Status Anestesia
                            </a>
                            <!-- <a class="dropdown-item" data-toggle="tab" aria-expanded="false" href="#tandavital_sa">
                            Tanda Vital Status Anestesia
                        </a> -->
                        </div>
                    </li>
                <?php endif ?>
                <!-- END MENU PROSEDUR DIAGNOSTIK -->
                <!-- START MENU RADIOTERAPI -->
                <!-- <?//php if ($id_ruangan == 105120101): ?> -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Radio Terapi
                        </a>
                        <div class="dropdown-menu">
                            <a href="#radioterapi" data-toggle="tab" aria-expanded="false" class="nav-link">
                                Form Pengkajian
                            </a>
                            <a class="dropdown-item ct_Simulator" data-toggle="tab" href="#ct_Simulator">
                                <em>CT Simulator</em>
                            </a>
                            <a class="dropdown-item simulatorInformation" data-toggle="tab" href="#simulatorInformation">
                                <em>Simulator Konvensional</em>
                            </a>
                            <a class="dropdown-item treatmentDose" data-toggle="tab" href="#treatmentDose">
                                <em>Treatment Dose</em>
                            </a>
                            <a class="dropdown-item penjadwalanradioterapi" data-toggle="tab" href="#penjadwalanradioterapi">
                                <em>Penjadwalan Radioterapi</em>
                            </a>
                            <a class="dropdown-item catatanRadioTerapi" href="#catatanRadioTerapi" data-toggle="tab">
                                Catatan Radioterapi
                            </a>
                        </div>
                    </li>
                <!-- <?//php endif ?> -->
                <!-- END MENU RADIOTERAPI -->
                <!-- START MENU RADIOLOGI -->
                <?php if ($id_ruangan == 105100101): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Radiologi
                        </a>
                        <div class="dropdown-menu">
                            <a class="dropdown-item serah_terima" data-toggle="tab" href="#serahterima">
                                Serah Terima Pemeriksaan
                            </a>
                            <a class="dropdown-item tindakan_invasif" data-toggle="tab" href="#pengkajianTindakanInvasif">
                                Pengkajian Tindakan Invasif
                            </a>
                            <a class="dropdown-item PengkajianAwalKeperawatan " data-toggle="tab" href="#<?= isset($pengkajian) ? $pengkajian : null ?>">
                                Form Pengkajian Kedokteran Diagnostik
                            </a>
                            <a class="dropdown-item" data-toggle="tab" href="#pengkajianKedokteranNuklir">
                                Form Pengkajian Kedokteran Nuklir
                            </a>
                            <a class="dropdown-item keselamatan_TindakanInvasif" data-toggle="tab" href="#keselamatanTindakanInvasif">
                                Keselamatan Tindakan Invasif
                            </a>
                            <a class="dropdown-item pemantauan_AnestesiLokal" data-toggle="tab" href="#pemantauanAnestesiLokal">
                                Pemantauan Anestesi Lokal
                            </a>
                            <a class="dropdown-item" data-toggle="tab" href="#tindakanIntervensi">
                                Form Pengkajian Tindakan Intervensi
                            </a>
                            <a class="dropdown-item pemTinAnesLokRj" data-toggle="tab" href="#pemTinAnesLokRj">
                                Form Pemantauan Tindakan Anestesi Lokal
                            </a>
                        </div>
                    </li>
                <?php endif ?>
                <!-- END MENU RADIOLOGI -->
                <!-- START MENU REHABILITASI MEDIK -->
                <?php if ($id_ruangan == 105110101): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Rehabilitasi Medik
                        </a>
                        <div class="dropdown-menu">
                            <a class="dropdown-item konsul-rehab-medik" data-toggle="tab" href="#konsul-rehab-medik">
                                Konsultasi Rehab
                            </a>
                            <a class="dropdown-item limfedema" data-toggle="tab" href="#limfedema">
                                Pengukuran Limfedema
                            </a>
                            <a href="#ekfm" data-toggle="tab" class="dropdown-item">
                                Evaluasi Kemampuan Fungsional Mobilitas
                            </a>
                            <a class="dropdown-item" id="klaim-rehab" data-toggle="tab" href="#menu-klaim-rehab">
                                Klaim Rehabilitasi Medik
                            </a>
                        </div>
                    </li>
                <?php endif ?>
                <!-- END MENU REHABILITASI MEDIK -->
                <!-- START MENU HEMODIALISA -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                        Hemodialisis
                    </a>
                    <div class="dropdown-menu">
                        <a class="dropdown-item ptk-hemodialisis" data-toggle="tab" href="#ptk-hemodialisis">
                            Persetujuan Tindakan Kedokteran Hemodialisis
                        </a>
                        <a class="dropdown-item" data-toggle="tab" href="#hemodialisaUtama">
                            Observasi
                        </a>
                        <a href="#pemantauanNyeri" class="dropdown-item pemantauanNyeri" data-toggle="tab">
                            Pemantauan Nyeri
                        </a>
                        <a href="#pengkajianRisikoJatuhPasienDewasa" class="dropdown-item pengkajianRisikoJatuhPasienDewasa" data-toggle="tab">
                            Resiko Jatuh Dewasa (Skala Morse)
                        </a>
                        <a href="#skalaOntario" class="dropdown-item skalaOntario" data-toggle="tab">
                            Resiko Jatuh Geriatri (Skala Ontario)
                        </a>
                        <a href="#skalaOntarioMASS" class="dropdown-item skalaOntarioMASS" data-toggle="tab">
                            Resiko Jatuh Geriatri (Skala Ontario Modified Atratify-Sidney Scorsing)
                        </a>
                        <a href="#humptyDumptyRi" class="dropdown-item humptyDumptyRi" data-toggle="tab">
                            Resiko Jatuh Anak <em>(Humpty Dumpty)</em>
                        </a>
                        <a href="#serah-terima-shift" class="dropdown-item serah-terima-shift" data-toggle="tab">
                            Serah Terima Antar Shift
                        </a>
                    </div>
                </li>
                <!-- END MENU HEMODIALISA -->
                <!-- START MENU ANYELIR -->
                <?php if ($id_ruangan == 105020101 || $id_ruangan == 105020102): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Anyelir
                        </a>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" data-toggle="tab" href="#observasiAnyelir">
                                Observasi
                            </a>
                            <a class="dropdown-item" data-toggle="tab" href="#keperawatanAnyelir">
                                Keperawatan
                            </a>
                            <a class="dropdown-item" data-toggle="tab" href="#kemoamlanak">
                                Persetujuan Tindakan kemoterapi AML Anak
                            </a>
                        </div>
                    </li>
                <?php endif ?>
                <!-- END MENU ANYELIR -->
                <!-- START MENU GIGI -->
                <?php if (in_array($id_ruangan, [105020705, 105020201, 105020706]) || $getNomr['GEDUNG'] == 1): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Gigi
                        </a>
                        <div class="dropdown-menu">
                            <a class="dropdown-item pengkajianGigi" data-toggle="tab" href="#pengkajianGigi">
                                Pengkajian
                            </a>
                            <?php if ($this->session->userdata('status') == 1): ?>
                                <a class="dropdown-item odontogram" data-toggle="tab" href="#odontogram">
                                    Odontogram
                                </a>
                            <?php endif ?>
                            <a class="dropdown-item tindakan_invasif" data-toggle="tab" href="#pengkajianTindakanInvasif">
                                Pengkajian Awal Tindakan Invasif
                            </a>
                            <a class="dropdown-item keselamatan_TindakanInvasif" data-toggle="tab" href="#keselamatanTindakanInvasif">
                                Keselamatan Tindakan Invasif
                            </a>
                            <a class="dropdown-item pemantauanAnastesiGigi" data-toggle="tab" href="#pemantauanAnastesiGigi">
                                Pemantauan Anastesi Lokal Tindakan Gigi
                            </a>
                        </div>
                    </li>
                <?php endif ?>
                <!-- END MENU GIGI -->
                <!-- START MENU LUKA -->
                <?php if (in_array($id_ruangan, [105020705, 105020702, 105020201, 105020706]) || $getNomr['GEDUNG'] == 1): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Luka
                        </a>
                        <div class="dropdown-menu">
                            <a class="dropdown-item pengkajianLuka" data-toggle="tab" href="#pengkajianLuka">
                                Pengkajian Luka
                            </a>
                            <a class="dropdown-item pengkajianStoma" data-toggle="tab" href="#pengkajianStoma">
                                Pengkajian Stoma
                            </a>
                        </div>
                    </li>
                <?php endif ?>
                <!-- END MENU LUKA -->
                <!-- START MENU BANK DARAH -->
                <?php if ($id_ruangan == *********): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Bank Darah
                        </a>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" data-toggle="tab" href="#specOptia">
                                <i>Spectra Optia - Platelet Collection Procedure Sheet</i>
                            </a>
                            <a class="dropdown-item" data-toggle="tab" href="#tindakanDonorAferesis">
                                Persetujuan Tindakan Donor Aferesis
                            </a>
                            <a class="dropdown-item" data-toggle="tab" href="#preaferesis">
                                Formulir Pasien Preaferesis Terapeutik <i>(Stem Cells)</i>
                            </a>
                            <a class="dropdown-item pengkajianAferesis" data-toggle="tab" href="#pengkajianAferesis">
                                Pengkajian Aferesis
                            </a>
                            <a class="dropdown-item" data-toggle="tab" href="#ceklisTindakanAferesis">
                                Ceklis Tindakan Aferesis
                            </a>
                            <a class="dropdown-item persetujuanTindakanTerapeutikAferesis" data-toggle="tab" href="#persetujuanTindakanTerapeutikAferesis">
                                Persetujuan Tindakan Terapeutik Aferesis
                            </a>
                            <a class="dropdown-item TrombaferesisDenganMesinHaemonetics_tab" data-toggle="tab" href="#TrombaferesisDenganMesinHaemonetics">
                                Trombaferesis Dengan Mesin Haemonetics
                            </a>
                            <a class="dropdown-item" data-toggle="tab" href="#trombaferesisMesinAmicus">
                                Trombaferesis Dengan Mesin Amicus
                            </a>
                            <a class="dropdown-item" data-toggle="tab" href="#persetujuanTindakanTD">
                                Persetujuan Tindakan Transfusi Darah
                            </a>
                            <a class="dropdown-item pemberianDanPemantauanDarah" data-toggle="tab" href="#pemberianDanPemantauan">
                                Pemberian dan Pemantauan Tranfusi Darah
                            </a>
                        </div>
                    </li>
                <?php endif ?>
                <!-- END MENU BANK DARAH -->
                <li class="nav-item">
                    <a href="#Eresep" data-toggle="tab" aria-expanded="false" class="nav-link Eresep">
                        E-Resep
                    </a>
                    <input type="hidden" class="EresepStts" value="1">
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                        Order Pemeriksaan Penunjang
                    </a>
                    <div class="dropdown-menu">
                        <a class="dropdown-item" data-toggle="tab" href="#labPk">Lab PK</a>
                        <a class="dropdown-item" id="lpa" data-toggle="tab" href="#menu-lpa">Lab PA</a>
                        <a href="#radiologi" data-toggle="tab" class="dropdown-item">Imaging</a>
                        <!-- <a class="dropdown-item" data-toggle="tab" href="#reevaluasi_lab">Re-Evaluasi Laboratorium</a> -->
                        <a href="#prosedur" data-toggle="tab" class="dropdown-item prosedur">Prosedur Diagnostik</a>
                        <a class="dropdown-item" id="lpk" data-toggle="tab" href="#menu-lpk">Laboratorium Patologi Klinik (Baru)</a>
                    </div>
                </li>
                <!-- START MENU KONSULTASI -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                        Konsultasi
                    </a>
                    <div class="dropdown-menu">
                        <a class="dropdown-item konsultasi" data-toggle="tab" href="#konsultasi">
                            Konsultasi
                        </a>
                        <a class="dropdown-item konsultasiEksternal" data-toggle="tab" href="#konsultasiEksternal">
                            Rujukan/Konsultasi Eksternal
                        </a>
                    </div>
                </li>
                <!-- END MENU KONSULTASI -->
                <!-- START MENU PROTOKOL KEMO -->
                <?php if (in_array($id_ruangan, [105020704, 105020705, 105020201, 105020101, 105020102, 105020706]) || $getNomr['GEDUNG'] == 1): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Pengobatan Sistemik Kanker
                        </a>
                        <div class="dropdown-menu" role="menu">
                            <a class="dropdown-item" data-toggle="tab" href="#protokolKemoterapi">
                                Protokol Kemoterapi Dewasa
                            </a>
                            <a class="dropdown-item" id="prokem-anak" data-toggle="tab" href="#menu-pro-kem-anak">
                                Protokol Kemoterapi Anak
                            </a>
                        </div>
                    </li>
                <?php endif ?>
                <!-- END MENU PROTOKOL KEMO -->
                <?php if ($id_ruangan == 105020401): ?>
                    <li class="nav-item">
                        <a href="#PengkajianNyeri" data-toggle="tab" aria-expanded="false" class="nav-link">
                            Pengkajian Nyeri
                        </a>
                    </li>
                <?php endif ?>
                <!-- START MENU TRANSFER RUANGAN -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                        Transfer Ruang
                    </a>
                    <div class="dropdown-menu">
                        <a class="dropdown-item serah_terima" data-toggle="tab" href="#serahterima">
                            Serah Terima Pemeriksaan
                        </a>
                        <a href="#permintaanDirawat" class="dropdown-item permintaanDirawat" data-toggle="tab">
                            Permintaan Dirawat
                        </a>
                        <a class="dropdown-item" id="fpr" data-toggle="tab" href="#menu-fpr">
                            Formulir Perpindahan Pasien Antar Ruang
                        </a>
                        <?php if ($id_ruangan == 105140101): ?>
                            <a href="#formulirKriteriaRawatIntensif" class="dropdown-item formulirKriteriaRawatIntensif" data-toggle="tab">
                                Formulir Kriteria Pasien Masuk Rawat Intensif
                            </a>
                            <a href="#formulirKriteriaRawatPicu" class="dropdown-item formulirKriteriaRawatPicu" data-toggle="tab">
                                Formulir Kriteria Pasien Masuk Rawat Picu
                            </a>
                        <?php endif ?>
                    </div>
                </li>
                <!-- END MENU TRANSFER RUANGAN -->
                <?php if (in_array($id_ruangan, [105130101, 105130103, 105130104, 105020601, 105021001])): ?>
                    <!-- START MENU MCU & Deteksi Dini -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            MCU & Deteksi Dini
                        </a>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" id="dd-payudara" data-toggle="tab" href="#menu-dd-payudara">
                                Deteksi Dini Kanker Payudara
                            </a>
                            <a class="dropdown-item" id="ukdd" data-toggle="tab" href="#menu-ukdd">
                                Uji Kesehatan dan Deteksi Dini Kanker
                            </a>
                            <a class="dropdown-item" id="mcudd" data-toggle="tab" href="#menu-mcudd">
                                <em>Medical Check Up</em> Deteksi Dini
                            </a>
                            <a class="dropdown-item" id="rdd" data-toggle="tab" href="#menu-rdd">
                                Resume Hasil Pemeriksaan Deteksi Dini Kanker
                            </a>
                            <?php if (in_array($id_ruangan, [105130101, 105130104, 105020601, 105021001])): ?>
                                <a class="dropdown-item" id="rpku" data-toggle="tab" href="#menu-rpku">
                                    Resume Hasil Pemeriksaan Kesehatan Umum
                                </a>
                            <?php endif ?>
                        </div>
                    </li>
                    <!-- END MENU MCU & Deteksi Dini -->
                <?php endif ?>
                <!-- START MENU JPOK -->
                <?php if (in_array($id_ruangan, [105020704, 105020705, 105020201, 105020706, 105140101]) || $getNomr['GEDUNG'] == 1): ?>
                    <li class="nav-item">
                        <a href="#cpo" data-toggle="tab" aria-expanded="false" class="nav-link">
                            JPOK
                        </a>
                    </li>
                <?php endif ?>
                <!-- END MENU JPOK -->
                <!-- START MENU IGD -->
                <?php if ($id_ruangan == 105140101): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            IGD
                        </a>
                        <div class="dropdown-menu">
                            <a href="#formulirTriase" class="dropdown-item" data-toggle="tab">
                                Formulir Triase
                            </a>
                            <a href="#formulirPMFAK" class="dropdown-item" data-toggle="tab">
                                Screening Akhir Kehidupan
                            </a>
                            <a href="#observasiIGD" class="dropdown-item observasiIGD" data-toggle="tab">
                                Observasi dan Tindakan Keperawatan IGD
                            </a>
                            <a href="#pengkajianPAK" class="dropdown-item" data-toggle="tab">
                                Pengkajian PAK
                            </a>
                            <a href="#formulirPemantauanNyeri" class="dropdown-item" data-toggle="tab">
                                Formulir Pemantauan Nyeri
                            </a>
                            <a href="#serah-terima-shift" class="dropdown-item serah-terima-shift" data-toggle="tab">
                                Serah Terima Antar Shift
                            </a>
                            <a href="#formulirObservasiPews" class="dropdown-item" data-toggle="tab">
                                Formulir Observasi PEWS
                            </a>
                            <a href="#pemberianIntravena" class="dropdown-item pemberianIntravena" data-toggle="tab">
                                Pemberian Cairan Intravena
                            </a>
                            <a href="#merujukPasien" class="dropdown-item" data-toggle="tab">
                                Formulir Merujuk Pasien
                            </a>
                            <a href="#kardekObatOral" class="dropdown-item" data-toggle="tab">
                                E-Kardek Obat Oral dan Injeksi
                            </a>
                            <!-- <a href="#prjpPasienGeriatri" class="dropdown-item" data-toggle="tab">
                            PRJ Pasien Geriatri
                        </a> -->
                            <a href="#rekonsiliasiObat" class="dropdown-item" data-toggle="tab">
                                Rekonsiliasi Obat
                            </a>
                            <a href="#pengkajianRisikoJatuhPasienDewasa" class="dropdown-item pengkajianRisikoJatuhPasienDewasa" data-toggle="tab">
                                Pengkajian Risiko Jatuh Pasien Dewasa
                            </a>
                            <a href="#skalaMorse" class="dropdown-item skala_Morse" data-toggle="tab">
                                Penilaian Risiko Geriatri (Skala Morse)
                            </a>
                            <a href="#humptyDumptyScale" class="dropdown-item humptyDumptyScale" data-toggle="tab">
                                Formulir Penilaian & Tindakan ( Humpty Dumpty Scale )
                            </a>
                        </div>
                    </li>
                <?php endif ?>
                <!-- END MENU IGD -->
                <!-- START MENU OPERASI -->
                <?php if (in_array($id_ruangan, [105020704, 105020705, 105060101, 105090101, 105020706, 105020201, 105020708, 105110101, 105020202, 105021101, 105021102, 105021103, 105021104, 105021201]) || $getNomr['GEDUNG'] == 1): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Operasi
                        </a>
                        <div class="dropdown-menu">
                            <a href="#pengkajianPraOperasi" class="dropdown-item" data-toggle="tab">
                                Pengkajian Pra Operasi
                            </a>
                            <a href="#pendaftaranPasienOperasi" class="dropdown-item" data-toggle="tab">
                                Pendaftaran Pasien Operasi
                            </a>
                            <a class="dropdown-item" id="lap-operasi" data-toggle="tab" href="#menu-lap-operasi">
                                Laporan Operasi
                            </a>
                            <a href="#siteMarking" class="dropdown-item" data-toggle="tab">
                                <em>Site Marking</em>
                            </a>
                            <a href="#ceklisPersiapanOperasi" class="dropdown-item ceklisPersiapanOperasi" data-toggle="tab">
                                Ceklis Persiapan Operasi
                            </a>
                        </div>
                    </li>
                <?php endif ?>
                <!-- END MENU OPERASI -->
                <!-- START MENU SEDASI -->
                <?php if ($id_ruangan == 105060101 || $id_ruangan == 105100101): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Sedasi
                        </a>
                        <div class="dropdown-menu">
                            <a href="#sedasi" class="dropdown-item sedasi" data-toggle="tab">
                                Sedasi
                            </a>
                            <a href="#medikasiObat" class="dropdown-item medikasiObat" data-toggle="tab">
                                Medikasi Obat
                            </a>
                            <a href="#medikasiNNTD" class="dropdown-item medikasiNNTD" data-toggle="tab">
                                Medikasi NNTD
                            </a>
                            <a href="#sedasiPemulihan" class="dropdown-item sedasiPemulihan" data-toggle="tab">
                                Kamar Pemulihan
                            </a>
                            <a href="#pemulihanNapas" class="dropdown-item pemulihanNapas" data-toggle="tab">
                                Kamar Pemulihan NNTD
                            </a>
                        </div>
                    </li>
                <?php endif ?>
                <!-- END MENU SEDASI -->
                <!-- START MENU GERIATRI -->
                <?php if ($id_ruangan != 105020706): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Geriatri
                        </a>
                        <div class="dropdown-menu">
                            <a href="#instrumenPPPG" class="dropdown-item instrumenPPPG" data-toggle="tab">
                                Instrumen Pengkajian Paripurna Pasien Geriatri (P3G)
                            </a>
                            <a href="#penilaianADL" class="dropdown-item penilaianADL" data-toggle="tab">
                                Penilaian <i>Activity of Daily Living</i>
                            </a>
                            <a href="#iadl" class="dropdown-item iadl" data-toggle="tab">
                                <i>Instrumental Activities of Daily Living (IADL) Lawton</i>
                            </a>
                            <a href="#instrumen-gds" class="dropdown-item instrumen-gds" data-toggle="tab">
                                Instrumen <i>Geriatric Depression Scale</i>
                            </a>
                            <a href="#pemeriksaanMiniCOG" class="dropdown-item pemeriksaanMiniCOG" data-toggle="tab">
                                Pemeriksaan Mini COG Dan Clock Drawing Test
                            </a>
                            <a href="#instrumenMmse" class="dropdown-item instrumenMmse" data-toggle="tab">
                                Instrumen Evaluasi Status Mental Mini (MMSE)
                            </a>
                            <a href="#abbreviatedMentalTest" class="dropdown-item abbreviatedMentalTest" data-toggle="tab">
                                <i>Abbreviated Mental Test</i>
                            </a>
                            <a href="#instrumenMna" class="dropdown-item instrumenMna" data-toggle="tab">
                                Instrumen Mini Nutrional Assesment (MNA)
                            </a>
                            <a href="#formulirG8Geriatri" class="dropdown-item formulirG8Geriatri" data-toggle="tab">
                                Formulir G8 Geriatri
                            </a>
                            <a href="#resikoJatuhLanjutUsia" class="dropdown-item resikoJatuhLanjutUsia" data-toggle="tab">
                                Formulir Resiko Jatuh Pasien Lanjut Usia
                            </a>
                        </div>
                    </li>
                <?php endif ?>
                <!-- END MENU GERIATRI -->
                <!-- START MENU INFORMED CONSENT -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                        <em>Informed Consent</em>
                    </a>
                    <div class="dropdown-menu pre-scrollable" role="menu">
                        <a class="dropdown-item persetujuan-tindakan-kedokteran" data-toggle="tab" href="#persetujuan-tindakan-kedokteran">
                            Persetujuan Tindakan Kedokteran
                        </a>
                        <a class="dropdown-item persetujuanTindakanPengobatanKemoterapi" data-toggle="tab" href="#persetujuanTindakanPengobatanKemoterapi">
                            Persetujuan Tindakan Pengobatan Kemoterapi
                        </a>
                        <a class="dropdown-item PTKemoRhabdomiosarkoma" data-toggle="tab" href="#PTKemoRhabdomiosarkoma">
                            Persetujuan Tindakan Kemoterapi Rhabdomiosarkoma
                        </a>
                        <a class="dropdown-item persetujuanTindakanKemoterapiLLAAnak" data-toggle="tab" href="#persetujuanTindakanKemoterapiLLAAnak">
                            Persetujuan Tindakan Kemoterapi LLA Anak
                        </a>
                        <a class="dropdown-item penolakanTindakanKedokteran" data-toggle="tab" href="#penolakanTindakanKedokteran">
                            Penolakan Tindakan Kedokteran
                        </a>
                        <a class="dropdown-item pttd" data-toggle="tab" href="#menu-pttd">
                            Persetujuan Tindakan Transfusi Darah
                        </a>
                        <a class="dropdown-item spinalEpi" data-toggle="tab" href="#spinalEpi">
                            Persetujuan Tindakan Kedokteran (Spinal/Epidural)
                        </a>
                        <a class="dropdown-item dnr" data-toggle="tab" href="#dnr">
                            Persetujuan Tindakan DNR
                        </a>
                        <a class="dropdown-item PIPTKS" data-toggle="tab" href="#PIPTKS">
                            Pemberian Informasi dan Persetujuan Tindakan Kedokteran (Sedasi)
                        </a>
                        <a class="dropdown-item PIPTKAU" data-toggle="tab" href="#PIPTKAU">
                            Pemberian Informasi dan Persetujuan Tindakan Kedokteran (Anestesi Umum)
                        </a>
                    </div>
                </li>
                <!-- START MENU BANK DARAH-->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                        Bank Darah
                    </a>
                    <div class="dropdown-menu" role="menu">
                        <a class="dropdown-item pemberianDanPemantauanDarah" data-toggle="tab" href="#pemberianDanPemantauanDarah">
                            Pemberian dan Pemantauan Tranfusi Darah
                        </a>
                    </div>
                </li>
                <!-- END MENU BANK DARAH -->
                <!-- END MENU INFORMED CONSENT -->
                <!-- START MENU BERKAS PASIEN LUAR -->
                <!-- <li class="nav-item">
                    <a href="#menu-berkas-pasien-luar" data-toggle="tab" aria-expanded="false" class="nav-link" id="berkas-pasien-luar">
                        Berkas Pasien Luar RSKD
                    </a>
                </li> -->
                <!-- END MENU BERKAS PASIEN LUAR -->
                <!-- START MENU BERKAS SCAN -->
                <!-- <li class="nav-item">
                    <a href="#menu-berkas-scan" data-toggle="tab" aria-expanded="false" class="nav-link" id="berkas-scan">
                        Berkas <em>Scan</em>
                    </a>
                </li> -->
                <!-- END MENU BERKAS SCAN -->
                <!-- START MENU RESUME MEDIS -->
                <li class="nav-item">
                    <a href="#resume-medis" data-toggle="tab" aria-expanded="false" class="nav-link resume-medis">
                        Profil Ringkas Medis
                    </a>
                </li>
                <!-- END MENU RESUME MEDIS -->
                <!-- START MENU COVID-19 -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                        Covid-19
                    </a>
                    <div class="dropdown-menu" role="menu">
                        <a class="dropdown-item formulirEpidemologiCovidRj" data-toggle="tab" href="#formulirEpidemologiCovidRj">
                            Formulir Penyelidikan Epidemiologi Covid-19
                        </a>
                    </div>
                </li>
                <!-- END MENU COVID-19 -->
                <!-- START MENU MANAJEMEN PELAYANAN PASIEN -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                        Manajemen Pelayanan Pasien (MPP)
                    </a>
                    <div class="dropdown-menu" role="menu">
                        <a class="dropdown-item" id="smpp" data-toggle="tab" href="#menu-smpp">
                            Skrining Pasien Membutuhkan Manajer Pelayanan Pasien (MPP)
                        </a>
                        <a class="dropdown-item" id="ampp" data-toggle="tab" href="#menu-ampp">
                            Asesmen Awal Manajer Pelayanan Pasien <em>(Case Manager)</em>
                        </a>
                        <a class="dropdown-item" id="impp" data-toggle="tab" href="#menu-impp">
                            Implementasi
                        </a>
                    </div>
                </li>
                <!-- END MENU MANAJEMEN PELAYANAN PASIEN -->
                <!-- START MENU HIV -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                        HIV
                    </a>
                    <div class="dropdown-menu" role="menu">
                        <a class="dropdown-item" id="fuart" data-toggle="tab" href="#menu-fuart">
                            Ikhtisar <em>Follow Up</em> Perawatan Pasien HIV dan Terapi Antiretroviral
                        </a>
                        <a class="dropdown-item" id="rart" data-toggle="tab" href="#menu-rart">
                            Formulir Rujukan
                        </a>
                        <?php if (isset($dataHIV['STATUS_HIV']) && $dataHIV['STATUS_HIV'] == "") { ?>
                            <a href="#tandaHIVRJ" class="dropdown-item tandaHIVRJ" id="tandaHIVRJ-info" data-toggle="modal" data-nomr="<?= $getNomr['NORM'] ?? null ?>" data-user="<?= $this->session->userdata('id') ?>">
                                Penanda HIV
                            </a>
                        <?php } ?>
                    </div>
                </li>
                <!-- END MENU HIV -->
                <!-- START MENU PALIATIF -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                        Paliatif
                    </a>
                    <div class="dropdown-menu" role="menu">
                        <a class="dropdown-item ffm" id="ffm" data-toggle="tab" href="#menu-ffm">
                            Formulir <em>Family Meeting</em>
                        </a>
                        <a class="dropdown-item hads" id="hads" data-toggle="tab" href="#menu-hads">
                            Formulir <em>Hospital Anxiety and Depression Scale</em>
                        </a>
                        <a class="dropdown-item iikpp" id="iikpp" data-toggle="tab" href="#menu-iikpp">
                            Instrumen Identifikasi Kebutuhan Perawatan Paliatif
                        </a>
                        <a class="dropdown-item kpppp" id="kpppp" data-toggle="tab" href="#menu-kpppp">
                            Formulir Kriteria Persiapan Pulang Pasien
                        </a>
                    </div>
                </li>
                <!-- END MENU PALIATIF -->
                <!-- START MENU ETIMJA -->
                <li class="nav-item">
                    <a href="#menu-etimja" data-toggle="tab" aria-expanded="false" class="nav-link" id="etimja">
                        eTimja
                    </a>
                </li>
                <!-- END MENU ETIMJA -->
                <!-- START MENU KARYAWAN -->
                <?php if ($id_ruangan == '105020601'): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                            Karyawan
                        </a>
                        <div class="dropdown-menu" role="menu">
                            <a class="dropdown-item" id="ss" data-toggle="tab" href="#menu-ss">
                                Surat Sakit
                            </a>
                        </div>
                    </li>
                <?php endif ?>
                <!-- END MENU KARYAWAN -->
                <!-- START MENU HIV ART -->
                <!-- <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                        HIV dan ART
                    </a>
                    <div class="dropdown-menu">
                        <a class="dropdown-item perawatanPasienHIV" data-toggle="tab" href="#perawatanPasienHIV">
                           Ikhtisar Perawatan Pasien HIV dan Terapi ART
                        </a>
                        <a class="dropdown-item followupPasienHIV" data-toggle="tab" href="#followupPasienHIV">
                            Ikhtisar <em>Follow-Up</em> Perawatan Pasien HIV dan Terapi ART
                        </a>
                        <a class="dropdown-item formulirRujukan" data-toggle="tab" href="#formulirRujukan">
                            Formulir Rujukan
                        </a>
                    </div>
                </li> -->
                <!-- END MENU HIV ART -->
            </ul>
            <div class="tab-content" style="border-color:#40739e">
                <!-- START Summary List -->
                <div role="tabpanel" class="tab-pane fade" id="summaryListPe">
                    <div class="text-white">
                        <div id="view_summaryListPe"></div>
                    </div>
                </div>
                <!-- END Summary List -->
                <!-- START Summary List -->
                <div role="tabpanel" class="tab-pane fade" id="buktiPelayananRJ">
                    <div class="text-white">
                        <div id="view_buktiPelayananRJ"></div>
                    </div>
                </div>
                <!-- END Summary List -->
                <!-- START Summary List -->
                <div role="tabpanel" class="tab-pane fade" id="konsultasiEksternal">
                    <div class="text-white">
                        <div id="view_konsultasiEksternal"></div>
                    </div>
                </div>
                <!-- END Summary List -->
                <!-- START INC VIEW MENU CPPT RAWAT JALAN -->
                <div role="tabpanel" class="tab-pane fade" id="ews">
                    <div class="text-white">.
                        <div id="view_ewsRawatJalan"></div>
                        <!--?php $this->load->view('Pengkajian/emr/ews/ews') ?-->
                    </div>
                </div>
                <!-- END INC VIEW MENU CPPT RAWAT JALAN -->
                <!-- START INC VIEW MENU CPPT RAWAT JALAN -->
                <div role="tabpanel" class="tab-pane fade <?= $this->uri->segment(6) == "cppt" ? "show active" : "" ?>" id="cppt">
                    <div class="text-white">
                        <div id="view_cppt">
                            <?php
                            $this->load->view('Pengkajian/emr/form-cppt-keperawatan-rj');
                            ?>
                        </div>
                    </div>
                </div>
                <!-- END INC VIEW MENU CPPT RAWAT JALAN -->
                <!-- START INC VIEW MENU DETEKSI DINI KANKER PAYUDARA -->
                <div role="tabpanel" class="tab-pane fade" id="menu-dd-payudara">
                    <div class="text-white">
                        <div id="view-dd-payudara"></div>
                    </div>
                </div>
                <!-- END INC VIEW MENU DETEKSI DINI KANKER PAYUDARA -->
                <!-- START INC VIEW MENU UJI KESEHATAN DAN DETEKSI DINI KANKER -->
                <div role="tabpanel" class="tab-pane fade" id="menu-ukdd">
                    <div class="text-white">
                        <div id="view-ukdd"></div>
                    </div>
                </div>
                <!-- END INC VIEW MENU UJI KESEHATAN DAN DETEKSI DINI KANKER -->
                <!-- START INC VIEW MENU MEDICAL CHECK UP DETEKSI DINI -->
                <div role="tabpanel" class="tab-pane fade" id="menu-mcudd">
                    <div class="text-white">
                        <div id="view-mcudd"></div>
                    </div>
                </div>
                <!-- END INC VIEW MENU MEDICAL CHECK UP DETEKSI DINI -->
                <!-- START INC VIEW MENU RESUME HASIL PEMERIKSAAN DETEKSI DINI KANKER -->
                <div role="tabpanel" class="tab-pane fade" id="menu-rdd">
                    <div class="text-white">
                        <div id="view-rdd"></div>
                    </div>
                </div>
                <!-- END INC VIEW MENU RESUME HASIL PEMERIKSAAN DETEKSI DINI KANKER -->
                <!-- START INC VIEW MENU RESUME HASIL PEMERIKSAAN DETEKSI DINI KANKER -->
                <div role="tabpanel" class="tab-pane fade" id="menu-rpku">
                    <div class="text-white">
                        <div id="view-rpku"></div>
                    </div>
                </div>
                <!-- END INC VIEW MENU RESUME HASIL PEMERIKSAAN DETEKSI DINI KANKER -->
                <!-- END INC VIEW MENU BARTHEL INDEK -->
                <div role="tabpanel" class="tab-pane fade" id="barthelIndek">
                    <div class="text-white">
                        <div id="view_barthelIndek"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="pengkajianpraanestesi">
                    <div class="text-white">
                        <div id="view_pengkajianpraanestesi"></div>
                    </div>
                </div>
                <!-- START INC VIEW MENU PENGKAJIAN KEPERAWATAN DEWASA -->
                <div role="tabpanel" class="tab-pane fade <?= $this->uri->segment(6) == "pengkajianKeperawatanDewasa" && ($getNomr['USIA'] == 2 or ($getNomr['USIA'] == 1 && in_array($id_ruangan, array('105140101', '105120101', '105020101', '105020901', '105100101')))) && $_SESSION['status'] == 2 ? "show active" : "" ?>" id="pengkajianKeperawatanDewasa">
                    <div class="text-white">
                        <?php if (in_array($this->uri->segment(6), array("pengkajianKeperawatanDewasa", "ews", "cppt")) && ($getNomr['USIA'] == 2 or ($getNomr['USIA'] == 1 && in_array($id_ruangan, array('105140101', '105120101', '105020101', '105020901', '105100101')))) && $_SESSION['status'] == 2): ?>
                            <div id="view_PengkajianAwalKeperawatan"></div>
                        <?php endif ?>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade <?= $this->uri->segment(6) == "pengkajianMedisDewasa" && ($getNomr['USIA'] == 2 or ($getNomr['USIA'] == 1 && in_array($id_ruangan, array('105140101', '105120101', '105020101', '105020901')))) && $_SESSION['status'] == 1 ? "show active" : "" ?>" id="pengkajianMedisDewasa">
                    <div class="text-white">
                        <?php in_array($this->uri->segment(6), array("pengkajianMedisDewasa", "ews", "cppt")) && ($getNomr['USIA'] == 2 or ($getNomr['USIA'] == 1 && in_array($id_ruangan, array('105140101', '105120101', '105020101', '105020901')))) && $_SESSION['status'] == 1 ? $this->load->view('Pengkajian/emr/form-medis-d') : "" ?>
                    </div>
                </div>
                <!-- END INC VIEW MENU PENGKAJIAN MEDIS -->
                <!-- START INC VIEW MENU PENGKAJIAN KEPERAWATAN DETEKSI DINI -->
                <div role="tabpanel" class="tab-pane fade <?= $this->uri->segment(6) == "pengkajianKeperawatanDeteksiDini" && $id_ruangan == 105130101 && $_SESSION['status'] == 2 || $this->uri->segment(6) == "pengkajianKeperawatanDeteksiDini" && $id_ruangan == 105130103 && $_SESSION['status'] == 2 ? "show active" : "" ?>" id="pengkajianKeperawatanDeteksiDini">
                    <div class="text-white">
                        <?php in_array($this->uri->segment(6), array("pengkajianKeperawatanDeteksiDini", "ews", "cppt")) && $id_ruangan == 105130101 && $_SESSION['status'] == 2 || in_array($this->uri->segment(6), array("pengkajianKeperawatanDeteksiDini", "ews", "cppt")) && $id_ruangan == 105130103 && $_SESSION['status'] == 2 ? $this->load->view('Pengkajian/emr/form-keperawatan-deteksi-dini') : "" ?>
                    </div>
                </div>
                <!-- END INC VIEW MENU PENGKAJIAN KEPERAWATAN DETEKSI DINI -->
                <!-- START INC VIEW MENU PENGKAJIAN KEPERAWATAN PALIATIF -->
                <div role="tabpanel" class="tab-pane fade <?= $this->uri->segment(6) == "paliatifKeperawatan" && $id_ruangan == 105020401 && $_SESSION['status'] == 2 ? "show active" : "" ?>" id="paliatifKeperawatan">
                    <div class="text-white">
                        <?php in_array($this->uri->segment(6), array("paliatifKeperawatan", "ews", "cppt")) && $id_ruangan == 105020401 && $_SESSION['status'] == 2 ? $this->load->view('Pengkajian/emr/form-paliatif-perawat') : "" ?>
                    </div>
                </div>
                <!-- END INC VIEW MENU PENGKAJIAN KEPERAWATAN PALIATIF -->
                <!-- START INC VIEW MENU PENGKAJIAN KEPERAWATAN ANAK -->
                <div role="tabpanel" class="tab-pane fade <?= $this->uri->segment(6) == "pengkajianKeperawatanAnak" && $getNomr['USIA'] == 1 && $_SESSION['status'] == 2 ? "show active" : "" ?>" id="pengkajianKeperawatanAnak">
                    <div class="text-white">
                        <?php in_array($this->uri->segment(6), array("pengkajianKeperawatanAnak", "ews", "cppt")) && $getNomr['USIA'] == 1 && $_SESSION['status'] == 2 ? $this->load->view('Pengkajian/emr/form-keperawatan-anak') : "" ?>
                    </div>
                </div>
                <!-- END INC VIEW MENU PENGKAJIAN KEPERAWATAN ANAK -->
                <!-- START INC VIEW MENU PENGKAJIAN MEDIS ANAK -->
                <div role="tabpanel" class="tab-pane fade <?= $this->uri->segment(6) == "pengkajianMedisAnak" && $getNomr['USIA'] == 1 && $_SESSION['status'] == 1 ? "show active" : "" ?>" id="pengkajianMedisAnak">
                    <div class="text-white">
                        <?php in_array($this->uri->segment(6), array("pengkajianMedisAnak", "ews", "cppt")) && $getNomr['USIA'] == 1 && $_SESSION['status'] == 1 ? $this->load->view('Pengkajian/emr/form-medis-anak') : "" ?>
                    </div>
                </div>
                <!-- END INC VIEW MENU PENGKAJIAN MEDIS ANAK -->
                <!-- START INC VIEW MENU PENGKAJIAN MEDIS DETEKSI DINI -->
                <div role="tabpanel" class="tab-pane fade <?= $this->uri->segment(6) == "pengkajianMedisDeteksiDini" && $id_ruangan == 105130101 && $_SESSION['status'] == 2 || $this->uri->segment(6) == "pengkajianMedisDeteksiDini" && $id_ruangan == 105130103 && $_SESSION['status'] == 2 ? "show active" : "" ?>" id="pengkajianMedisDeteksiDini">
                    <div class="text-white">
                        <?php in_array($this->uri->segment(6), array("pengkajianMedisDeteksiDini", "ews", "cppt")) && $id_ruangan == 105130101 && $_SESSION['status'] == 1 || in_array($this->uri->segment(6), array("pengkajianMedisDeteksiDini", "ews", "cppt")) && $id_ruangan == 105130103 && $_SESSION['status'] == 1 ? $this->load->view('Pengkajian/emr/form-medis-deteksi-dini') : "" ?>
                    </div>
                </div>
                <!-- END INC VIEW MENU PENGKAJIAN MEDIS DETEKSI DINI -->
                <!-- START INC VIEW MENU PENGKAJIAN MEDIS PALIATIF -->
                <div role="tabpanel" class="tab-pane fade <?= $this->uri->segment(6) == "paliatifMedis" && $id_ruangan == 105020401 && $_SESSION['status'] == 1 ? "show active" : "" ?>" id="paliatifMedis">
                    <div class="text-white">
                        <?php in_array($this->uri->segment(6), array("paliatifMedis", "ews", "cppt")) && $id_ruangan == 105020401 && $_SESSION['status'] == 1 ? $this->load->view('Pengkajian/emr/form-paliatif-medis') : "" ?>
                    </div>
                </div>
                <!-- END INC VIEW MENU PENGKAJIAN MEDIS PALIATIF -->
                <!-- START INC VIEW MENU FORMULIR SKRINING VISUAL -->
                <div role="tabpanel" class="tab-pane fade" id="formulirSkriningVisual">
                    <div class="text-white">
                        <?php $this->load->view('Pengkajian/igd/formulirSkriningVisual/index') ?>
                    </div>
                </div>
                <!-- END INC VIEW MENU FORMULIR SKRINING VISUAL -->
                <!-- START INC VIEW MENU ORDER DAN HASIL PENUNJANG -->
                <div role="tabpanel" class="tab-pane fade" id="labPk">
                    <div class="text-white">
                        <!-- Mulai pencarian -->
                        <div class="form-group" id="form-cari-pk" style="z-index: 2;">
                            <div class="input-group">
                                <input type="text" class="form-control" id="cari-pk" name="cari" placeholder="[ Cari ]" aria-label="Cari tindakan" autocomplete="off">
                                <div class="input-group-append">
                                    <button class="btn btn-primary waves-effect" type="button" id="aksi-cari-pk"><i class="fa fa-search"></i></button>
                                </div>
                            </div>
                        </div>
                        <!-- Akhir pencarian -->
                        <?php $this->load->view('Pengkajian/patologiKlinik/index') ?>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="menu-lpa">
                    <div class="text-white">
                        <div id="view-lpa"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="reevaluasi_lab">
                    <div class="text-white">
                        <!--?php $this->load->view('Pengkajian/reevaluasi_lab/index') ?-->
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="radiologi">
                    <div class="text-white">
                        <div class="cariRadiologiRj" id="cari-radiologi" style="z-index: 2">
                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <div class="input-group mb-2 mr-sm-2">
                                        <div class="input-group-prepend">
                                            <div class="input-group-text"><i class="fas fa-search"></i></div>
                                        </div>
                                        <input type="text" class="form-control form-control-lg cariInputanRadRj bg-info" placeholder="[ Cari ]">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php $this->load->view('Pengkajian/radiologi/index') ?>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="prosedur">
                    <div class="text-white">
                        <div id="view_prosedur"></div>
                    </div>
                </div>
                <!-- START INC VIEW MENU LABORATORIUM PATOLOGI KLINIK (BARU) -->
                <div role="tabpanel" class="tab-pane fade" id="menu-lpk">
                    <div class="text-white">
                        <div id="view-lpk"></div>
                    </div>
                </div>
                <!-- END INC VIEW MENU LABORATORIUM PATOLOGI KLINIK (BARU) -->
                <!-- END INC VIEW MENU ORDER DAN HASIL PENUNJANG -->
                <!-- START INC VIEW MENU SEDASI -->

                <div role="tabpanel" class="tab-pane fade" id="sedasi">
                    <div class="text-white">
                        <div id="view_sedasi"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="medikasiObat">
                    <div class="text-white">
                        <div id="view_medikasiObat"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="medikasiNNTD">
                    <div class="text-white">
                        <div id="view_medikasiNNTD"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="sedasiPemulihan">
                    <div class="text-white">
                        <div id="view_sedasiPemulihan"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="pemulihanNapas">
                    <div class="text-white">
                        <div id="view_pemulihanNapas"></div>
                    </div>
                </div>

                <!-- END INC VIEW MENU SEDASI -->
                <!-- START INC VIEW MENU GERIATRI -->
                <?php if ($id_ruangan != 105020706): ?>
                    <div role="tabpanel" class="tab-pane fade" id="abbreviatedMentalTest">
                        <div class="text-white">
                            <div id="view_abbreviatedMentalTest"></div>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="instrumenPPPG">
                        <div class="text-white">
                            <div id="view_instrumenPPPG"></div>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="penilaianADL">
                        <div class="text-white">
                            <div id="view_penilaianADL"></div>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="iadl">
                        <div class="text-white">
                            <div id="view_iadl"></div>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="instrumen-gds">
                        <div class="text-white">
                            <div id="view-instrumen-gds"></div>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="pemeriksaanMiniCOG">
                        <div class="text-white">
                            <div id="view_pemeriksaanMiniCOG"></div>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="instrumenMmse">
                        <div class="text-white">
                            <div id="view_instrumenMmse"></div>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="instrumenMna">
                        <div class="text-white">
                            <div id="view_instrumenMna"></div>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="formulirG8Geriatri">
                        <div class="text-white">
                            <div id="view_formulirG8Geriatri"></div>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="resikoJatuhLanjutUsia">
                        <div class="text-white">
                            <div id="view_resikoJatuhLanjutUsia"></div>
                        </div>
                    </div>
                <?php endif ?>
                <!-- END INC VIEW MENU GERIATRI -->
                <!-- START INC VIEW MENU RADIOTERAPI -->
                <!-- <?//php if ($id_ruangan == 105120101): ?> -->
                    <div role="tabpanel" class="tab-pane fade" id="radioterapi">
                        <div class="text-white">
                            <?php $this->load->view('Pengkajian/radioTerapi/index') ?>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="ct_Simulator">
                        <div class="text-white">
                            <?php $data = array('jenisMenu' => 'RJ') ?>
                            <?php //$this->load->view('Pengkajian/radioTerapi/ct_simulator.php', $data) ?>
                            <div id="view-ct_Simulator"></div>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="simulatorInformation">
                        <div class="text-white">
                            <div id="viewSimulatorInformation"></div>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="treatmentDose">
                        <div class="text-white">
                            <?php $data = array('jenisMenu' => 'RJ') ?>
                            <div id="view_doseRiRadio"></div>
                            <?php //$this->load->view('Pengkajian/radioTerapi/treatmentDose.php')
                                ?>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="penjadwalanradioterapi">
                        <div class="text-white">
                            <?php $data = array('jenisMenu' => 'RJ') ?>
                            <div id="view_penjadwalanradioterapi"></div>
                            <?php //$this->load->view('Pengkajian/radioTerapi/treatmentDose.php')
                                ?>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="catatanRadioTerapi">
                        <div class="text-white">
                            <div id="viewCatatanRadioTerapi"></div>
                        </div>
                    </div>
                <!-- <?//php endif ?> -->
                <!-- END INC VIEW MENU RADIOTERAPI -->
                <!-- START INC VIEW MENU PROSEDUR DIAGNOSTIK -->
                <div role="tabpanel" class="tab-pane fade" id="pengkajianTindakanInvasif">
                    <div class="text-white">
                        <div id="view_tindakan_invasif"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="persiapanBronkoskopi">
                    <div class="text-white">
                        <div id="view_persiapan_Bronkoskopi"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="persiapanGastroskopi">
                    <div class="text-white">
                        <?php $this->load->view('Pengkajian/persiapanGastroskopi/index') ?>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="pemeriksaanSpirometri">
                    <div class="text-white">
                        <?php $this->load->view('Pengkajian/pemeriksaanSpirometri/index') ?>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="laporanHasilPemeriksaan">
                    <div class="text-white">
                        <div id="view_laporanHasilPemeriksaan"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="scanHasilPemeriksaan">
                    <div class="text-white">
                        <div id="view_scanHasilPemeriksaan"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="serahterima">
                    <div class="text-white">
                        <div id="view_serahTerima"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="echoekg">
                    <div class="text-white">
                        <?php $this->load->view('Pengkajian/echoekg/index') ?>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="keselamatanTindakanInvasif">
                    <div class="text-white">
                        <div id="view_keselamatan_TindakanInvasif"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="pemantauanAnestesiLokal">
                    <div class="text-white">
                        <div id="view_pemantauan_AnestesiLokal"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="pemTinAnesLokRj">
                    <div class="text-white">
                        <div id="view_pemTinAnesLokRj"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="pengkajianPraSedasi">
                    <div class="text-white">
                        <div id="view_pengkajianPraSedasi"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="statusanestesia">
                    <div class="text-white">
                        <div id="view_status_anestsia"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="tandavital_sa">
                    <div class="text-white">
                        <?php $this->load->view('Pengkajian/anestesia/status_anestesia/tandavital_sa') ?>
                    </div>
                </div>
                <!-- END INC VIEW MENU PROSEDUR DIAGNOSTIK -->
                <!-- START INC VIEW MENU RADIOLOGI -->
                <div role="tabpanel" class="tab-pane fade" id="keselamatanTindakanInvasif">
                    <div class="text-white">
                        <div id="view_keselamatan_TindakanInvasif"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="pengkajianKedokteranNuklir">
                    <div class="text-white">
                        <?php $this->load->view('Pengkajian/radiologiNuklir/index') ?>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="pemantauanAnestesiLokal">
                    <div class="text-white">
                        <div id="view_pemantauan_AnestesiLokal"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="tindakanIntervensi">
                    <div class="text-white">
                        <?php $this->load->view('Pengkajian/tindakanIntervensi/index') ?>
                    </div>
                </div>
                <!-- END INC VIEW MENU RADIOLOGI -->
                <!-- START INC VIEW PROTOKOL KEMO -->
                <?php if (in_array($id_ruangan, [105020704, 105020705, 105020201, 105020101, 105020102, 105020706]) || $getNomr['GEDUNG'] == 1): ?>
                    <div role="tabpanel" class="tab-pane fade" id="protokolKemoterapi">
                        <div class="text-white">
                            <?php $this->load->view('Pengkajian/protokolKemoterapi/index') ?>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="menu-pro-kem-anak">
                        <div class="text-white">
                            <div id="view-pro-kem-anak"></div>
                        </div>
                    </div>
                <?php endif ?>
                <!-- END INC VIEW PROTOKOL KEMO -->
                <!-- START INC VIEW MENU ANYELIR -->
                <?php if ($id_ruangan == 105020101 || $id_ruangan == 105020102): ?>
                    <div role="tabpanel" class="tab-pane fade" id="observasiAnyelir">
                        <div class="text-white">
                            <?php $this->load->view('Pengkajian/observasiAnyelir/index') ?>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="keperawatanAnyelir">
                        <div class="text-white">
                            <?php $this->load->view('Pengkajian/keperawatanAnyelir/index') ?>
                        </div>
                    </div>
                <?php endif ?>
                <!-- END INC VIEW MENU ANYELIR -->
                <!-- START INC VIEW MENU GIGI -->
                <?php if ((in_array($id_ruangan, [105020705, 105020201, 105020706]) || $getNomr['GEDUNG'] == 1) && $this->session->userdata('status') == 1): ?>
                    <div role="tabpanel" class="tab-pane fade" id="odontogram">
                        <div class="text-white" id="view_odontogram"></div>
                    </div>
                <?php endif ?>
                <div role="tabpanel" class="tab-pane fade" id="pengkajianGigi">
                    <div class="text-white" id="view_pengkajianGigi"></div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="pemantauanAnastesiGigi">
                    <div class="text-white" id="view_pemantauanAnastesiGigi"></div>
                </div>
                <!-- END INC VIEW MENU GIGI -->
                <!-- START INC VIEW MENU LUKA -->
                <div role="tabpanel" class="tab-pane fade" id="pengkajianLuka">
                    <div class="text-white" id="view_pengkajianLuka"></div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="pengkajianStoma">
                    <div class="text-white">
                        <div id="view_pengkajian_stoma"></div>
                    </div>
                </div>
                <!-- END INC VIEW MENU LUKA -->
                <!-- START INC VIEW MENU BANK DARAH -->
                <?php if ($id_ruangan == *********): ?>
                    <div role="tabpanel" class="tab-pane fade" id="specOptia">
                        <div class="text-white">
                            <?php $this->load->view('Pengkajian/bankdarah/spectraOptia/index') ?>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="tindakanDonorAferesis">
                        <div class="text-white">
                            <?php $this->load->view('Pengkajian/bankdarah/tindakanDonorAferesis/index') ?>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="trombaferesisMesinAmicus">
                        <div class="text-white">
                            <?php $this->load->view('Pengkajian/bankdarah/trombaferesisMesinAmicus/index') ?>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="persetujuanTindakanTD">
                        <div class="text-white">
                            <?php $this->load->view('Pengkajian/bankdarah/persetujuanTindakanTD/index') ?>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="pemberianDanPemantauan">
                        <div class="text-white">
                            <div id="view_pemberianDanPemantauan"></div>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="preaferesis">
                        <div class="text-white">
                            <?php $this->load->view('Pengkajian/bankdarah/preaferesis/index') ?>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="pengkajianAferesis">
                        <div class="text-white" id="view_pengkajianAferesis"></div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="ceklisTindakanAferesis">
                        <div class="text-white">
                            <?php $this->load->view('Pengkajian/bankdarah/ceklisTindakanAferesis/index') ?>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="persetujuanTindakanTerapeutikAferesis">
                        <div class="text-white" id="view_persetujuanTindakanTerapeutikAferesis"></div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="TrombaferesisDenganMesinHaemonetics">
                        <div class="text-white" id="view_trombaferesisDenganMesinHaemonetics"></div>
                    </div>
                <?php endif ?>
                <!-- END INC VIEW MENU BANK DARAH -->
                <!-- START INC VIEW MENU ERESEP -->
                <div role="tabpanel" class="tab-pane fade" id="Eresep">
                    <div class="text-white">
                        <?php //$this->load->view('Pengkajian/eresep/form_eresep')
                        ?>
                        <div id="view-eresep"></div>
                    </div>
                </div>
                <!-- END INC VIEW MENU ERESEP -->
                <div role="tabpanel" class="tab-pane fade" id="konsultasi">
                    <div class="text-white">
                        <div id="view-konsultasi"></div>
                    </div>
                </div>
                <!-- START INC VIEW MENU OPERASI -->
                <div role="tabpanel" class="tab-pane fade" id="menu-lap-operasi">
                    <div class="text-white">
                        <div id="view-lap-operasi"></div>
                    </div>
                </div>
                <!-- END INC VIEW MENU OPERASI -->
                <?php if ($id_ruangan == 105020401): ?>
                    <div role="tabpanel" class="tab-pane fade" id="PengkajianNyeri">
                        <div class="text-white">
                            <?php $this->load->view('Pengkajian/pengkajiannyeri/index') ?>
                        </div>
                    </div>
                <?php endif ?>
                <!-- START INC VIEW TRANSFER RUANGAN -->
                <div role="tabpanel" class="tab-pane fade" id="permintaanDirawat">
                    <div class="text-white">
                        <div id="view_permintaanDirawat"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="menu-fpr">
                    <div class="text-white">
                        <div id="view-fpr"></div>
                    </div>
                </div>
                <?php if ($id_ruangan == 105140101): ?>
                    <!-- <div role="tabpanel" class="tab-pane fade" id="formulirKriteriaRawatIntensif">
                    <div class="text-white">
                        <!?php $this->load->view('Pengkajian/igd/formulirKriteriaRawatIntensif/index') ?>
                    </div>
                </div> -->
                    <div role="tabpanel" class="tab-pane fade" id="formulirKriteriaRawatIntensif">
                        <div class="text-white">
                            <div id="view_formulirKriteriaRawatIntensif"></div>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="formulirKriteriaRawatPicu">
                        <div class="text-white">
                            <div id="view_formulirKriteriaRawatPicu"></div>
                        </div>
                    </div>
                    <!-- <div role="tabpanel" class="tab-pane fade" id="formulirKriteriaRawatPicu">
                    <div class="text-white">
                        <!?php $this->load->view('Pengkajian/igd/formulirKriteriaRawatPicu/index') ?>
                    </div>
                </div> -->
                <?php endif ?>
                <!-- END INC VIEW TRANSFER RUANGAN -->
                <!-- START INC VIEW JPOK -->
                <?php if (in_array($id_ruangan, [105020704, 105020705, 105020201, 105020706, 105140101]) || $getNomr['GEDUNG'] == 1): ?>
                    <div role="tabpanel" class="tab-pane fade" id="cpo">
                        <div class="text-white">
                            <?php $this->load->view('Pengkajian/cpo/index') ?>
                        </div>
                    </div>
                <?php endif ?>
                <!-- END INC VIEW JPOK -->
                <!-- START INC VIEW MENU REHABMEDIK -->
                <?php if ($id_ruangan == 105110101): ?>
                    <div role="tabpanel" class="tab-pane fade" id="konsul-rehab-medik">
                        <div class="text-white">
                            <div id="view-konsul-rehab-medik"></div>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="limfedema">
                        <div class="text-white">
                            <div id="view-limfedema"></div>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="ekfm">
                        <div class="text-white">
                            <?php $this->load->view('Pengkajian/rehabilitasiMedik/evaluasikemampuanfungsionalmobilisasi/index') ?>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="menu-klaim-rehab">
                        <div class="text-whiter">
                            <div id="view-klaim-rehab"></div>
                        </div>
                    </div>
                <?php endif ?>
                <!-- END INC VIEW MENU REHABMEDIK -->
                <!-- START INC VIEW MENU HEMODIALISA -->
                <div role="tabpanel" class="tab-pane fade" id="hemodialisaUtama">
                    <div class="text-white">
                        <?php $this->load->view('Pengkajian/hemodialisa/obstinKeperawatanHemodialisis/index') ?>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="ptk-hemodialisis">
                    <div class="text-white">
                        <div id="view-ptk-hemodialisis"></div>
                    </div>
                </div>

                <div role="tabpanel" class="tab-pane fade" id="pemantauanNyeri">
                    <div class="text-white">
                        <div id="view_pemantauanNyeri"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="pengkajianRisikoJatuhPasienDewasa">
                    <div class="text-white">
                        <div id="view_pengkajianRisikoJatuhPasienDewasa"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="skalaOntario">
                    <div class="text-white">
                        <div id="view_skalaOntario"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="skalaOntarioMASS">
                    <div class="text-white">
                        <div id="view_skalaOntarioMASS"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="humptyDumptyRi">
                    <div class="text-white">
                        <div id="view_humptyDumptyRi"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="serah-terima-shift">
                    <div class="text-white">
                        <div id="view-serah-terima-shift"></div>
                    </div>
                </div>
                <!-- END INC VIEW MENU HEMODIALISA -->
                <!-- START VIEW MENU OPERASI -->
                <?php if (in_array($id_ruangan, [105020704, 105020705, 105060101, 105090101, 105020706, 105020201, 105020708, 105110101, 105020202, 105021101, 105021102, 105021103, 105021104, 105021201]) || $getNomr['GEDUNG'] == 1): ?>
                    <div role="tabpanel" class="tab-pane fade" id="pengkajianPraOperasi">
                        <div class="text-white">
                            <?php $this->load->view('Pengkajian/operasi/pengkajianPraOperasi') ?>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="pendaftaranPasienOperasi">
                        <div class="text-white">
                            <?php $this->load->view('Pengkajian/operasi/pendaftaranPasienOperasi') ?>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="laporanOperasi">
                        <div class="text-white">
                            <div id="view_laporan_operasi"></div>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="siteMarking">
                        <div class="text-white">
                            <?php $this->load->view('Pengkajian/operasi/siteMarking') ?>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="ceklisPersiapanOperasi">
                        <div class="text-white">
                            <div id="view_cpo"></div>
                        </div>
                    </div>
                <?php endif ?>
                <!-- END VIEW MENU OPERASI -->
                <!-- START VIEW MENU IGD -->
                <?php if ($id_ruangan == 105140101): ?>
                    <div role="tabpanel" class="tab-pane fade" id="formulirTriase">
                        <div class="text-white">
                            <?php $this->load->view('Pengkajian/igd/formulirTriase/index') ?>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="formulirPMFAK">
                        <div class="text-white">
                            <?php $this->load->view('Pengkajian/igd/formulirPMFAK/index') ?>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="observasiIGD">
                        <div class="text-white">
                            <div id="view-observasi-igd"></div>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="pengkajianPAK">
                        <div class="text-white">
                            <?php $this->load->view('Pengkajian/igd/pengkajianPAK/index') ?>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="formulirPemantauanNyeri">
                        <div class="text-white">
                            <?php $this->load->view('Pengkajian/igd/formulirPemantauanNyeri/index') ?>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="formulirObservasiPews">
                        <div class="text-white">
                            <?php $this->load->view('Pengkajian/igd/pews/pews') ?>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="pemberianIntravena">
                        <div class="text-white">
                            <div id="view_pemberianIntravena"></div>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="merujukPasien">
                        <div class="text-white">
                            <?php $this->load->view('Pengkajian/igd/formulirMerujukPasien/index') ?>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="kardekObatOral">
                        <div class="text-white">
                            <?php $this->load->view('Pengkajian/igd/kardek/index') ?>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane fade" id="humptyDumptyScale">
                        <div class="text-white">
                            <div id="view_humptyDumptyScale"></div>
                        </div>
                    </div>
                <?php endif ?>
                <!-- END VIEW MENU IGD -->
                <!-- START INFORMED CONSENT -->
                <div role="tabpanel" class="tab-pane fade" id="persetujuan-tindakan-kedokteran">
                    <div class="text-white">
                        <div id="view-persetujuan-tindakan-kedokteran"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="persetujuanTindakanPengobatanKemoterapi">
                    <div class="text-white">
                        <div id="view_persetujuanTindakanPengobatanKemoterapi"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="PTKemoRhabdomiosarkoma">
                    <div class="text-white">
                        <div id="view_PTKemoRhabdomiosarkoma"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="persetujuanTindakanKemoterapiLLAAnak">
                    <div class="text-white">
                        <div id="view_persetujuanTindakanKemoterapiLLAAnak"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="penolakanTindakanKedokteran">
                    <div class="text-white">
                        <div id="view_penolakanTindakanKedokteran"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="menu-pttd">
                    <div class="text-white">
                        <div id="view-pttd"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="spinalEpi">
                    <div class="text-white">
                        <div id="view_spinalEpi"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="dnr">
                    <div class="text-white">
                        <div id="view_dnr"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="PIPTKS">
                    <div class="text-white">
                        <div id="view_PIPTKS"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="PIPTKAU">
                    <div class="text-white">
                        <div id="view_PIPTKAU"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="EdukasiTAS">
                    <div class="text-white">
                        <div id="view_EdukasiTAS"></div>
                    </div>
                </div>
                <!-- END INFORMED CONSENT-->
                <!-- START PEMEBERIAN DARAH -->
                <div role="tabpanel" class="tab-pane fade" id="pemberianDanPemantauanDarah">
                    <div class="text-white">
                        <div id="view_pemberianDanPemantauanDarah"></div>
                    </div>
                </div>
                <!-- END PEMEBERIAN DARAH -->
                <!-- START HIV ART -->
                <div role="tabpanel" class="tab-pane fade" id="perawatanPasienHIV">
                    <div class="text-white">
                        <div id="view-perawatanPasienHIV"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="followupPasienHIV">
                    <div class="text-white">
                        <div id="view_followupPasienHIV"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="formulirRujukan">
                    <div class="text-white">
                        <div id="view_formulirRujukan"></div>
                    </div>
                </div>
                <!-- END HIV ART-->
                <div role="tabpanel" class="tab-pane fade" id="rekonsiliasiObat">
                    <div class="text-white">
                        <?php $this->load->view('Pengkajian/igd/rekonsiliasiObat/index') ?>
                    </div>
                </div>
                <!-- START KEMO AML AMAK -->
                <div role="tabpanel" class="tab-pane fade" id="kemoamlanak">
                    <div class="text-white">
                        <?php $this->load->view('Pengkajian/persetujuankemoAmlAnak/index') ?>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="skalaMorse">
                    <div class="text-white">
                        <div id="view_skalaMorse"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="menu-berkas-pasien-luar">
                    <div class="text-white">
                        <div id="view-berkas-pasien-luar"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="menu-berkas-scan">
                    <div class="text-white">
                        <div id="view-berkas-scan"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="catatan-edukasi">
                    <div class="text-white">
                        <div id="view-catatan-edukasi"></div>
                    </div>
                </div>
                <!-- END KEMO AML AMAK-->
                <div role="tabpanel" class="tab-pane fade <?= $this->uri->segment(6) == 'ews' ? 'show active' : null ?>" id="resume-medis">
                    <div class="text-white">
                        <div id="view-resume-medis"></div>
                    </div>
                </div>
                <!-- START EPID COVID -->
                <div role="tabpanel" class="tab-pane fade" id="formulirEpidemologiCovidRj">
                    <div class="text-white">
                        <div id="view_formulirEpidemologiCovidRj"></div>
                    </div>
                </div>
                <!-- START VIEW ETIMJA -->
                <div role="tabpanel" class="tab-pane fade" id="menu-etimja">
                    <div class="text-white">
                        <div id="view-etimja"></div>
                    </div>
                </div>
                <!-- END VIEW ETIMJA -->
                <!-- START INC VIEW MENU CPPT LIST -->
                <div role="tabpanel" class="tab-pane fade" id="cpptList">
                    <div class="text-white">
                        <div id="view_cpptList"></div>
                    </div>
                </div>
                <!-- END INC VIEW MENU CPPT LIST -->
                <!-- START INC VIEW PENGKAJIAN LIST -->
                <div role="tabpanel" class="tab-pane fade" id="menu-pengkajian-list">
                    <div class="text-white">
                        <div id="view-pengkajian-list"></div>
                    </div>
                </div>
                <!-- END INC VIEW PENGKAJIAN LIST -->
                <!-- START INC VIEW berkas LIST -->
                <div role="tabpanel" class="tab-pane fade" id="menu-berkas-list">
                    <div class="text-white">
                        <div id="view-berkas-list"></div>
                    </div>
                </div>
                <!-- END INC VIEW berkas LIST -->
                <div role="tabpanel" class="tab-pane fade" id="menu-faktor-resiko">
                    <div class="text-white">
                        <div id="view-faktor-resiko"></div>
                    </div>
                </div>
                <!-- START INC VIEW MENU MANAJER PELAYANAN PASIEN -->
                <div role="tabpanel" class="tab-pane fade" id="menu-smpp">
                    <div class="text-white">
                        <div id="view-smpp"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="menu-ampp">
                    <div class="text-white">
                        <div id="view-ampp"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="menu-impp">
                    <div class="text-white">
                        <div id="view-impp"></div>
                    </div>
                </div>
                <!-- END INC VIEW MENU MANAJER PELAYANAN PASIEN -->
                <!-- START INC VIEW MENU HIV -->
                <div role="tabpanel" class="tab-pane fade" id="menu-fuart">
                    <div class="text-white">
                        <div id="view-fuart"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="menu-rart">
                    <div class="text-white">
                        <div id="view-rart"></div>
                    </div>
                </div>
                <!-- END INC VIEW MENU HIV -->
                <!-- START INC VIEW MENU PALIATIF -->
                <div role="tabpanel" class="tab-pane fade" id="menu-ffm">
                    <div class="text-white">
                        <div id="view_ffm"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="menu-hads">
                    <div class="text-white">
                        <div id="view_hads"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="menu-iikpp">
                    <div class="text-white">
                        <div id="view_iikpp"></div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="menu-kpppp">
                    <div class="text-white">
                        <div id="view_kpppp"></div>
                    </div>
                </div>
                <!-- END INC VIEW MENU PALIATIF -->
                <!-- START INC VIEW MENU KARYAWAN -->
                <div role="tabpanel" class="tab-pane fade" id="menu-ss">
                    <div class="text-white">
                        <div id="view-ss"></div>
                    </div>
                </div>
                <!-- END INC VIEW MENU KARYAWAN -->
            </div>
        </div>
    </div>
    <!-- End col 9 -->
</div>
<!--<div class="modal fade" id="takePhoto" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel"
    aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title mt-0" id="mySmallModalLabel">Take Photo</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                    ×
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="form-group">
                            <div id="my_camera"></div>
                            <input type="hidden" name="nomr" id="nomrTakePhoto" value="<!?//= $getNomr['NORM']
                                                                                        ?>">
                            <input type="hidden" name="oleh" id="olehTakePhoto" value="<!?//= $id_pengguna
                                                                                        ?>">
                        </div>
                    </div> -->

<!-- Mulai modal history eTimja -->
<div class="modal fade" id="modal-history-etimja-info" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-dialog-centered" style="max-width: 90%">
        <div class="modal-content" id="lihat-history-modal-etimja-info"></div>
    </div>
</div>
<!-- Akhir modal history eTimja -->

<!-- START MODAL DISKON DOKTER -->
<div id="viewDiskonDokter" class="modal fade" role="dialog">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div id="hasilDiskonDokter"></div>
        </div>
    </div>
</div>
<!-- END MODAL DISKON DOKTER -->

<!-- START MODAL INPUT DIAGNOSA -->
<div id="viewInputDiagnosisDpjp" class="modal fade" role="dialog">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div id="hasilInputDiagnosaDpjp"></div>
        </div>
    </div>
</div>
<!-- END MODAL INPUT DIAGNOSA -->

<!-- Mulai modal iCare -->
<div class="modal fade" id="modal-icare" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" style="max-width: 90%">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">iCARE</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="lihat-icare" style="overflow-y: auto; max-height: 80vh;">
                <iframe id="icare-iframe" width="100%" height="600px" frameborder="0"></iframe>
            </div>
        </div>
    </div>
</div>
<!-- Akhir modal iCare -->

<!-- START MODAL VIEW REGKAN -->
<div id="viewModalREGKAN" class="modal fade" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div id="hasilModalREGKAN"></div>
        </div>
    </div>
</div>
<!-- END MODAL VIEW REGKAN -->

<!-- START MODAL TANDA HIV -->
<div id="tandaHIVRJ" class="modal fade" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Penanda Pasien HIV</h3>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <form id="formTandaHIVRJ">
                <input type="hidden" name="normHIV" value="<?= $getNomr['NORM'] ?>">
                <input type="hidden" name="userHIV" value="<?= $this->session->userdata('id') ?>">
                <div class="row form-group">
                    <label for="namaDNR_edit" class="col-sm-12 col-md-4 col-form-label">
                        Keterangan
                    </label>
                    <div class="col-sm-12 col-md-8">
                        <input type="text" class="form-control" id="keteranganTandaHIV" name="keteranganTandaHIV">
                    </div>
                </div>
                <div class="row">
                    <div class="offset-10 col-lg-2">
                        <div class="form-group">
                            <div class="pull-right">
                                <button type="button" class="btn btn-primary btn-block" id="simpanTandaHIVRJ"><i class="fa fa-save"></i> Simpan</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- END MODAL TANDA HIV -->

<div class="modal fade" id="uploadPhoto" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title mt-0" id="mySmallModalLabel">Upload Photo</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <form id="myFormUpload" enctype="multipart/form-data">
                        <div class="col-lg-12">
                            <div class="form-group">
                                <label for="fileInput">Foto Upload</label>
                                <input type="file" name="uploadPhotoPasien" id="uploadPhotoPasien">
                                <br><br>
                                <p class="help-block">
                                    <span style="color:#e96048;">Pilih Foto Pasien </span>( Max 2 MB )
                                </p>
                                <input type="hidden" name="mr" id="nomrUpload" value="<?= $getNomr['NORM'] ?>">
                                <input type="hidden" name="oleh" id="olehUpload" value="<?= $id_pengguna ?>">
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <div class="form-group">
                                <input type="submit" class="btn btn-block btn-primary waves-effect upload-Photo" value="Simpan Gambar">
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Modal -->
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="myModalLabel">
                    Hasil Laboratorium Patologi Klinik
                </h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="hasilLAB5"></div>
            </div>
        </div>
    </div>
</div>
<!-- End PK -->

<!-- START MODAL VIEW GERIATRI -->
<div id="modalViewP3g" class="modal fade" role="dialog">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">View Pengkajian Paripurna Pasien Geriatri</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div id="hasilModalViewP3g">
            </div>
        </div>
    </div>
</div>
<!-- END MODAL VIEW GERIATRI -->

<div class="modal fade" id="konfirmasiResep" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true" style="display: none; z-index: 1000000000000 !important;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title mt-0">Detil Order Resep</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <table class="table" style="background-color:#9ccbe2; color: #242a30; font-size: 13px; line-height:15px;">
                        <tr>
                            <td>NAMA LENGKAP / NORM</td>
                            <td><?= $getNomr['NAMA_PASIEN'] . ' / ' . $getNomr['NORM'] ?></td>
                        </tr>
                        <tr>
                            <td>JENIS KELAMIN</td>
                            <td><?= $getNomr['JK'] ?></td>
                        </tr>
                        <tr>
                            <td>TANGGAL LAHIR / UMUR</td>
                            <td><?= date("d-m-Y", strtotime($getNomr['TANGGAL_LAHIR'])) . ' / ' . $getNomr['UMUR'] ?></td>
                        </tr>
                    </table>
                    <div class="alert alert-warning">
                        <strong>Informasi</strong> Pastikan resep yang dikirim sudah benar, tekan Kirim untuk mengirim resep.
                    </div>
                </div>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Jenis</th>
                            <th>Nama Obat</th>
                            <th>Jumlah</th>
                            <th>Aturan Pakai</th>
                            <th>Dosis</th>
                            <th>Keterangan</th>
                            <th>Info</th>
                        </tr>
                    </thead>
                    <tbody id="listResep">
                    </tbody>
                </table>
                <div class="row alert border frmpenunjang d-none">
                    <div class="col">
                        <div class="form-group">
                            <label>Apakah pasien akan melakukan pemeriksaan penunjang <b>hari ini</b>?</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="radiopenunjang" id="inlineRadio1" value="1">
                            <label class="form-check-label" for="inlineRadio1">Ya, akan melakukan</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="radiopenunjang" id="inlineRadio2" value="0">
                            <label class="form-check-label" for="inlineRadio2">Tidak</label>
                        </div>
                        <div class="text-danger warnpenunjang d-none ">
                            Pilih salah satu
                        </div>
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-sm btn-danger" data-dismiss="modal" aria-hidden="true">Batal</button>
                <button type="button" class="btn btn-sm btn-success" id="kirimOrder">Kirim</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="paketObat" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title mt-0">Paket Obat</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <table id="tbl-paket-obat-eresep" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th width="5%">No</th>
                            <th>Nama Paket</th>
                            <th>#</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal Start -->
<div class="modal fade" id="viewDetilResep" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title mt-0" id="mySmallModalLabel">Detil Resep</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <div id="detilResep"></div>
            </div>
        </div>
    </div>
</div>

<!-- START MODAL VIralload -->
<div id="modalViraload" class="modal fade" role="dialog">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Tanggal Pemberian obat HIV pertama</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div id="ctnModalViraload" class="ctnModalViraload">
            </div>
        </div>
    </div>
</div>
<!-- END VIralload -->

<!-- START MODAL VIEW MONITORING TANDA VITAL -->
<div id="viewTandaVitalMon" class="modal fade" role="dialog">
    <div class="modal-dialog modal-xl" style="max-width: 1300px;">
        <div class="modal-content" style="padding: 0px">
            <div id="hasilShowTandaVital">

            </div>
        </div>
    </div>
</div>
<!-- END MODAL VIEW MONITORING TANDA VITAL -->
<style>
    .ajs-button.ajs-ok {
        color: #fff;
        background-color: #13950e;
        border-color: #f7ecec;
        cursor: pointer;
        margin-right: 25% !important;
    }

    .ajs-button.ajs-cancel {
        color: #fff;
        background-color: #f00;
        border-color: #f7ecec;
        cursor: pointer;
    }

    .alertify .ajs-body {
        min-height: 80px;
        font-size: 20px;
        text-align: center;
    }

    .alertify .ajs-header {
        color: #000;
        font-size: 19px;
        background-color: #fff;
        border: 2px solid #f9f7f7;
        border-radius: 10px;
    }

    .alertify .ajs-dialog {
        border: 2px solid #f9f7f7;
        top: 50%;
        transform: translateY(-50%);
        margin: auto;
        background-color: #fff;
        color: #000;
        border-radius: 10px;
    }

    .alertify .ajs-footer {
        background-color: #fff;
        border: 2px solid #f9f7f7;
        border-radius: 10px;
        padding: 10px;
    }

    .alertify .ajs-footer .ajs-buttons.ajs-primary {
        text-align: center;
    }
</style>

<script>
    $(document).ready(function () {
        $('.historyCpptRehabMedik').on('click', function () {
            if ($('.historyCpptRehabMedikStts').val() == 1) {
                $('#historycpptRehabMedik').DataTable({
                    "ordering": false,
                    "ajax": {
                        url: '<?= base_url() ?>PengkajianAwal/historycpptRehabMedik',
                        type: 'POST',
                        data: {
                            nomr: '<?= $nomr ?>'
                        },
                    },
                });
            }
            $('.historyCpptRehabMedikStts').val(0);
        });

        $('.dupHisRadiologi').select2();

        $('.dupHisRadiologi').on('change', function () {
            var id = $(this).val();

            $.ajax({
                type: 'POST',
                url: '<?= base_url() ?>PengkajianAwal/getDupHisRad',
                data: {
                    id: id
                },
                dataType: 'JSON',
                success: function (data) {
                    $('.tujuanRadiologiSimpelRjDupHis').val('105100101').trigger('change');
                    $('.citoKlikDupHis').prop('checked', false);
                    $('.diagnosaKlinisDupHis').prop('checked', false);
                    $('#tanggalKemoDiagnosaKlinis, #tanggalOperasiDiagnosaKlinis, #tanggalUlangDiagnosaKlinis, #tanggalLainnyaDiagnosaKlinis, #tanggalRecistDiagnosaKlinis, #tanggalKontrolDiagnosaKlinis').css('display', 'none');
                    $('.klinisiRadiologiOrderDupHis, .tanggalOperasiDiagnosaKlinisInputDupHis, .tanggalUlangDiagnosaKlinisInputDupHis, .keteranganDiagnosaKlinisInputDupHis, .tanggalLainnyaDiagnosaKlinisInputDupHis, .tanggalRecistDiagnosaKlinisInputDupHis, .tanggalKontrolDiagnosaKlinisInputDupHis, .tanggalKemoDiagnosaKlinisInputDupHis').val();

                    $('.tujuanRadiologiSimpelRjDupHis').val(data.TUJUAN).trigger('change');
                    $('.tglRadiologiSimpelRjDupHis').val(data.TANGGAL);
                    $('.citoKlikDupHis').prop('checked', data.CITO == 1 ? true : false);
                    $('input[name="diagnosaKlinis"][value="' + data.DIAGNOSA_KLINIS + '"]').prop('checked', data.DIAGNOSA_KLINIS != null ? true : false).trigger('click');
                    $('.klinisiRadiologiOrderDupHis').val(data.KLINISI);
                    $('.tglRencanaRadiologiRjDupHis').val(data.TANGGAL_RENCANA);
                    $('.dokterPerujukRadiologiRjDupHis').val(data.DOKTER_ASAL).trigger('change');

                    $('.tanggalOperasiDiagnosaKlinisInputDupHis').val(data.TANGGAL_OPERASI);
                    $('.tanggalKemoDiagnosaKlinisInputDupHis').val(data.TANGGAL_KEMO);
                    $('.tanggalUlangDiagnosaKlinisInputDupHis').val(data.TANGGAL_ULANG);
                    $('.tanggalRecistDiagnosaKlinisInputDupHis').val(data.TANGGAL_RECIST);
                    $('.tanggalKontrolDiagnosaKlinisInputDupHis').val(data.TANGGAL_KONTROL);
                    $('.keteranganDiagnosaKlinisInputDupHis').val(data.ISIAN_LAINNYA);
                    $('.tanggalLainnyaDiagnosaKlinisInputDupHis').val(data.TANGGAL_LAINNYA);

                    $.ajax({
                        url: "<?= base_url('PengkajianAwal/getDupHisRadDetail') ?>",
                        method: "POST",
                        data: {
                            id: id,
                        },
                        dataType: 'json',
                        success: function (data) {
                            $('[type="checkbox"]').prop('checked', false);
                            $.each(data, function (key, value) {
                                $('[type="checkbox"]#tindakanradiologi' + value.REF).prop('checked', true);
                            });
                        }
                    });
                }
            });
        });

        $('.history-order-pk').on('click', function () {
            if ($('.history-order-pkStts').val() == 1) {
                $('#history-order-lab-pk').DataTable({
                    'ajax': {
                        url: "<?= base_url('PengkajianAwal/tblHistoryOrderLab') ?>",
                        data: {
                            norm: "<?= $getNomr['NORM'] ?>"
                        },
                        type: 'POST',
                    },
                });
            }
            $('.history-order-pkStts').val(0);
        });

        $('.Eresep').on('click', function () {
            // var status = $('#buka-side-cppt').attr('status');
            // if(status == 'aktif'){
            //     $('#buka-side-cppt').trigger('click');
            // }
            // $('#menu-e-resep-side-rj').trigger('click');

            if ($('.EresepStts').val() == 1) {
                $('#tblDaftarOrder').DataTable({
                    "ajax": {
                        url: '<?= base_url() ?>PengkajianAwal/tblDaftarOrder',
                        data: {
                            nokun: '<?= $getNomr['NOKUN'] ?>'
                        },
                        type: 'post'
                    },
                });
            }
            $('.EresepStts').val(0);
        });

        $('.tombolTandaVitalMon').click(function () {
            // $('#viewTandaVitalMon').modal('show');
            var nomr = $(this).data('nomr');
            var nokun = $(this).data('nokun');
            $.ajax({
                type: 'POST',
                url: '<?= base_url() ?>PengkajianAwal/viewTandaVital',
                data: {
                    nomr: nomr,
                    nokun: nokun
                },
                success: function (data) {
                    // $('#showChartTandaVitalMon').html(data);
                    $('#hasilShowTandaVital').html(data);
                }
            });
        });

        function hasPenCppt() {
            $('#cppt-side-rj').load('<?= base_url('cpptView/' . $getNomr['NORM']) ?>');
            $(document).on('change', '.history_cppt_side', function () {
                var id = $(this).val();
                $('#cppt-side-rj').load('<?= base_url('cpptView/' . $getNomr['NORM'] . '/') ?>' + id);
            });
        }
        $('#menu-cppt-side-rj').click(function () {
            hasPenCppt();
        });
        $('#menu-listobat-side').click(function () {
            $('#listobat-side').load('<?= base_url('rekam_medis/eresep/daftarobat/' . $getNomr['NORM']) ?>');
        });
        $('#menu-tindakanBilling-side').click(function () {
            $('#tindakanBilling-side').load('<?= base_url('rekam_medis/TindakanBilling/index/' . $getNomr['NORM'] . '/' . $getNomr['NOPEN'] . '/' . $getNomr['NOKUN']) ?>');
        });
        $('#menu-history-pengkajian-side-rj').click(function () {
            $('#history-pengkajian-side-rj').load('<?= base_url('PengkajianAwal/historySidebar/' . $getNomr['NORM']) ?>');
        });
        $('.lab-pk-side-rj').on('click', function () {
            $('#lab-pk-side-rj').load('<?= base_url() ?>rekam_medis/Medis/labPkSideBarRi/<?= $getNomr['NOKUN'] ?>');
        });
        $('.lab-pa-side-rj').on('click', function () {
            $('#lab-pa-side-rj').load('<?= base_url() ?>rekam_medis/Medis/labPaSideBarRi/<?= $getNomr['NOKUN'] ?>');
        });
        $('.lab-pt-side-rj').on('click', function () {
            $('#lab-pt-side-rj').load('<?= base_url() ?>rekam_medis/Medis/labPtSideBarRi/<?= $getNomr['NOKUN'] ?>');
        });
        $('.radiologi-side-rj').on('click', function () {
            $('#radiologi-side-rj').load('<?= base_url() ?>rekam_medis/Medis/radiologiSideBarRi/<?= $getNomr['NOKUN'] ?>');
        });
        $('#menu-resep-side-rj').click(function () {
            $('#resep-side-rj').load('<?= base_url('PengkajianAwal/hasilPenunjangResep/' . $getNomr['NORM'] . '/' . $getNomr['NOPEN'] . '/' . $getNomr['NOKUN']) ?>');
        });
        // Akhir aksi sidebar

        $('#modalViewP3g').on('show.bs.modal', function (e) {
            var idgeriatri = $(e.relatedTarget).data('idgeriatri');
            var nokun = $(e.relatedTarget).data('nokun');

            $.ajax({
                type: 'POST',
                url: '<?= base_url() ?>PengkajianAwal/detailGeritatriPppg',
                data: {
                    idgeriatri: idgeriatri,
                    nokun: nokun
                },
                success: function (data) {
                    $('#hasilModalViewP3g').html(data);
                }

            });
            // e.preventDefault();
        });

        // View Detail diskon dokter
        $('.tombolDiskonDokter').on('click', function () {
            var nopen = $(this).data('nopen');
            var nomr = $(this).data('nomr');
            $.ajax({
                type: 'POST',
                url: '<?= base_url() ?>PengkajianAwal/viewDiskonDokter',
                data: {
                    nopen: nopen,
                    nomr: nomr,
                },
                success: function (data) {
                    $('#hasilDiskonDokter').html(data);
                }
            });
        });

        // Lihat modal input diagnosis
        $('#tombolInputDiagnosisDpjp, #tautanInputDiagnosisDpjp').click(function () {
            $.ajax({
                type: 'POST',
                url: "<?= base_url('DiagnosisDpjp/index') ?>",
                data: {
                    nomr: "<?= $getNomr['NORM'] ?>",
                    nopen: "<?= $getNomr['NOPEN'] ?>",
                    nokun: "<?= $getNomr['NOKUN'] ?>",
                },
                success: function (data) {
                    $('#hasilInputDiagnosaDpjp').html(data);
                }
            });
        });

        // Ketika modal input diagnosis ditutup
        $('#viewInputDiagnosisDpjp').on('hidden.bs.modal', function () {
            $.ajax({
                type: 'post',
                url: "<?= base_url('DiagnosisDpjp/cek') ?>",
                data: {
                    nomr: "<?= $getNomr['NORM'] ?>"
                },
                success: function (data) {
                    $('#tabelDiagnosisDpjp').empty();
                    $('#tabelDiagnosisDpjp').DataTable().destroy();
                    $('#isiTombolInputDiagnosisDpjp').html(data);
                    if (data <= 0) {
                        $('#tooltipTombolInputDiagnosisDpjp').html('Input diagnosis belum diisi');
                        $('#tombolInputDiagnosisDpjp').removeClass('btn-success').addClass('btn-warning');
                        $('#infoInputDiagnosisDpjp').removeClass('d-none');
                    } else {
                        $('#tooltipTombolInputDiagnosisDpjp').html('Input diagnosis');
                        $('#tombolInputDiagnosisDpjp').removeClass('btn-warning').addClass('btn-success');
                        $('#infoInputDiagnosisDpjp').addClass('d-none');
                    }
                }
            });
        });

        let stickyTop = $('.cariRadiologiRj, #form-cari-pk').offset().top;
        $('.cariRadiologiRj').click(function () {
            $('.cariRadiologiRj').animate({
                width: '400px'
            });
        });

        $(window).scroll(function () {
            $('.cariRadiologiRj').click(function () {
                $('.cariRadiologiRj').animate({
                    width: '400px'
                });
            });
            let windowTop = $(window).scrollTop();
            if (windowTop > 220) {
                $('#cari-radiologi, #form-cari-pk').css('position', 'fixed');
                $('#cari-radiologi, #form-cari-pk').css('top', '120px');
            } else {
                $('#cari-radiologi, #form-cari-pk').css('position', 'relative');
                $('#cari-radiologi, #form-cari-pk').css('top', '');
            }
            // $('.cariInputanRadRi').focus();
        });

        // Mulai dokter perujuk
        $('#dokter-perujuk-pk').select2({
            placeholder: '[ Pilih Dokter Perujuk ]'
        }).val(<?= $getNomr['ID_DOKTER'] ?? 0 ?>).trigger('change');
        // Akhir dokter perujuk

        // Mulai ruang tujuan
        $('#ruang-tujuan-pk').select2({
            placeholder: '[ Pilih Ruang Tujuan ]'
        }).val(<?= $getNomr['TUJUAN_ORDER_LAB_PK'] ?? 0 ?>).trigger('change');
        // Akhir ruang tujuan

        // Mulai buka CPPT
        $('#buka-side-cppt').click(function () {
            hasPenCppt();
            let statusSideCPPT = $(this).attr('status');
            if (statusSideCPPT == 'aktif') {
                // alert('besar');
                $('#lab-pk-side-rj').load('<?= base_url('rekam_medis/Medis/labPkSideBarRi/' . $getNomr['NOKUN']) ?>');
                $('#buka-side-cppt').attr('status', 'nonaktif');
                if ($('#buka-side-konsultasi').attr('status') == 'nonaktif') {
                    $('#buka-side-konsultasi').attr('status', 'aktif');
                    $('#tampil-sidebar-konsultasi-rj').fadeOut(300, function () {
                        $('#tampil-sidebar-konsultasi-rj').addClass('d-none').removeClass('d-block');
                    });
                } else {
                    $('#sidebar-pasien-rj').fadeOut(300, function () {
                        // item.toggleClass('oldClass newClass')
                        $('#sidebar-pasien-rj').toggleClass('col-sm-3 col-sm-6').fadeIn(300);
                    });
                    $('#bar-pasien-rj').fadeOut(300, function () {
                        // item.toggleClass('oldClass newClass')
                        $('#bar-pasien-rj').toggleClass('col-sm-9 col-sm-6').fadeIn(300);
                    });
                }
                $('#tampil-sidebar-pasien-rj, #tampil-sidebar-permintaan-darah-rj').fadeOut(300, function () {
                    $('#tampil-sidebar-pasien-rj, #tampil-sidebar-permintaan-darah-rj').addClass('d-none').removeClass('d-block');
                    $('#tampil-sidebar-hasil-lab-rj').removeClass('d-none').addClass('d-block').fadeIn(300);
                });
            } else if (statusSideCPPT == 'nonaktif') {
                // alert('kecil');
                $('#buka-side-cppt').attr('status', 'aktif');
                $('#sidebar-pasien-rj').fadeOut(300, function () {
                    // item.toggleClass('oldClass newClass')
                    $('#sidebar-pasien-rj').toggleClass('col-sm-6 col-sm-3').fadeIn(300);
                });
                $('#bar-pasien-rj').fadeOut(300, function () {
                    // item.toggleClass('oldClass newClass')
                    $('#bar-pasien-rj').toggleClass('col-sm-6 col-sm-9').fadeIn(300);
                });
                $('#tampil-sidebar-hasil-lab-rj').fadeOut(300, function () {
                    $('#tampil-sidebar-hasil-lab-rj').addClass('d-none').removeClass('d-block');
                    $('#tampil-sidebar-pasien-rj, #tampil-sidebar-permintaan-darah-rj').removeClass('d-none').addClass('d-block').fadeIn(300);
                });
            }
        });
        // Akhir buka CPPT

        // Mulai buka konsultasi
        $('#buka-side-konsultasi').click(function () {
            let statusSideKonsultasi = $(this).attr('status');
            if (statusSideKonsultasi == 'aktif') {
                // alert('besar');
                $('#buka-side-konsultasi').attr('status', 'nonaktif');
                if ($('#buka-side-cppt').attr('status') == 'nonaktif') {
                    $('#buka-side-cppt').attr('status', 'aktif');
                    $('#tampil-sidebar-hasil-lab-rj').fadeOut(300, function () {
                        $('#tampil-sidebar-hasil-lab-rj').addClass('d-none').removeClass('d-block');
                    });
                } else {
                    $('#sidebar-pasien-rj').fadeOut(300, function () {
                        // item.toggleClass('oldClass newClass')
                        $('#sidebar-pasien-rj').toggleClass('col-sm-3 col-sm-6').fadeIn(300);
                    });
                    $('#bar-pasien-rj').fadeOut(300, function () {
                        // item.toggleClass('oldClass newClass')
                        $('#bar-pasien-rj').toggleClass('col-sm-9 col-sm-6').fadeIn(300);
                    });
                }

                $.ajax({
                    url: "<?= base_url('konsultasi/Konsultasi/sidePane') ?>",
                    type: 'POST',
                    data: {
                        nomr: '<?= $getNomr['NORM'] ?>',
                        ruangTujuan: '<?= $getNomr['RUANGAN_TUJUAN'] ?>'
                    },
                    success: function (data) {
                        $('#tampil-sidebar-pasien-rj, #tampil-sidebar-permintaan-darah-rj').addClass('d-none').removeClass('d-block').fadeOut(300);
                        $('#tampil-sidebar-konsultasi-rj').removeClass('d-none').addClass('d-block').html(data).fadeIn(300);
                    }
                });
            } else if (statusSideKonsultasi == 'nonaktif') {
                // alert('kecil');
                $('#buka-side-konsultasi').attr('status', 'aktif');
                $('#sidebar-pasien-rj').fadeOut(300, function () {
                    // item.toggleClass('oldClass newClass')
                    $('#sidebar-pasien-rj').toggleClass('col-sm-6 col-sm-3').fadeIn(300);
                });
                $('#bar-pasien-rj').fadeOut(300, function () {
                    // item.toggleClass('oldClass newClass')
                    $('#bar-pasien-rj').toggleClass('col-sm-6 col-sm-9').fadeIn(300);
                });
                $('#tampil-sidebar-konsultasi-rj').fadeOut(300, function () {
                    $('#tampil-sidebar-konsultasi-rj').addClass('d-none').removeClass('d-block');
                    $('#tampil-sidebar-pasien-rj, #tampil-sidebar-permintaan-darah-rj').removeClass('d-none').addClass('d-block').fadeIn(300);
                });
            }
        });
        // Akhir buka konsultasi

        // Mulai history eTimja
        let eTimja = $('#etimja');
        $('#tbl-history-etimja-info').click(function () {
            let nokun = $(this).data('id');
            $.ajax({
                method: 'POST',
                url: "<?= base_url('Etimja/history') ?>",
                data: {
                    nomr: '<?= $getNomr['NORM'] ?>',
                    nokun: nokun,
                    keterangan: 'info'
                },
                success: function (data) {
                    $('#lihat-history-modal-etimja-info').html(data);
                }
            });
        });
        $(function () {
            $('[data-tooltip="true"]').tooltip();
        });

        $('.viewModalRegkan').click(function () {
            var nomr = $(this).data('nomr');
            var nopen = $(this).data('nopen');
            var nokun = $(this).data('nokun');
            $.ajax({
                type: 'POST',
                url: '<?= base_url() ?>PengkajianAwal/viewModalRegkan',
                data: {
                    nomr: nomr,
                    nokun: nokun,
                    nopen: nopen,
                },
                success: function (data) {
                    $('#hasilModalREGKAN').html(data);
                }
            });
        });

        eTimja.click(function (e, data) {
            // Mulai periksa jika id eTimja ada
            if (data) {
                $.ajax({
                    url: '<?= base_url('Etimja/form/' . $getNomr['NOKUN']) ?>',
                    type: 'POST',
                    data: {
                        id_etimja: data.id_etimja
                    },
                    success: function (data) {
                        $('#view-etimja').html(data);
                    },
                });
            } else {
                $('#view-etimja').load('<?= base_url('Etimja/form/' . $getNomr['NOKUN']) ?>');
            }
            // Akhir periksa jika id eTimja ada
        });
        if (eTimja.hasClass('active')) {
            $('#view-etimja').load('<?= base_url('Etimja/form/' . $getNomr['NOKUN']) ?>');
        }

        $(document).on('click', '.buka-etimja', function () {
            let id_etimja = $(this).data('id');
            // console.log(id_etimja);
            $('#modal-history-etimja-info').modal('hide');
            eTimja.trigger('click', [{
                id_etimja: id_etimja
            }]);
        });
        // Akhir history eTimja

        <?php
        $nomr_segment = $this->uri->segment(3);
        $nopen_segment = $this->uri->segment(4);
        $nokun_segment = $this->uri->segment(5);
        ?>

        $(function () {
            $('.PengkajianAwalKeperawatan').click(function () {
                $('#view_PengkajianAwalKeperawatan').load('<?= base_url() ?>PengkajianAwalKeperawatan/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>/ews/<?= $this->uri->segment(7) ? $this->uri->segment(7) : "999999999/" ?>');
            });
            if ($('.PengkajianAwalKeperawatan').hasClass("active")) {
                $('#view_PengkajianAwalKeperawatan').load('<?= base_url() ?>PengkajianAwalKeperawatan/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>/ews/<?= $this->uri->segment(7) ? $this->uri->segment(7) : "999999999/" ?>');
            }

            //EWS Rawat Jalan
            $('.ewsRawatJalan').click(function () {
                $('#view_ewsRawatJalan').load('<?= base_url() ?>emr/EwsRawatJalan/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            });
            if ($('.ewsRawatJalan').hasClass('active')) {
                $('#view_ewsRawatJalan').load('<?= base_url() ?>emr/EwsRawatJalan/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            }

            // Deteksi Dini Kanker Payudara
            let kankerPayudara = $('#dd-payudara');
            kankerPayudara.click(function () {
                $('#view-dd-payudara').load("<?= base_url('emr/deteksiDini/KankerPayudara/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            });
            if (kankerPayudara.hasClass('active')) {
                $('#view-dd-payudara').load("<?= base_url('emr/deteksiDini/KankerPayudara/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            }

            // Deteksi Dini Kanker Payudara
            let ukdd = $('#ukdd');
            ukdd.click(function () {
                $('#view-ukdd').load("<?= base_url('emr/deteksiDini/UKDD/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            });
            if (ukdd.hasClass('active')) {
                $('#view-ukdd').load("<?= base_url('emr/deteksiDini/UKDD/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            }

            // Medical Check Up Deteksi Dini
            let mcudd = $('#mcudd');
            mcudd.click(function () {
                $('#view-mcudd').load("<?= base_url('emr/deteksiDini/MCUDD/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            });
            if (mcudd.hasClass('active')) {
                $('#view-mcudd').load("<?= base_url('emr/deteksiDini/MCUDD/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            }

            // Resume Hasil Pemeriksaan Deteksi Dini Kanker
            let rdd = $('#rdd');
            rdd.click(function () {
                $('#view-rdd').load("<?= base_url('emr/deteksiDini/RDD/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            });
            if (rdd.hasClass('active')) {
                $('#view-rdd').load("<?= base_url('emr/deteksiDini/RDD/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            }

            // Resume Hasil Pemeriksaan Deteksi Dini Kanker
            let rpku = $('#rpku');
            rpku.click(function () {
                $('#view-rpku').load("<?= base_url('emr/deteksiDini/MCUDD/rpku/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            });
            if (rpku.hasClass('active')) {
                $('#view-rpku').load("<?= base_url('emr/deteksiDini/MCUDD/rpku/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            }

            $('.summaryListPe').click(function () {
                $('#view_summaryListPe').load('<?= base_url() ?>emr/SummaryList/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            });
            if ($('.summaryListPe').hasClass("active")) {
                $('#view_summaryListPe').load('<?= base_url() ?>emr/SummaryList/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            }

            $('.buktiPelayananRJ').click(function () {
                $('#view_buktiPelayananRJ').load('<?= base_url() ?>emr/buktiPelayananRJ/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            });
            if ($('.buktiPelayananRJ').hasClass("active")) {
                $('#view_buktiPelayananRJ').load('<?= base_url() ?>emr/buktiPelayananRJ/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            }

            // _-_-_-_-_-_-_-_-_-_-_- START Hemodialisis _-_-_-_-_-_-_-_-_-_-_-
            // Persetujuan Tindakan Kedokteran Hemodialisis
            $('.ptk-hemodialisis').click(function () {
                $('#view-ptk-hemodialisis').load('<?= base_url() ?>hemodialisis/PersetujuanHemodialisis/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            });
            if ($('.ptk-hemodialisis').hasClass('active')) {
                $('#view-ptk-hemodialisis').load('<?= base_url() ?>hemodialisis/PersetujuanHemodialisis/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            }
            // Pemantauan Nyeri
            $(document).on('click', '.pemantauanNyeri', function () {
                $('#view_pemantauanNyeri').load('<?= base_url() ?>rekam_medis/rawat_inap/keperawatan/pemantauanNyeri/indexRJ/<?= $nokun_segment ?>');
            });
            if ($('.pemantauanNyeri').hasClass('active')) {
                $('#view_pemantauanNyeri').load('<?= base_url() ?>rekam_medis/rawat_inap/keperawatan/pemantauanNyeri/indexRJ/<?= $nokun_segment ?>');
            }
            // Skala Ontario
            $('.skalaOntario').click(function () {
                $('#view_skalaOntario').load('<?= base_url() ?>igd/SkalaMorse/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            });
            if ($('.skalaOntario').hasClass('active')) {
                $('#view_skalaOntario').load('<?= base_url() ?>igd/SkalaMorse/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            }
            // Edit Skala Ontario
            $(document).on('click', '.editSkalaOntario', function () {
                var id = $(this).data('id');
                $('#view_skalaOntario').load('<?= base_url() ?>igd/SkalaMorse/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>/' + id + '/' + id);
            });

            // Skala Ontario Modified Atratify Sidney Scorsing
            $('.skalaOntarioMASS').click(function () {
                $('#view_skalaOntarioMASS').load('<?= base_url() ?>geriatri/SkalaOntarioMASS/indexRJ/<?= $nokun_segment ?>');
            });
            if ($('.skalaOntarioMASS').hasClass('active')) {
                $('#view_skalaOntarioMASS').load('<?= base_url() ?>geriatri/SkalaOntarioMASS/indexRJ/<?= $nokun_segment ?>');
            }
            // Humpty Dumpty
            $('.humptyDumptyRi').click(function () {
                $('#view_humptyDumptyRi').load('<?= base_url() ?>igd/HumptyDumptyScale/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            });
            if ($('.humptyDumptyRi').hasClass('active')) {
                $('#view_humptyDumptyRi').load('<?= base_url() ?>igd/HumptyDumptyScale/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            }
            // _-_-_-_-_-_-_-_-_-_-_- END Hemodialisis _-_-_-_-_-_-_-_-_-_-_-

            // _-_-_-_-_-_-_-_-_-_-_- START Rehabilitasi Medik _-_-_-_-_-_-_-_-_-_-_-
            // Konsultasi Rehabilitasi Medik
            $('.konsul-rehab-medik').click(function () {
                $('#view-konsul-rehab-medik').load("<?= base_url('rehabilitasiMedik/KonsulRehabMedik/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            });
            if ($('.konsul-rehab-medik').hasClass('active')) {
                $('#view-konsul-rehab-medik').load("<?= base_url('rehabilitasiMedik/KonsulRehabMedik/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            }

            // Pengukuran Limfedema
            $('.limfedema').click(function () {
                $('#view-limfedema').load("<?= base_url('rehabilitasiMedik/PengukuranLimfedema/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            });
            if ($('.limfedema').hasClass('active')) {
                $('#view-limfedema').load("<?= base_url('rehabilitasiMedik/PengukuranLimfedema/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            }

            // Klaim Rehabilitasi Medik
            let klaimRehab = $('#klaim-rehab');
            klaimRehab.click(function () {
                $('#view-klaim-rehab').load("<?= base_url('rehabilitasiMedik/KlaimRehab/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            });
            if (klaimRehab.hasClass('active')) {
                $('#view-klaim-rehab').load("<?= base_url('rehabilitasiMedik/KlaimRehab/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            }
            // _-_-_-_-_-_-_-_-_-_-_- END Rehabilitasi Medik _-_-_-_-_-_-_-_-_-_-_-

            // _-_-_-_-_-_-_-_-_-_-_- START Informed Consent _-_-_-_-_-_-_-_-_-_-_-
            // Persetujuan Tindakan Kedokteran
            $('.persetujuan-tindakan-kedokteran').click(function () {
                $('#view-persetujuan-tindakan-kedokteran').load('<?= base_url() ?>informedConsent/persetujuanTindakanKedokteran/index/<?= $nomr_segment . '/' . $nokun_segment . '/' . $this->uri->segment(7) ?>');
            });
            if ($('.persetujuan-tindakan-kedokteran').hasClass('active')) {
                $('#view-persetujuan-tindakan-kedokteran').load('<?= base_url() ?>informedConsent/persetujuanTindakanKedokteran/index/<?= $nomr_segment . '/' . $nokun_segment . '/' . $this->uri->segment(7) ?>');
            }

            //Persetujuan Tindakan Pengobatan Kemoterapi
            $('.persetujuanTindakanPengobatanKemoterapi').click(function () {
                $('#view_persetujuanTindakanPengobatanKemoterapi').load('<?= base_url() ?>informedConsent/PT_PengobatanKemoterapi/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            });
            if ($('.persetujuanTindakanPengobatanKemoterapi').hasClass('active')) {
                $('#view_persetujuanTindakanPengobatanKemoterapi').load('<?= base_url() ?>informedConsent/PT_PengobatanKemoterapi/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            }

            // Persetujuan Tindakan Kemoterapi Rhabdomiosarkoma
            $('.PTKemoRhabdomiosarkoma').click(function () {
                $('#view_PTKemoRhabdomiosarkoma').load('<?= base_url() ?>informedConsent/PTKemoRhabdomiosarkoma/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(7) ?>');
            });
            if ($('.PTKemoRhabdomiosarkoma').hasClass("active")) {
                $('#view_PTKemoRhabdomiosarkoma').load('<?= base_url() ?>informedConsent/PTKemoRhabdomiosarkoma/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(7) ?>');
            }

            //Persetujuan Tindakan Kemoterapi LL Anak
            $('.persetujuanTindakanKemoterapiLLAAnak').click(function () {
                $('#view_persetujuanTindakanKemoterapiLLAAnak').load('<?= base_url() ?>informedConsent/PersetujuanTindakanKemoterapiLlaAnak/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            });
            if ($('.persetujuanTindakanKemoterapiLLAAnak').hasClass('active')) {
                $('#view_persetujuanTindakanKemoterapiLLAAnak').load('<?= base_url() ?>informedConsent/PersetujuanTindakanKemoterapiLlaAnak/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            }

            // Penolakan Tindakan Kedokteran
            $('.penolakanTindakanKedokteran').click(function () {
                $('#view_penolakanTindakanKedokteran').load('<?= base_url() ?>penolakantindakankedokteran/<?= $nokun_segment ?>');
            });
            if ($('.penolakanTindakanKedokteran').hasClass("active")) {
                $('#view_penolakanTindakanKedokteran').load('<?= base_url() ?>penolakantindakankedokteran/<?= $nokun_segment ?>');
            }
            $(document).on('click', '.editPenolakanTindakanKedokteran', function () {
                $('#modal').modal('hide');
                var id = $(this).data('id');
                $('#view_penolakanTindakanKedokteran').load('<?= base_url() ?>penolakantindakankedokteran/<?= $nokun_segment ?>/' + id);
            });

            // Persetujuan Tindakan Transfusi Darah
            let pttd = $('.pttd');
            pttd.click(function () {
                $('#view-pttd').load('<?= base_url() ?>pttd/<?= $getNomr['NOKUN'] ?>');
            });
            if (pttd.hasClass('active')) {
                $('#view-pttd').load('<?= base_url() ?>pttd/<?= $getNomr['NOKUN'] ?>');
            }

            // DNR
            $('.dnr').click(function () {
                $('#view_dnr').load('<?= base_url() ?>rekam_medis/rawat_inap/informedConsent/DNR/index/<?= $getNomr['NORM'] . '/' . $getNomr['NOPEN'] . '/' . $getNomr['NOKUN'] ?>');
            });
            if ($('.dnr').hasClass("active")) {
                $('#view_dnr').load('<?= base_url() ?>rekam_medis/rawat_inap/informedConsent/DNR/index/<?= $getNomr['NORM'] . '/' . $getNomr['NOPEN'] . '/' . $getNomr['NOKUN'] ?>');
            }

            // Spinal Epidural
            $('.spinalEpi').click(function () {
                $('#view_spinalEpi').load('<?= base_url() ?>rekam_medis/rawat_inap/informedConsent/SpinalEpi/index/<?= $getNomr['NORM'] . '/' . $getNomr['NOPEN'] . '/' . $getNomr['NOKUN'] ?>');
            });
            if ($('.spinalEpi').hasClass("active")) {
                $('#view_spinalEpi').load('<?= base_url() ?>rekam_medis/rawat_inap/informedConsent/SpinalEpi/index/<?= $getNomr['NORM'] . '/' . $getNomr['NOPEN'] . '/' . $getNomr['NOKUN'] ?>');
            }

            // Pemberian Infromasi dan Persetujuan Tindakan Kedokteran
            $('.PIPTKS').click(function () {
                $('#view_PIPTKS').load('<?= base_url() ?>piptks/<?= $nokun_segment ?>');
            });
            if ($('.PIPTKS').hasClass('active')) {
                $('#view_PIPTKS').load('<?= base_url() ?>piptks/<?= $nokun_segment ?>');
            }
            $(document).on('click', '.editPIPTKS', function () {
                $('#modal').modal('hide');
                var id = $(this).data('id');
                $('#view_PIPTKS').load('<?= base_url() ?>piptks/<?= $nokun_segment ?>/' + id);
            });

            // Pemberian Informasi dan Persetujuan Tindakan Kedokteran Anestesi Umum
            $('.PIPTKAU').click(function () {
                $('#view_PIPTKAU').load('<?= base_url() ?>piptkau/<?= $nokun_segment ?>');
            });
            if ($('.PIPTKAU').hasClass("active")) {
                $('#view_PIPTKAU').load('<?= base_url() ?>piptkau/<?= $nokun_segment ?>');
            }
            $(document).on('click', '.editPIPTKAU', function () {
                $('#modal').modal('hide');
                var id = $(this).data('id');
                $('#view_PIPTKAU').load('<?= base_url() ?>piptkau/<?= $nokun_segment ?>/' + id);
            });
            // _-_-_-_-_-_-_-_-_-_-_- END Informed Consent _-_-_-_-_-_-_-_-_-_-_-

            // BANK DARAH PEMANTAUAN DARAH
            $(document).on('click', '.pemberianDanPemantauanDarah', function () {
                $('#view_pemberianDanPemantauanDarah').load('<?= base_url() ?>rekam_medis/rawat_inap/bankDarah/PemberianDanPemantauanDarah/index/<?= $getNomr['NORM'] . '/' . $getNomr['NOPEN'] . '/' . $getNomr['NOKUN'] ?>');
            });
            if ($('.pemberianDanPemantauanDarah').hasClass('active')) {
                $('#view_pemberianDanPemantauanDarah').load('<?= base_url() ?>rekam_medis/rawat_inap/bankDarah/PemberianDanPemantauanDarah/index/<?= $getNomr['NORM'] . '/' . $getNomr['NOPEN'] . '/' . $getNomr['NOKUN'] ?>');
            }

            // _-_-_-_-_-_-_-_-_-_-_- START HIV ART _-_-_-_-_-_-_-_-_-_-_-
            // Perawatan Pasien HIV dan Terapi ART
            $('.perawatanPasienHIV').click(function () {
                $('#view-perawatanPasienHIV').load('<?= base_url() ?>hivART/PerawatanHIVART/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            });
            if ($('.perawatanPasienHIV').hasClass('active')) {
                $('#view-perawatanPasienHIV').load('<?= base_url() ?>hivART/PerawatanHIVART/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            }

            //Follow-Up Perawatan Pasien HIV dan Terapi ART
            $('.followupPasienHIV').click(function () {
                $('#view_followupPasienHIV').load('<?= base_url() ?>hivART/FollowupHIVART/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            });
            if ($('.followupPasienHIV').hasClass('active')) {
                $('#view_followupPasienHIV').load('<?= base_url() ?>hivART/FollowupHIVART/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            }

            // Formulir Rujukan
            $('.formulirRujukan').click(function () {
                $('#view_formulirRujukan').load('<?= base_url() ?>hivART/FormulirRujukan/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            });
            if ($('.formulirRujukan').hasClass("active")) {
                $('#view_formulirRujukan').load('<?= base_url() ?>hivART/FormulirRujukan/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            }
            // _-_-_-_-_-_-_-_-_-_-_- END HIV ART _-_-_-_-_-_-_-_-_-_-_-

            // _-_-_-_-_-_-_-_-_-_-_- START Sedasi _-_-_-_-_-_-_-_-_-_-_-

            // Status Sedasi
            $('.sedasi').click(function () {
                $('#view_sedasi').load('<?= base_url() ?>sedasi/StatusSedasi/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) . '/' . $this->uri->segment(7) ?>');
            });
            if ($('.sedasi').hasClass('active')) {
                $('#view_sedasi').load('<?= base_url() ?>sedasi/StatusSedasi/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) . '/' . $this->uri->segment(7) ?>');
            }

            // Medikasi Obat
            $('.medikasiObat').click(function () {
                $('#view_medikasiObat').load('<?= base_url() ?>sedasi/MedikasiObat/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) . '/' . $this->uri->segment(7) ?>');
            });
            if ($('.medikasiObat').hasClass('active')) {
                $('#view_medikasiObat').load('<?= base_url() ?>sedasi/MedikasiObat/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) . '/' . $this->uri->segment(7) ?>');
            }

            // Medikasi NNTD
            $('.medikasiNNTD').click(function () {
                $('#view_medikasiNNTD').load('<?= base_url() ?>sedasi/MedikasiNapas/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) . '/' . $this->uri->segment(7) ?>');
            });
            if ($('.medikasiNNTD').hasClass('active')) {
                $('#view_medikasiNNTD').load('<?= base_url() ?>sedasi/MedikasiNapas/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) . '/' . $this->uri->segment(7) ?>');
            }

            // Sedasi Pemulihan
            $('.sedasiPemulihan').click(function () {
                $('#view_sedasiPemulihan').load('<?= base_url() ?>sedasi/SedasiPemulihan/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) . '/' . $this->uri->segment(7) ?>');
            });
            if ($('.sedasiPemulihan').hasClass('active')) {
                $('#view_sedasiPemulihan').load('<?= base_url() ?>sedasi/SedasiPemulihan/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) . '/' . $this->uri->segment(7) ?>');
            }

            // Pemulihan Napas
            $('.pemulihanNapas').click(function () {
                $('#view_pemulihanNapas').load('<?= base_url() ?>sedasi/PemulihanNapas/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) . '/' . $this->uri->segment(7) ?>');
            });
            if ($('.pemulihanNapas').hasClass('active')) {
                $('#view_pemulihanNapas').load('<?= base_url() ?>sedasi/PemulihanNapas/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) . '/' . $this->uri->segment(7) ?>');
            }

            // _-_-_-_-_-_-_-_-_-_-_- END Sedasi _-_-_-_-_-_-_-_-_-_-_-

            // _-_-_-_-_-_-_-_-_-_-_- START Geriatri _-_-_-_-_-_-_-_-_-_-_-

            // Formulir Abbreviated Mental Test
            $('.abbreviatedMentalTest').click(function () {
                $('#view_abbreviatedMentalTest').load('<?= base_url() ?>geriatri/AbbreviatedMentalTest/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            });
            if ($('.abbreviatedMentalTest').hasClass('active')) {
                $('#view_abbreviatedMentalTest').load('<?= base_url() ?>geriatri/AbbreviatedMentalTest/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            }

            // Instrumen Geriatric Development Scale
            $('.instrumen-gds').click(function () {
                $('#view-instrumen-gds').load('<?= base_url() ?>geriatri/InstrumenGDS/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            });
            if ($('.instrumen-gds').hasClass('active')) {
                $('#view-instrumen-gds').load('<?= base_url() ?>geriatri/InstrumenGDS/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            }

            //GERIATRI
            //Instrimen PPPG
            $('.instrumenPPPG').click(function () {
                $('#view_instrumenPPPG').load('<?= base_url() ?>geriatri/InstrumenPPPG/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            });
            if ($('.instrumenPPPG').hasClass('active')) {
                $('#view_instrumenPPPG').load('<?= base_url() ?>geriatri/InstrumenPPPG/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            }
            // Penilaian ADL
            $('.penilaianADL').click(function () {
                $('#view_penilaianADL').load('<?= base_url() ?>geriatri/Adl/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            });
            if ($('.penilaianADL').hasClass('active')) {
                $('#view_penilaianADL').load('<?= base_url() ?>geriatri/Adl/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            }
            // Edit penilaianADL
            $(document).on('click', '.editPenilaianADL', function () {
                var id = $(this).data('id');
                $('#view_penilaianADL').load('<?= base_url() ?>geriatri/Adl/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>' + id + '/' + id);
            });

            // iadl
            $('.iadl').click(function () {
                $('#view_iadl').load('<?= base_url() ?>geriatri/Iadl/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            });
            if ($('.iadl').hasClass('active')) {
                $('#view_iadl').load('<?= base_url() ?>geriatri/Iadl/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            }
            // Edit iadl
            $(document).on('click', '.editiadl', function () {
                var id = $(this).data('id');
                $('#view_iadl').load('<?= base_url() ?>geriatri/Iadl/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>' + id + '/' + id);
            });
            // Pemeriksaan MiniCOG
            $('.pemeriksaanMiniCOG').click(function () {
                $('#view_pemeriksaanMiniCOG').load('<?= base_url() ?>geriatri/PemeriksaanMiniCOG/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            });
            if ($('.pemeriksaanMiniCOG').hasClass('active')) {
                $('#view_pemeriksaanMiniCOG').load('<?= base_url() ?>geriatri/PemeriksaanMiniCOG/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            }
            // instrumen Evalusi Mmse
            $('.instrumenMmse').click(function () {
                $('#view_instrumenMmse').load('<?= base_url() ?>geriatri/InstrumenMmse/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            });
            if ($('.instrumenMmse').hasClass('active')) {
                $('#view_instrumenMmse').load('<?= base_url() ?>geriatri/InstrumenMmse/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            }
            // instrumen Mna
            $('.instrumenMna').click(function () {
                $('#view_instrumenMna').load('<?= base_url() ?>geriatri/InstrumenMNA/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            });
            if ($('.instrumenMna').hasClass('active')) {
                $('#view_instrumenMna').load('<?= base_url() ?>geriatri/InstrumenMNA/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            }

            // Formulir G8 Geriatri
            $('.formulirG8Geriatri').click(function () {
                $('#view_formulirG8Geriatri').load('<?= base_url() ?>geriatri/FormulirG8Geriatri/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            });
            if ($('.formulirG8Geriatri').hasClass('active')) {
                $('#view_formulirG8Geriatri').load('<?= base_url() ?>geriatri/FormulirG8Geriatri/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            }
            $(document).on('click', '.editG8Geriatri', function () {
                $('#modal').modal('hide');
                var id = $(this).data('id');
                $('#view_formulirG8Geriatri').load('<?= base_url() ?>geriatri/FormulirG8Geriatri/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>/' + id);
            });

            // resiko jatuh pasien lanjut usia
            $('.resikoJatuhLanjutUsia').click(function () {
                $('#view_resikoJatuhLanjutUsia').load('<?= base_url() ?>geriatri/ResikoJatuhLanjutUsia/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            });
            if ($('.resikoJatuhLanjutUsia').hasClass('active')) {
                $('#view_resikoJatuhLanjutUsia').load('<?= base_url() ?>geriatri/ResikoJatuhLanjutUsia/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            }

            // _-_-_-_-_-_-_-_-_-_-_- END Geriatri _-_-_-_-_-_-_-_-_-_-_-

            // _-_-_-_-_-_-_-_-_-_-_- START Transfer Ruangan _-_-_-_-_-_-_-_-_-_-_-

            // Permintaan Dirawat
            $('.permintaanDirawat').click(function () {
                $('#view_permintaanDirawat').load('<?= base_url() ?>transferRuangan/PermintaanDirawat/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            });
            if ($('.permintaanDirawat').hasClass('active')) {
                $('#view_permintaanDirawat').load('<?= base_url() ?>transferRuangan/PermintaanDirawat/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            }

            // _-_-_-_-_-_-_-_-_-_-_- END Transfer Ruangan _-_-_-_-_-_-_-_-_-_-_-

            // _-_-_-_-_-_-_-_-_-_-_- START EMR _-_-_-_-_-_-_-_-_-_-_-

            // Barthel Indek
            $('.barthelIndek').click(function () {
                $('#view_barthelIndek').load('<?= base_url() ?>emr/BarthelIndek/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            });
            if ($('.barthelIndek').hasClass('active')) {
                $('#view_barthelIndek').load('<?= base_url() ?>emr/BarthelIndek/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            }

            // _-_-_-_-_-_-_-_-_-_-_- END EMR _-_-_-_-_-_-_-_-_-_-_-

            // _-_-_-_-_-_-_-_-_-_-_- START Prosedur Diagnostik _-_-_-_-_-_-_-_-_-_-_-
            //Poli Luka
            //Pengkajian Stoma
            $('.pengkajianStoma').click(function () {
                $('#view_pengkajian_stoma').load('<?= base_url() ?>luka/PengkajianStoma/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(7) ?>');
            });
            if ($('.pengkajianStoma').hasClass("active")) {
                $('#view_pengkajian_stoma').load('<?= base_url() ?>luka/PengkajianStoma/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(7) ?>');
            }

            // Pengkajian Luka
            $('.pengkajianLuka').click(function () {
                $('#view_pengkajianLuka').load('<?= base_url() ?>pengkajianLuka/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            });
            if ($('.pengkajianLuka').hasClass('active')) {
                $('#view_pengkajianLuka').load('<?= base_url() ?>pengkajianLuka/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            }

            // Mulai operasi
            // Laporan operasi
            let lapOperasi = $('#lap-operasi');
            lapOperasi.click(function () {
                $('#view-lap-operasi').load("<?= base_url('operasi/FormLaporanOperasi/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            });
            if (lapOperasi.hasClass('active')) {
                $('#view-lap-operasi').load("<?= base_url('operasi/FormLaporanOperasi/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            }
            // Akhir operasi

            // Form Ceklis Persiapan Operasi (CPO)
            $('.ceklisPersiapanOperasi').click(function () {
                $('#view_cpo').load('<?= base_url() ?>rekam_medis/rawat_inap/operasi/CPO/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            });
            if ($('.ceklisPersiapanOperasi').hasClass("active")) {
                $('#view_cpo').load('<?= base_url() ?>rekam_medis/rawat_inap/operasi/CPO/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            }

            //Prosedur Diagnostik
            //Pengkajian Awal Tindakan Invasif
            $('.tindakan_invasif').click(function () {
                $('#view_tindakan_invasif').load('<?= base_url() ?>pengkajianprorad/FormTindakanInvasif/index/<?= $nomr_segment ?>/<?= $nopen_segment ?>/<?= $nokun_segment ?>/<?= $this->uri->segment(7) ?>');
            });
            if ($('.tindakan_invasif').hasClass("active")) {
                $('#view_tindakan_invasif').load('<?= base_url() ?>pengkajianprorad/FormTindakanInvasif/index/<?= $nomr_segment ?>/<?= $nopen_segment ?>/<?= $nokun_segment ?>/<?= $this->uri->segment(7) ?>');
            }
            //Status Anestesia
            $('.status_anestesia').click(function () {
                $('#view_status_anestsia').load('<?= base_url() ?>prosedur/Status_Anestesia/index/<?= $nomr_segment ?>/<?= $nopen_segment ?>/<?= $nokun_segment ?>/<?= $this->uri->segment(7) ?>');
            });
            if ($('.status_anestesia').hasClass("active")) {
                $('#view_status_anestsia').load('<?= base_url() ?>prosedur/Status_Anestesia/index/<?= $nomr_segment ?>/<?= $nopen_segment ?>/<?= $nokun_segment ?>/<?= $this->uri->segment(7) ?>');
            }
            // Pemantauan Anestesi Lokal
            $('.pemantauan_AnestesiLokal').click(function () {
                $('#view_pemantauan_AnestesiLokal').load('<?= base_url() ?>pengkajianprorad/FormPemantauanAnestesiLokal/index/<?= $nomr_segment ?>/<?= $nopen_segment ?>/<?= $nokun_segment ?>/<?= $this->uri->segment(7) ? $this->uri->segment(7) : "999999999/" ?>');
            });
            if ($('.pemantauan_AnestesiLokal').hasClass('active')) {
                $('#view_pemantauan_AnestesiLokal').load('<?= base_url() ?>pengkajianprorad/FormPemantauanAnestesiLokal/index/<?= $nomr_segment ?>/<?= $nopen_segment ?>/<?= $nokun_segment ?>/<?= $this->uri->segment(7) ? $this->uri->segment(7) : "999999999/" ?>');
            }
            // Pemantauan Tindakan Anestesi Lokal
            $('.pemTinAnesLokRj').click(function () {
                $('#view_pemTinAnesLokRj').load('<?= base_url() ?>pengkajianprorad/FormPemTinAnesLok/index/<?= $nomr_segment ?>/<?= $nopen_segment ?>/<?= $nokun_segment ?>/<?= $this->uri->segment(7) ? $this->uri->segment(7) : "999999999/" ?>');
            });
            if ($('.pemTinAnesLokRj').hasClass('active')) {
                $('#view_pemTinAnesLokRj').load('<?= base_url() ?>pengkajianprorad/FormPemTinAnesLok/index/<?= $nomr_segment ?>/<?= $nopen_segment ?>/<?= $nokun_segment ?>/<?= $this->uri->segment(7) ? $this->uri->segment(7) : "999999999/" ?>');
            }
            // Serah terima
            $('.serah_terima').click(function () {
                $('#view_serahTerima').load('<?= base_url() ?>prosedur/FormSerahTerima/index/<?= $nomr_segment ?>/<?= $nopen_segment ?>/<?= $nokun_segment ?>/<?= $this->uri->segment(7) ?>');
            });
            if ($('.serah_terima').hasClass('active')) {
                $('#view_serahTerima').load('<?= base_url() ?>prosedur/FormSerahTerima/index/<?= $nomr_segment ?>/<?= $nopen_segment ?>/<?= $nokun_segment ?>/<?= $this->uri->segment(7) ?>');
            }
            // Laporan Hasil Pemeriksaan
            $('.laporanHasilPemeriksaan').click(function () {
                $('#view_laporanHasilPemeriksaan').load('<?= base_url() ?>prosedur/LaporanHasilPemeriksaan/index/<?= $nomr_segment ?>/<?= $nopen_segment ?>/<?= $nokun_segment ?>');
            });

            if ($('.laporanHasilPemeriksaan').hasClass('active')) {
                $('#view_laporanHasilPemeriksaan').load('<?= base_url() ?>prosedur/LaporanHasilPemeriksaan/index/<?= $nomr_segment ?>/<?= $nopen_segment ?>/<?= $nokun_segment ?>');
            }
            // Scan Hasil Pemeriksaan
            $('.scanHasilPemeriksaan').click(function () {
                $('#view_scanHasilPemeriksaan').load('<?= base_url() ?>prosedur/LaporanHasilPemeriksaan/scan/<?= $nomr_segment ?>/<?= $nopen_segment ?>/<?= $nokun_segment ?>');
            });

            if ($('.scanHasilPemeriksaan').hasClass('active')) {
                $('#view_scanHasilPemeriksaan').load('<?= base_url() ?>prosedur/LaporanHasilPemeriksaan/scan/<?= $nomr_segment ?>/<?= $nopen_segment ?>/<?= $nokun_segment ?>');
            }
            // Keselamatan Tindakan Invasif
            $('.keselamatan_TindakanInvasif').click(function () {
                $('#view_keselamatan_TindakanInvasif').load('<?= base_url() ?>pengkajianprorad/FormKeselamatanTindakanInvasif/index/<?= $nomr_segment ?>/<?= $nopen_segment ?>/<?= $nokun_segment ?>/<?= $this->uri->segment(7) ? $this->uri->segment(7) : "999999999/" ?>');
            });
            if ($('.keselamatan_TindakanInvasif').hasClass('active')) {
                $('#view_keselamatan_TindakanInvasif').load('<?= base_url() ?>pengkajianprorad/FormKeselamatanTindakanInvasif/index/<?= $nomr_segment ?>/<?= $nopen_segment ?>/<?= $nokun_segment ?>/<?= $this->uri->segment(7) ? $this->uri->segment(7) : "999999999/" ?>');
            }
            // Persiapan Bronkoskopi
            $('.persiapan_Bronkoskopi').click(function () {
                $('#view_persiapan_Bronkoskopi').load('<?= base_url() ?>persiapanBronkoskopi/PersiapanBronkoskopi/index/<?= $nomr_segment ?>/<?= $nopen_segment ?>/<?= $nokun_segment ?>/<?= $this->uri->segment(7) ? $this->uri->segment(7) : "999999999/" ?>');
            });
            if ($('.persiapan_Bronkoskopi').hasClass('active')) {
                $('#view_persiapan_Bronkoskopi').load('<?= base_url() ?>persiapanBronkoskopi/PersiapanBronkoskopi/index/<?= $nomr_segment ?>/<?= $nopen_segment ?>/<?= $nokun_segment ?>/<?= $this->uri->segment(7) ? $this->uri->segment(7) : "999999999/" ?>');
            }
            // Pengkajian pra anestesi
            $('.pengkajianpraanestesi').click(function () {
                $('#view_pengkajianpraanestesi').load('<?= base_url() ?>anestesi/Pra_anestesi/index/<?= $nomr_segment ?>/<?= $nopen_segment ?>/<?= $nokun_segment ?>/<?= $this->uri->segment(6) ?>/<?= $this->uri->segment(7) ?>');
            });
            if ($('.pengkajianpraanestesi').hasClass('active')) {
                $('#view_pengkajianpraanestesi').load('<?= base_url() ?>anestesi/Pra_anestesi/index/<?= $nomr_segment ?>/<?= $nopen_segment ?>/<?= $nokun_segment ?>/<?= $this->uri->segment(6) ?>/<?= $this->uri->segment(7) ?>');
            }

            // pengkajian Pra Sedasi
            $('.pengkajianPraSedasi').click(function () {
                $('#view_pengkajianPraSedasi').load('<?= base_url() ?>sedasi/PengkajianPraSedasi/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            });
            if ($('.pengkajianPraSedasi').hasClass('active')) {
                $('#view_pengkajianPraSedasi').load('<?= base_url() ?>sedasi/PengkajianPraSedasi/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            }
            // _-_-_-_-_-_-_-_-_-_-_- END Prosedur Diagnostik _-_-_-_-_-_-_-_-_-_-_-

            // _-_-_-_-_-_-_-_-_-_-_- START IGD _-_-_-_-_-_-_-_-_-_-_-

            // Pelaksanaan pencegahan jatuh pada pasien geriatri (skala morse)
            $('.skala_Morse').click(function () {
                $('#view_skalaMorse').load('<?= base_url() ?>igd/SkalaMorse/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(7) ?>');
            });
            if ($('.skala_Morse').hasClass('active')) {
                $('#view_skalaMorse').load('<?= base_url() ?>igd/SkalaMorse/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(7) ?>');
            }
            // Edit Skala Ontario
            $(document).on('click', '.editSkalaOntario', function () {
                var id = $(this).data('id');
                $('#view_skalaMorse').load('<?= base_url() ?>igd/SkalaMorse/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>/' + id + '/' + id);
            });

            // Pemberian Intravena
            $('.pemberianIntravena').click(function () {
                $('#view_pemberianIntravena').load('<?= base_url() ?>igd/FormulirPemberianIntravena/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(7) ?>');
            });
            if ($('.pemberianIntravena').hasClass('active')) {
                $('#view_pemberianIntravena').load('<?= base_url() ?>igd/FormulirPemberianIntravena/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(7) ?>');
            }
            // pengkajian Risiko Jatuh Pasien Dewasa
            $('.pengkajianRisikoJatuhPasienDewasa').click(function () {
                $('#view_pengkajianRisikoJatuhPasienDewasa').load('<?= base_url() ?>igd/PengkajianRisikoJatuhPasienDewasa/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(7) ?>');
            });
            if ($('.pengkajianRisikoJatuhPasienDewasa').hasClass('active')) {
                $('#view_pengkajianRisikoJatuhPasienDewasa').load('<?= base_url() ?>igd/PengkajianRisikoJatuhPasienDewasa/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(7) ?>');
            }

            // _-_-_-_-_-_-_-_-_-_-_- END IGD _-_-_-_-_-_-_-_-_-_-_-

            // Pengkajian Gigi
            $('.pengkajianGigi').click(function () {
                $('#view_pengkajianGigi').load('<?= base_url() ?>pengkajianGigi/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            });
            if ($('.pengkajianGigi').hasClass('active')) {
                $('#view_pengkajianGigi').load('<?= base_url() ?>pengkajianGigi/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            }

            // Odontogram
            $('.odontogram').click(function () {
                $('#view_odontogram').load('<?= base_url() ?>odontogram/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            });
            if ($('.odontogram').hasClass('active')) {
                $('#view_odontogram').load('<?= base_url() ?>odontogram/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            }

            // Pemantauan Anastesi Lokal Tindakan Gigi
            $('.pemantauanAnastesiGigi').click(function () {
                $('#view_pemantauanAnastesiGigi').load('<?= base_url() ?>pemantauanAnastesiGigi/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            });
            if ($('.pemantauanAnastesiGigi').hasClass('active')) {
                $('#view_pemantauanAnastesiGigi').load('<?= base_url() ?>pemantauanAnastesiGigi/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            }

            // Pengkajian Aferesis
            $('.pengkajianAferesis').click(function () {
                $('#view_pengkajianAferesis').load('<?= base_url() ?>pengkajianAferesis/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            });
            if ($('.pengkajianAferesis').hasClass('active')) {
                $('#view_pengkajianAferesis').load('<?= base_url() ?>pengkajianAferesis/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            }

            // Persetujuan Tidakan Terapeutik Aferesis
            $('.persetujuanTindakanTerapeutikAferesis').click(function () {
                $('#view_persetujuanTindakanTerapeutikAferesis').load('<?= base_url() ?>persetujuanTindakanTerapeutikAferesis/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            });
            if ($('.persetujuanTindakanTerapeutikAferesis').hasClass('active')) {
                $('#view_persetujuanTindakanTerapeutikAferesis').load('<?= base_url() ?>persetujuanTindakanTerapeutikAferesis/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            }

            // BANK DARAH PEMANTAUAN DARAH
            $(document).on('click', '.pemberianDanPemantauanDarah', function () {
                $('#view_pemberianDanPemantauan').load('<?= base_url() ?>rekam_medis/rawat_inap/bankDarah/PemberianDanPemantauanDarah/index/<?= $this->uri->segment(3) . '/' . $this->uri->segment(4) . '/' . $this->uri->segment(5) ?>');
            });
            if ($('.pemberianDanPemantauanDarah').hasClass('active')) {
                $('#view_pemberianDanPemantauan').load('<?= base_url() ?>rekam_medis/rawat_inap/bankDarah/PemberianDanPemantauanDarah/index/<?= $this->uri->segment(3) . '/' . $this->uri->segment(4) . '/' . $this->uri->segment(5) ?>');
            }

            // Trombaferesis Dengan Mesin Haemonetics
            $('.TrombaferesisDenganMesinHaemonetics_tab').click(function () {
                $('#view_trombaferesisDenganMesinHaemonetics').load('<?= base_url() ?>bankdarah/TrombaferesisHaemo/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            });
            if ($('.TrombaferesisDenganMesinHaemonetics_tab').hasClass('active')) {
                $('#view_trombaferesisDenganMesinHaemonetics').load('<?= base_url() ?>bankdarah/TrombaferesisHaemo/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) . '/' . $this->uri->segment(7) ?>');
            }

            //Start Igd
            // Observasi dan Tindakan Keperawatan IGD
            $('.observasiIGD').click(function () {
                $('#view-observasi-igd').load('<?= base_url() ?>igd/ObservasiIGD/index/<?= $nomr_segment . '/' . $nokun_segment . '/' . $this->uri->segment(7) ?>');
            });
            if ($('.observasiIGD').hasClass('active')) {
                $('#view-observasi-igd').load('<?= base_url() ?>igd/ObservasiIGD/index/<?= $nomr_segment . '/' . $nokun_segment . '/' . $this->uri->segment(7) ?>');
            }

            // Serah terima
            $('.serah-terima-shift').click(function () {
                $('#view-serah-terima-shift').load('<?= base_url() ?>keperawatan/serahTerima/index/<?= $nokun_segment ?>');
            });
            if ($('.serah-terima-shift').hasClass('active')) {
                $('#view-serah-terima-shift').load('<?= base_url() ?>keperawatan/serahTerima/index/<?= $nokun_segment ?>');
            }

            // Berkas IPPJ
            let berkasPasien = $('#berkas-pasien-luar');
            berkasPasien.click(function () {
                $('#view-berkas-pasien-luar').load('<?= base_url() ?>filePendukungPasien/berkasPasien/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            });
            if (berkasPasien.hasClass('active')) {
                $('#view-berkas-pasien-luar').load('<?= base_url() ?>filePendukungPasien/berkasPasien/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            }

            // Berkas Scan
            let berkasScan = $('#berkas-scan');
            berkasScan.click(function () {
                $('#view-berkas-scan').load('<?= base_url() ?>rekam_medis/rawat_inap/scanberkas/Scan/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            });
            if (berkasPasien.hasClass('active')) {
                $('#view-berkas-scan').load('<?= base_url() ?>rekam_medis/rawat_inap/scanberkas/Scan/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            }

            //humptyDumptyScale
            $('.humptyDumptyScale').click(function () {
                $('#view_humptyDumptyScale').load('<?= base_url() ?>igd/humptyDumptyScale/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            });
            if ($('.humptyDumptyScale').hasClass("active")) {
                $('#view_humptyDumptyScale').load('<?= base_url() ?>igd/humptyDumptyScale/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            }
            //pelaksanaanPencegahanRj
            $('.pelaksanaanPencegahanRj').click(function () {
                $('#view_pelaksanaanPencegahanRj').load('<?= base_url() ?>igd/pelaksanaanPencegahanRj/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            });
            if ($('.pelaksanaanPencegahanRj').hasClass("active")) {
                $('#view_pelaksanaanPencegahanRj').load('<?= base_url() ?>igd/pelaksanaanPencegahanRj/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            }
            // _-_-_-_-_-_-_-_-_-_-_- END Igd _-_-_-_-_-_-_-_-_-_-_-

            // Catatan Edukasi
            $('.catatan-edukasi').click(function () {
                $('#view-catatan-edukasi').load("<?= base_url('catatanEdukasi/index/' . $nokun_segment) ?>");
            });
            if ($('.catatan-edukasi').hasClass('active')) {
                $('#view-catatan-edukasi').load("<?= base_url('catatanEdukasi/index/' . $nokun_segment) ?>");
            }

            // Resume Medis
            $('.resume-medis').click(function () {
                $('#view-resume-medis').load('<?= base_url() ?>ResumeMedis/index/<?= $nomr_segment . '/' . $nokun_segment . '/' . $this->uri->segment(7) ?>');
            });
            if ($('.resume-medis').hasClass('active')) {
                $('#view-resume-medis').load('<?= base_url() ?>ResumeMedis/index/<?= $nomr_segment . '/' . $nokun_segment . '/' . $this->uri->segment(7) ?>');
            }

            // Epidemologi COVID
            $('.formulirEpidemologiCovidRj').click(function () {
                $('#view_formulirEpidemologiCovidRj').load('<?= base_url() ?>rekam_medis/covidRj/FormEpidCovidRj/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            });
            if ($('.formulirEpidemologiCovidRj').hasClass('active')) {
                $('#view_formulirEpidemologiCovidRj').load('<?= base_url() ?>rekam_medis/covidRj/FormEpidCovidRj/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment . '/' . $this->uri->segment(6) ?>');
            }

            // _-_-_-_-_-_-_-_-_-_-_- START KONSULTASI _-_-_-_-_-_-_-_-_-_-_-
            // Konsultasi
            $('.konsultasi').click(function () {
                $('#view-konsultasi').load('<?= base_url() ?>konsultasi/Konsultasi/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            });
            if ($('.konsultasi').hasClass('active')) {
                $('#view-konsultasi').load('<?= base_url() ?>konsultasi/Konsultasi/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            }
            // Eresep
            $('.Eresep').click(function () {
                $('#view-eresep-side-rj').html('');
                $('#view-eresep').load('<?= base_url() ?>PengkajianAwal/eresep/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            });

            // ct_Simulator
            $('.ct_Simulator').click(function () {
                $('#view-ct_Simulator').html('');
                $('#view-ct_Simulator').load('<?= base_url() ?>radioterapi/Ct_simulator/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            });
            if ($('.ct_Simulator').hasClass('active')) {
                $('#view-ct_Simulator').html('');
                $('#view-ct_Simulator').load('<?= base_url() ?>radioterapi/Ct_simulator/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            }


            // Eresep-Side-RJ
            $('#menu-e-resep-side-rj').click(function () {
                $('#view-eresep').html('');
                $('#view-eresep-side-rj').load('<?= base_url() ?>PengkajianAwal/eresep/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            });

            // konsultasiEksternal
            $('.konsultasiEksternal').click(function () {
                $('#view_konsultasiEksternal').load('<?= base_url() ?>transferRuangan/rujukanEksternal/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            });
            if ($('.konsultasiEksternal').hasClass('active')) {
                $('#view_konsultasiEksternal').load('<?= base_url() ?>transferRuangan/rujukanEksternal/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            }
            // _-_-_-_-_-_-_-_-_-_-_- END KONSULTASI _-_-_-_-_-_-_-_-_-_-_-

            // Mulai Formulir Perpindahan Pasien Antar Ruang
            $('#fpr').click(function () {
                $('#view-fpr').load("<?= base_url('transferRuangan/FormulirPindahRuangan/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            });
            if ($('#fpr').hasClass('active')) {
                $('#view-fpr').load("<?= base_url('transferRuangan/FormulirPindahRuangan/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            }
            // Akhir Formulir Perpindahan Pasien Antar Ruang
            // formulirKriteriaRawatIntensif
            $('.formulirKriteriaRawatIntensif').click(function () {
                $('#view_formulirKriteriaRawatIntensif').load('<?= base_url() ?>igd/FormulirKriteriaRawatIntensif/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            });
            if ($('.formulirKriteriaRawatIntensif').hasClass('active')) {
                $('#view_formulirKriteriaRawatIntensif').load('<?= base_url() ?>igd/FormulirKriteriaRawatIntensif/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            }
            // formulirKriteriaRawatPicu
            $('.formulirKriteriaRawatPicu').click(function () {
                $('#view_formulirKriteriaRawatPicu').load('<?= base_url() ?>igd/FormulirKriteriaRawatPicu/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            });
            if ($('.formulirKriteriaRawatPicu').hasClass('active')) {
                $('#view_formulirKriteriaRawatPicu').load('<?= base_url() ?>igd/FormulirKriteriaRawatPicu/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            }

            // formulir epidemiologi covid R.jalan
            $('.formulirKriteriaRawatPicu').click(function () {
                $('#view_formulirKriteriaRawatPicu').load('<?= base_url() ?>igd/FormulirKriteriaRawatPicu/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            });
            if ($('.formulirKriteriaRawatPicu').hasClass('active')) {
                $('#view_formulirKriteriaRawatPicu').load('<?= base_url() ?>igd/FormulirKriteriaRawatPicu/index/<?= $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment ?>');
            }

            // CPPT LIST
            $(document).on('click', '.cpptList', function () {
                $('#view_cpptList').load("<?= base_url('cpptList/' . $nomr_segment . '/' . $nopen_segment) ?>");
            });
            if ($('.cpptList').hasClass('active')) {
                $('#view_cpptList').load("<?= base_url('cpptList/' . $nomr_segment . '/' . $nopen_segment) ?>");
            }

            $(document).on('change', '.pendaftaran', function () {
                var id = $(this).val();
                $('.cpptList').trigger('click');
                $('#view_cpptList').load("<?= base_url('cpptList/') . $nomr_segment ?>/" + id);
            });

            // Pengkajian list
            let listPengkajian = $('#pengkajian-list');
            listPengkajian.click(function () {
                $('#view-pengkajian-list').load('<?= base_url('PengkajianAwal/historySidebar/' . $nomr_segment) ?>');
            });
            if (listPengkajian.hasClass('active')) {
                $('#view-pengkajian-list').load('<?= base_url('PengkajianAwal/historySidebar/' . $nomr_segment) ?>');
            }

            // berkas list
            let listBerkas = $('#berkas-list');
            listBerkas.click(function () {
                $('#view-berkas-list').load('<?= base_url('rekam_medis/UploadRM/historyRM/' . $nomr_segment) ?>');
            });
            if (listBerkas.hasClass('active')) {
                $('#view-berkas-list').load('<?= base_url('rekam_medis/UploadRM/historyRM/' . $nomr_segment) ?>');
            }

            // berkas list
            let faktorresiko = $('#faktor-resiko');
            faktorresiko.click(function () {
                $('#view-faktor-resiko').load('<?= base_url('rekam_medis/faktorresiko/index/' . $nomr_segment) ?>');
            });
            if (faktorresiko.hasClass('active')) {
                $('#view-faktor-resiko').load('<?= base_url('rekam_medis/faktorresiko/index/' . $nomr_segment) ?>');
            }

            // Protokol Kemoterapi Anak
            let proKemAnak = $('#prokem-anak');
            proKemAnak.click(function () {
                $('#view-pro-kem-anak').load('<?= base_url() ?>ProKemAnak/<?= $nokun_segment ?>');
            });
            if (proKemAnak.hasClass('active')) {
                $('#view-pro-kem-anak').load('<?= base_url() ?>ProKemAnak/<?= $nokun_segment ?>');
            }

            // eTimja
            eTimja.click(function () {
                $('#view-etimja').load("<?= base_url('Etimja/form/' . $nokun_segment) ?>");
            });
            if (eTimja.hasClass('active')) {
                $('#view-etimja').load("<?= base_url('Etimja/form/' . $nokun_segment) ?>");
            }

            // Prosedur Diagnostik
            $('.prosedur').click(function () {
                $('#view_prosedur').load('<?= base_url() ?>prosedurDiagnostik/<?= $nokun_segment ?>');
            });
            if ($('.prosedur').hasClass('active')) {
                $('#view_prosedur').load('<?= base_url() ?>prosedurDiagnostik/<?= $nokun_segment ?>');
            }

            // Laboratorium Patologi Klinik (Baru)
            let lpk = $('#lpk');
            lpk.click(function () {
                $('#view-lpk').load("<?= base_url('penunjang/PatologiKlinik/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            });
            if (lpk.hasClass('active')) {
                $('#view-lpk').load("<?= base_url('penunjang/PatologiKlinik/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            }

            // Laboratorium Patologi Anatomi
            let lpa = $('#lpa');
            lpa.click(function () {
                $('#view-lpa').load("<?= base_url('patologiAnatomi/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            });
            if (lpa.hasClass('active')) {
                $('#view-lpa').load("<?= base_url('patologiAnatomi/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            }

            // Mulai Manajemen Pelayanan Pasien
            // Mulai Skrining Pasien Membutuhkan Manajer Pelayanan Pasien
            let smpp = $('#smpp');
            smpp.click(function () {
                $('#view-smpp').load("<?= base_url('MPP/Skrining/' . $nokun_segment) ?>");
            });
            if (smpp.hasClass('active')) {
                $('#view-smpp').load("<?= base_url('MPP/Skrining/' . $nokun_segment) ?>");
            }
            // Akhir Skrining Pasien Membutuhkan Manajer Pelayanan Pasien

            // Mulai Asesmen Awal Manajer Pelayanan Pasien
            let ampp = $('#ampp');
            ampp.click(function () {
                $('#view-ampp').load("<?= base_url('MPP/Asesmen/' . $nokun_segment) ?>");
            });
            if (ampp.hasClass('active')) {
                $('#view-ampp').load("<?= base_url('MPP/Asesmen/' . $nokun_segment) ?>");
            }
            // Akhir Implementasi Awal Manajer Pelayanan Pasien

            // Mulai Implementasi
            let impp = $('#impp');
            impp.click(function () {
                $('#view-impp').load("<?= base_url('MPP/Implementasi/' . $nokun_segment) ?>");
            });
            if (impp.hasClass('active')) {
                $('#view-impp').load("<?= base_url('MPP/Implementasi/' . $nokun_segment) ?>");
            }
            // Akhir Implementasi
            // Akhir Manajemen Pelayanan Pasien

            // Mulai HIV
            // Mulai Ikhtisar Follow-up Perawatan Pasien HIV dan Terapi Antiretroviral
            let fuart = $('#fuart');
            fuart.click(function () {
                $('#view-fuart').load("<?= base_url('HIV/FollowUp/' . $nokun_segment) ?>");
            });
            if (fuart.hasClass('active')) {
                $('#view-fuart').load("<?= base_url('HIV/FollowUp/' . $nokun_segment) ?>");
            }
            // Akhir Ikhtisar Follow-up Perawatan Pasien HIV dan Terapi Antiretroviral
            // Mulai Formulir Rujukan
            let rart = $('#rart');
            rart.click(function () {
                $('#view-rart').load("<?= base_url('HIV/Rujukan/' . $nokun_segment) ?>");
            });
            if (rart.hasClass('active')) {
                $('#view-rart').load("<?= base_url('HIV/Rujukan/' . $nokun_segment) ?>");
            }
            // Akhir Formulir Rujukan
            // Akhir HIV

            // Mulai Paliatif
            // Mulai Family Meeting
            $('.ffm').click(function () {
                $('#view_ffm').load("<?= base_url('rekam_medis/rawat_inap/paliatif/FamilyMeeting/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            });
            if ($('.ffm').hasClass("active")) {
                $('#view_ffm').load("<?= base_url('rekam_medis/rawat_inap/paliatif/FamilyMeeting/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            }
            // Akhir Family Meeting
            // Mulai Hospital Anxiety and Depression Scale (HADS)
            $('.hads').click(function () {
                $('#view_hads').load("<?= base_url('rekam_medis/rawat_inap/paliatif/HADS/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            });
            if ($('.hads').hasClass("active")) {
                $('#view_hads').load("<?= base_url('rekam_medis/rawat_inap/paliatif/HADS/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            }
            // Akhir Hospital Anxiety and Depression Scale (HADS)
            // Mulai Instrumen Kebutuhan Paliatif
            $('.iikpp').click(function () {
                $('#view_iikpp').load("<?= base_url('rekam_medis/rawat_inap/paliatif/IIKPP/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            });
            if ($('.iikpp').hasClass("active")) {
                $('#view_iikpp').load("<?= base_url('rekam_medis/rawat_inap/paliatif/IIKPP/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            }
            // Akhir Instrumen Kebutuhan Paliatif
            // Mulai Kriteria Persiapan Pulang Pasien (KPPPP)
            $('.kpppp').click(function () {
                $('#view_kpppp').load("<?= base_url('rekam_medis/rawat_inap/paliatif/KPPPP/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            });
            if ($('.kpppp').hasClass("active")) {
                $('#view_kpppp').load("<?= base_url('rekam_medis/rawat_inap/paliatif/KPPPP/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            }
            // Akhir Kriteria Persiapan Pulang Pasien (KPPPP)
            // Akhir Paliatif

            // Mulai karyawan
            // Mulai surat sakit
            let ss = $('#ss');
            ss.click(function () {
                $('#view-ss').load("<?= base_url('karyawan/SuratSakit/' . $nokun_segment) ?>");
            });
            if (ss.hasClass('active')) {
                $('#view-ss').load("<?= base_url('karyawan/SuratSakit/' . $nokun_segment) ?>");
            }
            // Akhir surat sakit
            // Akhir karyawan

            $('.treatmentDose').click(function () {
                $('#view_doseRiRadio').load("<?= base_url('radioterapi/TreatmentDose/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            });
            if ($('.treatmentDose').hasClass("active")) {
                $('#view_doseRiRadio').load("<?= base_url('radioterapi/TreatmentDose/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            }
            $('.catatanRadioTerapi').click(function () {
                $('#viewCatatanRadioTerapi').load("<?= base_url('radioterapi/CatatanRadioTerapi/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            });
            if ($('.catatanRadioTerapi').hasClass("active")) {
                $('#viewCatatanRadioTerapi').load("<?= base_url('radioterapi/CatatanRadioTerapi/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            }
            $('.simulatorInformation').click(function () {
                let nomr = "<?= $nomr_segment ?>";
                let nopen = "<?= $nopen_segment ?>";
                let nokun = "<?= $nokun_segment ?>";

                $('#viewSimulatorInformation').load("<?= base_url('radioterapi/rawat_jalan/Radioterapi/indexSimu') ?>?nomr=" + nomr + "&nopen=" + nopen + "&nokun=" + nokun);
            });

            if ($('.simulatorInformation').hasClass("active")) {
                let nomr = "<?= $nomr_segment ?>";
                let nopen = "<?= $nopen_segment ?>";
                let nokun = "<?= $nokun_segment ?>";

                $('#viewSimulatorInformation').load("<?= base_url('radioterapi/rawat_jalan/Radioterapi/indexSimu') ?>?nomr=" + nomr + "&nopen=" + nopen + "&nokun=" + nokun);
            }
            $('.penjadwalanradioterapi').click(function () {
                $('#view_penjadwalanradioterapi').load("<?= base_url('radioterapi/Penjadwalanradioterapi/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            });
            if ($('.penjadwalanradioterapi').hasClass("active")) {
                $('#view_penjadwalanradioterapi').load("<?= base_url('radioterapi/Penjadwalanradioterapi/index/' . $nomr_segment . '/' . $nopen_segment . '/' . $nokun_segment) ?>");
            }
        });

        $("#tbakDpjpp").select2();
        // Webcam.set({
        //     width: 250,
        //     height: 150,
        //     image_format: 'jpeg',
        //     jpeg_quality: 90
        // });

        // Webcam.attach('#my_camera');

        // preload shutter audio clip
        // var shutter = new Audio();
        // shutter.autoplay = false;
        // shutter.src = navigator.userAgent.match(/Firefox/) ? 'shutter.ogg' :
        //     "<!?=  base_url('assets/admin/assets/plugins/webcamjs-master/shutter.mp3') ?>";

        // $(document).on("click", ".take-Photo", function() {
        //     var nomr = $("#nomrTakePhoto").val();
        //     var oleh = $("#olehTakePhoto").val();
        //     // play sound effect
        //     shutter.play();

        //     // take snapshot and get image data
        //     Webcam.snap(function(data_uri) {
        //         // display results in page
        //         document.getElementById('my_camera_result').innerHTML =
        //             '<img id="imageprev" src="' + data_uri + '"/>';
        //     });

        //     // Get base64 value from <img id='imageprev'> source
        //     var base64image = document.getElementById("imageprev").src;

        //     Webcam.upload(base64image, "<!?=  base_url('pengkajianAwal/UploadTakePhoto') ?>", function(
        //         code, text) {

        //         var name_pic = text;

        //         $.ajax({
        //             type: "POST",
        //             url: "<!?=  base_url('pengkajianAwal/insert_takePhoto') ?>",
        //             data: {
        //                 nomr: nomr,
        //                 name_pic: name_pic,
        //                 oleh: oleh
        //             },
        //             success: function(data) {
        //                 Command: toastr["info"]("Foto Berhasil disimpan")

        //                 toastr.options = {
        //                     "closeButton": false,
        //                     "debug": false,
        //                     "newestOnTop": false,
        //                     "progressBar": false,
        //                     "positionClass": "toast-top-right",
        //                     "preventDuplicates": false,
        //                     "onclick": null,
        //                     "showDuration": "300",
        //                     "hideDuration": "1000",
        //                     "timeOut": "2000",
        //                     "extendedTimeOut": "1000",
        //                     "showEasing": "swing",
        //                     "hideEasing": "linear",
        //                     "showMethod": "fadeIn",
        //                     "hideMethod": "fadeOut"
        //                 }
        //                 $('#takePhoto').modal('hide');
        //                 $('#my_camera_result').html("");
        //             }
        //         });
        //     });
        // });

        $('#myFormUpload').submit(function (e) {
            e.preventDefault();
            $.ajax({
                url: '<?= base_url() ?>pengkajianAwal/uploadPhoto',
                type: "post",
                data: new FormData(this),
                processData: false,
                contentType: false,
                cache: false,
                async: false,
                success: function (data) {
                    if (data == 'true') {
                        $('#uploadPhoto').modal('hide');
                    } else {
                        Command: toastr["error"]("Foto terlalu besar, max 2mb")

                        toastr.options = {
                            "closeButton": false,
                            "debug": false,
                            "newestOnTop": false,
                            "progressBar": false,
                            "positionClass": "toast-top-right",
                            "preventDuplicates": false,
                            "onclick": null,
                            "showDuration": "300",
                            "hideDuration": "1000",
                            "timeOut": "5000",
                            "extendedTimeOut": "1000",
                            "showEasing": "swing",
                            "hideEasing": "linear",
                            "showMethod": "fadeIn",
                            "hideMethod": "fadeOut"
                        }
                        $('#uploadPhoto').modal('hide');
                    }
                }
            });
        });

        <?php if (isset($getNomr['TUJUAN_ORDER_RADIOLOGI'])) { ?>
            $('.tujuanRadiologiSimpelRj').select2({
                placeholder: '[ Pilih Tujuan ]'
            }).val(<?= $getNomr['TUJUAN_ORDER_RADIOLOGI'] ?? 0 ?>).trigger('change');
        <?php } else { ?>
            $('.tujuanRadiologiSimpelRj').select2({
                placeholder: '[ Pilih Tujuan ]'
            });
        <?php } ?>

        // Pilih dokter perujuk radiologi
        $('#dokterPerujukRadiologiRj').select2({
            placeholder: '[ Pilih Dokter Perujuk ]'
        }).val('<?= $getNomr['ID_DOKTER'] ?>').trigger('change');

        $("#kirimRadiologi").on('click', function (event) {
            var data = $("#formOrderRadiologi").serializeArray();
            var klinisi = $("#klinisiRadiologiOrder").val();
            // var alasan = $("#alasanRadiologiOrder").val();
            var tglkemo = $(".tanggalKemoDiagnosaKlinisInput").val();
            var tgloperasi = $(".tanggalOperasiDiagnosaKlinisInput").val();
            var tglulang = $(".tanggalUlangDiagnosaKlinisInput").val();
            var tglkontrol = $(".tanggalKontrolDiagnosaKlinisInput").val();
            var tglRecist = $(".tanggalRecistDiagnosaKlinisInput").val();
            var tglLainnya = $(".tanggalLainnyaDiagnosaKlinisInput").val();
            var keteranganLain = $(".keteranganDiagnosaKlinisInput").val();

            if (klinisi == "") {
                alert('Mohon Klinis di isi');
            } else if (!$(".diagnosaKlinis").is(":checked")) {
                alert('Mohon Tujuan Pemeriksaan di isi');
            } else if ($(".diagnosaKlinis:checked").val() == 5907 && tglkemo == "") {
                alert('Mohon Tanggal Kemo di isi');
            } else if ($(".diagnosaKlinis:checked").val() == 5908 && tgloperasi == "" && !$(".cekStgRenOpe").is(":checked")) {
                alert('Mohon Tanggal Operasi di isi');
            } else if ($(".diagnosaKlinis:checked").val() == 6015 && tglulang == "" && !$(".cekStgRenUlg").is(":checked")) {
                alert('Mohon Tanggal ulang di isi');
            } else if ($(".diagnosaKlinis:checked").val() == 5909 && tglkontrol == "" && !$(".cekStgevaluasi").is(":checked")) {
                alert('Mohon Tanggal Kontrol diisi');
            } else if ($(".diagnosaKlinis:checked").val() == 6225 && tglRecist == "" && !$(".cekStgRenRec").is(":checked")) {
                alert('Mohon Tanggal Recist diisi');
            } else if ($(".diagnosaKlinis:checked").val() == 6226 && tglLainnya == "" && !$(".cekStgKetRec").is(":checked")) {
                alert('Mohon Tanggal Lainnya diisi');
            } else {
                $.ajax({
                    url: "<?= base_url('pengkajianAwal/orderRadiologi') ?>",
                    method: "POST",
                    data: data,
                    dataType: 'json',
                    success: function (data) {
                        if (data.status == 'success') {
                            alertify.success('Berhasil Dikirim');
                            $.each($("input[name = 'tindakanRadiologi[]']:checked"), function () {
                                var id = $(this).data('idcek');
                                var idsimpel = $(this).val();
                                $.ajax({
                                    url: "<?= base_url('rekam_medis/penunjang/Radiologi/simpanRadDetailView') ?>",
                                    method: "POST",
                                    data: {
                                        id: id,
                                        idsimpel: idsimpel,
                                        kode: data.kode
                                    }
                                });

                            });
                            location.reload();
                        } else {
                            alertify.warning('Internal Server Error');
                        }
                    }
                });
            }
            event.preventDefault();
        });


        // Datetime Picker
        jQuery.datetimepicker.setLocale('id');

        //Simpan Form PD
        $("#simpanDataPD").on('click', function (event) {
            var data = $("#FormPD").serializeArray();
            $.ajax({
                url: "<?= base_url('pengkajianAwal/simpanOrderProsedurDiagnostik') ?>",
                method: "POST",
                data: data,
                success: function (data) {
                    alertify.success('Order Terkirim')
                    location.reload();
                }
            });
            event.preventDefault();
        });

        // History Order
        $('#viewDetilORderRadiologiRj').on('show.bs.modal', function (e) {
            var id = $(e.relatedTarget).data('id');
            $.ajax({
                type: 'POST',
                url: '<?= base_url() ?>PengkajianAwal/detilOrderRadiologi',
                data: {
                    id: id
                },
                success: function (data) {
                    $('#hasilOrderLABRad').html(data);
                }
            });
        });

        //Simpan Form Deteksi Dini
        $("#FormUjiKesehatandanDeteksiDini").submit(function (event) {
            data = $("#FormUjiKesehatandanDeteksiDini").serializeArray();
            $.ajax({
                url: "<?= base_url('pengkajianAwal/actionDeteksiDini/tambah') ?>",
                method: "POST",
                data: data,
                success: function (data) {
                    alertify.success('Data Tersimpan');
                    location.reload();
                }
            });
            event.preventDefault();
        });
        $(document).on('click', '#batalPK', function () {
            var id = $(this).data('id');
            alertify.confirm('Konfirmasi', 'Pilih Ok, jika setuju untuk membatalkan!',
                function () {
                    $.ajax({
                        dataType: 'json',
                        url: '<?= base_url() ?>PengkajianAwal/batalPK',
                        method: 'POST',
                        data: {
                            id: id
                        },
                        success: function (data) {
                            if (data.status == 'success') {
                                alertify.success(data.pesan);
                                $('#view-detail-order-lab-pk').modal('hide');

                                // Refresh the DataTable instead of reloading the page
                                $('#history-order-lab-pk').DataTable().ajax.reload();
                            } else {
                                alertify.warning(data.pesan);
                            }
                        }
                    });
                },
                function () { }
            );
        });

        $(document).on('click', '#orderUlangPK', function () {
            var id_order = $(this).data('id');
            alertify.confirm(
                'Konfirmasi',
                'Apakah Anda ingin melakukan order ulang Lab PK tersebut?',
                function () {
                    alertify.message('Memproses permintaan...');
                    $.ajax({
                        dataType: 'json',
                        url: '<?= base_url() ?>PengkajianAwal/orderUlangPk',
                        method: 'POST',
                        data: {
                            id_order: id_order,
                            ruang_awal: '<?= $getNomr['ID_RUANGAN'] ?>',
                            id_kunjungan: '<?= $getNomr['NOKUN'] ?>'
                        },
                        success: function (data) {
                            if (data.status === 'success') {
                                alertify.success(data.pesan || 'Order ulang berhasil.');
                                $('#view-detail-order-lab-pk').modal('hide');

                                // Refresh the DataTable instead of reloading the page
                                $('#history-order-lab-pk').DataTable().ajax.reload();
                            } else {
                                alertify.warning(data.pesan || 'Gagal melakukan order ulang.');
                            }
                        },
                        error: function () {
                            alertify.error('Terjadi kesalahan pada server. Silakan coba lagi.');
                        }
                    });
                },
                function () { }
            );
        });

        $('#buka_hasil_lab').click(function () {
            var norm = "<?= $getNomr['NORM'] ?>";
            var tgls = moment("<?= $cekHasilLabPk['MASUK'] ?? null ?>").format("LLL");
            $('#modal').modal('show');
            $('#modal .modal-body').html('');
            $('#modal .modal-title').html('Hasil Lab Patologi Klinik');
            $('#modal .modal-body').append('<div class="row jarak"><div class="col-sm-3"><p class="text-white">Tanggal Lab: </p></div><div class="col-sm-3"><span class="text-white">' + tgls + '</span></div></div>');
            // $('#nokun_pk').select2({
            //     dropdownParent: $("#modal")
            // });
            // var kunjunganPK = getJSON('<?= base_url() ?>rekam_medis/medis/kunjungan_pk_baru', {
            //     norm: norm
            // });
            // if (kunjunganPK.length !== 0) {
            //     $.each(kunjunganPK, function (index, element) {
            //         var html = '<option value="' + element.ID + '">' + moment(element.DESKRIPSI).format("LLL") + ' - ' + element.TINDAKAN + '</option>';
            //         $('#nokun_pk').append(html);
            //     });
            // } else {
            //     $('#nokun_pk').html('');
            // }
            $('#modal .modal-body').append('<div class="row">' +
                '<div class="col-lg-6">' +
                '<div class="form-group">' +
                '<table class="table" id="tbl_detail_kunjungan_pk_kritis" style="background-color:#9ccbe2; color: #242a30; font-size: 13px; line-height:15px;">' +
                '</table>' +
                '</div>' +
                '</div>' +
                '<div class="col-lg-6">' +
                '<div class="form-group">' +
                '<table class="table" id="tbl_tanggal_pk_kritis" style="background-color: #f7ca71; color: #242a30; font-size: 13px; line-height:15px;">' +
                '</table>' +
                '</div>' +
                '</div>' +
                '</div>');
            $('#modal .modal-body').append('<div class="table-responsive">' +
                '<table  class="table table-bordered table-hover dt-responsive dt-responsive nowrap table-custom dataTable" id="tbl_history_patologi_klinik_kritis" cellspacing="0" width="100%">' +
                '<thead>' +
                '<tr class="table-tr-custom">' +
                '<th>No</th>' +
                '<th>Parameter</th>' +
                '<th>Hasil</th>' +
                '<th>Nilai Rujukan</th>' +
                '<th>Satuan</th>' +
                '<th>Keterangan</th>' +
                '</tr>' +
                '</thead>' +
                '<tbody>' +
                '</tbody>' +
                '</table>' +
                '</div>');
            // load_detail_pk();
            $('#nokun_pk').on('change', function () {
                tbl_history_patologi_klinik_kritis.ajax.reload(null, false);
                // load_detail_pk();
            });

            var tbl_history_patologi_klinik_kritis = $('#tbl_history_patologi_klinik_kritis').DataTable({
                "sPaginationType": "full_numbers",
                "responsive": true,
                "bPaginate": true,
                "lengthMenu": [
                    [1, 5, 10, 25, 50, 100, -1],
                    [1, 5, 10, 25, 50, 100, "All"]
                ],
                "processing": false,
                "serverSide": false,
                "bFilter": true,
                "bLengthChange": true,
                "iDisplayLength": 10,
                "ordering": false,
                "order": [],
                "ajax": {
                    url: '<?= base_url() ?>rekam_medis/penunjang/PatologiKlinik/datatablesKritis',
                    type: "POST",
                    data: function (data) {
                        data.nokun = "<?= $cekNokunHasilLabPk['NOKUN'] ?>";
                        data.kritis = '1';
                    }
                },
                "fnRowCallback": function (nRow, aData, iDisplayIndex, iDisplayIndexFull) {
                    if (aData[6] != "" && aData[6] != null) {
                        $('td', nRow).css('background-color', '#d9534f');
                    }
                },
                "columnDefs": [{
                    "visible": false,
                    "targets": 6
                }]
            });

            $.ajax({
                url: "<?= base_url('rekam_medis/penunjang/PatologiKlinik/simpanLogHasilLabKritis') ?>",
                method: 'POST',
                dataType: 'json',
                data: {
                    norm: "<?= $getNomr['NORM'] ?>",
                    nokun: "<?= $cekNokunHasilLabPk['NOKUN'] ?>",
                    tgl_lab: "<?= $detailKritis['TGL'] ?>",
                    tgl_sampling: "<?= $tanggalKritis['retrieved_dt'] ?>",
                    tgl_hasil: "<?= $tanggalKritis['authorization_date'] ?>"
                },
                success: function (resp) {
                    $("#rowKritis").hide();
                },
                error: function () { }
            });

        });


        // START HISTORY PATOLOGI KLINIK
        $('#history_patologi_klinik').click(function () {
            var norm = "<?= $getNomr['NORM'] ?>";
            $('#modal').modal('show');
            $('#modal .modal-body').html('');
            $('#modal .modal-title').html('History Hasil Lab Patologi Klinik');
            $('#modal .modal-body').append('<div class="row jarak"><div class="col-sm-3"><p class="text-white">History Kunjungan : </p></div><div class="col-sm-9"><select name="nokun_pk" id="nokun_pk" class="form-control select2"></select></div></div>');
            $('#nokun_pk').select2();
            var kunjunganPK = getJSON('<?= base_url() ?>rekam_medis/medis/kunjungan_pk', {
                norm: norm
            });
            if (kunjunganPK.length !== 0) {
                $.each(kunjunganPK, function (index, element) {
                    var html = '<option value="' + element.ID + '">' + moment(element.DESKRIPSI).format("LLL") + '</option>';
                    $('#nokun_pk').append(html);
                });
            } else {
                $('#nokun_pk').html('');
            }
            $('#modal .modal-body').append('<div class="row">' +
                '<div class="col-lg-6">' +
                '<div class="form-group">' +
                '<table class="table" id="tbl_detail_kunjungan_pk" style="background-color:#9ccbe2; color: #242a30; font-size: 13px; line-height:15px;">' +
                '</table>' +
                '</div>' +
                '</div>' +
                '<div class="col-lg-6">' +
                '<div class="form-group">' +
                '<table class="table" id="tbl_tanggal_pk" style="background-color: #f7ca71; color: #242a30; font-size: 13px; line-height:15px;">' +
                '</table>' +
                '</div>' +
                '</div>' +
                '</div>');
            $('#modal .modal-body').append('<div class="table-responsive">' +
                '<table  class="table table-bordered table-hover dt-responsive dt-responsive nowrap table-custom dataTable" id="tbl_history_patologi_klinik" cellspacing="0" width="100%">' +
                '<thead>' +
                '<tr class="table-tr-custom">' +
                '<th>No</th>' +
                '<th>Parameter</th>' +
                '<th>Hasil</th>' +
                '<th>Nilai Rujukan</th>' +
                '<th>Satuan</th>' +
                '<th>Keterangan</th>' +
                '</tr>' +
                '</thead>' +
                '<tbody>' +
                '</tbody>' +
                '</table>' +
                '</div>');
            load_detail_pk();
            $('#nokun_pk').on('change', function () {
                tbl_history_patologi_klinik.ajax.reload(null, false);
                load_detail_pk();
            });

            var tbl_history_patologi_klinik = $('#tbl_history_patologi_klinik').DataTable({
                "sPaginationType": "full_numbers",
                "responsive": true,
                "bPaginate": true,
                "lengthMenu": [
                    [1, 5, 10, 25, 50, 100, -1],
                    [1, 5, 10, 25, 50, 100, "All"]
                ],
                "processing": false,
                "serverSide": true,
                "bFilter": true,
                "bLengthChange": true,
                "iDisplayLength": 10,
                "ordering": false,
                "order": [],
                "ajax": {
                    url: '<?= base_url() ?>rekam_medis/penunjang/PatologiKlinik/datatables',
                    type: "POST",
                    data: function (data) {
                        data.nokun = $('#nokun_pk').val();
                    }
                },
                "fnRowCallback": function (nRow, aData, iDisplayIndex, iDisplayIndexFull) {
                    if (aData[7] != "" && aData[7] != null) {
                        $('td', nRow).css('background-color', '#d9534f');
                    }
                },
                "columnDefs": [{
                    "visible": false,
                    "targets": 6
                }]
            });
        });

        function load_detail_pk() {
            var data = getJSON('<?= base_url() ?>rekam_medis/medis/detail_kunjungan_pk', {
                nokun: $('#nokun_pk').val()
            });

            $('#tbl_detail_kunjungan_pk').html('<tr>' +
                '<td>Tanggal Lab</td>' +
                '<td>' + moment(data.detail["TGL"]).format("LLL") + '</td>' +
                '</tr>' +
                '<tr>' +
                '<td>Dokter Perujuk</td>' +
                '<td>' + text(data.detail["NMDA"]) + '</td>' +
                '</tr>' +
                '<tr>' +
                '<td>Dokter Lab</td>' +
                '<td>' + text(data.detail["NMDL"]) + '</td>' +
                '</tr>' +
                '<tr>' +
                '<td>Ruang Asal Order</td>' +
                '<td>' + text(data.detail["RUANGASAL"]) + '</td>' +
                '</tr>');
            $('#tbl_tanggal_pk').html('<tr>' +
                '<td>Tanggal Sampling</td>' +
                '<td>' + moment(data.tanggal["retrieved_dt"]).format("LLL") + '</td>' +
                '</tr>' +
                '<tr>' +
                '<td>Tanggal Hasil</td>' +
                '<td>' + moment(data.tanggal["authorization_date"]).format("LLL") + '</td>' +
                '</tr>');

        }
        // END HISTORY PATOLOGI KLINIK

        // START HISTORY RADIOLOGI
        $('#history_radiologi').click(function () {
            var norm = "<?= $getNomr['NORM'] ?>";
            $('#modal').modal('show');
            $('#modal .modal-body').html('');
            $('#modal .modal-title').html('History Hasil Radiologi');
            $('#modal .modal-body').append('<div class="row jarak"><div class="col-sm-3"><p class="text-white">History Pemeriksaan : </p></div><div class="col-sm-9"><select name="nokun_rad" id="nokun_rad" class="form-control select2"></select></div></div><div class="row jarak"><div class="col-sm-3"><p class="text-white">Tindakan : </p></div><div class="col-sm-9" id="tindakan_medis_rad"></div></div><div id="view_expertise"></div>');

            $('#nokun_rad').select2();
            var pemeriksaanRad = getJSON('<?= base_url() ?>rekam_medis/medis/pemeriksaan_radiologi', {
                norm: norm
            });
            if (pemeriksaanRad.length !== 0) {
                $.each(pemeriksaanRad, function (index, element) {
                    var html = '<option value="' + element.ID + '">' + element.TINDAKAN + ' / ' + moment(element.DESKRIPSI).format("LLL") + '</option>';
                    $('#nokun_rad').append(html);
                });
                load_tindakan_radiologi();
                $('#nokun_rad').on('change', function () {
                    load_tindakan_radiologi();
                });
            } else {
                $('#nokun_rad').html('');
            }
        });

        function load_tindakan_radiologi() {
            $('#tindakan_medis_rad').html('<select name="id_tindakan_radiologi" id="id_tindakan_radiologi" class="form-control select2"></select>');
            $('#id_tindakan_radiologi').select2();
            var tindakanRad = getJSON('<?= base_url() ?>rekam_medis/medis/tindakan_radiologi', {
                nokun: $('#nokun_rad').val()
            });
            if (tindakanRad.length !== 0) {
                $.each(tindakanRad, function (index, element) {
                    var html = '<option value="' + element.ID + '">' + element.DESKRIPSI + '</option>';
                    $('#id_tindakan_radiologi').append(html);
                });
                $('#id_tindakan_radiologi').on('change', function () {
                    load_expertise();
                });
                load_expertise();
            } else {
                $('#id_tindakan_radiologi').html('');
            }
        }

        function load_expertise() {
            var norm = "<?= $getNomr['NORM'] ?>";
            var expertise = getJSON('<?= base_url() ?>rekam_medis/medis/expertise', {
                id: $('#id_tindakan_radiologi').val(),
                norm: norm
            });
            var viewPacs = '<div class="btn-group">' + '<a id="viewSemuaFoto" target="_blank" class="btn btn-info btn-sm waves-effect" data-nomr=' + norm + '><i class="fa fa-eye"></i> Lihat Semua Foto</a>' + '</div><br/>';
            if (expertise !== null) {
                if (expertise['STUDY_ID_LINK'] !== undefined) {
                    var url = "/oviyam2/viewer.html?patientID=" + norm + "&studyUID=" + expertise['STUDY_ID_LINK'];

                    viewPacs += '<div class="btn-group">' + '<a href="' + url + '" target="_blank" class="btn btn-success btn-sm waves-effect"><i class="fa  fa-file-image-o"></i> Lihat Foto</a>' + '</div><br/>';
                }

                $('#view_expertise').html('<div class="row">' +
                    '<div class="col-lg-12">' +
                    '<div class="form-group">' +
                    '<div class="row float-right">' +
                    viewPacs +
                    '</div>' +
                    '</div>' +
                    '<div class="form-group">' +
                    '<label for="hasil">HASIL</label>' +
                    '<textarea name="txthasil" class="form-control" rows="12" placeholder="[ Hasil ]" readonly>' + text(expertise['HASIL']) + '</textarea>' +
                    '</div>' +
                    '</div>' +
                    '</div><br><br>' +

                    '<div class="row">' +
                    '<div class="col-lg-6">' +
                    '<div class="form-group">' +
                    '<label for="dokter_nuklir">DOKTER SPESIALIS NUKLIR</label>' +
                    '<input type="text" class="form-control" value="' + text(expertise['DOKTER_SATU']) + '" readonly>' +
                    '</div>' +
                    '</div>' +

                    '<div class="col-lg-6">' +
                    '<div class="form-group">' +
                    '<label for="dokter_radiologi">DOKTER SPESIALIS RADIOLOGI</label>' +
                    '<input type="text" class="form-control" value="' + text(expertise['DOKTER_DUA']) + '" readonly>' +
                    '</div>' +
                    '</div>' +
                    '</div>'
                );
            } else {
                $('#view_expertise').html('<div class="alert alert-danger">' + '<strong>Tidak di temukan!</strong> Hasil Pemeriksaan Belum Tersedia.' + '</div>');
            }

            $('#viewSemuaFoto').on('click', function (e) {
                var nomr = $(this).data('nomr');
                $.ajax({
                    type: 'POST',
                    url: '<?= base_url() ?>pengkajianAwal/viewFoto',
                    data: {
                        nomr: nomr
                    },
                    success: function (data) {
                        $('#modal2').modal('show');
                        $('#modal2 .modal-body').html('');
                        $('#modal2 .modal-title').html('Hasil Foto Radiologi');
                        $('#modal2 .modal-body').html(data);
                    }
                });
            });
        }

        // END HISTORY RADIOLOGI

        // START HISTORY PATALOGI ANATOMI
        $('#history_patologi_anatomi').click(function () {
            var norm = "<?= $getNomr['NORM'] ?>";
            $('#modal').modal('show');
            $('#modal .modal-body').html('');
            $('#modal .modal-title').html('History Hasil Patologi Anatomi');
            $('#modal .modal-body').append('<div class="row jarak"><div class="col-sm-3"><p class="text-white">Jenis Pemeriksaan : </p></div><div class="col-sm-9"><select name="jenis_pemeriksaan" id="jenis_pemeriksaan" class="form-control select2"><option value="1">Sitologi</option><option value="2">Histologi</option><option value="3">Imunohistokimia</option></select></div></div><div class="row jarak"><div class="col-sm-3"><p class="text-white">Tindakan : </p></div><div class="col-sm-9" id="pemeriksaan_patologi_anatomi"></div></div><div id="view_hasil_pa"></div>');
            $('#jenis_pemeriksaan').select2();

            loadPemeriksaan();
            $('#jenis_pemeriksaan').on('change', function () {
                loadPemeriksaan();
            });

            function loadPemeriksaan() {
                $('#pemeriksaan_patologi_anatomi').html('<select name="id_tindakan_pa" id="id_tindakan_pa" class="form-control select2"></select>');
                $('#id_tindakan_pa').select2();
                var pemeriksaanPA = getJSON('<?= base_url() ?>rekam_medis/medis/pemeriksaan_patologi_anatomi', {
                    norm: norm,
                    jenis: $('#jenis_pemeriksaan').val()
                });

                if (pemeriksaanPA.length !== 0) {
                    $.each(pemeriksaanPA, function (index, element) {
                        var html = '<option value="' + element.NOMOR_LAB + '">' + element.NOMOR_LAB + ' / ' + moment(element.TANGGAL_LAB).format("LLL") + '</option>';
                        $('#id_tindakan_pa').append(html);
                    });
                    loadHasilPemeriksaan();
                    $('#id_tindakan_pa').on('change', function () {
                        loadHasilPemeriksaan();
                    });
                } else {
                    $('#id_tindakan_pa').html('<option disabled selected>[ TIDAK ADA TINDAKAN ]</option>');
                    $('#view_hasil_pa').html('');
                }
            }

            function loadHasilPemeriksaan() {
                var hasilPA = getJSON('/rekam_medis/hasil_pemeriksaan_patologi_anatomi', {
                    nolab: $('#id_tindakan_pa').val(),
                    jenis: $('#jenis_pemeriksaan').val()
                });

                if (hasilPA !== null) {
                    $('#view_hasil_pa').html('<div class="container">' +
                        '<div class="row">' +
                        '<div class="col-lg-6">' +
                        '<div class="form-group">' +
                        '<table class="table table-borderless" style="background-color:#9ccbe2; color: #242a30; font-size: 13px; line-height:15px;">' +
                        '<tr>' +
                        '<td width="35%">Tanggal Terima</td>' +
                        '<td>' + moment(text(hasilPA['TERIMA'])).format("LLL") + '</td>' +
                        '</tr>' +
                        '<tr>' +
                        '<td>Dokter Perujuk</td>' +
                        '<td>' + text(hasilPA['PENGIRIM']) + '</td>' +
                        '</tr>' +
                        '<tr>' +
                        '<td>Dokter Lab Pa</td>' +
                        '<td>' + text(hasilPA['PEMERIKSA']) + '</td>' +
                        '</tr>' +
                        '<tr>' +
                        '<td>Ruang Pengirim</td>' +
                        '<td>' + text(hasilPA['RUANGAN']) + '</td>' +
                        '</tr>' +
                        '<tr>' +
                        '<td>Penjamin</td>' +
                        '<td>' + text(hasilPA['PENJAMIN']) + '</td>' +
                        '</tr>' +
                        '</table>' +
                        '</div>' +
                        '</div>' +
                        '<div class="col-lg-6">' +
                        '<table class="table table-borderless" style="background-color:#f7ca71; color: #242a30; font-size: 13px; line-height:15px;">' +
                        '<tr>' +
                        '<td width="35%">Tanggal Sampling</td>' +
                        '<td>' + moment(text(hasilPA['TANGGAL_LAB'])).format("LLL") + '</td>' +
                        '</tr>' +
                        '<tr>' +
                        '<td>Tanggal Hasil </td>' +
                        '<td>' + moment(text(hasilPA['TANGGAL'])).format("LLL") + '</td>' +
                        '</tr>' +
                        '<tr>' +
                        '<td>Nomor Lab</td>' +
                        '<td>' + text(hasilPA['NOMOR_LAB']) + '</td>' +
                        '</tr>' +
                        '<tr>' +
                        '<td>Tindakan</td>' +
                        '<td>' + text(hasilPA['TIND']) + '</td>' +
                        '</tr>' +
                        '</table>' +
                        '</div>' +
                        '</div>' +
                        '</div>' +
                        '<hr>' +
                        '<div class="row">' +
                        '<div class="col-lg-12">' +
                        '<div class="form-group">' +
                        '<h4 style="color:#ff5e28;">Hasil Konsultasi Patologi Anatomi</h4>' +
                        '</div>' +
                        '</div>' +
                        '</div>	' +
                        '<div class="row">' +
                        '<div class="col-lg-3">' +
                        '<div class="form-group">' +
                        '<label for="lokasi">Lokasi</label>' +
                        '</div>' +
                        '</div>' +
                        '<div class="col-lg-9">' +
                        '<div class="form-group">' +
                        '<input type="text" class="form-control" placeholder="[ Lokasi ]" value="' + text(hasilPA['LOKASI']) + '" readonly>' +
                        '</div>' +
                        '</div>' +
                        '</div>' +
                        '<div class="row">' +
                        '<div class="col-lg-3">' +
                        '<div class="form-group">' +
                        '<label for="didapat_dengan">Didapat Dengan</label>' +
                        '</div>' +
                        '</div>' +
                        '<div class="col-lg-9">' +
                        '<div class="form-group">' +
                        '<input type="text" class="form-control" placeholder="[ Didapat Dengan ]" value="' + text(hasilPA['DIDAPAT_DENGAN']) + '" readonly>' +
                        '</div>' +
                        '</div>' +
                        '</div>' +
                        '<div class="row">' +
                        '<div class="col-lg-3">' +
                        '<div class="form-group">' +
                        '<label for="cairaan_fiksasi">Cairan Fiksasi</label>' +
                        '</div>' +
                        '</div>' +
                        '<div class="col-lg-9">' +
                        '<div class="form-group">' +
                        '<textarea name="" class="form-control" rows="5" placeholder="[ Cairan Fiksasi ]" readonly>' + text(hasilPA['CAIRAN_FIKSASI']) + '</textarea>' +
                        '</div>' +
                        '</div>' +
                        '</div>' +
                        '<div class="row">' +
                        '<div class="col-lg-3">' +
                        '<div class="form-group">' +
                        '<label for="diagnosa_klinik" >Diagnosa Klinik</label>' +
                        '</div>' +
                        '</div>' +
                        '<div class="col-lg-9">' +
                        '<div class="form-group">' +
                        '<textarea name="" class="form-control" rows="5" placeholder="[ Diagnosa Klinik ]" readonly>' + text(hasilPA['DIAGNOSA_KLINIK']) + '</textarea>' +
                        '</div>' +
                        '</div>' +
                        '</div>' +
                        '<div class="row">' +
                        '<div class="col-lg-3">' +
                        '<div class="form-group">' +
                        '<label for="keterangan_klinik">Keterangan Klinik</label>' +
                        '</div>' +
                        '</div>' +
                        '<div class="col-lg-9">' +
                        '<div class="form-group">' +
                        '<textarea name="" class="form-control" rows="5" placeholder="[ Keterangan Klinik ]" readonly>' + text(hasilPA['KETERANGAN_KLINIK']) + '</textarea>' +
                        '</div>' +
                        '</div>' +
                        '</div>' +
                        '<div class="row">' +
                        '<div class="col-lg-3">' +
                        '<div class="form-group">' +
                        '<label for="makroskopik">Makroskopik</label>' +
                        '</div>' +
                        '</div>' +
                        '<div class="col-lg-9">' +
                        '<div class="form-group">' +
                        '<textarea name="" class="form-control" rows="5" +placeholder="[ Makroskopik ]" readonly>' + text(hasilPA['MAKROSKOPIK']) + '</textarea>' +
                        '</div>' +
                        '</div>' +
                        '</div>' +
                        '<div class="row">' +
                        '<div class="col-lg-3">' +
                        '<div class="form-group">' +
                        '<label for="mikroskopik">Mikroskopik</label>' +
                        '</div>' +
                        '</div>' +
                        '<div class="col-lg-9">' +
                        '<div class="form-group">' +
                        '<textarea name="" class="form-control" rows="5" placeholder="[ Mikroskopik ]" readonly>' + text(hasilPA['MIKROSKOPIK']) + '</textarea>' +
                        '</div>' +
                        '</div>' +
                        '</div>' +
                        '<div class="row">' +
                        '<div class="col-lg-3">' +
                        '<div class="form-group">' +
                        '<label for="kesimpulan">Kesimpulan</label>' +
                        '</div>' +
                        '</div>' +
                        '<div class="col-lg-9">' +
                        '<div class="form-group">' +
                        '<textarea name="" class="form-control" rows="5" placeholder="[ Kesimpulan ]" readonly>' + text(hasilPA['KESIMPULAN']) + '</textarea>' +
                        '</div>' +
                        '</div>' +
                        '</div>' +
                        '<div class="row">' +
                        '<div class="col-lg-3">' +
                        '<div class="form-group">' +
                        '<label for="kesimpulan">Imuno Histokimia</label>' +
                        '</div>' +
                        '</div>' +
                        '<div class="col-lg-9">' +
                        '<div class="form-group">' +
                        '<textarea name="" class="form-control" rows="5" placeholder="[ Imuno Histokimia ]" readonly>' + text(hasilPA['IMUNO_HISTOKIMIA']) + '</textarea>' +
                        '</div>' +
                        '</div>' +
                        '</div>' +
                        '<div class="row">' +
                        '<div class="col-lg-3">' +
                        '<div class="form-group">' +
                        '<label for="kesimpulan">Reevaluasi</label>' +
                        '</div>' +
                        '</div>' +
                        '<div class="col-lg-9">' +
                        '<div class="form-group">' +
                        '<textarea name="" class="form-control" rows="5" placeholder="[ Reevaluasi ]" readonly>' + text(hasilPA['REEVALUASI']) + '</textarea>' +
                        '</div>' +
                        '</div>' +
                        '</div>'
                    );
                } else {
                    $('#view_hasil_pa').html('<div class="alert alert-danger">' + '<strong>Tidak di temukan!</strong> Hasil Pemeriksaan Belum Tersedia.' + '</div>');
                }
            }
        });
        // END HISTORY PATALOGI ANATOMI

        $('#cppt_template').select2({
            width: 'resolve'
        });

        loadListTemplate();
        // Load Template
        function loadListTemplate() {
            $('#cppt_template').html('<option value="0"  selected> Tanpa Template</option>');
            var listTemplate = getJSON('<?= base_url() ?>rekam_medis/rawat_inap/catatanTerintegrasi/CpptTemplate/action/getTemplate', {});
            if (listTemplate.length !== 0) {
                $.each(listTemplate.data, function (index, element) {
                    var html = '<option value="' + element.id + '">' + element.deskripsi + '</option>';
                    $('#cppt_template').append(html);
                });
            } else {
                $('#cppt_template').html('<option value="0"  selected> Tanpa Template</option>');
            }
        }

        $(document).on('click', '#simpan_template', function (e) {
            e.preventDefault();

            alertify.confirm('Data disimpan', 'Pilih Ok, jika setuju disimpan',
                function () {
                    var action = $('#formCpptTemplate').attr('action');
                    var form = $('#formCpptTemplate').serialize();

                    $.ajax('<?= base_url() ?>rekam_medis/rawat_inap/catatanTerintegrasi/CpptTemplate/action/' + action, {
                        dataType: 'json',
                        type: 'POST',
                        data: form,

                        success: function (data) {
                            if (data.status == 'success') {
                                toastr.success('Berhasil Disimpan');
                                $('#modal').modal('hide');
                                loadListTemplate();
                            } else {
                                $.each(data.errors, function (index, element) {
                                    toastr.warning(element);
                                });
                            }
                        },
                        error: function (jqXHR, textStatus, errorThrown) {
                            toastr.error('Internal Server Error!');
                        }
                    });
                },
                function () {
                    alertify.error('Cancel')
                }
            );
        });

        // Mulai kontrol kembali
        $('#kontrol-cppt').select2({
            placeholder: '[ Pilih jadwal kontrol kembali ]'
        }).change(function () {
            if ($(this).val() === '6248') {
                $('#tanggal-kontrol-cppt').attr('readonly', true).val(null);
            } else {
                if ($(this).val() !== '6247') {
                    if ($(this).val() === '6243') {
                        $('#tanggal-kontrol-cppt').attr('readonly', true).val(moment().add(1, 'month').format('YYYY-MM-DD'));
                    } else if ($(this).val() === '6269') {
                        $('#tanggal-kontrol-cppt').attr('readonly', true).val(moment().add(2, 'months').format('YYYY-MM-DD'));
                    } else if ($(this).val() === '6244') {
                        $('#tanggal-kontrol-cppt').attr('readonly', true).val(moment().add(3, 'months').format('YYYY-MM-DD'));
                    } else if ($(this).val() === '6245') {
                        $('#tanggal-kontrol-cppt').attr('readonly', true).val(moment().add(6, 'months').format('YYYY-MM-DD'));
                    } else if ($(this).val() === '6246') {
                        $('#tanggal-kontrol-cppt').attr('readonly', true).val(moment().add(1, 'year').format('YYYY-MM-DD'));
                    }
                } else if ($(this).val() === '6247') {
                    $('#tanggal-kontrol-cppt').removeAttr('readonly', true);
                }
            }
        });
        // Akhir kontrol kembali


        $(document).on('click', '#ewsKesadaranTerakhir', function () {
            // console.log('-');
            if ($(this).is(':checked')) {
                ewsKesadaran();
            } else {
                var obyektif = $('#obyektif').val();
                var res = obyektif.replace(/Skor EWS :(.+)\nKesadaran :(.+)/i, '');
                $('#obyektif').val(res);
            }
        });

        function ewsKesadaran() {
            var obyektif = $('#obyektif').val();
            var stringEWS = '';
            var stringKesadaran = '';

            var ews = getJSON('<?= base_url() ?>pengkajianAwal/ewsRJTerakhir', {
                nokun: '<?= $nokun ?>'
            });
            var kesadaran = getJSON('<?= base_url() ?>pengkajianAwal/kesadaranRJTerakhir', {
                nokun: '<?= $nokun ?>'
            });

            var dataKesadaran = kesadaran.data[0];

            if (ews.data != null) {
                var stringEWS = 'Skor EWS : ' + ews.data['TOTAL_EWS'] + ' pada : ' + moment(ews.data['TANGGAL_EWS']).format("LLL") + '\n';
            } else {
                toastr.warning('EWS tidak ada.');
            }

            if (kesadaran != null) {
                var stringKesadaran = 'Kesadaran : ' + kesadaran.data['KESADARAN'] + ' pada : ' + moment(kesadaran.data['ADD_DATE']).format("LLL");
            } else {
                toastr.warning('Kesadaran tidak ada.');
            }
            $('#obyektif').val(obyektif + stringEWS + stringKesadaran);
        }

        function getMulaiMelayani() {
            var nomr = "<?= isset($getNomr['NORM']) ? $getNomr['NORM'] : null ?>";
            var nokun = "<?= isset($getNomr['NOKUN']) ? $getNomr['NOKUN'] : null ?>";
            var data = {
                nomr: nomr,
                nokun: nokun,
            };

            $.ajax({
                url: "<?= base_url('pengkajianAwal/getmulailayani') ?>",
                method: "POST",
                data: data,
                dataType: 'json',
                success: function (data) {
                    if (data != null && data.status == 'success') {
                        if (data.waktumulai != null) {
                            $(".divmulailayani").removeClass("d-none");
                            var lb = $(".divmulailayani").find(".lbmulailayani");
                            lb.text(`Mulai dilayani ${data.waktumulai}`).removeClass("d-none");
                            $(".mulailayanipasien").addClass("d-none");
                        } else if (data.showbutton == 1) {
                            $(".divmulailayani").removeClass("d-none");
                            $(".mulailayanipasien").removeClass("d-none");
                            $(".lbmulailayani").addClass("d-none");
                            setTimeout(buttonPopUp, 300);
                        }
                    } else {
                        $(".divmulailayani").addClass("d-none");
                    }
                },
            });
        }

        getMulaiMelayani();
        $(".mulailayanipasien").on('click', function (event) {
            buttonPopUp();
            event.preventDefault();
        });

        function buttonPopUp() {
            var nomr = "<?= isset($getNomr['NORM']) ? $getNomr['NORM'] : null ?>";
            var nokun = "<?= isset($getNomr['NOKUN']) ? $getNomr['NOKUN'] : null ?>";
            var vm = $(".mulailayanipasien");
            alertify.confirm('', 'Apakah anda mulai melayani pasien?',
                function () {
                    var data = {
                        nomr: nomr,
                        nokun: nokun,
                    };

                    $.ajax({
                        url: "<?= base_url('pengkajianAwal/simpanmulailayani') ?>",
                        method: "POST",
                        data: data,
                        dataType: 'json',
                        success: function (data) {
                            if (data != null && data.status == 'success') {
                                var lb = vm.closest(".divmulailayani").find(".lbmulailayani");
                                lb.html(`Mulai dilayani ${data.waktu}`).removeClass("d-none");
                                vm.addClass("d-none");
                                alertify.success('Mulai dilayani');
                                // postIDTaskBPJS();
                            } else {
                                alertify.warning('Internal Server Error');
                            }
                        }
                    });
                },
                function () {
                    setTimeout(() => {
                        getMulaiMelayani();
                    }, 60000);
                    // alertify.error('Cancel')
                }).set({
                    'pinnable': false,
                    'modal': true,
                    'closable': false,
                    transition: 'zoom',
                    'movable': false
                }).set('labels', {
                    ok: 'Iya',
                    cancel: 'Tidak'
                });
        }

        function postIDTaskBPJS() {
            $.ajax({
                url: "http://192.168.7.12:8005/api/antrean/updatewaktu",
                method: "POST",
                data: {
                    kodebooking: "<?= isset($getNomr['ID_PERJANJIAN']) ? $getNomr['ID_PERJANJIAN'] : '' ?>",
                    taskid: 4, // 4:mulai dilayani poli, 7:selesai dilayani farmasi
                    waktu: Date.now()
                },
                dataType: "json",
                success: function (data) {
                    console.log('bpjs', data);
                },
                error: function (data) {
                    console.log('bpjs e', data);
                }
            });
        }

        function getNotifViraload() {
            var nomr = "<?= isset($getNomr['NORM']) ? $getNomr['NORM'] : null ?>";
            var vm = $(this);
            var data = {
                nomr: nomr
            };

            $.ajax({
                url: "<?= base_url('pengkajianAwal/getnotifviraload') ?>",
                method: "POST",
                data: data,
                dataType: 'json',
                success: function (data) {
                    var lb = $(".notifviraload");
                    if (data != null && data.status == 'success') {
                        lb.html(`${data.data['NOTIF']}`).removeClass("d-none");
                        intervalHandler = setInterval(notifBlink, 2000);
                        lb.mouseover(function () {
                            clearInterval(intervalHandler);
                            intervalHandler = undefined;
                        });
                        lb.mouseout(function () {
                            if (!intervalHandler) {
                                intervalHandler = setInterval(notifBlink, 2000);
                            }
                        });
                    } else {
                        vm.addClass("d-none");
                    }

                }
            });

            // var canDismiss = false;
            // alertify.set('notifier','position', 'bottom-left');
            // var notification = alertify.error('notif');
            // notification.ondismiss = function(){ return canDismiss; };
        }
        cektglPemberioanobat();

        function notifBlink() {
            $(".notifviraload").fadeOut(1000);
            $(".notifviraload").fadeIn(1000);
        }

        function cektglPemberioanobat() {
            var nomr = "<?= isset($getNomr['NORM']) ? $getNomr['NORM'] : null ?>";
            var nokun = "<?= isset($getNomr['NOKUN']) ? $getNomr['NOKUN'] : null ?>";
            var data = {
                nomr: nomr,
                nokun: nokun,
            };

            $.ajax({
                url: "<?= base_url('pengkajianAwal/cektglobatviraload') ?>",
                method: "POST",
                data: data,
                dataType: 'json',
                success: function (data) {
                    if (data != null && data.status == 'success') {
                        $(".divviralload").removeClass("d-none");

                    } else {
                        $(".divviralload").addClass("d-none");
                    }
                    getNotifViraload();
                },

            });
        }

        $(".viralloadpasien").on('click', function (event) {
            getFormViralload();
            event.preventDefault();
        });

        function getFormViralload() {
            $('#modalViraload').modal('show');
            var nokun = "<?= isset($getNomr['NOKUN']) ? $getNomr['NOKUN'] : null ?>";
            var vm = $(this);

            var data = {
                nokun: nokun
            };
            $.ajax({
                url: "<?= base_url('pengkajianAwal/getFormviraload') ?>",
                method: "POST",
                data: data,
                dataType: 'html',
                success: function (data) {
                    $("#ctnModalViraload").html(data);
                    // var lb = $(".notifviraload");
                }
            });
        }

        $(document).on("submit", ".formViraLoad", function (event) {
            event.preventDefault();
            var divviralload = $(".divviralload");
            var tgl = $("#tgl_obatpertama").val();
            var obat_viralload = $("#obat_viralload").val();

            if (tgl === null || tgl === "") {
                alertify.warning('Tanggal pertama pemberian obat belum diisi');
            } else if (obat_viralload == null || obat_viralload == "") {
                alertify.warning('Obat belum diisi');
            } else {
                alertify.confirm('Konfirmasi', 'Pilih Ok, jika setuju untuk Simpan', function () {
                    var formViraLoad = $("#formViraLoad").serializeArray();
                    $.ajax({
                        dataType: 'json',
                        url: "<?= base_url('pengkajianAwal/saveFormviraload') ?>",
                        method: "POST",
                        data: formViraLoad,
                        success: function (data) {
                            if (data.status == 'success') {
                                alertify.success('Data Tersimpan');
                                divviralload.addClass("d-none");
                                // $("#btnEso").trigger('click');
                                $('#modalViraload').modal('hide');
                                getNotifViraload();
                            } else {
                                alertify.warning('Gagal Simpan');
                            }
                        }
                    });
                }, function () {
                    alertify.error('Batal')
                });
            }
        });

        // start simpan penanda hiv
        $("#simpanTandaHIVRJ").on('click', function (event) {
            event.preventDefault();
            alertify.confirm('Konfirmasi', 'Pilih Ok, jika setuju untuk Simpan', function () {
                var formTandaHIVRJ = $("#formTandaHIVRJ").serializeArray();
                $.ajax({
                    dataType: 'json',
                    url: "<?= base_url('rekam_medis/Medis/simpanPenandaHIV') ?>",
                    method: "POST",
                    data: formTandaHIVRJ,
                    success: function (data) {
                        if (data.status == 'success') {
                            alertify.success('Data Tersimpan');
                            location.reload();
                        } else {
                            alertify.warning('Internal Server Error');
                        }
                    }
                });
            }, function () {
                alertify.error('Batal')
            });
        });
        // end penanda hiv

        // Mulai iframe iCare
        $('#buka-icare').on('click', function () {
            event.preventDefault();
            $.ajax({
                url: 'http://192.168.7.131:8000/api/icare/fkrtl',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    "noKartu": "<?= isset($getNoBPJS->noKartu) ? $getNoBPJS->noKartu : 0 ?>",
                    "kodeDokter": <?= isset($getKodeDokter->HAFIS) ? $getKodeDokter->HAFIS : 0 ?>
                }),
                success: function (response) {
                    if (response.metaData.code === 200 && response.response) {
                        $('#icare-iframe').attr('src', response.response.url);
                        $('#modal-icare').modal('show');
                    } else {
                        alertify.error('Error: ' + response.metaData.message);
                    }
                },
                error: function () {
                    alertify.error('Error tidak diketahui.');
                }
            });
        });
        // Akhir iframe iCare

        <?php if (in_array($id_ruangan, ['105020201', '105020202', '105020204']) || $getNomr['GEDUNG'] == 1): ?>
            // Ketika link CPPT diklik
            $('.dropdown-item.cppt').on('click', function (e) {
                e.preventDefault(); // Mencegah action default

                // Klik otomatis tombol #buka-side-cppt
                $('#buka-side-cppt').click();

                // Setelah 2 detik, klik elemen #menu-tindakanBilling-side
                setTimeout(function () {
                    $('#menu-tindakanBilling-side').click();
                }, 2000); // 2 detik
            });
        <?php endif ?>

        <?php if (in_array($id_ruangan, ['105060101'])): ?>
            setTimeout(function () {
                let dropdown = $('.menuProsedurDiagnostik');
                let target = $('.laporanHasilPemeriksaan');

                if (dropdown.length > 0 && target.length > 0) {
                    dropdown.click();

                    setTimeout(function () {
                        target.click();
                    }, 500);
                } else {
                    console.log('Dropdown atau target tidak ditemukan');
                }
            }, 1000);
        <?php endif ?>
    });
</script>
<div class="row">
  <div class="col-lg-12">
    <div class="card-box pb-0">
      <div id="rootwizard" class="pull-in">
        <ul class="nav nav-tabs nav-justified">
          <?php if (isset($getNomr['JENIS_KUNJUNGAN']) && $this->session->userdata('status') == 1) : ?>
            <li class="nav-item"><a href="#order" data-toggle="tab" class="nav-link <?= $this->session->userdata('status') == 1 ? 'active' : '' ?>">Input</a></li>
          <?php endif ?>
          <li class="nav-item"><a href="#historyOrder" data-toggle="tab" class="nav-link <?= $this->session->userdata('status') == 2 ? 'active' : '' ?>">Total Billing</a></li>
        </ul>
        <div class="tab-content mb-0 b-0" style="border-color:#40739e">
          <div class="tab-pane fade <?= $this->session->userdata('status') == 1 ? 'show active' : '' ?>" id="order">
            <form id="orderTindakan">
              <input type="hidden" name="nourut" id="nourut" value="0">
              <input type="hidden" name="dokter" id="dokter" value="<?= $getNomr['ID_DOKTER'] ?>">
              <input type="hidden" name="ruangan" id="ruangan" value="<?= $getNomr['ID_RUANGAN'] ?>">
              <input type="hidden" name="SESSION" id="SESSION" value="<?= $this->session->userdata('id') ?>">
              <input type="hidden" name="dokter_asal" id="dokter_asal" value="<?= $this->session->userdata('iddokter') ?>">
              <input type="hidden" name="norm" id="dokter" value="<?= $getNomr['NORM'] ?>">
              <input type="hidden" name="nopen" id="nopen" value="<?= $this->uri->segment(5) ?>">
              <input type="hidden" name="kunjungan" id="kunjungan" value="<?= $this->uri->segment(6) ?>">
              <input type="hidden" name="smf" id="smf" value="<?= $smf ?>">
              <input type="hidden" id="total_tarif" name="total_tarif" value="0">

              <!-- Mulai peringatan -->
              <?php if ($getNomr['STATUS_KUNJUNGAN'] == 'Pasien dibatalkan' || $getNomr['STATUS_KUNJUNGAN'] == 'Pasien sudah final') : ?>
                <div class="alert alert-warning" role="alert">
                  Pengisian form ini tidak bisa dilakukan karena <strong><?= strtolower($getNomr['STATUS_KUNJUNGAN']) ?>.</strong>
                </div>
              <?php endif ?>
              <!-- Akhir peringatan -->
              <div class="row">
                <div class="col-lg-12">
                  <div class="form-group">
                    <table class="table" style="background-color:#9ccbe2; color: #242a30; font-size: 13px; line-height:15px;">
                      <tr>
                        <td>Nama Lengkap</td>
                        <td><?= isset($getNomr['NAMA_PASIEN']) ? $getNomr['NAMA_PASIEN'] : null ?></td>
                      </tr>
                      <tr>
                        <td>Nomor MR</td>
                        <td><?= isset($getNomr['NORM']) ? $getNomr['NORM'] : null ?></td>
                      </tr>
                      <tr>
                        <td>Tgl Lahir/Umur</td>
                        <td><?= isset($getNomr['TANGGAL_LAHIR']) && isset($getNomr['UMUR']) ? date('d-m-Y', strtotime($getNomr['TANGGAL_LAHIR'])) . '/' . $getNomr['UMUR'] : null ?></td>
                      </tr>
                      <tr>
                        <td>DPJP</td>
                        <td><?= isset($getNomr['DOKTER_TUJUAN']) ? $getNomr['DOKTER_TUJUAN'] : null ?></td>
                      </tr>
                      <tr>
                        <td>Ruang Asal</td>
                        <td><?= isset($getNomr['RUANGAN_TUJUAN']) ? $getNomr['RUANGAN_TUJUAN'] : null ?></td>
                      </tr>
                    </table>
                  </div>
                </div>
              </div>
              <div class="col-md-12">
                <!-- Start Status SOAP  -->
                <?php if (isset($getNomr['JENIS_KUNJUNGAN'])) { ?>
                  <fieldset style="">
                    <legend style="">Input Tindakan</legend>
                    <?php if ($getNomr['status_pasien'] == "2") { ?>
                      <div class="alert alert-warning" role="alert">
                        Pengisian Tindakan tidak bisa dilakukan karena <strong>pasien sudah final.</strong>
                      </div>
                    <?php
                    }
                    if ($getNomr['status_pasien'] == "1" && isset($getNomr['JENIS_KUNJUNGAN'])) {
                    ?>
                      <div class="row form-group">
                        <div class="col-md-3">
                          <span>Pilih Nama Tindakan</span>
                        </div>
                        <div class="col-md-7">
                          <select name="select_tindakan" id="select_tindakan" class="form-control select_tindakan">
                          </select>
                        </div>
                        <div class="col-md-2">
                          <button type="button" class="btn btn-warning btn-block" onclick="tambah_tindakan()"><i class="fa fa-plus"></i></button>
                        </div>
                      </div>

                      <div class="row">
                        <table class="table table-striped table-bordered table_tindakan">
                          <thead>
                            <tr>
                              <th>No</th>
                              <th>Nama Tindakan</th>
                              <th>Tarif</th>
                              <th>Aksi</th>
                            </tr>
                          </thead>
                          <tbody>
                          </tbody>
                          <tfoot>
                            <tr>
                              <?php if(isset($getNomr['JENIS_KUNJUNGAN']) && $getNomr['JENIS_KUNJUNGAN'] == 14){?>
                              <th colspan="2" style="text-align:right">Total Tarif:</th>
                              <th id="totalTarif">0</th>
                              <th></th>
                              <?php }else{ ?>
                              <th colspan="2" style="text-align:right"></th>
                              <th>-</th>
                              <th></th>
                              <?php } ?>
                            </tr>
                          </tfoot>
                        </table>
                      </div>

                    <?php } ?>
                  </fieldset>
                  <br>
                <?php } ?>

                <div class="pull-right">
                  <!-- <i class="fa fa-save"></i> -->
                  <input type="submit" class="btn btn-sm btn-success" type="button" id="kirimorder" value="Simpan" <?= $getNomr['STATUS_KUNJUNGAN'] == 'Pasien dibatalkan' || $getNomr['STATUS_KUNJUNGAN'] == 'Pasien sudah final' ? 'disabled' : null ?>>
                </div>
              </div>

            </form>
            <br>
            <span>Daftar Tindakan</span>
            <div class="row mt-4">
              <div class="col-md-12">
                <div class="table-responsive">
                  <table id="tableTindakan" class="table table-bordered table-striped" style="width:100%">
                    <thead>
                      <tr>
                        <th width="5%">No</th>
                        <th width="60%">Nama Tindakan</th>
                        <th width="20%">Tarif</th>
                        <th width="15%">Aksi</th>
                      </tr>
                    </thead>
                    <tbody>
                    </tbody>
                    <tfoot>
                      <tr>
                        <?php if(isset($getNomr['JENIS_KUNJUNGAN']) && $getNomr['JENIS_KUNJUNGAN'] == 14){?>
                        <th colspan="2" style="text-align:right">Total Tarif:</th>
                        <th id="totalTariftindakan">0</th>
                        <th></th>
                        <?php } else { ?>
                        <th colspan="2" style="text-align:right"></th>
                        <th>-</th>
                        <th></th>
                        <?php } ?>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>
            </div>
          </div>

          <div class="tab-pane fade <?= $this->session->userdata('status') == 2 ? 'show active' : '' ?>" id="historyOrder">
            <div class="row">
              <div class="col-lg-12">
                <div class="form-group">
                  <table class="table" style="background-color:#9ccbe2; color: #242a30; font-size: 13px; line-height:15px;">
                    <tr>
                      <td>Nama Lengkap</td>
                      <td><?= isset($getNomr['NAMA_PASIEN']) ? $getNomr['NAMA_PASIEN'] : null ?></td>
                    </tr>
                    <tr>
                      <td>Nomor MR</td>
                      <td><?= isset($getNomr['NORM']) ? $getNomr['NORM'] : null ?></td>
                    </tr>
                    <tr>
                      <td>Tgl Lahir/Umur</td>
                      <td><?= isset($getNomr['TANGGAL_LAHIR']) && isset($getNomr['UMUR']) ? date('d-m-Y', strtotime($getNomr['TANGGAL_LAHIR'])) . '/' . $getNomr['UMUR'] : null ?></td>
                    </tr>
                    <tr>
                      <td>DPJP</td>
                      <td><?= isset($getNomr['DOKTER_TUJUAN']) ? $getNomr['DOKTER_TUJUAN'] : null ?></td>
                    </tr>
                    <tr>
                      <td>Ruang Asal</td>
                      <td><?= isset($getNomr['RUANGAN_TUJUAN']) ? $getNomr['RUANGAN_TUJUAN'] : null ?></td>
                    </tr>
                  </table>
                </div>
              </div>
            </div>

            <table id="tblListTindakan" class="table table-bordered dt-responsive" cellspacing="0" width="100%">
              <thead>
                <tr>
                  <th>No</th>
                  <th>Nama Tindakan</th>
                  <th>Tarif</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tfoot>
                <tr>
                  <?php if(isset($getNomr['JENIS_KUNJUNGAN']) && $getNomr['JENIS_KUNJUNGAN'] == 14){?>
                    <th colspan="2" style="text-align:right">Total Tarif:</th>
                    <th id="totalTarifs">0</th>
                    <th><button id="btnCetakBilling" class="btn btn-primary btnCetakBilling"><i class="fa fa-print"></i> Cetak</button></th>
                    <?php } else { ?>
                    <th colspan="2" style="text-align:right"></th>
                    <th>-</th>
                    <th></th>
                     <?php } ?>
                </tr>
              </tfoot>
            </table>

          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  var tindakanArr = [];
  var totalTarif = 0;

  $(document).ready(function() {

    var table2 = $('#tblListTindakan').DataTable({
      "processing": true,
      "serverSide": true,
      "ajax": {
        "url": "<?= site_url('rekam_medis/TindakanBilling/getTindakanTable') ?>",
        "type": "POST",
        "data": function(d) {
          d.kunjungan = $('#kunjungan').val(); // Pass the 'kunjungan' parameter
        },
        "dataSrc": function(json) {
          var totalTarifs = 0;

          $.each(json.data, function(index, item) {
            if (!item.is_header) {
              var tarif = parseFloat(item.tarif.replace(/\./g, '').replace(/,/g, '.')); // Format the price
              if (!isNaN(tarif)) {
                totalTarifs += tarif;
              }
            }
          });

          $('#totalTarifs').text(totalTarifs.toLocaleString('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0
          }));

          return json.data; // Kembalikan data seperti biasa
        }
      },
      "lengthMenu": [],
      "pageLength": 1000,
      "lengthChange": false,
      "pagination": false, // Disable pagination
      "columns": [{
          "data": "no",
          "render": function(data, type, row) {
            return row.is_header ? '' : data;
          }
        },
        {
          "data": "nama_tindakan",
          "render": function(data, type, row) {
            if (row.is_header) {
              return '<strong>' + row.nama_tindakan + '</strong>';
            } else {
              return data;
            }
          }
        },
        {
          "data": "tarif",
          "render": function(data, type, row) {
            return row.is_header ? '' : data;
          }
        },
        {
          "data": "status",
          "render": function(data, type, row) {
            return row.is_header ? '' : data;
          }
        }
      ],
      "rowCallback": function(row, data) {
        if (data.is_header) {
          $(row).find('td:eq(1)').attr('colspan', '4').addClass('text-center bg-dark');
          $(row).find('td:gt(1)').remove();
        }
      }
    });


    var table = $('#tableTindakan').DataTable({
      "processing": true,
      "serverSide": true,
      "ajax": {
        "url": "<?= site_url('rekam_medis/TindakanBilling/getDaftarTindakan') ?>",
        "type": "POST",
        "data": function(d) {
          d.kunjungan = $('#kunjungan').val();
        },
        "dataSrc": function(json) {
          var totalTariftindakan = 0;

          $.each(json.data, function(index, item) {
            var tempDiv = $("<div>").html(item.status);
            var button = tempDiv.find('button');

            var statusTitle = button.attr('title');

            if (statusTitle === 'Batalkan') {
              var tarif = parseFloat(item.tarif.replace(/\./g, '').replace(/,/g, '.'));

              if (!isNaN(tarif)) {
                totalTariftindakan += tarif;
              }
            }
          });

          $('#totalTariftindakan').text(totalTariftindakan.toLocaleString('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0
          }));

          return json.data;
        }
      },
      "columns": [{
          "data": "no"
        },
        {
          "data": "nama_tindakan"
        },
        {
          "data": "tarif"
        },
        {
          "data": "status"
        }
      ]
    });

    $(document).on('click', '.btn-batalkan', function() {
      const idTindakan = $(this).data('id');

      alertify.confirm('Konfirmasi Pembatalan', 'Apakah Anda yakin ingin membatalkan tindakan ini?',
        function() {
          $.ajax({
            url: '<?= site_url('rekam_medis/TindakanBilling/batalkanTindakan') ?>',
            type: 'POST',
            data: {
              tindakan_id: idTindakan
            },
            success: function() {
              $('#tableTindakan, #tblListTindakan').DataTable().ajax.reload();
              alertify.success('Tindakan berhasil dibatalkan');
            },
            error: function(xhr, status, error) {
              alertify.error('Terjadi kesalahan saat membatalkan tindakan: ' + error);
            }
          });
        },
        function() {
          alertify.error('Pembatalan tindakan dibatalkan');
        }
      );
    });


    $('#select_tindakan').select2({
      placeholder: 'Pilih Tindakan',
      allowClear: true,
      ajax: {
        url: '<?= base_url("rekam_medis/TindakanBilling/getTindakanList") ?>',
        type: 'POST',
        dataType: 'json',
        delay: 250,
        data: function (params) {
          return {
            search: params.term, // search term
            ruangan: <?= $getNomr['ID_RUANGAN'] ?>,
            smf: $('#smf').val()
          };
        },
        processResults: function (data) {
          var results = [];
          $.each(data, function(key, value) {
            var tarifFormatted = parseInt(value.TARIF).toLocaleString('id-ID', {
              style: 'currency',
              currency: 'IDR',
              minimumFractionDigits: 0
            }).replace("IDR", "");
            
            <?php if($getNomr['JENIS_KUNJUNGAN'] == 14){ ?>
            var text = value.NAMA + ' - ' + tarifFormatted;
            <?php }else{ ?>
            var text = value.NAMA;
            <?php } ?>
            
            results.push({
              id: value.ID_TINDAKAN,
              text: text,
              tarif: value.TARIF
            });
          });
          
          return {
            results: results
          };
        },
        cache: true
      },
      templateResult: function(data) {
        return data.text;
      },
      templateSelection: function(data) {
        // Set data-tarif attribute when selection is made
        if (data.tarif) {
          $('#select_tindakan').attr('data-tarif', data.tarif);
        }
        return data.text;
      }
    });

    loadTindakanList();

    $('#orderTindakan').on('submit', function(e) {
      e.preventDefault(); // Prevent default form submission

      var id_tindakan_arr = [];
      $('.table_tindakan tbody tr').each(function() {
        var id_tindakan = $(this).attr('alt'); // 'alt' contains the tindakan ID
        id_tindakan_arr.push(id_tindakan);
      });

      if (id_tindakan_arr.length === 0) {
        alertify.error('Tidak ada tindakan yang dipilih.');
        return; // Keluar dari fungsi jika tidak ada tindakan
      }

      alertify.confirm('Konfirmasi', 'Apakah anda yakin ingin membuat billing?', function() {
        var nopen = $('#nopen').val();
        var kunjungan = $('#kunjungan').val();
        var dokter = $('#dokter').val();
        var dokter_asal = $('#dokter_asal').val();
        var ruangan = $('#ruangan').val();
        var total_tarif = totalTarif; // Ambil total tarif dari variabel global

        var data = {
          nopen: nopen,
          kunjungan: kunjungan,
          dokter: dokter,
          dokter_asal: dokter_asal,
          ruangan: ruangan,
          id_tindakan: id_tindakan_arr,
        };

        $.ajax({
          url: '<?= base_url("rekam_medis/TindakanBilling/kirimOrder") ?>',
          type: 'POST',
          data: data,
          dataType: 'json',
          success: function(response) {
            if (response.success) {
              alertify.success('Order berhasil dikirim!');
              $('#tableTindakan, #tblListTindakan').DataTable().ajax.reload();
              $('.table_tindakan tbody').empty();
              $('#totalTarif').text('0');
            } else {
              alertify.error('Gagal mengirim order. Coba lagi.');
            }
          },
          error: function(xhr, status, error) {
            console.error(xhr.responseText);
            alertify.error('Terjadi kesalahan saat mengirim order.');
          }
        });
      }, function() {
        alertify.error('Pengiriman order dibatalkan.');
      }).set('labels', {
        ok: 'Ya',
        cancel: 'Tidak'
      });
    });
  });

  function loadTindakanList(search = null) {
    $.ajax({
      url: '<?= base_url("rekam_medis/TindakanBilling/getTindakanList") ?>',
      type: 'POST',
      dataType: 'json',
      data: {
        ruangan: <?= $getNomr['ID_RUANGAN'] ?>,
        smf: $('#smf').val(),
        search: search
      },
      success: function(data) {
        var tindakanSelect = $('#select_tindakan');
        tindakanSelect.empty(); // Hapus opsi yang ada
        tindakanSelect.append('<option value="">Pilih Tindakan</option>'); // Opsi default

        $.each(data, function(key, value) {
          var tarifFormatted = parseInt(value.TARIF).toLocaleString('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0
          }).replace("IDR", "");
          <?php if($getNomr['JENIS_KUNJUNGAN'] == 14){ ?>
          tindakanSelect.append('<option value="' + value.ID_TINDAKAN + '" data-tarif="' + value.TARIF + '">' + value.NAMA + ' - ' + tarifFormatted + '</option>');
          <?php }else{ ?>
            tindakanSelect.append('<option value="' + value.ID_TINDAKAN + '" data-tarif=" ">' + value.NAMA +'</option>');
          <?php } ?>
        });
      },
      error: function(xhr, status, error) {
        console.error(xhr.responseText);
      }
    });
  }

  function tambah_tindakan() {
    var val = $("#select_tindakan").val();
    var selectedData = $('#select_tindakan').select2('data')[0];
    var txt = selectedData ? selectedData.text : '';
    var tarif = selectedData ? parseFloat(selectedData.tarif) : 0;

    if (!val) {
      alert("Pilih tindakan terlebih dahulu");
      return;
    }

    if (jQuery.inArray(val, tindakanArr) !== -1) {
      alertify.confirm('Konfirmasi', `Tindakan ${txt} sudah ada,\r\nTetap tambahkan tindakan?`, function() {
        tambahTindakan(val, txt, tarif);
      }, function() {}).set('labels', {
        ok: 'Ya',
        cancel: 'Tidak'
      });
    } else {
      tambahTindakan(val, txt, tarif);
    }
  }

  var jenisKunjungan = <?php echo isset($getNomr['JENIS_KUNJUNGAN']) ? $getNomr['JENIS_KUNJUNGAN'] : 'null'; ?>;
  function tambahTindakan(val, txt, tarif) {
    tindakanArr.push(val);

    var no = $('.table_tindakan tbody').find('tr').length + 1;
    var tarifDisplay = (jenisKunjungan === 14) ? `Rp ${formatRupiah(tarif.toString())}` : '-';

    var newRowContent = `<tr class="trt" alt="${val}">
                          <td>${no}</td>
                          <td>${txt}</td>
                          <td>${tarifDisplay}</td>  <!-- Tampilkan tarif -->
                          <td><button type="button" class="btn btn-danger hapus_tindakan" alt="${val}"><i class="fa fa-trash"></i></button></td>
                        </tr>`;

    $(".table_tindakan tbody").append(newRowContent);

    if (jenisKunjungan === 14) {
        totalTarif += tarif;
    }
    updateTotalTarif();
  }

  $(document).on('click', '.hapus_tindakan', function() {
    var id = $(this).attr("alt");
    var row = $(this).closest('tr');
    var tarifText = row.find('td:eq(2)').text(); // Ambil teks tarif dari kolom
    var tarif = parseFloat(tarifText.replace('Rp ', '').replace(/\./g, '').replace(',', '.')); // Ubah menjadi angka

    alertify.confirm('Konfirmasi', 'Hapus Tindakan?', function() {
      // Menghapus tindakan dari array
      var index = tindakanArr.indexOf(id);
      if (index > -1) {
        tindakanArr.splice(index, 1);
      }

      row.remove();
      updateTableNumbering();

      totalTarif -= tarif;
      updateTotalTarif();
    }, function() {}).set('labels', {
      ok: 'Ya',
      cancel: 'Tidak'
    });
  });

  function updateTotalTarif() {
    $('#total_tarif').val(totalTarif); // Perbarui input tersembunyi (hidden)
    $('#totalTarif').text('Rp ' + formatRupiah(totalTarif.toString())); // Tampilkan total tarif pada elemen dengan ID 'totalTarif'
  }

  function updateTableNumbering() {
    $('.table_tindakan tbody tr').each(function(index) {
      $(this).find('td:first').text(index + 1); // Perbarui penomoran pada kolom pertama
    });
  }

  function formatRupiah(angka) {
    return angka.replace(/\B(?=(\d{3})+(?!\d))/g, "."); // Menambahkan titik untuk format rupiah
  }

  $(document).ready(function() {
    $('#btnCetakBilling').on('click', function() {
      var NOKUN_BILL = '<?= $this->uri->segment(6) ?>';

      var url = `http://************/reports/simrskd/cendana/billing.php?NOKUN=${NOKUN_BILL}&format=pdf`;

      window.open(url, '_blank');
    });
  });
</script>

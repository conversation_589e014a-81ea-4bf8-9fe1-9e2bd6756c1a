<form id="formPAGEdit">
      <input type="hidden" name="PAR" value="update" readonly>
      <input type="hidden" name="idAnastesi" value="<?=$getData['id'];?>" readonly>
      <input type="hidden" name="nokun" value="<?=$getNomr['NOKUN'];?>">
        <div class="row jarak">
            <div class="col-md-3">
              <span>1. Tanggal Tindakan</span>
            </div>
            <div class="col-md-9">
                <input type="date" class="form-control" id="tglAnalokEdit" name="tglAnalokEdit" value="<?=$getData['tgl_tindakan']?>">
            </div>
        </div>
        <div class="row jarak">
             <div class="col-md-3">
                 <span>2. Tindakan Operasi</span>
            </div>
            <div class="col-md-9">
                <input type="text" class="form-control" id="tndAnalokEdit" name="tndAnalokEdit" value="<?=$getData['tindakan_operasi']?>">
            </div>
        </div>
        <div class="row jarak">
            <div class="col-md-3">
                <span>3. Keadaan umum pasien pr tindakan:</span>
            </div>
        </div>
        <div class="row jarak">
            <div class="col-md-3">
                <span class="ml-3">Status Mental:</span>
            </div>
            <div class="col-md-9 form-group">
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="mentalAnalokEdit" id="mentalAnalokEdit1" value="1">
                    <label class="form-check-label" for="mentalAnalokEdit1">Komposmetis</label>  
                </div>
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="mentalAnalokEdit" id="mentalAnalokEdit2" value="2">
                    <label class="form-check-label" for="mentalAnalokEdit2">Cemas</label>
                </div>
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="mentalAnalokEdit" id="mentalAnalokEdit3" value="3">
                    <label class="form-check-label" for="mentalAnalokEdit3">Agitasi</label>
                </div>
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="mentalAnalokEdit" id="mentalAnalokEdit4" value="4">
                    <label class="form-check-label" for="mentalAnalokEdit4">Mengantuk</label>
                </div>
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="mentalAnalokEdit" id="mentalAnalokEdit5" value="5">
                    <label class="form-check-label" for="mentalAnalokEdit5">Koma</label>
                </div>
                <div class="radio radio-primary form-check-inline" style="width: 50%;">
                    <input class="form-check-input" type="radio" name="mentalAnalokEdit" id="mentalAnalokEdit6" value="6">
                    <label class="form-check-label" for="mentalAnalokEdit6" style="width: 16%">Sebutkan</label>
                    <input class="form-control" type="text" name="mentalAnalokEditDll" id="mentalAnalokEditDll" style="display:none;width: 100%">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3"></div>
            <div class="col-md-9">
                <div class="row">
                    <div class="col-md-4">
                        <div class="input-group mb-3">
                            <div class="input-group-prepend">
                                <span class="input-group-text">BB</span>
                            </div>
                            <input type="text" class="form-control" name="bbAnalokEdit" value="<?=$getData['berat_badan']?>">
                            <div class="input-group-append">
                                <span class="input-group-text">kg</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="input-group mb-3">
                            <div class="input-group-prepend">
                                <span class="input-group-text">TB</span>
                            </div>
                            <input type="text" class="form-control" name="TBAnalokEdit" value="<?=$getData['tinggi_badan']?>">
                            <div class="input-group-append">
                                <span class="input-group-text">cm</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="input-group mb-3">
                            <div class="input-group-prepend">
                                <span class="input-group-text">TD</span>
                            </div>
                            <input type="text" class="form-control" name="tdAnalokEdit" value="<?=$getData['tekanan_darah']?>">
                            <div class="input-group-append">
                                <span class="input-group-text">mmHg</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="input-group mb-3">
                            <div class="input-group-prepend">
                                <span class="input-group-text">RR</span>
                            </div>
                            <input type="text" class="form-control" name="rrAnalokEdit" value="<?=$getData['respiratory_rate']?>">
                            <div class="input-group-append">
                                <span class="input-group-text">x/mnt</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="input-group mb-3">
                            <div class="input-group-prepend">
                                <span class="input-group-text">N</span>
                            </div>
                            <input type="text" class="form-control" name="nAnalokEdit" value="<?=$getData['nadi']?>">
                            <div class="input-group-append">
                                <span class="input-group-text">x/mnt</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="input-group mb-3">
                            <div class="input-group-prepend">
                                <span class="input-group-text">S</span>
                            </div>
                            <input type="text" class="form-control" name="sAnalokEdit" value="<?=$getData['suhu']?>">
                            <div class="input-group-append">
                                <span class="input-group-text">C</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row jarak">
             <div class="col-md-3">
                 <span>4. Laboratorium</span>
            </div>
            <div class="col-md-9">
                <div class="row">
                    <div class="col-md-4">
                        <div class="input-group mb-3">
                            <div class="input-group-prepend">
                                <span class="input-group-text">HB</span>
                            </div>
                            <input type="text" class="form-control" name="hbAnalokEdit" value="<?=$getData['hemoglobin']?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="input-group mb-3">
                            <div class="input-group-prepend">
                                <span class="input-group-text">L</span>
                            </div>
                            <input type="text" class="form-control" name="lAnalokEdit" value="<?=$getData['leukosit']?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="input-group mb-3">
                            <div class="input-group-prepend">
                                <span class="input-group-text">tr</span>
                            </div>
                            <input type="text" class="form-control" name="trAnalokEdit" value="<?=$getData['trombosit']?>">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row jarak">
            <div class="col-md-3">
                <span>5. Riwayat alergi:</span>
            </div>
            <div class="col-md-9 form-group">
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="riwayatAnalokEdit" id="riwayatAnalokEdit1" value="1">
                    <label class="form-check-label" for="riwayatAnalokEdit1">Tidak</label>
                </div>
                <div class="radio radio-primary form-check-inline" style="width: 91%;">
                    <input class="form-check-input" type="radio" name="riwayatAnalokEdit" id="riwayatAnalokEdit2" value="2">
                    <label class="form-check-label mr-2" for="riwayatAnalokEdit2" style="width:9%">Ya, yaitu</label>
                    <input class="form-control" type="text" name="riwayatAnalokEditDll" id="riwayatAnalokEditDll" style="display:none;width: 100%;">
                </div>
            </div>
        </div>  
        <div class="row jarak">
            <div class="col-md-3">
                <span>6. Obat anastesi yang digunakan:</span>
            </div>
            <div class="col-md-9 form-group">
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="obatEdit" id="obatEdit1" value="1">
                    <label class="form-check-label" for="obatEdit1">Lidocain</label>
                </div>
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="obatEdit" id="obatEdit2" value="2">
                    <label class="form-check-label" for="obatEdit2">Pehacain</label>
                </div>
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="obatEdit" id="obatEdit3" value="3">
                    <label class="form-check-label" for="obatEdit3">Articaine</label>
                </div>
            </div>
        </div>  
        <div class="row jarak">
            <div class="col-md-3">
                <span class="ml-3">Dosis atau jumlah yang digunakan</span>
            </div>
            <div class="col-md-9">
                <input type="text" class="form-control" id="dosisAnalokEdit" name="dosisAnalokEdit" value="<?=$getData['dosis']?>">
            </div>
        </div>
        <div class="row jarak">
            <div class="col-md-3">
                <span class="ml-3">Diencerkan:</span>
            </div>
            <div class="col-md-9 form-group">
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="encerAnalokEdit" id="encerAnalokEdit1" value="1">
                    <label class="form-check-label" for="encerAnalokEdit1">Tidak</label>
                </div>
                <div class="radio radio-primary form-check-inline" style="width: 91%;">
                    <input class="form-check-input" type="radio" name="encerAnalokEdit" id="encerAnalokEdit2" value="2">
                    <label class="form-check-label mr-2" for="encerAnalokEdit2" style="width:9%">Ya, dosis</label>
                    <input class="form-control" type="text" name="encerAnalokEditDll" id="encerAnalokEditDll" style="display:none;width: 100%;">
                </div>
            </div>
        </div> 
        <div class="row jarak">
             <div class="col-md-3">
                <span class="ml-3">Lokasi Pemberian</span>
            </div>
            <div class="col-md-3">
                <input type="text" class="form-control" id="lokasiAnalokEdit" name="lokasiAnalokEdit" value="<?=$getData['lokasi_pemberian']?>">
            </div>
            <div class="col-md-1">
                <span>Jam</span>
            </div>
            <div class="col-md-2">
                <input type="time" class="form-control" id="timelokasiAnalokEdit" name="timelokasiAnalokEdit" value="<?=$getData['jam_lokasi_pemberian']?>">
            </div>
        </div>

        <div class="row jarak">
             <div class="col-md-3">
                 <span>7. Pemantauan status fsikologi selama tindakan (minimal tiap 5 menit):</span>
            </div>
            <div class="col-md-9">
                <table class="table table-bordered">
                    <tbody>
                        <tr>
                            <td style="width:10%">Jam</td>
                            <?php for($i = 0; $i <= 6; $i++): ?>
                                <input type="hidden" class="form-control" id="idFsikologi<?=$i;?>" name="idFsikologi[]" value="<?= isset($getDataFsikologi[$i]['id']) ? $getDataFsikologi[$i]['id'] : '';?>" readonly>
                                <?php $jam = isset($getDataFsikologi[$i]['jam']) ? $getDataFsikologi[$i]['jam'] : ''; ?>
                                <td><input type="time" class="form-control" id="pantauJamAnalokEdit<?=$i;?>" name="pantauJamAnalokEdit[]"></td>
                            <?php endfor; ?>
                        </tr>
                        <tr>
                            <td style="width:10%">TD (mmHg)</td>
                            <?php for($i = 0; $i <= 6; $i++): ?>
                                <?php $tekanan_darah = isset($getDataFsikologi[$i]['tekanan_darah']) ? $getDataFsikologi[$i]['tekanan_darah'] : ''; ?>
                                <td><input type="text" class="form-control" id="pantauTdAnalokEdit<?=$i;?>" name="pantauTdAnalokEdit[]"></td>
                            <?php endfor; ?>
                        </tr>
                        <tr>
                            <td style="width:10%">N (x/mnt)</td>
                            <?php for($i = 0; $i <= 6; $i++): ?>
                                <?php $nadi = isset($getDataFsikologi[$i]['nadi']) ? $getDataFsikologi[$i]['nadi'] : ''; ?>
                                <td><input type="text" class="form-control" id="pantauNAnalokEdit<?=$i;?>" name="pantauNAnalokEdit[]"></td>
                            <?php endfor; ?>
                        </tr>
                        <tr>
                            <td style="width:10%">RR (x/mnt)</td>
                            <?php for($i = 0; $i <= 6; $i++): ?>
                                <?php $respiratory_rate = isset($getDataFsikologi[$i]['respiratory_rate']) ? $getDataFsikologi[$i]['respiratory_rate'] : ''; ?>
                                <td><input type="text" class="form-control" id="pantauRRAnalokEdit<?=$i;?>" name="pantauRRAnalokEdit[]"></td>
                            <?php endfor; ?>    
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="row jarak">
            <div class="col-md-3">
                <span>8. Kejadian konversianastesi selama tindakan:</span>
            </div>
            <div class="col-md-9 form-group">
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="kejadianAnalokEdit" id="kejadianAnalokEdit1" value="1">
                    <label class="form-check-label" for="kejadianAnalokEdit1">Tidak</label>
                </div>
                <div class="radio radio-primary form-check-inline" style="width: 30%;">
                    <input class="form-check-input" type="radio" name="kejadianAnalokEdit" id="kejadianAnalokEdit2" value="2">
                    <label class="form-check-label mr-2" for="kejadianAnalokEdit2" style="width:50%">Ya, jenis anastesi</label>
                </div>
            </div>
        </div> 
        <div class="row">
            <div class="col-md-3">
                <span>9. Selesai Tindakan</span>
            </div>
            <div class="col-md-9">
                <div class="input-group mb-3">
                    <div class="input-group-prepend">
                        <span class="input-group-text">Jam</span>
                    </div>
                    <input type="time" class="form-control" name="jamAnalokEdit" value="<?=$getData['selesai_tindakan']?>">
                    <div class="input-group-append">
                        <span class="input-group-text">wib</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="row jarak">
            <div class="col-md-3">
                <span>10. Kondisi pasien saat tindakan:</span>
            </div>
        </div>
        <div class="row jarak">
            <div class="col-md-3">
                <span class="ml-4">Status Mental:</span>
            </div>
            <div class="col-md-9 form-group">
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="kondisiAnalokEdit" id="kondisiAnalokEdit1" value="1">
                    <label class="form-check-label" for="kondisiAnalokEdit1">Komposmetis</label>
                </div>
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="kondisiAnalokEdit" id="kondisiAnalokEdit2" value="2">
                    <label class="form-check-label" for="kondisiAnalokEdit2">Cemas</label>
                </div>
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="kondisiAnalokEdit" id="kondisiAnalokEdit3" value="3">
                    <label class="form-check-label" for="kondisiAnalokEdit3">Agitasi</label>
                </div>
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="kondisiAnalokEdit" id="kondisiAnalokEdit4" value="4">
                    <label class="form-check-label" for="kondisiAnalokEdit4">Mengantuk</label>
                </div>
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="kondisiAnalokEdit" id="kondisiAnalokEdit5" value="5">
                    <label class="form-check-label" for="kondisiAnalokEdit5">Koma</label>
                </div>
                <div class="radio radio-primary form-check-inline" style="width: 50%;">
                    <input class="form-check-input" type="radio" name="kondisiAnalokEdit" id="kondisiAnalokEdit6" value="6">
                    <label class="form-check-label" for="kondisiAnalokEdit6" style="width: 16%">Lainnya</label>
                </div>
            </div>
            <div class="col-md-3">
                <span class="ml-4">Tanda Toksisitas:</span>
            </div>
            <div class="col-md-9 form-group">
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="toksikAnalokEdit" id="toksikAnalokEdit1" value="1">
                    <label class="form-check-label" for="toksikAnalokEdit1">Tidak Ada</label>
                </div>
                <div class="radio radio-primary form-check-inline" style="width: 50%;">
                    <input class="form-check-input" type="radio" name="toksikAnalokEdit" id="toksikAnalokEdit2" value="2">
                    <label class="form-check-label" for="toksikAnalokEdit2" style="width: 16%">Ada</label>
                    <input class="form-control" type="text" name="toksikAnalokEditDll" id="toksikAnalokEditDll" style="display:none;width: 100%;">
                </div>
            </div>
            <div class="col-md-3">
                <span class="ml-4">Mual Muntah:</span>
            </div>
            <div class="col-md-9 form-group">
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="muntahAnalokEdit" id="muntahAnalokEdit1" value="1">
                    <label class="form-check-label" for="muntahAnalokEdit1">Tidak Ada</label>
                </div>
                <div class="radio radio-primary form-check-inline" style="width: 50%;">
                    <input class="form-check-input" type="radio" name="muntahAnalokEdit" id="muntahAnalokEdit2" value="2">
                    <label class="form-check-label" for="muntahAnalokEdit2" style="width: 16%">Ada</label>
                </div>
            </div>
            <div class="col-md-3">
                <span class="ml-4">Keluhan Nyeri:</span>
            </div>
            <div class="col-md-9 form-group">
                <div class="radio radio-primary form-check-inline">
                    <input class="form-check-input" type="radio" name="nyeriAnalokEdit" id="nyeriAnalokEdit1" value="1">
                    <label class="form-check-label" for="nyeriAnalokEdit1">Tidak Ada</label>
                </div>
                <div class="radio radio-primary form-check-inline" style="width: 50%;">
                    <input class="form-check-input" type="radio" name="nyeriAnalokEdit" id="nyeriAnalokEdit2" value="2">
                    <label class="form-check-label" for="nyeriAnalokEdit2" style="width: 16%">Ada</label>
                    <input class="form-control" type="text" name="nyeriAnalokEditDll" id="nyeriAnalokEditDll" style="display:none;width: 100%;">
                </div>
            </div>
            <div class="col-md-3">
                <span class="ml-4">Perdarahan:</span>
            </div>
            <div class="col-md-9 form-group">
                <div class="input-group mb-3">
                    <input type="text" class="form-control" name="perdarahanAnalokEdit" id="perdarahanAnalokEdit" value="<?=$getData['pendarahan']?>">
                    <div class="input-group-append">
                        <span class="input-group-text" id="basic-addon2">CC</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row jarak">
             <div class="col-md-3">
                <span>11. Rencana asuhan pasca tindakan</span>
            </div>
        </div>
        <div class="row jarak">
             <div class="col-md-3">
                <span class="ml-4">Observasi</span>
            </div>
            <div class="col-md-9">
                <input type="text" class="form-control" id="observasiAnalokEdit" name="observasiAnalokEdit" value="<?=$getData['observasi']?>">
            </div>
        </div>
        <div class="row jarak">
             <div class="col-md-3">
                <span class="ml-4">Edukasi dan follow up</span>
            </div>
            <div class="col-md-9">
                <input type="text" class="form-control" id="eduAnalokEdit" name="eduAnalokEdit" value="<?=$getData['edukasi_followup']?>">
            </div>
        </div>

         <div class="row jarak">
             <div class="col-md-3">
                <span>12. Pasien pindah jam:</span>
            </div>
            <div class="col-md-9">
                <div class="input-group mb-3" style="min-width: 100%;">
                    <input type="time" class="form-control" name="pindahAnalokEdit" id="pindahAnalokEdit" style="max-width: 10%;" value="<?=$getData['jam_pasien_pindah']?>">
                    <div class="input-group-append">
                        <span class="input-group-text" id="basic-addon2">wib, ke</span>
                    </div>
                    <div class="radio radio-primary form-check-inline ml-2">
                        <input class="form-check-input" type="radio" name="manaAnalokEdit" id="manaAnalokEdit1" value="1">
                        <label class="form-check-label" for="manaAnalokEdit1">Ruang rawat</label>
                    </div>
                    <div class="radio radio-primary form-check-inline">
                        <input class="form-check-input" type="radio" name="manaAnalokEdit" id="manaAnalokEdit2" value="2">
                        <label class="form-check-label" for="manaAnalokEdit2">Pulang</label>
                    </div>
                    <div class="radio radio-primary form-check-inline" style="width: 50%;">
                        <input class="form-check-input" type="radio" name="manaAnalokEdit" id="manaAnalokEdit3" value="3">
                        <label class="form-check-label" for="manaAnalokEdit3">Lainnya</label>
                        <input class="form-control" type="text" name="manaAnalokEditDll" id="manaAnalokEditDll" style="display:none;width: 100%;">
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="offset-10 col-lg-2">
                <div class="form-group">
                    <div class="pull-right">
                    <button type="button" class="btn btn-primary btn-block" id="simpan-PAGEdit"><i class="fa fa-save"></i> Simpan</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
<script>
$(document).ready(function() {
    $("input[name='mentalAnalokEdit']").on('change', function() {
        if($(this).val()== 6){
            $("#mentalAnalokEditDll").show();
            $('#mentalAnalokEditDll').val('<?=$getData['status_mental_pra_tindakan_desk']?>');
        } else{
            $("#mentalAnalokEditDll").hide();
            $("#mentalAnalokEditDll").val('');
        }
    });
    $("input[name='riwayatAnalokEdit']").on('change', function() {
        if($(this).val()== 2){
            $("#riwayatAnalokEditDll").show();
            $('#riwayatAnalokEditDll').val('<?=$getData['riwayat_alergi_desk']?>');
        } else{
            $("#riwayatAnalokEditDll").hide();
            $('#riwayatAnalokEditDll').val('');
        }
    });
    $("input[name='encerAnalokEdit']").on('change', function() {
        if($(this).val()== 2){
            $("#encerAnalokEditDll").show();
            $('#encerAnalokEditDll').val('<?=$getData['diencerkan_dosis']?>');
        } else{
            $("#encerAnalokEditDll").hide();
            $('#encerAnalokEditDll').val('');
        }
    });
    $("input[name='toksikAnalokEdit']").on('change', function() {
        if($(this).val()== 2){
            $("#toksikAnalokEditDll").show();
            $('#toksikAnalokEditDll').val('<?=$getData['tanda_toksisitas_desk']?>');
        } else{
            $("#toksikAnalokEditDll").hide();
            $('#toksikAnalokEditDll').val('');
        }
    });
    $("input[name='nyeriAnalokEdit']").on('change', function() {
        if($(this).val()== 2){
            $("#nyeriAnalokEditDll").show();
            $('#nyeriAnalokEditDll').val('<?=$getData['keluhan_nyeri_desk']?>');
        } else{
            $("#nyeriAnalokEditDll").hide();
            $('#nyeriAnalokEditDll').val('');
        }
    });
    $("input[name='manaAnalokEdit']").on('change', function() {
        if($(this).val()== 3){
            $("#manaAnalokEditDll").show();
            $('#manaAnalokEditDll').val('<?=$getData['lokasi_pasien_pindah_desk']?>');
        } else{
            $("#manaAnalokEditDll").hide();
            $('#manaAnalokEditDll').val('');
        }
    });
    // Inisialisasi form dengan data yang ada
    $('#mentalAnalokEdit' + '<?=$getData['status_mental_pra_tindakan']?>').prop('checked', true).trigger('change');
    $('#riwayatAnalokEdit' + '<?=$getData['riwayat_alergi']?>').prop('checked', true).trigger('change');
    $('#obatEdit' + '<?=$getData['obat_anastesi']?>').prop('checked', true).trigger('change');
    $('#encerAnalokEdit' + '<?=$getData['diencerkan']?>').prop('checked', true).trigger('change');
    $('#kejadianAnalokEdit' + '<?=$getData['kejadian_selama_tindakan']?>').prop('checked', true).trigger('change');
    $('#kondisiAnalokEdit' + '<?=$getData['status_mental_saat_tindakan']?>').prop('checked', true).trigger('change');
    $('#toksikAnalokEdit' + '<?=$getData['tanda_toksisitas']?>').prop('checked', true).trigger('change');
    $('#muntahAnalokEdit' + '<?=$getData['mual_muntah']?>').prop('checked', true).trigger('change');
    $('#nyeriAnalokEdit' + '<?=$getData['keluhan_nyeri']?>').prop('checked', true).trigger('change');
    $('#manaAnalokEdit' + '<?=$getData['lokasi_pasien_pindah']?>').prop('checked', true).trigger('change');
    // $('#dosisAnalokEdit').val('<?//=$getData['dosis']?>');
    // $('#lokasiAnalokEdit').val('<?//=$getData['lokasi_pemberian']?>');
    // $('#timelokasiAnalokEdit').val('<?//=$getData['selesai_tindakan']?>');
    // $('#pindahAnalokEdit').val('<?//=$getData['jam_pasien_pindah']?>');
    // $('#tglAnalokEdit').val('<?//=$getData['tgl_tindakan']?>');
    // $('#tndAnalokEdit').val('<?//=$getData['tindakan_operasi']?>');
    // $('#bbAnalokEdit').val('<?//=$getData['berat_badan']?>');
    // $('#TBAnalokEdit').val('<?//=$getData['tinggi_badan']?>');
    // $('#tdAnalokEdit').val('<?//=$getData['tekanan_darah']?>');
    // $('#rrAnalokEdit').val('<?//=$getData['respiratory_rate']?>');
    // $('#nAnalokEdit').val('<?//=$getData['nadi']?>');
    // $('#sAnalokEdit').val('<?//=$getData['suhu']?>');
    // $('#hbAnalokEdit').val('<?//=$getData['hemoglobin']?>');
    // $('#lAnalokEdit').val('<?//=$getData['leukosit']?>');
    // $('#trAnalokEdit').val('<?//=$getData['trombosit']?>');
    // $('#observasiAnalokEdit').val('<?//=$getData['observasi']?>');
    // $('#eduAnalokEdit').val('<?//=$getData['edukasi_followup']?>');
    // $('#perdarahanAnalokEdit').val('<?//=$getData['pendarahan']?>');
    // Set jam pantau
    <?php for($i = 0; $i <= 6; $i++): ?>
        $('#pantauJamAnalokEdit<?=$i;?>').val('<?=$getDataFsikologi[$i]['jam']?>');
        $('#pantauTdAnalokEdit<?=$i;?>').val('<?=$getDataFsikologi[$i]['tekanan_darah']?>');
        $('#pantauNAnalokEdit<?=$i;?>').val('<?=$getDataFsikologi[$i]['nadi']?>');
        $('#pantauRRAnalokEdit<?=$i;?>').val('<?=$getDataFsikologi[$i]['respiratory_rate']?>');
    <?php endfor; ?>

    $("#simpan-PAGEdit").on('click', function (event) {
        event.preventDefault();
        alertify.confirm('Konfirmasi', 'Pilih Ok, jika setuju untuk Update',
        function(){
        var formPAG = $("#formPAGEdit").serializeArray();
            $.ajax({
                    dataType:'json',
                    url: "<?php echo base_url('gigi/PemantauanAnastesiGigi/simpan') ?>",
                    method: "POST",
                    data: formPAG,
                    success: function(data) {
                        if(data.status == 'success'){
                            alertify.success('Data Tersimpan');
                            location.reload();
                            }else{
                            alertify.warning('Internal Server Error');
                            }
                    }
                });
        }, function(){ 
            alertify.error('Batal')
        });
    });
});
</script>
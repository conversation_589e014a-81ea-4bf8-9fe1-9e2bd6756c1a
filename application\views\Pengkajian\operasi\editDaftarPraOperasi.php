<form id="inputEditDaftarPraOperasi">
    <div class="modal-header">
        <h4 class="modal-title mt-0" id="mySmallModalLabel">Ubah Pendaftaran Pra Operasi</h4>
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
    </div>
    <div class="modal-body" style="max-height: calc(100vh - 200px); overflow-y: auto;">
        <input type="hidden" name="id_waiting_list" value="<?= $getDaftarOperasi['id_waiting_list'] ?? 0 ?>">
        <input type="hidden" name="norm" value="<?= $getDaftarOperasi['norm'] ?? null ?>">
        <input type="hidden" name="id" value="<?= $id ?? null ?>">
        <input type="hidden" name="nokun" value="<?= $getDaftarOperasi['nokun'] ?? null ?>">
        <input type="hidden" name="ruang_tujuan" value="<?= $getDaftarOperasi['ruang_tujuan'] ?? null ?>">
        <!-- <PERSON><PERSON> dokter bedah -->
        <div class="row form-group">
            <label for="dokter-bedah-edit-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                Dokter Bedah
            </label>
            <div class="col-md-8">
                <select name="dokter_bedah[]" id="dokter-bedah-edit-pendaftaran-pra-operasi" class="form-control">
                    <option value=""></option>
                    <?php foreach ($listDr as $ld): ?>
                        <option id="<?= $ld['ID_DOKTER'] ?>" value="<?= $ld['ID_DOKTER'] ?>" data-smf="<?= $ld['ID_SMF'] ?>">
                            <?= $ld['DOKTER'] . ' - ' . $ld['SMF'] ?>
                        </option>
                    <?php endforeach ?>
                </select>
            </div>
        </div>
        <!-- Akhir dokter bedah -->
        <!-- Mulai rencana tindakan operasi -->
        <div class="row form-group">
            <label for="rencana-tindakan-edit-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                Rencana Tindakan Operasi <br>
                <small>(diisi lengkap dengan bagian/organ yang akan dioperasi)</small>
            </label>
            <div class="col-md-8">
                <textarea id="rencana-tindakan-edit-pendaftaran-pra-operasi" name="rencana_tindakan_operasi[]" class="form-control" placeholder="[ Tuliskan Rencana Tindakan ]"><?= $getDaftarOperasi['rencana_tindakan_operasi'] ?? null ?></textarea>
            </div>
        </div>
        <!-- Akhir rencana tindakan operasi -->
        <!-- Mulai join operasi -->
        <div class="row form-group">
            <label for="join-operasi-edit-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                <em>Join</em> Operasi
            </label>
            <?php foreach ($joinOperasi as $jo): ?>
                <div class="col-md form-check form-check-inline mx-1">
                    <div class="radio radio-primary">
                        <input type="radio" name="join_operasi" id="join-operasi-edit-pendaftaran-pra-operasi<?= $jo['id_variabel'] ?>" class="join-operasi-edit-pendaftaran-pra-operasi" value="<?= $jo['id_variabel'] ?>" <?= isset($getDaftarOperasi['join_operasi']) ? ($getDaftarOperasi['join_operasi'] == $jo['id_variabel'] ? 'checked' : null) : ($jo['status_checked'] == 1 ? 'checked' : null) ?>>
                        <label for="join-operasi-edit-pendaftaran-pra-operasi<?= $jo['id_variabel'] ?>">
                            <?= $jo['variabel'] ?>
                        </label>
                    </div>
                </div>
            <?php endforeach ?>
        </div>
        <!-- Akhir join operasi -->
        <!-- Mulai form dokter bedah lain -->
        <div class="card new-card form-group" id="form-dokter-bedah-lain-edit-pendaftaran-pra-operasi">
            <div class="card-header new-card-header">Dokter Bedah Lain dan Tindakannya</div>
            <div class="card-body">
                <!-- Mulai dokter bedah lain -->
                <div class="row form-group">
                    <label for="dokter-lain-edit-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                        Dokter Bedah Lain
                    </label>
                    <div class="col-md-8">
                        <select id="dokter-lain-edit-pendaftaran-pra-operasi" class="form-control">
                            <option value=""></option>
                            <?php foreach ($listDr as $ld): ?>
                                <option id="dokter-lain-edit-pendaftaran-pra-operasi-<?= $ld['ID_DOKTER'] ?>" value="<?= $ld['ID_DOKTER'] ?>">
                                    <?= $ld['DOKTER'] . ' - ' . $ld['SMF'] ?>
                                </option>
                            <?php endforeach ?>
                        </select>
                    </div>
                </div>
                <!-- Akhir dokter bedah lain -->
                <!-- Mulai rencana tindakan dokter bedah lain -->
                <div class="row form-group">
                    <label for="rencana-tindakan-lain-edit-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                        Rencana Tindakan Operasi untuk Dokter Lain <br>
                        <small>(diisi lengkap dengan bagian atau organ yang akan dioperasi)</small>
                    </label>
                    <div class="col-md-8">
                        <textarea id="rencana-tindakan-lain-edit-pendaftaran-pra-operasi" class="form-control" placeholder="[ Tuliskan Rencana Tindakan untuk Dokter Lain ]"></textarea>
                    </div>
                </div>
                <!-- Akhir rencana tindakan dokter bedah lain -->
                <!-- Mulai aksi dokter bedah lain -->
                <div class="row form-group">
                    <!-- <div class="col-sm-6">
                        <button type="button" class="btn btn-outline-danger btn-block waves-effect" id="hapus-dokter-bedah-lain-edit-pendaftaran-pra-operasi">
                            Hapus
                        </button>
                    </div> -->
                    <div class="col-sm-12">
                        <button type="button" class="btn btn-outline-success btn-block waves-effect" id="tambah-dokter-bedah-lain-edit-pendaftaran-pra-operasi">
                            Tambah
                        </button>
                    </div>
                </div>
                <!-- Akhir aksi dokter bedah lain -->
                <!-- Mulai tabel dokter bedah lain -->
                <div class="table-responsive overflow-auto">
                    <table class="table table-bordered table-hover table-custom" cellspacing="0" width="100%">
                        <thead>
                            <tr class="table-tr-custom">
                                <th>#</th>
                                <th>Dokter Bedah dan Rencana Tindakan Lain</th>
                            </tr>
                        </thead>
                        <tbody id="list-dokter-lain-edit-pendaftaran-pra-operasi">
                            <?php
                            $i = 0;
                            foreach ($tindakanDokterLain as $tdl) {
                            ?>
                                <tr>
                                    <td class="text-center">
                                        <button type="button" class="btn btn-danger btn-sm hapus-item-dokter-lain-edit" title="Hapus"><i class="fa fa-xmark"></i></button>
                                        <input type="hidden" class="isi-id-dokter-lain-edit-pendaftaran-pra-operasi" name="dokter_bedah[]" value="<?= $tdl['dokter_lain'] ?>">
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <input type="text" class="form-control isi-dokter-lain-edit-pendaftaran-pra-operasi" value="<?= $tdl['nama_dokter_lain'] ?>" aria-label="Dokter Bedah Lain" readonly>
                                        </div>
                                        <div>
                                            <textarea class="form-control isi-rencana-tindakan-edit-pendaftaran-pra-operasi" name="rencana_tindakan_operasi[]" aria-label="Rencana Tindakan Operasi untuk Dokter Lain" readonly><?= $tdl['rencana_tindakan'] ?></textarea>
                                        </div>
                                    </td>
                                </tr>
                            <?php
                            }
                            $i++;
                            ?>
                        </tbody>
                    </table>
                </div>
                <!-- Akhir tabel dokter bedah lain -->
            </div>
        </div>
        <!-- Akhir form dokter bedah lain -->
        <!-- Mulai diagnosis medis -->
        <div class="row form-group">
            <label for="diagnosa-medis-edit-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                Diagnosis Medis
            </label>
            <div class="col-md-8">
                <input type="text" id="diagnosa-medis-edit-pendaftaran-pra-operasi" name="diagnosa_medis" class="form-control" placeholder="[ Diagnosis Medis ]" value="<?= $getDaftarOperasi['diagnosa_medis'] ?? null ?>">
            </div>
        </div>
        <!-- Akhir diagnosis medis -->
        <!-- Mulai perkiraan lama operasi -->
        <div class="row form-group">
            <label for="lama-operasi-edit-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                Perkiraan Lama Operasi
            </label>
            <div class="col-md-8">
                <div class="input-group">
                    <input type="number" class="form-control" id="lama-operasi-edit-pendaftaran-pra-operasi" name="perkiraan_lama_operasi" placeholder="[ Lama Operasi dalam Menit ]" value="<?= $getDaftarOperasi['perkiraan_lama_operasi'] ?? null ?>">
                    <span class="input-group-text">menit</span>
                </div>
            </div>
        </div>
        <!-- Akhir perkiraan lama operasi -->
        <!-- Mulai tanggal -->
        <div class="row form-group">
            <label for="tanggal-edit-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                Tanggal
            </label>
            <div class="col-md-8">
                <div class="input-group">
                    <input type="date" class="form-control" name="tanggal_operasi" id="tanggal-edit-pendaftaran-pra-operasi" value="<?= $getDaftarOperasi['tanggal_operasi'] != '0000-00-00' ? date('Y-m-d', strtotime($getDaftarOperasi['tanggal_operasi'])) : null ?>">
                    <div class="input-group-append">
                        <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
        </div>
        <!-- Akhir tanggal -->
        <!-- Mulai waktu -->
        <div class="row form-group">
            <label for="waktu-edit-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                Waktu
            </label>
            <div class="col-md-8">
                <div class="input-group">
                    <input type="time" class="form-control" name="jam_operasi" id="waktu-edit-pendaftaran-pra-operasi" value="<?= $getDaftarOperasi['jam_operasi'] != '00:00:00' ? date('H:i', strtotime($getDaftarOperasi['jam_operasi'])) : null ?>">
                    <div class="input-group-append">
                        <span class="input-group-text"><i class="fa fa-clock"></i></span>
                    </div>
                </div>
            </div>
        </div>
        <!-- Akhir waktu -->
        <!-- Mulai penjamin/pembiayaan operasi -->
        <div class="row form-group">
            <label for="ruangan-operasi-edit-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                Penjamin/Pembiayaan Operasi <span class="text-danger">*</span>
            </label>
            <div class="col-md-8">
                <select name="ruang_operasi" id="ruangan-operasi-edit-pendaftaran-pra-operasi" class="form-control" required>
                    <option value=""></option>
                    <?php
                    $penjaminOperasi = $this->masterModel->referensiSimpel(81);
                    foreach ($penjaminOperasi as $po):
                        if ($po['ID'] == 2 || $po['ID'] == 16):
                    ?>
                        <option value="<?= $po['ID'] ?>" <?= $getDaftarOperasi['ruang_operasi'] == $po['ID'] ? 'selected' : '' ?>><?= $po['DESKRIPSI'] ?></option>
                    <?php
                        endif;
                    endforeach;
                    ?>
                </select>
            </div>
        </div>
        <!-- Akhir penjamin/pembiayaan operasi -->
        <!-- Mulai kelas operasi -->
        <div class="row form-group">
            <label for="kelas-operasi-edit-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                Kelas Rawat
            </label>
            <div class="col-md-8">
                <select name="kelas" id="kelas-operasi-edit-pendaftaran-pra-operasi" class="form-control">
                    <option value=""></option>
                    <?php foreach ($kelasOperasi as $ko): ?>
                        <option value="<?= $ko['ID'] ?>" <?= $getDaftarOperasi['kelas'] == $ko['ID'] ? 'selected' : '' ?>><?= $ko['DESKRIPSI'] ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
        <!-- Akhir kelas operasi -->
        <!-- Mulai tujuan operasi -->
        <div class="row form-group">
            <label for="tujuanOperasiDafOpeEdit" class="col-form-label col-md-4">
                Tujuan Operasi
            </label>
            <?php foreach ($tujuanOperasi as $to): ?>
                <div class="col-md form-check form-check-inline mx-1">
                    <div class="radio radio-primary">
                        <input type="radio" name="tujuan_operasi" id="tujuanOperasiDafOpeEdit<?= $to['id_variabel'] ?>" class="tujuanOperasiDafOpeEdit" value="<?= $to['id_variabel'] ?>" <?= isset($getDaftarOperasi['tujuan_operasi']) ? ($getDaftarOperasi['tujuan_operasi'] == $to['id_variabel'] ? 'checked' : null) : ($to['status_checked'] == 1 ? 'checked' : null) ?>>
                        <label for="tujuanOperasiDafOpeEdit<?= $to['id_variabel'] ?>">
                            <?= $to['variabel'] ?>
                        </label>
                    </div>
                </div>
            <?php endforeach ?>
        </div>
        <!-- Akhir tujuan operasi -->
        <!-- Mulai sifat operasi -->
        <div class="row form-group">
            <label for="sifatOperasiDafOpeEdit" class="col-form-label col-md-4">
                Sifat Operasi
            </label>
            <?php foreach ($sifatOperasi as $so): ?>
                <div class="col-md form-check form-check-inline mx-1">
                    <div class="radio radio-primary">
                        <input type="radio" name="sifat_operasi" id="sifatOperasiDafOpeEdit<?= $so['id_variabel'] ?>" class="sifatOperasiDafOpeEdit" value="<?= $so['id_variabel'] ?>" <?= isset($getDaftarOperasi['sifat_operasi']) ? ($getDaftarOperasi['sifat_operasi'] == $so['id_variabel'] ? 'checked' : null) : ($so['status_checked'] == 1 ? 'checked' : null) ?>>
                        <label for="sifatOperasiDafOpeEdit<?= $so['id_variabel'] ?>">
                            <?= $so['variabel'] ?>
                        </label>
                    </div>
                </div>
            <?php endforeach ?>
        </div>
        <!-- Akhir sifat operasi -->
        <!-- Mulai alasan CITO -->
        <div class="row form-group d-none" id="form-cito-edit-pendaftaran-pra-operasi">
            <label for="alasan-cito-edit-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                Alasan CITO
            </label>
            <div class="col-md-8">
                <select name="sifat_operasi_lain" id="alasan-cito-edit-pendaftaran-pra-operasi" class="form-controller">
                    <option value=""></option>
                </select>
            </div>
        </div>
        <!-- Akhir alasan CITO -->
        <!-- Mulai alasan urgent -->
        <div class="row form-group d-none" id="form-urgent-edit-pendaftaran-pra-operasi">
            <label for="alasan-urgent-edit-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                Alasan <em>Urgent</em>
            </label>
            <div class="col-md-8">
                <select name="alasan_urgent" id="alasan-urgent-edit-pendaftaran-pra-operasi" class="form-controller">
                    <option value=""></option>
                </select>
            </div>
        </div>
        <!-- Akhir alasan urgent -->
        <!-- Mulai alasan prioritas -->
        <div class="row form-group d-none" id="form-prioritas-edit-pendaftaran-pra-operasi">
            <label for="alasan-prioritas-edit-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                Alasan Prioritas
            </label>
            <div class="col-md-8">
                <select name="alasan_prioritas" id="alasan-prioritas-edit-pendaftaran-pra-operasi" class="form-controller">
                    <option value=""></option>
                </select>
            </div>
        </div>
        <!-- Akhir alasan prioritas -->
        <!-- Mulai rencana jenis pembiusan -->
        <div class="row form-group">
            <label for="rencanaJenisPembiusanDafOpeEdit" class="col-form-label col-md-4">
                Rencana Jenis Pembiusan
            </label>
            <?php foreach ($rencanaJenisPembiusan as $renJenPem): ?>
                <div class="col-md form-check form-check-inline mx-1">
                    <div class="radio radio-primary">
                        <input type="radio" name="rencana_jenis_pembiusan" id="rencanaJenisPembiusanDafOpeEdit<?= $renJenPem['id_variabel'] ?>" class="rencanaJenisPembiusanDafOpeEdit" value="<?= $renJenPem['id_variabel'] ?>" <?= isset($getDaftarOperasi['rencana_jenis_pembiusan']) ? ($getDaftarOperasi['rencana_jenis_pembiusan'] == $renJenPem['id_variabel'] ? 'checked' : null) : ($renJenPem['status_checked'] == 1 ? 'checked' : null) ?>>
                        <label for="rencanaJenisPembiusanDafOpeEdit<?= $renJenPem['id_variabel'] ?>">
                            <?= $renJenPem['variabel'] ?>
                        </label>
                    </div>
                </div>
            <?php endforeach ?>
        </div>
        <!-- Akhir rencana jenis pembiusan -->
        <!-- Mulai rencana jenis pembiusan lain -->
        <div class="row form-group d-none" id="ShowrencanaJenisPembiusanEdit">
            <label for="rencanaJenisPembiusan_lainnyaEdit" class="col-form-label col-md-4">
                Jenis Pembiusan Lainnya
            </label>
            <div class="col-md-8">
                <textarea id="rencanaJenisPembiusan_lainnyaEdit" name="rencana_jenis_pembiusan_lain" class="form-control" placeholder="[ Jelaskan Lainnya ]"><?= $getDaftarOperasi['rencana_jenis_pembiusan_lain'] ?? null ?></textarea>
            </div>
        </div>
        <!-- Akhir rencana jenis pembiusan lain -->
        <!-- Mulai potong beku -->
        <div class="row form-group">
            <label for="potong-beku-edit-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                Potong Beku
            </label>
            <?php foreach ($potongBeku as $pb): ?>
                <div class="col-md form-check form-check-inline mx-1">
                    <div class="radio radio-primary">
                        <input type="radio" name="potong_beku" id="potong-beku-edit-pendaftaran-pra-operasi<?= $pb['id_variabel'] ?>" class="potong-beku-edit-pendaftaran-pra-operasi" value="<?= $pb['id_variabel'] ?>" <?= isset($getDaftarOperasi['potong_beku']) ? ($getDaftarOperasi['potong_beku'] == $pb['id_variabel'] ? 'checked' : null) : ($pb['status_checked'] == 1 ? 'checked' : null) ?>>
                        <label for="potong-beku-edit-pendaftaran-pra-operasi<?= $pb['id_variabel'] ?>">
                            <?= $pb['variabel'] ?>
                        </label>
                    </div>
                </div>
            <?php endforeach ?>
        </div>
        <!-- Akhir potong beku -->
        <!-- Mulai catatan khusus -->
        <div class="row form-group">
            <label for="catatan-khusus-edit-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                Catatan Khusus
            </label>
            <div class="col-md-8">
                <textarea id="catatan-khusus-edit-pendaftaran-pra-operasi" name="catatan_khusus" class="form-control" placeholder="[ Tuliskan catatan khusus]"><?= $getDaftarOperasi['catatan_khusus'] ?? null ?></textarea>
            </div>
        </div>
        <!-- Akhir catatan khusus -->
        <!-- Mulai keterangan perubahan -->
        <div class="row form-group">
            <label for="keterangan-perubahan-edit-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                Keterangan Perubahan Rencana
            </label>
            <div class="col-md-8">
                <textarea id="keterangan-perubahan-edit-pendaftaran-pra-operasi" name="keterangan" class="form-control" placeholder="[ Tuliskan Keterangan Perubahan Rencana ]"><?= $getDaftarOperasi['keterangan_rencana'] ?? null ?></textarea>
            </div>
        </div>
        <!-- Akhir keterangan perubahan -->
    </div>
    <div class="modal-footer">
        <button type="submit" class="btn btn-primary waves-effect" id="btn-simpan-edit-pendaftaran-pra-operasi">
            <i class="fa fa-save"></i> Simpan Perubahan
        </button>
    </div>
</form>

<script>
    $(document).ready(function() {
        // Mulai dokter bedah
$('#dokter-bedah-edit-pendaftaran-pra-operasi').select2({
    placeholder: '[ Pilih ]',
    dropdownParent: $('#dokter-bedah-edit-pendaftaran-pra-operasi').closest('.modal')
}).val("<?= $getDaftarOperasi['dokter_bedah'] ?? 0 ?>").trigger('change');
        // Akhir dokter bedah

        // Mulai dokter bedah lain
        $('#dokter-lain-edit-pendaftaran-pra-operasi').select2({
            placeholder: '[ Pilih dokter bedah lain ]',
            dropdownParent: $('#dokter-lain-edit-pendaftaran-pra-operasi').closest('.modal')
        });
        // Akhir dokter bedah lain

        // Mulai penjamin/pembiayaan operasi
$('#ruangan-operasi-edit-pendaftaran-pra-operasi').select2({
    placeholder: '[ Pilih penjamin/pembiayaan ]',
    dropdownParent: $('#ruangan-operasi-edit-pendaftaran-pra-operasi').closest('.modal')
}).val("<?= $getDaftarOperasi['ruang_operasi'] ?? null ?>").trigger('change');
        // Akhir penjamin/pembiayaan operasi

        // Mulai kelas operasi
        $('#kelas-operasi-edit-pendaftaran-pra-operasi').select2({
            placeholder: '[ Pilih kelas operasi ]',
            dropdownParent: $('#kelas-operasi-edit-pendaftaran-pra-operasi').closest('.modal')
        }).val("<?= $getDaftarOperasi['kelas'] ?? null ?>").trigger('change');
        // Akhir kelas operasi

        // Fungsi untuk menambah dokter bedah lain
        function tambahDokterBedahLainEdit() {
            let jumlah = $('.hapus-item-dokter-lain-edit').length;
            let id_dokter = $('#dokter-lain-edit-pendaftaran-pra-operasi').val();
            let dokter = $('#dokter-lain-edit-pendaftaran-pra-operasi option:selected').text().trim();
            let tindakan = $('#rencana-tindakan-lain-edit-pendaftaran-pra-operasi').val();
            let isi = null;

            // Mulai periksa
            if (jumlah <= 4) { // Periksa jumlah apakah sudah ada 5
                if (id_dokter !== '' && tindakan !== '') { // Periksa apakah sudah diisi
                    // Mulai isi
                    isi = '<tr>' +
                        '<td class="text-center">' +
                        "<button type='button' class='btn btn-danger btn-sm hapus-item-dokter-lain-edit' title='Hapus'><i class='fa fa-xmark'></i></button>" +
                        "<input type='hidden' class='isi-id-dokter-lain-edit-pendaftaran-pra-operasi' name='dokter_bedah[]' value='" + id_dokter + "'>" +
                        '</td>' +
                        '<td>' +
                        "<div class='form-group'><input type='text' class='form-control isi-dokter-lain-edit-pendaftaran-pra-operasi' value='" + dokter + "' aria-label='Dokter Bedah Lain' readonly></div>" +
                        "<div><textarea class='form-control isi-rencana-tindakan-edit-pendaftaran-pra-operasi' name='rencana_tindakan_operasi[]' aria-label='Rencana Tindakan Operasi untuk Dokter Lain' readonly>" + tindakan + "</textarea></div>" +
                        '</td>' +
                        '</tr>';
                    $(isi).hide().appendTo('#list-dokter-lain-edit-pendaftaran-pra-operasi').fadeIn(1000);
                    // Akhir isi

                    // Mulai bersihkan form`
                    $('#dokter-lain-edit-pendaftaran-pra-operasi').val(0).trigger('change');
                    $('#rencana-tindakan-lain-edit-pendaftaran-pra-operasi').val(null);
                    // Akhri bersihkan form
                    return true;
                } else {
                    return false;
                }
            } else {
                alertify.warning('Sudah ada 5 dokter bedah lain');
                return false;
            }
            // Akhir periksa
        }

        // Mulai tambah dokter bedah lain (manual button click)
        $('#tambah-dokter-bedah-lain-edit-pendaftaran-pra-operasi').click(function() {
            if (!tambahDokterBedahLainEdit()) {
                alertify.warning('Mohon isi data dokter bedah lain dan tindakannya lebih dulu');
            }
        });
        // Akhir tambah dokter bedah lain

        // Auto-add yang lebih user-friendly - dipicu ketika user pindah ke field lain atau melakukan action
        // HANYA akan auto-add jika KEDUA field sudah terisi (dokter DAN rencana tindakan)
        function checkAutoAddEdit() {
            let id_dokter = $('#dokter-lain-edit-pendaftaran-pra-operasi').val();
            let tindakan = $('#rencana-tindakan-lain-edit-pendaftaran-pra-operasi').val().trim();
            
            // Validasi ketat: KEDUA field harus terisi
            if (id_dokter !== '' && id_dokter !== null && tindakan !== '' && tindakan.length > 0) {
                tambahDokterBedahLainEdit();
            }
        }

        // Event listener untuk auto-add - hanya pada action tertentu, bukan saat mengetik
        $('#rencana-tindakan-lain-edit-pendaftaran-pra-operasi').on('blur', function() {
            // Auto-add ketika user keluar dari textarea (blur) - tapi hanya jika dokter juga sudah dipilih
            let id_dokter = $('#dokter-lain-edit-pendaftaran-pra-operasi').val();
            if (id_dokter !== '' && id_dokter !== null) {
                checkAutoAddEdit();
            }
        });

        // Auto-add ketika user mengubah pilihan dokter - tapi hanya jika rencana tindakan juga sudah diisi
        $('#dokter-lain-edit-pendaftaran-pra-operasi').on('change', function() {
            let tindakan = $('#rencana-tindakan-lain-edit-pendaftaran-pra-operasi').val().trim();
            if (tindakan !== '' && tindakan.length > 0) {
                checkAutoAddEdit();
            }
        });

        // Auto-add ketika user klik field lain setelah mengisi KEDUA field
        $('#diagnosa-medis-edit-pendaftaran-pra-operasi, #lama-operasi-edit-pendaftaran-pra-operasi, #tanggal-edit-pendaftaran-pra-operasi, #waktu-edit-pendaftaran-pra-operasi, #ruangan-operasi-edit-pendaftaran-pra-operasi').on('focus', function() {
            // Auto-add ketika user klik field lain - tapi hanya jika KEDUA field sudah terisi
            checkAutoAddEdit();
        });

        // Auto-add ketika user klik radio button tujuan operasi, sifat operasi, dll
        $('.tujuanOperasiDafOpeEdit, .sifatOperasiDafOpeEdit, .rencanaJenisPembiusanDafOpeEdit, .potong-beku-edit-pendaftaran-pra-operasi').on('click', function() {
            // Auto-add ketika user klik radio button lain - tapi hanya jika KEDUA field sudah terisi
            checkAutoAddEdit();
        });

        // Event listener untuk hapus individual dengan icon trash
        $(document).on('click', '.hapus-item-dokter-lain-edit', function() {
            $(this).closest('tr').fadeOut(500, function() {
                $(this).remove();
            });
        });

        // Hapus bulk (tidak digunakan lagi, tapi tetap dipertahankan untuk kompatibilitas)
        $('#hapus-dokter-bedah-lain-edit-pendaftaran-pra-operasi').click(function() {
            $('#list-dokter-lain-edit-pendaftaran-pra-operasi').find($('.pilih-dokter-lain-edit-pendaftaran-pra-operasi')).each(function() {
                if ($(this).is(':checked')) {
                    $(this).parents('tr').fadeOut(500, function() {
                        $(this).remove();
                    });
                }
            });
        });
        // Akhir hapus dokter bedah lain

        // Mulai sifat operasi
        let id_sifat_operasi;
        id_sifat_operasi = $('.sifatOperasiDafOpeEdit:checked').val();
        if (id_sifat_operasi === '2131') { // CITO
            $('#form-cito-edit-pendaftaran-pra-operasi').removeClass('d-none');
$('#alasan-cito-edit-pendaftaran-pra-operasi').select2({
    placeholder: '[ Pilih alasan ]',
    dropdownParent: $('#alasan-cito-edit-pendaftaran-pra-operasi').closest('.modal'),
    ajax: {
                    url: "<?= base_url('operasi/PengkajianDafOpe/alasan') ?>",
                    dataType: 'json',
                    delay: 250,
                    method: 'POST',
                    data: {
                        sifatOperasi: id_sifat_operasi,
                        smf: $('#dokter-bedah-edit-pendaftaran-pra-operasi').find(':selected').data('smf')
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                }
            }).append("<option value='<?= $getDaftarOperasi['id_alasan_cito'] ?? null ?>' selected><?= $getDaftarOperasi['alasan_cito'] ?? null ?></option>").trigger('change');
            $('#form-urgent-edit-pendaftaran-pra-operasi, #form-prioritas-edit-pendaftaran-pra-operasi').addClass('d-none');
            $('#alasan-urgent-edit-pendaftaran-pra-operasi, #alasan-prioritas-edit-pendaftaran-pra-operasi').val(null).trigger('change');
        } else if (id_sifat_operasi === '6080') { // Urgent
            $('#form-cito-edit-pendaftaran-pra-operasi, #form-prioritas-edit-pendaftaran-pra-operasi').addClass('d-none');
            $('#form-urgent-edit-pendaftaran-pra-operasi').removeClass('d-none');
$('#alasan-urgent-edit-pendaftaran-pra-operasi').select2({
    placeholder: '[ Pilih alasan ]',
    dropdownParent: $('#alasan-urgent-edit-pendaftaran-pra-operasi').closest('.modal'),
    ajax: {
                    url: "<?= base_url('operasi/PengkajianDafOpe/alasan') ?>",
                    dataType: 'json',
                    delay: 250,
                    method: 'POST',
                    data: {
                        sifatOperasi: id_sifat_operasi,
                        smf: $('#dokter-bedah-edit-pendaftaran-pra-operasi').find(':selected').data('smf')
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                }
            }).append("<option value='<?= $getDaftarOperasi['id_alasan_urgent'] ?? null ?>' selected><?= $getDaftarOperasi['alasan_urgent'] ?? null ?></option>").trigger('change');
            $('#alasan-cito-edit-pendaftaran-pra-operasi, #alasan-prioritas-edit-pendaftaran-pra-operasi').val(null).trigger('change');
        } else if (id_sifat_operasi === '6125') { // Prioritas
            $('#form-cito-edit-pendaftaran-pra-operasi, #form-urgent-edit-pendaftaran-pra-operasi').addClass('d-none');
            $('#alasan-cito-edit-pendaftaran-pra-operasi, #alasan-urgent-edit-pendaftaran-pra-operasi').val(null).trigger('change');
            $('#form-prioritas-edit-pendaftaran-pra-operasi').removeClass('d-none');
$('#alasan-prioritas-edit-pendaftaran-pra-operasi').select2({
    placeholder: '[ Pilih alasan ]',
    dropdownParent: $('#alasan-prioritas-edit-pendaftaran-pra-operasi').closest('.modal'),
    ajax: {
                    url: "<?= base_url('operasi/PengkajianDafOpe/alasan') ?>",
                    dataType: 'json',
                    delay: 250,
                    method: 'POST',
                    data: {
                        sifatOperasi: id_sifat_operasi,
                        smf: $('#dokter-bedah-edit-pendaftaran-pra-operasi').find(':selected').data('smf')
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                }
            }).append("<option value='<?= $getDaftarOperasi['id_alasan_prioritas'] ?? null ?>' selected><?= $getDaftarOperasi['alasan_prioritas'] ?? null ?></option>").trigger('change');
        } else { // Elektif
            $('#form-cito-edit-pendaftaran-pra-operasi, #form-urgent-edit-pendaftaran-pra-operasi, #form-prioritas-edit-pendaftaran-pra-operasi').addClass('d-none');
            $('#alasan-cito-edit-pendaftaran-pra-operasi, #alasan-urgent-edit-pendaftaran-pra-operasi, #alasan-prioritas-edit-pendaftaran-pra-operasi').val(null).trigger('change');
        }

        $('.sifatOperasiDafOpeEdit').click(function() {
            id_sifat_operasi = $(this).val();
            if (id_sifat_operasi === '2131') { // CITO
                $('#form-cito-edit-pendaftaran-pra-operasi').removeClass('d-none');
                $('#alasan-cito-edit-pendaftaran-pra-operasi').val("<?= $getDaftarOperasi['sifat_operasi_lain'] ?? 0 ?>").trigger('change');
                $('#form-urgent-edit-pendaftaran-pra-operasi, #form-prioritas-edit-pendaftaran-pra-operasi').addClass('d-none');
                $('#alasan-urgent-edit-pendaftaran-pra-operasi, #alasan-prioritas-edit-pendaftaran-pra-operasi').val(null).trigger('change');
            } else if (id_sifat_operasi === '6080') { // Urgent
                $('#form-cito-edit-pendaftaran-pra-operasi, #form-prioritas-edit-pendaftaran-pra-operasi').addClass('d-none');
                $('#form-urgent-edit-pendaftaran-pra-operasi').removeClass('d-none');
                $('#alasan-urgent-edit-pendaftaran-pra-operasi').val("<?= $getDaftarOperasi['alasan_urgent'] ?? null ?>").trigger('change');
                $('#alasan-cito-edit-pendaftaran-pra-operasi, #alasan-prioritas-edit-pendaftaran-pra-operasi').val(null).trigger('change');
            } else if (id_sifat_operasi === '6125') { // Prioritas
                $('#form-cito-edit-pendaftaran-pra-operasi, #form-urgent-edit-pendaftaran-pra-operasi').addClass('d-none');
                $('#alasan-cito-edit-pendaftaran-pra-operasi, #alasan-urgent-edit-pendaftaran-pra-operasi').val(null).trigger('change');
                $('#form-prioritas-edit-pendaftaran-pra-operasi').removeClass('d-none');
                $('#alasan-prioritas-edit-pendaftaran-pra-operasi').val("<?= $getDaftarOperasi['alasan_prioritas'] ?? 0 ?>").trigger('change');
            } else { // Elektif
                $('#form-cito-edit-pendaftaran-pra-operasi, #form-urgent-edit-pendaftaran-pra-operasi, #form-prioritas-edit-pendaftaran-pra-operasi').addClass('d-none');
                $('#alasan-cito-edit-pendaftaran-pra-operasi, #alasan-urgent-edit-pendaftaran-pra-operasi, #alasan-prioritas-edit-pendaftaran-pra-operasi').val(null).trigger('change');
            }

            // Mulai alasan sifat operasi
$('#alasan-cito-edit-pendaftaran-pra-operasi, #alasan-urgent-edit-pendaftaran-pra-operasi, #alasan-prioritas-edit-pendaftaran-pra-operasi').select2({
    placeholder: '[ Pilih alasan ]',
    dropdownParent: $(this).closest('.modal'),
    ajax: {
                    url: "<?= base_url('operasi/PengkajianDafOpe/alasan') ?>",
                    dataType: 'json',
                    delay: 250,
                    method: 'POST',
                    data: {
                        sifatOperasi: id_sifat_operasi,
                        smf: $('#dokter-bedah-edit-pendaftaran-pra-operasi').find(':selected').data('smf')
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                }
            });
            // Akhir alasan sifat operasi
        });
        // Akhir sifat operasi

        // Mulai rencana jenis pembiusan
        $('.rencanaJenisPembiusanDafOpeEdit').click(function() {
            let id = $(this).val();
            if (id === '2138') {
                $('#ShowrencanaJenisPembiusanEdit').removeClass('d-none');
                $('#rencanaJenisPembiusan_lainnyaEdit').val("<?= $getDaftarOperasi['rencana_jenis_pembiusan_lain'] ?? null ?>");
            } else {
                $('#ShowrencanaJenisPembiusanEdit').addClass('d-none');
                $('#rencanaJenisPembiusan_lainnyaEdit').val(null);
            }
        });

        if ($('.rencanaJenisPembiusanDafOpeEdit:checked').val() === '2138') {
            $('#ShowrencanaJenisPembiusanEdit').removeClass('d-none');
            $('#rencanaJenisPembiusan_lainnyaEdit').val("<?= $getDaftarOperasi['rencana_jenis_pembiusan_lain'] ?? null ?>");
        }
        // Akhir rencana jenis pembiusan

        // Mulai join operasi
        $('.join-operasi-edit-pendaftaran-pra-operasi').click(function() {
            let joinOperasiValue = $(this).val();
            if (joinOperasiValue === '6249') { // Ya
                $('#form-dokter-bedah-lain-edit-pendaftaran-pra-operasi').show();
            } else { // Tidak
                $('#form-dokter-bedah-lain-edit-pendaftaran-pra-operasi').hide();
            }
        });

        // Inisialisasi visibility form dokter bedah lain berdasarkan nilai yang sudah dipilih
        let selectedJoinOperasi = $('.join-operasi-edit-pendaftaran-pra-operasi:checked').val();
        if (selectedJoinOperasi === '6249') {
            $('#form-dokter-bedah-lain-edit-pendaftaran-pra-operasi').show();
        } else {
            $('#form-dokter-bedah-lain-edit-pendaftaran-pra-operasi').hide();
        }
        // Akhir join operasi

        // Mulai ubah daftar pra operasi dengan validasi
        $('#inputEditDaftarPraOperasi').submit(function(event) {
            event.preventDefault();
            
            // Validasi join operasi
            let joinOperasiValue = $('.join-operasi-edit-pendaftaran-pra-operasi:checked').val();
            let jumlahDokterLain = $('.hapus-item-dokter-lain-edit').length;
            
            // Jika pilih "Ya" tapi list dokter bedah lain kosong
            if (joinOperasiValue === '6249' && jumlahDokterLain === 0) {
                alertify.warning('Dokter Bedah Lain dan Tindakannya harus diisi karena Anda memilih "Ya" pada Join Operasi');
                return false;
            }
            
            dataDafPRA_OPERASIUbah = $(this).serializeArray();
            $.ajax({
                dataType: 'json',
                url: "<?= base_url('operasi/PengkajianDafOpe/action_dafoperasi/ubah') ?>",
                method: 'POST',
                data: dataDafPRA_OPERASIUbah,
                success: function(data) {
                    if (data.status == 'success') {
                        alertify.success('Data Tersimpan');
                        location.reload();
                    } else {
                        $.each(data.errors, function(index, element) {
                            alertify.warning(element);
                        });
                    }
                }
            });
        });
        // Akhir ubah daftar pra operasi
    });
</script>

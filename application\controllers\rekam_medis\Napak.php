<?php
defined('BASEPATH') or exit('No direct script access allowed');

class <PERSON>pak extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'rekam_medis/NapakModel',
        'emr/buktiPelayananRJ/BuktiPelayananRJModel'
      )
    );
  }

  public function index()
  {
    $nomr = $this->uri->segment(2);
    $dataPasien = $this->NapakModel->dataDiriPasien($nomr);
    $pengkajianNapak = $this->NapakModel->pengkajianNapak($nomr);
    $data = array(
      'title' => 'Napak',
      'isi' => 'rekam_medis/napak/index',
      'dataPasien' => $dataPasien,
      // 'HubunganPasienDNR' => $this->masterModel->referensi(1610),
      'yesno' => $this->masterModel->referensi(1783),
      'hubunganPendampingNapak'=> $this->NapakModel->listHubungan(),
      'hubPasien'=> $this->NapakModel->listHubungan(),
      'penyakitPenyerta' => $this->masterModel->referensi(1772),
      'riwayatKanker' => $this->masterModel->referensi(1773),
      'riwayatAnggotaKeluarga' => $this->masterModel->referensi(1774),
      'bagaimanaNapak' => $this->masterModel->referensi(1775),
      'asuransi' => $this->masterModel->referensi(1776),
      'komunikasiNapak' => $this->masterModel->referensi(1777),
      'financialNapak' => $this->masterModel->referensi(1778),
      'psikososialNapak' => $this->masterModel->referensi(1779),
      'logistikNapak' => $this->masterModel->referensi(1780),
      'fisikNapak' => $this->masterModel->referensi(1781),
      'sistemPelayananNapak' => $this->masterModel->referensi(1782),
      'stadiumNapak' => $this->masterModel->stadium(),
      'pengkajianNapak' => $pengkajianNapak,
      'diagnosaNapak' => $this->BuktiPelayananRJModel->icd10_new(),



      // 'stadiumNapak' => $this->masterModel->referensi(1610),

    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('layout/wrapper', $data);
  }

  public function simpanPengkajianNapak()
  {
    $post = $this->input->post();

    $this->db->trans_begin();
    $dataPengkajianNapak = array(
      'nomr'  => $post['norm'],
      'nopen' => $post['nopen'],
      'terima_wa' => $post['waNapak'],
      'keluarga_pendamping' => $post['pendampingNapak'],
      'kontak_pendamping' => $post['noPendampingNapak'],
      'hubungan_pendamping' => $post['hubunganPendampingNapak'],
      'bagaimana_dirujuk' => $post['bagaimanaNapak'],
      'bagaimana_dirujuk_deskripsi' => $post['bagaimanaIsian'],
      // 'diagnosis' => $post['diagnosaNapak'],
      'stadium' => $post['stadiumNapak'],
      'tgl_biopsi' => $post['tglBiopsiNapak'],
      'hasil_biopsi' => $post['hasilBiopsiNapak'],
      'tgl_hasil_pemeriksaan' => $post['tglPemeriksaanNapak'],
      'hasil_pemeriksaan' => $post['hasilPemeriksaanLainnya'],
      'rencana_pengobatan' => $post['rencanaPengobatanNapak'],
      'rencana_pemeriksaan_lanjutan' => $post['rencanaPeriksaLanjutNapak'],
      'prognosis' => $post['perjalananSakitNapak'],
      'penjelasan_lainnya' => $post['lainnyaNapak'],
      'asuransi' => $post['punyBPJS'],
      'asuransi_deskripsi' => $post['punyaBPJS1'],
      'penyakit_penyerta' => $post['penyertaNapak'],
      'penyakit_penyerta_deskripsi' => $post['penyertaNapak1'],
      'riwayat_kanker' => $post['riwayatKankerSebelumnyaNapak'],
      'riwayat_kanker_diagnosa' => $post['riwayatKankerSebelumnyaNapak1'],
      'riwayat_kanker_tahun' => $post['thnTerdiagnosaKankerNapak1'],
      'riwayat_kanker_pengobatan' => $post['pengobatanKankerNapak1'],
      'riwayat_kanker_tempat_pengobatan' => $post['tempatPengobatanNapak1'],
      'riwayat_anggota_keluarga' => $post['riwayatKeluargaNapak'],
      'riwayat_hubungan_anggota' => $post['hubPasien'],
      'riwayat_diagnosa_anggota' => $post['diagnosaKeluarga'],
      'tindak_lanjut_navigator' => $post['tindakLanjutNavigator'],
      'target_hasil' => $post['targetHasilNapak'],
      'rencana_follow_up' => $post['tglRencanaFollowNapak'],
      'oleh' => $this->session->userdata('id'),

    );
    // echo "<pre>"; print_r($dataPengkajianNapak); echo "</pre>";
    $this->db->insert('db_layanan.tb_napak_pengkajian', $dataPengkajianNapak);
    $idPengkajian = $this->db->insert_id();

    //Diagnosa Penyakit NAPAK
    $dataDiagnosa = array();
    $diagnosa = $post['diagnosis_pra_bedah'];

    $index = 0;
    foreach($diagnosa as $d){
    array_push($dataDiagnosa, array(
        'id_pengkajian_napak' => $idPengkajian,
        'diagnosa'             => $d,
        'oleh'                => $this->session->userdata('id'),
      ));
      
      $index++;
    }

    // echo "<pre>";print_r($dataDiagnosa);echo "</pre>";
    $this->db->insert_batch('db_layanan.tb_napak_diagnosa_penyakit', $dataDiagnosa);

    //Masalah (A. Hambatan Komunikasi, Informasi dan Edukasi)
    $dataMasalahA = array();
    $komunikasi = $post['komunikasiNapak'];

    $index = 0;
    foreach($komunikasi as $k){
    array_push($dataMasalahA, array(
        'id_pengkajian_napak' => $idPengkajian,
        'masalah'             => $k,
        'referensi'           => 1777,
        'deskripsi'           => $k == 5950 ? $post['lainnyaHambatanKomunikasi'] : NULL,
        'oleh'                => $this->session->userdata('id'),
      ));
      
      $index++;
    }

    // echo "<pre>";print_r($dataMasalahA);echo "</pre>";
    $this->db->insert_batch('db_layanan.tb_napak_masalah', $dataMasalahA);

    //Masalah (B. Hambatan Finansial dan Ekonomi)
    $dataMasalahB = array();
    $finansial = $post['financialNapak'];

    $index = 0;
    foreach($finansial as $f){
    array_push($dataMasalahB, array(
        'id_pengkajian_napak' => $idPengkajian,
        'masalah'             => $f,
        'referensi'           => 1778,
        'deskripsi'           => $f == 5956 ? $post['lainnyaHambatanFinansial'] : NULL,
        'oleh'                => $this->session->userdata('id'),
      ));
      
      $index++;
    }

    // echo "<pre>";print_r($dataMasalahB);echo "</pre>";
    $this->db->insert_batch('db_layanan.tb_napak_masalah', $dataMasalahB);

    //Masalah (C. Hambatan Psikososial dan Sikap Perilaku)
    $dataMasalahC = array();
    $psiko = $post['psikososialNapak'];

    $index = 0;
    foreach($psiko as $p){
    array_push($dataMasalahC, array(
        'id_pengkajian_napak' => $idPengkajian,
        'masalah'             => $p,
        'referensi'           => 1779,
        'deskripsi'           => $p == 5964 ? $post['lainnyaHambatanPsikososial'] : NULL,
        'oleh'                => $this->session->userdata('id'),
      ));
      
      $index++;
    }

    // echo "<pre>";print_r($dataMasalahC);echo "</pre>";
    $this->db->insert_batch('db_layanan.tb_napak_masalah', $dataMasalahC);

    //Masalah (D. Hambatan Logistik)
    $dataMasalahD = array();
    $logistik = $post['logistikNapak'];

    $index = 0;
    foreach($logistik as $l){
    array_push($dataMasalahD, array(
        'id_pengkajian_napak' => $idPengkajian,
        'masalah'             => $l,
        'referensi'           => 1780,
        'deskripsi'           => $l == 5976 ? $post['lainnyaHambatanLogistik'] : NULL,
        'oleh'                => $this->session->userdata('id'),
      ));
      
      $index++;
    }

    // echo "<pre>";print_r($dataMasalahD);echo "</pre>";
    $this->db->insert_batch('db_layanan.tb_napak_masalah', $dataMasalahD);

    //Masalah (E. Hambatan Fisik)
    $dataMasalahE = array();
    $fisik = $post['fisikNapak'];

    $index = 0;
    foreach($fisik as $f){
    array_push($dataMasalahE, array(
        'id_pengkajian_napak' => $idPengkajian,
        'masalah'             => $f,
        'referensi'           => 1781,
        'deskripsi'           => $f == 5982 ? $post['lainnyaHambatanFisik'] : NULL,
        'oleh'                => $this->session->userdata('id'),
      ));
      
      $index++;
    }

    // echo "<pre>";print_r($dataMasalahE);echo "</pre>";
    $this->db->insert_batch('db_layanan.tb_napak_masalah', $dataMasalahE);

    //Masalah (F. Hambatan Sistem Pelayanan Kesehatan)
    $dataMasalahF = array();
    $pelayanan = $post['sistemPelayananNapak'];

    $index = 0;
    foreach($pelayanan as $p){
    array_push($dataMasalahF, array(
        'id_pengkajian_napak' => $idPengkajian,
        'masalah'             => $p,
        'referensi'           => 1782,
        'deskripsi'           => $p == 5988 ? $post['lainnyaHambatanPelayanan'] : NULL,
        'oleh'                => $this->session->userdata('id'),
      ));
      
      $index++;
    }

    // echo "<pre>";print_r($dataMasalahF);echo "</pre>";exit();
    $this->db->insert_batch('db_layanan.tb_napak_masalah', $dataMasalahF);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
      //   $this->session->set_flashdata('error', "Gagal Simpan");
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
      //   $this->session->set_flashdata('success', "Berhasil Simpan"); 
    }
    echo json_encode($result);
  }

  public function listPasien()
  {
    $data = array(
      'title' => 'Napak',
      'isi' => 'rekam_medis/napak/listPasien',
    );

    $this->load->view('layout/wrapper', $data);
  }

  public function simpanTindakLanjut()
  {
    $post = $this->input->post();

    if(isset($post['pemeriksaanPenunjangPilihPA'])){
      if($post['pemeriksaanPenunjangPilihPA'] == 1){
        $data = array(
          'id_pengkajian_napak' => $post['idPengkajianNapak'],
          'jenis' => 5992,
          'tanggal' => date('Y-m-d', strtotime($post['tanggalPemeriksaanPenunjangPilihPA'])),
          'oleh' => $this->session->userdata('id'),
        );
        // echo "<pre>"; print_r($data); echo "</pre>";
        $this->db->insert('db_layanan.tb_napak_fu', $data);
      }
    }

    if(isset($post['pemeriksaanPenunjangPilihLab'])){
      if($post['pemeriksaanPenunjangPilihLab'] == 1){
        $data = array(
          'id_pengkajian_napak' => $post['idPengkajianNapak'],
          'jenis' => 5991,
          'tanggal' => date('Y-m-d', strtotime($post['tanggalPemeriksaanPenunjangPilihLab'])),
          'oleh' => $this->session->userdata('id'),
        );
        // echo "<pre>"; print_r($data); echo "</pre>";
        $this->db->insert('db_layanan.tb_napak_fu', $data);
      }
    }

    if(isset($post['pemeriksaanPenunjangPilihRad'])){
      if($post['pemeriksaanPenunjangPilihRad'] == 1){
        $data = array(
          'id_pengkajian_napak' => $post['idPengkajianNapak'],
          'jenis' => 5993,
          'tanggal' => date('Y-m-d', strtotime($post['tanggalPemeriksaanPenunjangPilihRad'])),
          'pemeriksaan' => $post['namaPemeriksaanPenunjangPilihRad'],
          'oleh' => $this->session->userdata('id'),
        );
       // echo "<pre>"; print_r($data); echo "</pre>";
        $this->db->insert('db_layanan.tb_napak_fu', $data);
      }
    }

    if(isset($post['operasiPilih'])){
      if($post['operasiPilih'] == 1){
        $data = array(
          'id_pengkajian_napak' => $post['idPengkajianNapak'],
          'jenis' => 5994,
          'tanggal' => date('Y-m-d', strtotime($post['tanggalTindakanPilih'])),
          'pemeriksaan' => $post['namaTindakanPilih'],
          'dpjp' => $post['dpjpTindakanPilih'],
          'preop' => $post['operasiPilihPreOp'],
          'spesialis' => $post['spesialisYangDituju'],
          'jadwal_janji' => date('Y-m-d', strtotime($post['tanggalJadwalJanjiTemuPilih'])),
          'oleh' => $this->session->userdata('id'),
        );
        // echo "<pre>"; print_r($data); echo "</pre>";
        $this->db->insert('db_layanan.tb_napak_fu', $data);
      }
    }

    if(isset($post['kemoterapiPilih'])){
      if($post['kemoterapiPilih'] == 1){
        $data = array(
          'id_pengkajian_napak' => $post['idPengkajianNapak'],
          'jenis' => 5995,
          'siklus' => $post['siklusKemoterapiPilih'],
          'total_siklus' => $post['totalSiklusKemoterapiPilih'],
          'tanggal' => date('Y-m-d', strtotime($post['tanggalKemoterapiPilih'])),
          'dpjp' => $post['dpjpKemoterapiPilih'],
          'oleh' => $this->session->userdata('id'),
        );
        // echo "<pre>"; print_r($data); echo "</pre>";
        $this->db->insert('db_layanan.tb_napak_fu', $data);
      }
    }

    if(isset($post['radiasiPilih'])){
      if($post['radiasiPilih'] == 1){
        $data = array(
          'id_pengkajian_napak' => $post['idPengkajianNapak'],
          'jenis' => 5996,
          'siklus' => $post['siklusRadiasiPilih'],
          'total_siklus' => $post['totalSiklusRadiasiPilih'],
          'tanggal' => date('Y-m-d', strtotime($post['tanggalRadiasiPilih'])),
          'dpjp' => $post['dpjpRadiasiPilih'],
          'oleh' => $this->session->userdata('id'),
        );
        // echo "<pre>"; print_r($data); echo "</pre>";
        $this->db->insert('db_layanan.tb_napak_fu', $data);
      }
    }

    if(isset($post['konsultasiDokterPilih'])){
      if($post['konsultasiDokterPilih'] == 1){
        $data = array(
          'id_pengkajian_napak' => $post['idPengkajianNapak'],
          'jenis' => 5997,
          'tanggal' => date('Y-m-d', strtotime($post['tanggalKonsultasiDokterPilihYa'])),
          'dpjp' => $post['dpjpKonsultasiDokterPilihYa'],
          'oleh' => $this->session->userdata('id'),
        );
        // echo "<pre>"; print_r($data); echo "</pre>";
        $this->db->insert('db_layanan.tb_napak_fu', $data);
      }
    }

    if(isset($post['terapiSupportPilih'])){
      if($post['terapiSupportPilih'] == 1){
        $data = array(
          'id_pengkajian_napak' => $post['idPengkajianNapak'],
          'jenis' => 6235,
          'tanggal' => date('Y-m-d', strtotime($post['tanggalTerapiSupportPilihYa'])),
          'nama_obat' =>$post['namaObatTerapiSupport'],
          'oleh' => $this->session->userdata('id'),
        );
        // echo "<pre>"; print_r($data); echo "</pre>";
        $this->db->insert('db_layanan.tb_napak_fu', $data);
      }
    }

  }

  public function viewTindakLanjut()
  {
    $id = $this->input->post('id');
    $tgl = $this->input->post('tgl');
    $data = array(
      'id' => $id,
      'tgl' => $tgl,
      'listDr' => $this->masterModel->listDrUmum(),
      'operasiPreOp' => $this->masterModel->referensi(1783),
      'spesialisDokter' => $this->NapakModel->spesialisDokter(),
    );

    $this->load->view('rekam_medis/napak/viewTindakLanjut', $data);
  }

  public function modalViewTindakLanjut()
  {
    $jenis = $this->input->post('jenis');
    $tgl = $this->input->post('tgl');
    $pmrks = $this->input->post('pmrks');
    $dpjp = $this->input->post('dpjp');
    $preop = $this->input->post('preop');
    $spes = $this->input->post('spes');
    $tgltemu = $this->input->post('tgltemu');
    $siklus = $this->input->post('siklus');
    $ttlsiklus = $this->input->post('ttlsiklus');
    $namaobat = $this->input->post('namaobat');
    $data = array(
      'jenis' => $jenis,
      'tgl' => $tgl,
      'pmrks' => $pmrks,
      'dpjp' => $dpjp,
      'preop' => $preop,
      'spes' => $spes,
      'tgltemu' => $tgltemu,
      'siklus' => $siklus,
      'ttlsiklus' => $ttlsiklus,
      'namaobat' => $namaobat,
    );

    $this->load->view('rekam_medis/napak/modalViewTindakLanjut', $data);
  }

  public function tblTindakLanjut($id)
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $followUpHistory = $this->NapakModel->dbFollowUpHistory($id);

    $data = array();
    $no = $_POST['start'];
    foreach ($followUpHistory as $FU) {
      $no++;
      $TOMBOL = "<a href='#' class='btn btn-danger btn-sm tombolTindakLanjutNonAktif mr-2' data-idfu='" . $FU->ID_FU . "' data-toggle='modal'><i class='fa fa-ban'></i></a>

      <a href='#modalViewTindakLanjut' class='btn btn-warning btn-sm tombolTindakLanjutView' data-jenis='" . $FU->JENIS_ROW . "' data-tgl='" . $FU->TANGGAL . "' data-pmrks='" . $FU->PEMERIKSAAN . "' data-dpjp='" . $FU->DPJP . "' data-preop='" . $FU->PREOP . "' data-spes='" . $FU->SPESIALIS . "' data-tgltemu='" . $FU->TANGGAL_TEMU . "' data-siklus='" . $FU->SIKLUS . "' data-ttlsiklus='" . $FU->TOTAL_SIKLUS . "' data-namaobat='" . $FU->NAMA_OBAT . "' data-toggle='modal'><i class='fa fa-edit'></i></a>";

      $data[] = array(
        // $no,
        $FU->JENIS,
        $FU->TANGGAL,
        $FU->NAMAPEGAWAI,
        $TOMBOL
      );
    }
    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $this->NapakModel->total_count_followUpHistory($id),
      "recordsFiltered" => $this->NapakModel->filter_count_followUpHistory($id),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function simpanNonAktifkan()
  {
   $id = $this->input->post('id');
   $data = array(
    'status' => 0, 
    ); 
   $this->db->where('tb_napak_fu.id', $id);
   $this->db->update('db_layanan.tb_napak_fu', $data);
  }

  public function simpanNonAktifkanPengkajianNapak()
  {
   $id = $this->input->post('id');
   $data = array(
    'status' => 0, 
    ); 
   $this->db->where('tb_napak_pengkajian.id', $id);
   $this->db->update('db_layanan.tb_napak_pengkajian', $data);
  }

  public function action($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'rencanaTindakLanjut') {
        // $rules_rencana_tindak_lanjut = $this->NapakModel->rules_rencana_tindak_lanjut;
        // $this->form_validation->set_rules($rules_rencana_tindak_lanjut);

        $post = $this->input->post();
          $this->db->trans_begin();

          $dataRencanaTindakLanjut = array(
            'id_pengkajian_napak' => $post['id_pengkajian_napak_rt'],
            // 'identifikasi_hambatan' => $post['identifikasiPotensi'],
            'tindak_lanjut_navigator' => $post['tindakLanjut'],
            'target_hasil' => $post['targetHasil'],
            'rencana_fu' => $post['rencanaTanggal'],
            'oleh' => $this->session->userdata("id"),
          );

          if($post['id_rencana_tindak_lanjut']) {
            $this->db->where('id', $this->input->post('id_rencana_tindak_lanjut'));
            $this->db->update('db_layanan.tb_napak_rencana', $dataRencanaTindakLanjut);

            $dataHambatanEdit = array();
            $idHambatanEdit = $post['id_rencana_tindak_lanjut'];
            $hambatanEdit = $post['identifikasiPotensi'];
            $index = 0;

            $dataNapakRencanaEdit1 = array (
                'STATUS'        => 0,
            );
            $this->db->where('id_napak_rencana', $idHambatanEdit);
            $this->db->update('db_layanan.tb_napak_rencana_hambatan', $dataNapakRencanaEdit1);
            // echo "<pre>";print_r($dataNapakRencanaEdit1);echo "</pre>";

            foreach($hambatanEdit as $h){

              array_push($dataHambatanEdit, array(
                'id_napak_rencana'  => $idHambatanEdit,
                'jenis'    	  	    => $h,
                // 'ID_DAFTAR'		  	=> $idDaftar,
              ));
              $index++;
            }
            // echo "<pre>";print_r($dataHambatanEdit);echo "</pre>";exit();
            $this->db->insert_batch('db_layanan.tb_napak_rencana_hambatan', $dataHambatanEdit);

          } else {
            // echo "<pre>";print_r($dataRencanaTindakLanjut);echo "</pre>";
            $this->db->insert('db_layanan.tb_napak_rencana', $dataRencanaTindakLanjut);

            $idNapakRencana = $this->db->insert_id();

            $dataHambatan = array();
            $hambatan = $post['identifikasiPotensi'];

            $index = 0;
            foreach($hambatan as $h){
            array_push($dataHambatan, array(
                'id_napak_rencana'  => $idNapakRencana,
                'jenis'             => $h,
              ));
              $index++;
            }
          // echo "<pre>";print_r($dataHambatan);echo "</pre>";
          $this->db->insert_batch('db_layanan.tb_napak_rencana_hambatan', $dataHambatan);
          }

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
          }
        echo json_encode($result);
      } else if ($param == 'hapus') {
        $this->db->where('id', $this->input->post('id'));
        $this->db->update('db_layanan.tb_napak_rencana', array('status' => 0));
        $result = array('status' => 'success');
        echo json_encode($result);
      }
    }
  }

  public function listPasienNapak()
  {
    $listPasien = $this->NapakModel->listPasienNapak();
    echo json_encode(['data' => $listPasien]);
  }

  public function listHistoryPasienNapak()
  {
    $listPasien = $this->NapakModel->listHistoryPasienNapak();
    echo json_encode(['data' => $listPasien]);
  }

  public function historyRencanaTindakLanjut()
  {
    $result = $this->NapakModel->historyRencanaTindakLanjut(FALSE);
    echo json_encode(['data' => $result]);
  }

  public function getRencanaTindakLanjut()
  {
    $result = $this->NapakModel->historyRencanaTindakLanjut();
    echo json_encode($result);
  }


  public function modalLihatPengkajianNapak()
  {
    $id = $this->input->post('id');
    $dataPengkajian = $this->NapakModel->dataPengkajian($id)->row_array();
    $diagnosa = $this->NapakModel->diagnosa($id)->result_array();
    $masalah1 = $this->NapakModel->masalah1($id)->result_array();
    $masalah2 = $this->NapakModel->masalah2($id)->result_array();
    $masalah3 = $this->NapakModel->masalah3($id)->result_array();
    $masalah4 = $this->NapakModel->masalah4($id)->result_array();
    $masalah5 = $this->NapakModel->masalah5($id)->result_array();
    $masalah6 = $this->NapakModel->masalah6($id)->result_array();


    $data = array(
      'id' => $id,
      'dataPengkajian' => $dataPengkajian,
      'diagnosa' => $diagnosa,
      'masalah1' => $masalah1,
      'masalah2' => $masalah2,
      'masalah3' => $masalah3,
      'masalah4' => $masalah4,
      'masalah5' => $masalah5,
      'masalah6' => $masalah6
    );

    $this->load->view('rekam_medis/napak/modalViewPengkajianNapak', $data);
  }

  public function listPasienAll(){
		$draw   = intval($this->input->POST("draw"));
		$start  = intval($this->input->POST("start"));
		$length = intval($this->input->POST("length"));

    $nomr = $this->input->post('NOMR');
		$dataPasien = $this->NapakModel->listAllPasien($nomr);
	
		$data = array();
		$no = 1;
		foreach ($dataPasien->result() as $DP) {

      if($DP->NAPAK == 1){
				$cek = "checked";
			}else{
				$cek = "";
			}

      if($DP->NAPAK == 1){
        $status = "Sudah Napak";
      }else if($DP->NAPAK == 0 && $DP->NAPAK != ""){
			  $status = "Sudah Tidak Napak";
      }else{
        $status = "Belum Napak";
      }


      
      $button = "<div class='form-check'>
			<input type='checkbox' name='daftarNapakBaru' class='form-check-input daftarNapakBaru' data-id='" . $DP->NORM . "' " . $cek . ">
			</div>";
			
            $data[] = array(
                // $no,
              $DP->NORM,
              $DP->NAMAPASIEN,
              $DP->TANGGAL_LAHIR,
              $status,
              $button,
            );
            $no++;
		}
	
		$output = array(
        "draw"            => $draw,
        "recordsTotal"    => $dataPasien->num_rows(),
        "recordsFiltered" => $dataPasien->num_rows(),
        "data"            => $data
		);
		echo json_encode($output);
	}


  // public function simpanPasienNapakBaru()
  // {
  //  $id = $this->input->post('id');
  //  $data = array(
  //   'nomr' => $post['pendampingNapak'],
  //   'tanggal' => $post['pendampingNapak'],
  //   'status' => 1,
  //   'oleh' => 0, 
  //   ); 
  //  echo "<pre>"; print_r($data); echo "</pre>";
  //   $this->db->insert('pendaftaran.napak', $data);
  // }

  public function simpanPasienNapakBaru()
	{
		$this->db->trans_begin();

		$post = $this->input->post();
		$id = $this->input->post('id');
		$cek = $this->input->post('cek');
    $now = date('Y-m-d H:i:s');

		if($cek == "true"){
			$status = 1;
		}else{
			$status = 0;
		}

		$data = array(
      'nomr'        => $id,
			'status'     	=> $status,
      'tanggal'     => $now,
      'oleh'        => $this->session->userdata('id'),
		);

   $this->db->where('nomr',$id);
   $q = $this->db->get('pendaftaran.napak');

   if ( $q->num_rows() > 0 ) 
   {
      // echo "ada";
      $this->db->where('napak.nomr',$id);
      $this->db->update('pendaftaran.napak',$data);
   } else {
      // echo "gaada anjng";
      $this->db->insert('pendaftaran.napak',$data);
   }

    // echo "<pre>"; print_r($data); echo "</pre>";
		// $this->db->insert('pendaftaran.napak', $data);

		if ($this->db->trans_status() === false) {
			$this->db->trans_rollback();
			$result = array('status' => 'failed');
		} else {
			$this->db->trans_commit();
			$result = array('status' => 'success');
		}

		echo json_encode($result);
	}

}

/* End of file Napak.php */
/* Location: ./application/controllers/rekam_medis/Napak.php */
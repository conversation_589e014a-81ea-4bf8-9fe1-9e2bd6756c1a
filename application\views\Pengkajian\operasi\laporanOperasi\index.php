<div class="row">
    <div class="col-sm-12">
        <div class="pull-right m-t-20">
            <button class="btn btn-secondary btn-sm waves-effect" id="tbl-baru-lap-operasi" onClick="window.location.reload();">
                <i class="fa fa-file"></i> Buat baru
            </button>
            <button href="#modal-history-lap-operasi" class="btn btn-custom btn-sm waves-effect" id="tbl-history-lap-operasi" data-toggle="modal" data-id="<?= $nomr ?>">
                <i class="fa fa-history"></i> History
                <?php if ($jumlah >= 1): ?>
                    <span class="badge badge-danger"><?= $jumlah ?></span>
                <?php endif ?>
            </button>
        </div>
        <h4 class="page-title">LAPORAN OPERASI</h4>
    </div>
</div>
<!-- Mulai form laporan operasi -->
<form id="form-lap-operasi" autocomplete="off">
    <input type="hidden" name="nomr" id="nomr-lap-operasi" value="<?= $nomr ?>">
    <input type="hidden" name="nokun" id="nokun-lap-operasi" value="<?= $nokun ?>">
    <!-- Mulai daftar tunggu operasi -->
    <div class="form-group d-none">
        <label for="daftar-tunggu-lap-operasi">
            Daftar tunggu operasi
        </label>
        <select name="id_waiting_list" id="daftar-tunggu-lap-operasi" class="form-control">
            <option value="">Kosong</option>
            <?php foreach ($daftarTunggu as $dt): ?>
                <option id="daftar-tunggu-lap-operasi-<?= $dt['id'] ?>" value="<?= $dt['id'] ?>" <?= empty($selectedFirst) && ($selectedFirst = true) ? 'selected' : '' ?>>
                    <?= date('d/m/Y', strtotime($dt['tanggal'])) . ' - ' . $dt['dokter'] ?>
                </option>
            <?php endforeach ?>
        </select>
    </div>
    <!-- Akhir daftar tunggu operasi -->
    <!-- Mulai dokter operator bedah -->
    <div class="form-group">
        <label for="dok-operator-bedah-lap-operasi">
            Dokter operator bedah
        </label>
        <select name="dokter_bedah[]" id="dok-operator-bedah-lap-operasi" class="form-control" multiple>
            <?php foreach ($listDr as $ld): ?>
                <option id="dok-operator-bedah-lap-operasi-<?= $ld['ID_DOKTER'] ?>" value="<?= $ld['ID_DOKTER'] ?>">
                    <?= $ld['DOKTER'] . ' - ' . $ld['SMF'] ?>
                </option>
            <?php endforeach ?>
        </select>
    </div>
    <!-- Akhir dokter operator bedah -->
    <!-- Mulai asisten operator -->
    <div class="form-group">
        <label for="ass-operator-lap-operasi">
            Asisten operator
        </label>
        <select name="asisten_operator[]" id="ass-operator-lap-operasi" class="form-control" multiple>
            <option value="">Kosong</option>
            <?php foreach ($listDr as $ld): ?>
                <option id="ass-operator-lap-operasi-<?= $ld['ID_DOKTER'] ?>" value="<?= $ld['ID_DOKTER'] ?>">
                    <?= $ld['DOKTER'] . ' - ' . $ld['SMF'] ?>
                </option>
            <?php endforeach ?>
        </select>
    </div>
    <!-- Akhir asisten operator -->
    <!-- Mulai asisten operator lainnya -->
    <div class="form-group">
        <label for="ass-operator-lainnya-lap-operasi">
            Asisten operator lainnya
        </label>
        <input type="text" class="form-control" id="ass-operator-lainnya-lap-operasi" placeholder="[ Sebutkan asisten operator lainnya ]" name="asisten_operator_lainnya">
    </div>
    <!-- Akhir asisten operator lainnya -->
    <!-- Mulai perawat instrumentator -->
    <div class="form-group">
        <label for="instrumentator-lap-operasi">
            Perawat instrumentator
        </label>
        <select name="perawat_instrumentator[]" id="instrumentator-lap-operasi" class="form-control" multiple>
            <option value="">Kosong</option>
            <?php foreach ($listPerawat as $lp): ?>
                <option id="instrumentator-lap-operasi-<?= $lp['NIP'] ?>" value="<?= $lp['NIP'] ?>">
                    <?= $lp['NAMA'] ?>
                </option>
            <?php endforeach ?>
        </select>
    </div>
    <!-- Akhir perawat instrumentator -->
    <!-- Mulai perawat sirkuler -->
    <div class="form-group">
        <label for="sirkuler-lap-operasi">
            Perawat sirkuler
        </label>
        <select name="perawat_sirkuler" id="sirkuler-lap-operasi" class="form-control">
            <option value="">Kosong</option>
            <?php foreach ($listPerawat as $lp): ?>
                <option id="sirkuler-lap-operasi-<?= $lp['NIP'] ?>" value="<?= $lp['NIP'] ?>">
                    <?= $lp['NAMA'] ?>
                </option>
            <?php endforeach ?>
        </select>
    </div>
    <!-- Akhir perawat sirkuler -->
    <!-- Mulai dokter anestesi -->
    <div class="form-group">
        <label for="dok-anestesi-lap-operasi">
            Dokter anestesi
        </label>
        <select name="dokter_anestesi[]" id="dok-anestesi-lap-operasi" class="form-control" multiple>
            <option value="">Kosong</option>
            <?php foreach ($listDrAnestesi as $ld): ?>
                <option id="dok-anestesi-lap-operasi-<?= $ld['ID_DOKTER'] ?>" value="<?= $ld['ID_DOKTER'] ?>">
                    <?= $ld['DOKTER'] . ' - ' . $ld['SMF'] ?>
                </option>
            <?php endforeach ?>
        </select>
    </div>
    <!-- Akhir dokter anestesi -->
    <!-- Mulai jenis anestesi -->
    <div class="form-group">
        <label for="jenis-anestesi-lap-operasi">
            Jenis anestesi
        </label>
        <div class="row px-1">
            <?php foreach ($jenisAnestesi as $ja): ?>
                <div class="col-md form-check form-check-inline">
                    <div class="radio radio-primary">
                        <input type="radio" class="jenis-anestesi-lap-operasi" name="jenis_anestesi" id="jenis-anestesi-lap-operasi-<?= $ja['id_variabel'] ?>" value="<?= $ja['id_variabel'] ?>">
                        <label for="jenis-anestesi-lap-operasi-<?= $ja['id_variabel'] ?>" class="form-check-label">
                            <?= $ja['variabel'] ?>
                        </label>
                    </div>
                </div>
            <?php endforeach ?>
        </div>
    </div>
    <!-- Akhir jenis anestesi -->
    <!-- Mulai keterangan anestesi lokal -->
    <div class="form-group d-none" id="ket-anestesi-lokal-lap-operasi">
        <div class="card new-card">
            <div class="card-header new-card-header">Keterangan Anestesi Lokal</div>
            <div class="card-body">
                <!-- Mulai teknik anestesi lokal -->
                <div class="form-group">
                    <label for="teknik-anestesi-lokal-lap-operasi">
                        Teknik anestesi lokal
                    </label>
                    <div class="row px-1">
                        <?php foreach ($teknikAnestesiLokal as $tal): ?>
                            <div class="col-md form-check form-check-inline">
                                <div class="radio radio-primary">
                                    <input type="radio" class="teknik-anestesi-lokal-lap-operasi" name="teknik_anestesi_lokal" id="teknik-anestesi-lokal-lap-operasi-<?= $tal['id_variabel'] ?>" value="<?= $tal['id_variabel'] ?>">
                                    <label for="teknik-anestesi-lokal-lap-operasi-<?= $tal['id_variabel'] ?>" class="form-check-label">
                                        <?= $tal['variabel'] ?>
                                    </label>
                                </div>
                            </div>
                        <?php endforeach ?>
                    </div>
                </div>
                <!-- Akhir teknik anestesi lokal -->
                <!-- Mulai sebutkan teknik anestesi lokal -->
                <div class="form-group d-none" id="form-teknik-lap-operasi">
                    <label for="ket-teknik-lap-operasi">
                        Keterangan teknik anestesi
                    </label>
                    <input type="text" class="form-control" id="ket-teknik-lap-operasi" placeholder="[ Sebutkan teknik ]" name="keterangan_teknik">
                </div>
                <!-- Akhir sebutkan teknik anestesi lokal -->
                <!-- Mulai lokasi anestesi -->
                <div class="form-group">
                    <label for="lokasi-lap-operasi">
                        Lokasi anestesi
                    </label>
                    <input type="text" class="form-control" id="lokasi-lap-operasi" placeholder="[ Lokasi anestesi ]" name="lokasi">
                </div>
                <!-- Akhir lokasi anestesi -->
                <!-- Mulai obat-obat anestesi -->
                <div class="form-group">
                    <label for="obat-anestesi-lap-operasi">
                        Obat-obat anestesi
                    </label>
                    <input type="text" class="form-control" id="obat-anestesi-lap-operasi" placeholder="[ Sebutkan obat-obatnya ]" name="obat_anestesi">
                </div>
                <!-- Akhir obat-obat anestesi -->
                <!-- Mulai respon hipersensitivitas -->
                <div class="form-group">
                    <label for="respon-hipersensitivitas-lap-operasi">
                        Respon hipersensitivitas
                    </label>
                    <div class="row px-1">
                        <?php foreach ($responHipersensitivitas as $rh): ?>
                            <div class="col-md form-check form-check-inline">
                                <div class="radio radio-primary">
                                    <input type="radio" class="respon-hipersensitivitas-lap-operasi" name="respon_hipersensitivitas" id="respon-hipersensitivitas-lap-operasi-<?= $rh['id_variabel'] ?>" value="<?= $rh['id_variabel'] ?>">
                                    <label for="respon-hipersensitivitas-lap-operasi-<?= $rh['id_variabel'] ?>" class="form-check-label">
                                        <?= $rh['variabel'] ?>
                                    </label>
                                </div>
                            </div>
                        <?php endforeach ?>
                    </div>
                </div>
                <!-- Akhir respon hipersensitivitas -->
                <!-- Mulai keterangan respon hipersensitivitas -->
                <div class="form-group d-none" id="form-respon-lap-operasi">
                    <label for="ket-respon-lap-operasi">
                        Keterangan respon
                    </label>
                    <input type="text" class="form-control" id="ket-respon-lap-operasi" placeholder="[ Keterangan respon ]" name="isi_respon_hipersensitivitas">
                </div>
                <!-- Akhir keterangan respon hipersensitivitas -->
                <!-- Mulai kejadian toksikasi -->
                <div class="form-group">
                    <label for="kejadian-toksikasi-lap-operasi">
                        Kejadian toksikasi
                    </label>
                    <div class="row px-1">
                        <?php foreach ($kejadianToksikasi as $kt): ?>
                            <div class="col-md form-check form-check-inline">
                                <div class="radio radio-primary">
                                    <input type="radio" class="kejadian-toksikasi-lap-operasi" name="kejadian_toksikasi" id="kejadian-toksikasi-lap-operasi-<?= $kt['id_variabel'] ?>" value="<?= $kt['id_variabel'] ?>">
                                    <label for="kejadian-toksikasi-lap-operasi-<?= $kt['id_variabel'] ?>" class="form-check-label">
                                        <?= $kt['variabel'] ?>
                                    </label>
                                </div>
                            </div>
                        <?php endforeach ?>
                    </div>
                </div>
                <!-- Akhir kejadian toksikasi -->
                <!-- Mulai keterangan kejadian toksikasi -->
                <div class="form-group d-none" id="form-toksikasi-lap-operasi">
                    <label for="sebutkan-toksikasi-lap-operasi">
                        Keterangan kejadian
                    </label>
                    <input type="text" class="form-control" id="sebutkan-toksikasi-lap-operasi" placeholder="[ Keterangan kejadian ]" name="isi_kejadian_toksikasi">
                </div>
                <!-- Akhir keterangan kejadian toksikasi -->
            </div>
        </div>
    </div>
    <!-- Akhir keterangan anestesi lokal -->
    <!-- Mulai kategori operasi -->
    <div class="form-group">
        <label for="kat-operasi-lap-operasi">
            Kategori operasi
        </label>
        <div class="row px-1">
            <?php foreach ($kategoriOperasi as $ko): ?>
                <div class="col-md form-check form-check-inline">
                    <div class="radio radio-primary">
                        <input type="radio" class="kat-operasi-lap-operasi" name="kategori_operasi" id="kat-operasi-lap-operasi-<?= $ko['id_variabel'] ?>" value="<?= $ko['id_variabel'] ?>">
                        <label for="kat-operasi-lap-operasi-<?= $ko['id_variabel'] ?>" class="form-check-label">
                            <?= $ko['variabel'] ?>
                        </label>
                    </div>
                </div>
            <?php endforeach ?>
        </div>
    </div>
    <!-- Akhir kategori operasi -->
    <!-- Mulai lokasi pengambilan sampel -->
    <div class="form-group">
        <label for="lok-pengambilan-sampel-lap-operasi">
            Lokasi pengambilan sampel
        </label>
        <input type="text" class="form-control" id="lok-pengambilan-sampel-lap-operasi" placeholder="[ Lokasi pengambilan sampel ]" name="lok_pengambilan_sampel">
    </div>
    <!-- Akhir lokasi pengambilan sampel -->
    <!-- Mulai diagnosis pra bedah -->
    <div class="form-group">
        <label for="diagnosis-pra-bedah-lap-operasi">
            Diagnosis pra bedah
        </label>
        <div class="row px-1">
            <div class="col-md form-check form-check-inline">
                <div class="radio radio-primary">
                    <input type="radio" class="diagnosis-pra-bedah-lap-operasi" name="radio_diagnosis_pra_bedah" id="diagnosis-pra-bedah-lap-operasi-pilihan" value="pilihan">
                    <label for="diagnosis-pra-bedah-lap-operasi-pilihan" class="form-check-label">
                        ICD 10
                    </label>
                </div>
            </div>
            <div class="col-md form-check form-check-inline">
                <div class="radio radio-primary">
                    <input type="radio" class="diagnosis-pra-bedah-lap-operasi" name="radio_diagnosis_pra_bedah" id="diagnosis-pra-bedah-lap-operasi-isian" value="isian">
                    <label for="diagnosis-pra-bedah-lap-operasi-isian" class="form-check-label">
                        Teks
                    </label>
                </div>
            </div>
        </div>
    </div>
    <!-- Akhir diagnosis pra bedah -->
    <!-- Mulai pilihan diagnosis pra bedah -->
    <div class="form-group d-none" id="form-pilihan-diagnosis-pra-bedah-lap-operasi">
        <label for="pilihan-diagnosis-pra-bedah-lap-operasi">
            Pilihan diagnosis pra bedah
        </label>
        <select name="diagnosis_pra_bedah[]" id="pilihan-diagnosis-pra-bedah-lap-operasi" class="form-control" multiple>
            <option value="">Kosong</option>
            <?php if (!empty($getSur['diagnosa_utama'])): ?>
                <option id="pilihan-diagnosis-pra-bedah-lap-operasi-<?= $getSur['diagnosa_utama'] ?>" value="<?= $getSur['diagnosa_utama'] ?>">
                    <?= $getSur['diagnosa_utama'] ?>
                </option>
            <?php endif ?>
        </select>
    </div>
    <!-- Akhir pilihan diagnosis pra bedah -->
    <!-- Mulai diagnosis pra bedah lainnya -->
    <div class="form-group d-none" id="form-diagnosis-pra-bedah-lainnya-lap-operasi">
        <label for="diagnosis-pra-bedah-lainnya-lap-operasi">
            Diagnosis pra bedah lainnya
        </label>
        <input type="text" class="form-control" placeholder="[ Diagnosis pra bedah lainnya ]" id="diagnosis-pra-bedah-lainnya-lap-operasi" name="diagnosis_pra_bedah_lainnya">
    </div>
    <!-- Akhir diagnosis pra bedah lainnya -->
    <!-- Mulai tanggal operasi -->
    <div class="form-group">
        <label for="tgl-operasi-lap-operasi">
            Tanggal operasi
        </label>
        <div class="input-group">
            <input type="date" class="form-control" placeholder="[ Tanggal operasi ]" id="tgl-operasi-lap-operasi" name="tgl_operasi">
            <div class="input-group-append">
                <span class="input-group-text"><i class="fa fa-calendar"></i></span>
            </div>
        </div>
    </div>
    <!-- Akhir tanggal operasi -->
    <!-- Mulai jam mulai insisi -->
    <div class="form-group">
        <label for="mulai-insisi-lap-operasi">
            Jam mulai insisi
        </label>
        <div class="input-group">
            <input type="time" class="form-control" placeholder="[ Jam mulai insisi ]" id="mulai-insisi-lap-operasi" name="jam_mulai">
            <div class="input-group-append">
                <span class="input-group-text">WIB</span>
            </div>
        </div>
    </div>
    <!-- Akhir jam mulai insisi -->
    <!-- Mulai jam selesai insisi -->
    <div class="form-group">
        <label for="selesai-insisi-lap-operasi">
            Jam selesai insisi
        </label>
        <div class="input-group">
            <input type="time" class="form-control" placeholder="[ Jam selesai insisi ]" id="selesai-insisi-lap-operasi" name="jam_selesai">
            <div class="input-group-append">
                <span class="input-group-text">WIB</span>
            </div>
        </div>
    </div>
    <!-- Akhir jam selesai insisi -->
    <!-- Mulai diagnosis pasca bedah -->
    <div class="form-group">
        <label for="diagnosis-pasca-bedah-lap-operasi">
            Diagnosis pasca bedah
        </label>
        <div class="row px-1">
            <div class="col-md form-check form-check-inline">
                <div class="radio radio-primary">
                    <input type="radio" class="diagnosis-pasca-bedah-lap-operasi" name="radio_diagnosis_pasca_bedah" id="diagnosis-pasca-bedah-lap-operasi-pilihan" value="pilihan">
                    <label for="diagnosis-pasca-bedah-lap-operasi-pilihan" class="form-check-label">
                        ICD 10
                    </label>
                </div>
            </div>
            <div class="col-md form-check form-check-inline">
                <div class="radio radio-primary">
                    <input type="radio" class="diagnosis-pasca-bedah-lap-operasi" name="radio_diagnosis_pasca_bedah" id="diagnosis-pasca-bedah-lap-operasi-isian" value="isian">
                    <label for="diagnosis-pasca-bedah-lap-operasi-isian" class="form-check-label">
                        Teks
                    </label>
                </div>
            </div>
        </div>
    </div>
    <!-- Akhir diagnosis pasca bedah -->
    <!-- Mulai pilihan diagnosis pasca bedah -->
    <div class="form-group d-none" id="form-pilihan-diagnosis-pasca-bedah-lap-operasi">
        <label for="pilihan-diagnosis-pasca-bedah-lap-operasi">
            Pilihan diagnosis pasca bedah
        </label>
        <select name="diagnosis_pasca_bedah[]" id="pilihan-diagnosis-pasca-bedah-lap-operasi" class="form-control" multiple>
            <option value="">Kosong</option>
            <?php if (!empty($getSur['diagnosa_utama'])): ?>
                <option id="pilihan-diagnosis-pasca-bedah-lap-operasi-<?= $getSur['diagnosa_utama'] ?>" value="<?= $getSur['diagnosa_utama'] ?>">
                    <?= $getSur['diagnosa_utama'] ?>
                </option>
            <?php endif ?>
        </select>
    </div>
    <!-- Akhir pilihan diagnosis pasca bedah -->
    <!-- Mulai diagnosis pasca bedah lainnya -->
    <div class="form-group d-none" id="form-diagnosis-pasca-bedah-lainnya-lap-operasi">
        <label for="diagnosis-pasca-bedah-lainnya-lap-operasi">
            Diagnosis pasca bedah lainnya
        </label>
        <input type="text" class="form-control" placeholder="[ Diagnosis pasca bedah lainnya ]" id="diagnosis-pasca-bedah-lainnya-lap-operasi" name="diagnosis_pasca_bedah_lainnya">
    </div>
    <!-- Akhir diagnosis pasca bedah lainnya -->
    <!-- Mulai letak tumor primer -->
    <div class="form-group">
        <label for="letak-tumor-primer-lap-operasi">
            Letak tumor primer
        </label>
        <textarea name="letak_tumor_primer" id="letak-tumor-primer-lap-operasi" class="form-control" placeholder="[ Sebutkan letak tumor primer ]"></textarea>
    </div>
    <!-- Akhir letak tumor primer -->
    <!-- Mulai sifat operasi -->
    <div class="form-group">
        <label for="sifat-operasi-lap-operasi">
            Sifat operasi
        </label>
        <div class="row px-1">
            <?php foreach ($sifatOperasi as $so): ?>
                <div class="col-md form-check form-check-inline">
                    <div class="radio radio-primary">
                        <input type="radio" class="sifat-operasi-lap-operasi" name="sifat_operasi" id="sifat-operasi-lap-operasi-<?= $so['id_variabel'] ?>" value="<?= $so['id_variabel'] ?>">
                        <label for="sifat-operasi-lap-operasi-<?= $so['id_variabel'] ?>" class="form-check-label">
                            <?= $so['variabel'] ?>
                        </label>
                    </div>
                </div>
            <?php endforeach ?>
        </div>
    </div>
    <!-- Akhir sifat operasi -->
    <!-- Mulai tujuan operasi -->
    <div class="form-group">
        <label for="tujuan-operasi-lap-operasi">
            Tujuan operasi
        </label>
        <div class="row px-1">
            <?php foreach ($tujuanOperasi as $to): ?>
                <div class="col-md form-check form-check-inline">
                    <div class="radio radio-primary">
                        <input type="radio" class="tujuan-operasi-lap-operasi" name="tujuan_operasi" id="tujuan-operasi-lap-operasi-<?= $to['id_variabel'] ?>" value="<?= $to['id_variabel'] ?>">
                        <label for="tujuan-operasi-lap-operasi-<?= $to['id_variabel'] ?>" class="form-check-label">
                            <?= $to['variabel'] ?>
                        </label>
                    </div>
                </div>
            <?php endforeach ?>
        </div>
    </div>
    <!-- Akhir tujuan operasi -->
    <!-- Mulai jenis pembedahan -->
    <div class="form-group">
        <label for="jenis-pembedahan-lap-operasi">
            Jenis pembedahan
        </label>
        <div class="row px-1">
            <?php foreach ($jenisPembedahan as $jp): ?>
                <div class="col-md form-check form-check-inline">
                    <div class="radio radio-primary">
                        <input type="radio" class="jenis-pembedahan-lap-operasi" name="jenis_pembedahan" id="jenis-pembedahan-lap-operasi-<?= $jp['id_variabel'] ?>" value="<?= $jp['id_variabel'] ?>">
                        <label for="jenis-pembedahan-lap-operasi-<?= $jp['id_variabel'] ?>" class="form-check-label">
                            <?= $jp['variabel'] ?>
                        </label>
                    </div>
                </div>
            <?php endforeach ?>
        </div>
    </div>
    <!-- Akhir jenis pembedahan -->
    <!-- Mulai antibiotik propilaksis -->
    <div class="form-group">
        <label for="ab-propilaksis-lap-operasi">
            Antibiotik propilaksis
        </label>
        <div class="row px-1">
            <?php foreach ($antibiotikPropilaksis as $ap): ?>
                <div class="col-md form-check form-check-inline">
                    <div class="radio radio-primary">
                        <input type="radio" class="ab-propilaksis-lap-operasi" name="antibiotik_propilaksis" id="ab-propilaksis-lap-operasi-<?= $ap['id_variabel'] ?>" value="<?= $ap['id_variabel'] ?>">
                        <label for="ab-propilaksis-lap-operasi-<?= $ap['id_variabel'] ?>" class="form-check-label">
                            <?= $ap['variabel'] ?>
                        </label>
                    </div>
                </div>
            <?php endforeach ?>
        </div>
    </div>
    <!-- Akhir antibiotik propilaksis -->
    <!-- Mulai keterangan antibiotik propilaksis -->
    <div class="form-group d-none" id="ket-ab-lap-operasi">
        <div class="card new-card">
            <div class="card-header new-card-header">Keterangan Antibiotik Propilaksis</div>
            <div class="card-body">
                <!-- Mulai jenis antibiotik propilaksis -->
                <div class="form-group">
                    <label for="jenis-ab-lap-operasi">
                        Jenis antibiotik propilaksis
                    </label>
                    <input type="text" class="form-control" id="jenis-ab-lap-operasi" placeholder="[ Sebutkan jenisnya ]" name="jenis_antibiotik_propilaksis">
                </div>
                <!-- Akhir jenis antibiotik propilaksis -->
                <!-- Mulai waktu pemberian antibiotik propilaksis -->
                <div class="form-group">
                    <label for="waktu-ab-lap-operasi">
                        Waktu pemberian
                    </label>
                    <div class="input-group">
                        <input type="number" class="form-control" placeholder="[ Waktu pemberian ]" id="waktu-ab-lap-operasi" name="waktu_antibiotik_propilaksis" min="0" step="0.01">
                        <div class="input-group-append">
                            <span class="input-group-text">menit sebelum insisi</span>
                        </div>
                    </div>
                </div>
                <!-- Akhir waktu pemberian antibiotik propilaksis -->
            </div>
        </div>
    </div>
    <!-- Akhir keterangan antibiotik propilaksis -->
    <!-- Mulai tindakan operasi yang dilakukan -->
    <div class="form-group">
        <label for="tindakan-operasi-lap-operasi">
            Tindakan operasi yang dilakukan
        </label>
        <div class="row">
            <div class="col-md form-check form-check-inline">
                <div class="checkbox checkbox-primary px-0">
                    <input type="checkbox" class="tindakan-operasi-lap-operasi" id="tindakan-operasi-lap-operasi-pilihan" name="jenis_tindakan" value="pilihan">
                    <label for="tindakan-operasi-lap-operasi-pilihan" class="form-check-label">
                        ICD 9
                    </label>
                </div>
            </div>
            <div class="col-md form-check form-check-inline">
                <div class="checkbox checkbox-primary px-0">
                    <input type="checkbox" class="tindakan-operasi-lap-operasi" id="tindakan-operasi-lap-operasi-isian" name="jenis_tindakan" value="isian">
                    <label for="tindakan-operasi-lap-operasi-isian" class="form-check-label">
                        Teks
                    </label>
                </div>
            </div>
        </div>
    </div>
    <!-- Akhir tindakan operasi yang dilakukan -->
    <!-- Mulai pilihan tindakan operasi yang dilakukan -->
    <div class="form-group d-none" id="form-pilihan-tindakan-operasi-yang-dilakukan-lap-operasi">
        <label for="pilihan-tindakan-operasi-lap-operasi">
            Pilihan tindakan operasi yang dilakukan
        </label>
        <select name="tindakan_operasi[]" id="pilihan-tindakan-operasi-lap-operasi" class="form-control" multiple>
            <?php
            if (!empty($getSur['tindakan_procedure'])):
                $dataTindakan = json_decode($getSur['tindakan_procedure']);
                for ($i = 0; $i < count($dataTindakan); $i++):
                    ?>
                    <option id="tindakan-operasi-lap-operasi-<?= $dataTindakan[$i] ?>" value="<?= $dataTindakan[$i] ?>">
                        <?= $dataTindakan[$i] ?>
                    </option>
                    <?php
                endfor;
            endif;
            ?>
        </select>
    </div>
    <!-- Akhir pilihan tindakan operasi yang dilakukan -->
    <!-- Mulai tindakan operasi lainnya yang dilakukan -->
    <div class="form-group d-none" id="form-tindakan-operasi-lainnya-lap-operasi">
        <label for="tindakan-operasi-lainnya-lap-operasi">
            Tindakan operasi lainnya yang dilakukan
        </label>
        <input type="text" class="form-control" placeholder="[ Tindakan operasi lainnya yang dilakukan ]" id="tindakan-operasi-lainnya-lap-operasi" name="tindakan_operasi_lainnya">
    </div>
    <!-- Akhir tindakan operasi lainnya yang dilakukan -->
    <!-- Mulai deskripsi atau uraian operasi dokter 1 -->
    <div class="form-group" id="form-deskripsi-operasi-1-lap-operasi">
        <label for="deskripsi-operasi-1-lap-operasi">
            Deskripsi atau uraian operasi dokter 1
        </label>
        <textarea name="deskripsi_operasi_1" id="deskripsi-operasi-1-lap-operasi" class="form-control" placeholder="[ Sebutkan deskripsi atau uraian operasi dokter 1 ]"></textarea>
    </div>
    <!-- Akhir deskripsi atau uraian operasi dokter 1 -->
    <!-- Mulai deskripsi atau uraian operasi dokter 2 -->
    <div class="form-group d-none" id="form-deskripsi-operasi-2-lap-operasi">
        <label for="deskripsi-operasi-2-lap-operasi">
            Deskripsi atau uraian operasi dokter 2
        </label>
        <textarea name="deskripsi_operasi_2" id="deskripsi-operasi-2-lap-operasi" class="form-control" placeholder="[ Sebutkan deskripsi atau uraian operasi dokter 2 ]"></textarea>
    </div>
    <!-- Akhir deskripsi atau uraian operasi dokter 2 -->
    <!-- Mulai deskripsi atau uraian operasi dokter 3 -->
    <div class="form-group d-none" id="form-deskripsi-operasi-3-lap-operasi">
        <label for="deskripsi-operasi-3-lap-operasi">
            Deskripsi atau uraian operasi dokter 3
        </label>
        <textarea name="deskripsi_operasi_3" id="deskripsi-operasi-3-lap-operasi" class="form-control" placeholder="[ Sebutkan deskripsi atau uraian operasi dokter 3 ]"></textarea>
    </div>
    <!-- Akhir deskripsi atau uraian operasi dokter 3 -->
    <!-- Mulai komplikasi -->
    <div class="form-group">
        <label for="komplikasi-lap-operasi">
            Komplikasi
        </label>
        <div class="row px-1">
            <?php foreach ($komplikasi as $k): ?>
                <div class="col-md form-check form-check-inline">
                    <div class="radio radio-primary">
                        <input type="radio" class="komplikasi-lap-operasi" name="komplikasi" id="komplikasi-lap-operasi-<?= $k['id_variabel'] ?>" value="<?= $k['id_variabel'] ?>">
                        <label for="komplikasi-lap-operasi-<?= $k['id_variabel'] ?>" class="form-check-label">
                            <?= $k['variabel'] ?>
                        </label>
                    </div>
                </div>
            <?php endforeach ?>
        </div>
    </div>
    <!-- Akhir komplikasi -->
    <!-- Mulai sebutkan komplikasi -->
    <div class="form-group d-none" id="form-komplikasi-lap-operasi">
        <label for="ket-komplikasi-lap-operasi">
            Keterangan komplikasi
        </label>
        <input type="text" class="form-control" id="ket-komplikasi-lap-operasi" placeholder="[ Keterangan komplikasi ]" name="isi_komplikasi">
    </div>
    <!-- Akhir sebutkan komplikasi -->
    <!-- Mulai jumlah kehilangan darah -->
    <div class="form-group">
        <label for="jml-hlg-drh-lap-operasi">
            Jumlah kehilangan darah
        </label>
        <div class="input-group">
            <div class="input-group-prepend">
                <span class="input-group-text">±</span>
            </div>
            <input type="number" class="form-control" id="jml-hlg-drh-lap-operasi" placeholder="[ Sebutkan jumlah kehilangan darah ]" name="jml_kehilangan_darah" min="0" step="0.01">
            <div class="input-group-append">
                <span class="input-group-text">cc</span>
            </div>
        </div>
    </div>
    <!-- Akhir jumlah kehilangan darah -->
    <!-- Mulai transfusi -->
    <div class="form-group" id="form-transfusi-lap-operasi">
        <label for="transfusi-lap-operasi">
            Transfusi darah
        </label>
        <div class="input-group">
            <div class="input-group-prepend">
                <label for="jenis-transfusi-lap-operasi" class="input-group-text">Jenis</label>
            </div>
            <input type="text" class="form-control" id="jenis-transfusi-lap-operasi" placeholder="[ Sebutkan jenisnya ]">
            <div class="input-group-prepend">
                <label for="volume-transfusi-lap-operasi" class="input-group-text">Volume</label>
            </div>
            <input type="number" class="form-control" id="volume-transfusi-lap-operasi" placeholder="[ Sebutkan volumenya ]" step="0.01">
            <div class="input-group-append">
                <span class="input-group-text">cc</span>
            </div>
        </div>
    </div>
    <!-- Akhir transfusi -->
    <!-- Mulai aksi transfusi -->
    <div class="row form-group">
        <div class="col-sm-6">
            <button type="button" class="btn btn-outline-danger btn-block waves-effect" id="hapus-transfusi-lap-operasi">
                Hapus transfusi
            </button>
        </div>
        <div class="col-sm-6">
            <button type="button" class="btn btn-outline-success btn-block waves-effect" id="tambah-transfusi-lap-operasi">
                Tambah transfusi
            </button>
        </div>
    </div>
    <!-- Akhir aksi transfusi -->
    <!-- Mulai tabel transfusi -->
    <div class="form-group" style="max-height: 200px; overflow-y: auto;">
        <div class="table-responsive">
            <table class="table table-bordered table-hover table-custom" cellspacing="0" width="100%">
                <thead>
                    <tr class="table-tr-custom">
                        <th>#</th>
                        <th>Jenis Transfusi</th>
                        <th>Volume</th>
                    </tr>
                </thead>
                <tbody id="list-transfusi-lap-operasi"></tbody>
            </table>
        </div>
    </div>
    <!-- Akhir tabel transfusi -->
    <!-- Mulai spesimen -->
    <div class="form-group">
        <label for="spesimen-lap-operasi">
            Spesimen
        </label>
        <div class="row px-1">
            <?php foreach ($spesimen as $s): ?>
                <div class="col-md form-check form-check-inline">
                    <div class="radio radio-primary">
                        <input type="radio" class="spesimen-lap-operasi" name="spesimen" id="spesimen-lap-operasi-<?= $s['id_variabel'] ?>" value="<?= $s['id_variabel'] ?>">
                        <label for="spesimen-lap-operasi-<?= $s['id_variabel'] ?>" class="form-check-label">
                            <?= $s['variabel'] ?>
                        </label>
                    </div>
                </div>
            <?php endforeach ?>
        </div>
    </div>
    <!-- Akhir spesimen -->
    <!-- Mulai sebutkan spesimen -->
    <div class="form-group d-none" id="form-spesimen-lap-operasi">
        <label for="sebutkan-spesimen-lap-operasi">
            Sebutkan spesimen
        </label>
        <textarea name="isi_spesimen" class="form-control" id="sebutkan-spesimen-lap-operasi" placeholder="[ Sebutkan spesimen ]"></textarea>
    </div>
    <!-- Akhir sebutkan spesimen -->
    <!-- Mulai pemasangan implan -->
    <div class="form-group">
        <label for="pemasangan-implan-lap-operasi">
            Pemasangan implan
        </label>
        <div class="row px-1">
            <?php foreach ($pemasanganImplan as $pi): ?>
                <div class="col-md form-check form-check-inline">
                    <div class="radio radio-primary">
                        <input type="radio" class="pemasangan-implan-lap-operasi" name="pemasangan_implan" id="pemasangan-implan-lap-operasi-<?= $pi['id_variabel'] ?>" value="<?= $pi['id_variabel'] ?>">
                        <label for="pemasangan-implan-lap-operasi-<?= $pi['id_variabel'] ?>" class="form-check-label">
                            <?= $pi['variabel'] ?>
                        </label>
                    </div>
                </div>
            <?php endforeach ?>
        </div>
    </div>
    <!-- Akhir pemasangan implan -->
    <!-- Mulai keterangan pemasangan implan -->
    <div class="form-group d-none" id="form-pemasangan-implan-lap-operasi">
        <div class="card new-card">
            <div class="card-header new-card-header">Keterangan Pemasangan Implan</div>
            <div class="card-body">
                <!-- Mulai nomor seri implan -->
                <div class="form-group">
                    <label for="seri-implan-lap-operasi">
                        Nomor seri implan
                    </label>
                    <input type="text" class="form-control" id="seri-implan-lap-operasi" placeholder="[ Nomor seri implan ]" name="seri_implan">
                </div>
                <!-- Akhir nomor seri implan -->
                <!-- Mulai nama implan -->
                <div class="form-group">
                    <label for="nama-implan-lap-operasi">
                        Nama implan
                    </label>
                    <input type="text" class="form-control" id="nama-implan-lap-operasi" placeholder="[ Nama implan ]" name="nama_implan">
                </div>
                <!-- Akhir nama implan -->
            </div>
        </div>
    </div>
    <!-- Akhir keterangan pemasangan implan -->
    <!-- Mulai riwayat penyakit sekarang -->
    <div class="form-group">
        <label for="riwayat-penyakit-sekarang-lap-operasi">
            Data riwayat penyakit sekarang
        </label>
        <div class="row px-1">
            <?php foreach ($pilihanCPPT as $pc): ?>
                <div class="col-md form-check form-check-inline">
                    <div class="radio radio-primary">
                        <input type="radio" name="riwayat_penyakit_sekarang" id="riwayat-penyakit-sekarang-lap-operasi-<?= $pc['id_variabel'] ?>" value="<?= $pc['id_variabel'] ?>" class="riwayat-penyakit-sekarang-lap-operasi">
                        <label for="riwayat-penyakit-sekarang-lap-operasi-<?= $pc['id_variabel'] ?>" class="form-check-label">
                            <?= $pc['variabel'] ?>
                        </label>
                    </div>
                </div>
            <?php endforeach ?>
        </div>
    </div>
    <!-- Akhir riwayat penyakit sekarang -->
    <!-- Mulai keterangan riwayat penyakit sekarang -->
    <div class="form-group alert alert-info text-justify d-none" id="info-riwayat-penyakit-sekarang-lap-operasi"></div>
    <div class="form-group d-none" id="form-riwayat-penyakit-sekarang-lap-operasi">
        <label for="ket-riwayat-penyakit-sekarang-lap-operasi">
            Riwayat penyakit sekarang
        </label>
        <textarea name="ket_riwayat_penyakit_sekarang" class="form-control" id="ket-riwayat-penyakit-sekarang-lap-operasi" placeholder="[ Riwayat penyakit sekarang ]"></textarea>
    </div>
    <!-- Akhir keterangan riwayat penyakit sekarang -->
    <div class="row">
        <div class="col-sm-12">
            <div class="btn-group pull-right">
                <button type="button" class="btn btn-primary waves-effect" id="btn-simpan-lap-operasi">
                    <i class="fa fa-save"></i> Simpan
                </button>
            </div>
        </div>
    </div>
</form>
<!-- Akhir form laporan operasi -->

<!-- Mulai modal history -->
<div class="modal fade" id="modal-history-lap-operasi" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-dialog-centered" style="max-width: 90%">
        <div class="modal-content" id="lihat-history-modal-lap-operasi"></div>
    </div>
</div>
<!-- Akhir modal history -->

<!-- Mulai modal yakin batal -->
<div class="modal fade" id="modal-batal-lap-operasi" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title mt-0" id="mySmallModalLabel">Peringatan</h4>
            </div>
            <div class="modal-body">
                <div class="form-group">Apakah Anda yakin untuk membatalkan data ini?</div>
                <input type="hidden" id="id-lap-operasi" value="">
                <div class="alert alert-danger mb-0" role="alert">
                    <strong>Peringatan!</strong> Data yang sudah dibatalkan tidak dapat diubah lagi!
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary waves-effect btn-block" id="tutup-batal-lap-operasi">
                    <i class="fa fa-times"></i> Tutup
                </button>
                <button type="button" class="btn btn-danger waves-effect btn-block mt-0" id="yakin-batal-lap-operasi">
                    <i class="fa fa-window-close"></i> Batal
                </button>
            </div>
        </div>
    </div>
</div>
<!-- Akhir modal yakin batal -->

<!-- Mulai modal detail -->
<div class="modal fade" id="modal-detail-lap-operasi" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content" id="lihat-detail-lap-operasi"></div>
    </div>
</div>
<!-- Akhir modal detail -->

<script>
    $(document).ready(function () {
        // mulai daftar tunggu
        // $('#daftar-tunggu-lap-operasi').select2({
        //     allowClear: true,
        //     placeholder: '[ Pilih daftar tunggu ]'
        // });
        
        // Auto select daftar tunggu pertama jika ada
        var firstWaitingOption = $('#daftar-tunggu-lap-operasi option:not([value=""])').first();
        if (firstWaitingOption.length > 0) {
            $('#daftar-tunggu-lap-operasi').val(firstWaitingOption.val()).trigger('change');
        }
        // akhir daftar tunggu

        let dokOperatorBedah = $('#dok-operator-bedah-lap-operasi');

        // Mulai dokter bedah
        dokOperatorBedah.select2({
            maximumSelectionLength: 3, // Ambil jumlah dokter
            placeholder: '[ Pilih dokter operator bedah ]'
        });
        // Akhir dokter bedah

        // Mulai asisten operator
        $('#ass-operator-lap-operasi').select2({
            placeholder: '[ Pilih asisten operator bedah ]'
        });
        // Akhir asisten operator

        // Mulai perawat instrumentator
        $('#instrumentator-lap-operasi').select2({
            placeholder: '[ Pilih perawat instrumentator ]'
        });
        // Akhir perawat instrumentator

        // Mulai perawat sirkuler
        $('#sirkuler-lap-operasi').select2({
            placeholder: '[ Pilih perawat sirkuler ]'
        });
        // Akhir perawat sirkuler

        // Mulai dokter anestesi
        $('#dok-anestesi-lap-operasi').select2({
            placeholder: '[ Pilih dokter anestesi ]'
        });
        // Akhir dokter anestesi

        // Mulai pilihan diagnosis pra bedah
        $('#pilihan-diagnosis-pra-bedah-lap-operasi').select2({
            placeholder: '[ Pilih diagnosis pra bedah ]',
            ajax: {
                url: "<?= base_url('operasi/FormLaporanOperasi/icd10') ?>",
                dataType: 'json',
                delay: 250,
                processResults: function (data) {
                    return {
                        results: data
                    };
                },
                cache: true
            }
        });
        // Akhir pilihan diagnosis pra bedah

        // Mulai pilihan diagnosis pasca bedah
        $('#pilihan-diagnosis-pasca-bedah-lap-operasi').select2({
            placeholder: '[ Pilih diagnosis pasca bedah ]',
            ajax: {
                url: "<?= base_url('operasi/FormLaporanOperasi/icd10') ?>",
                dataType: 'json',
                delay: 250,
                processResults: function (data) {
                    return {
                        results: data
                    };
                },
                cache: true
            }
        });
        // Akhir pilihan diagnosis pasca bedah

        // Mulai pilihan tindakan operasi yang dilakukan
        $('#pilihan-tindakan-operasi-lap-operasi').select2({
            placeholder: '[ Pilih tindakan operasi yang dilakukan ]',
            ajax: {
                url: "<?= base_url('operasi/FormLaporanOperasi/icd9') ?>",
                dataType: 'json',
                delay: 250,
                processResults: function (data) {
                    return {
                        results: data
                    };
                },
                cache: true
            }
        });
        // Akhir pilihan tindakan operasi yang dilakukan

        // Mulai deskripsi atau uraian operasi
        dokOperatorBedah.change(function () {
            let isi;
            isi = [];
            let data = dokOperatorBedah.find(':selected');
            isi.push(data);
            if (data.length === 1) {
                $('#form-deskripsi-operasi-2-lap-operasi, #form-deskripsi-operasi-3-lap-operasi').addClass('d-none');
                $('#deskripsi-operasi-2-lap-operasi, #deskripsi-operasi-3-lap-operasi').removeAttr('required', null).val();
            } else if (data.length === 2) {
                $('#form-deskripsi-operasi-2-lap-operasi').removeClass('d-none');
                $('#deskripsi-operasi-2-lap-operasi').attr('required', null);
                $('#form-deskripsi-operasi-3-lap-operasi').addClass('d-none');
                $('#deskripsi-operasi-3-lap-operasi').removeAttr('required', null).val();
            } else if (data.length === 3) {
                $('#form-deskripsi-operasi-2-lap-operasi, #form-deskripsi-operasi-3-lap-operasi').removeClass('d-none');
                $('#deskripsi-operasi-2-lap-operasi, #deskripsi-operasi-3-lap-operasi').attr('required', null);
            }
        });
        // Akhir deskripsi atau uraian operasi

        // Mulai jenis anestesi
        $('.jenis-anestesi-lap-operasi').change(function () {
            let id = $(this).val();
            if (id === '1982') {
                $('#ket-anestesi-lokal-lap-operasi').removeClass('d-none');
            } else {
                $('#ket-anestesi-lokal-lap-operasi, #form-teknik-lap-operasi, #form-respon-lap-operasi, #form-toksikasi-lap-operasi').addClass('d-none');
                $('.teknik-anestesi-lokal-lap-operasi, .respon-hipersensitivitas-lap-operasi, .kejadian-toksikasi-lap-operasi').prop('checked', false);
                $('#lokasi-lap-operasi, #obat-anestesi-lap-operasi, #ket-teknik-lap-operasi, #waktu-ab-lap-operasi, #ket-respon-lap-operasi, #sebutkan-toksikasi-lap-operasi').val(null);
            }
        });
        if ($('.jenis-anestesi-lap-operasi:checked').val() === '1982') {
            $('#ket-anestesi-lokal-lap-operasi').removeClass('d-none');
        }
        // Akhir jenis anestesi

        // Mulai diagnosis pra bedah
        $('.diagnosis-pra-bedah-lap-operasi').change(function () {
            let id = $(this).val();
            if (id === 'pilihan') {
                $('#form-pilihan-diagnosis-pra-bedah-lap-operasi').removeClass('d-none');
                $('#pilihan-diagnosis-pra-bedah-lap-operasi').attr('required', null);
                $('#form-diagnosis-pra-bedah-lainnya-lap-operasi').addClass('d-none');
                $('#diagnosis-pra-bedah-lainnya-lap-operasi').removeAttr('required', null).val(null);
            } else if (id === 'isian') {
                $('#form-pilihan-diagnosis-pra-bedah-lap-operasi').addClass('d-none');
                $('#pilihan-diagnosis-pra-bedah-lap-operasi').removeAttr('required', null).val(null).trigger('change');
                $('#form-diagnosis-pra-bedah-lainnya-lap-operasi').removeClass('d-none');
                $('#diagnosis-pra-bedah-lainnya-lap-operasi').attr('required', null);
            }
        });
        // Akhir diagnosis pra bedah

        // Mulai diagnosis pasca bedah
        $('.diagnosis-pasca-bedah-lap-operasi').change(function () {
            let id = $(this).val();
            if (id === 'pilihan') {
                $('#form-pilihan-diagnosis-pasca-bedah-lap-operasi').removeClass('d-none');
                $('#pilihan-diagnosis-pasca-bedah-lap-operasi').attr('required', null);
                $('#form-diagnosis-pasca-bedah-lainnya-lap-operasi').addClass('d-none');
                $('#diagnosis-pasca-bedah-lainnya-lap-operasi').removeAttr('required', null).val(null);
            } else if (id === 'isian') {
                $('#form-pilihan-diagnosis-pasca-bedah-lap-operasi').addClass('d-none');
                $('#pilihan-diagnosis-pasca-bedah-lap-operasi').removeAttr('required', null).val(null).trigger('change');
                $('#form-diagnosis-pasca-bedah-lainnya-lap-operasi').removeClass('d-none');
                $('#diagnosis-pasca-bedah-lainnya-lap-operasi').attr('required', null);
            }
        });
        // Akhir diagnosis pasca bedah

        // Mulai tindakan operasi yang dilakukan
        let checkPilihanTindakanOperasi = $('#tindakan-operasi-lap-operasi-pilihan');
        let checkIsianTindakanOperasi = $('#tindakan-operasi-lap-operasi-isian');
        checkPilihanTindakanOperasi.change(function () {
            if (checkPilihanTindakanOperasi.prop('checked')) {
                $('#form-pilihan-tindakan-operasi-yang-dilakukan-lap-operasi').removeClass('d-none');
                $('#pilihan-tindakan-operasi-lap-operasi').attr('required', null);
            } else if (checkPilihanTindakanOperasi.prop('checked', false)) {
                $('#form-pilihan-tindakan-operasi-yang-dilakukan-lap-operasi').addClass('d-none');
                $('#pilihan-tindakan-operasi-lap-operasi').removeAttr('required', null).val(null).trigger('change');
            }
        });
        checkIsianTindakanOperasi.change(function () {
            if (checkIsianTindakanOperasi.prop('checked')) {
                $('#form-tindakan-operasi-lainnya-lap-operasi').removeClass('d-none');
                $('#tindakan-operasi-lainnya-lap-operasi').attr('required', null);
            } else if (checkIsianTindakanOperasi.prop('checked', false)) {
                $('#form-tindakan-operasi-lainnya-lap-operasi').addClass('d-none');
                $('#tindakan-operasi-lainnya-lap-operasi').removeAttr('required', null).val(null);
            }
        });
        // Akhir tindakan operasi yang dilakukan

        // Mulai antibiotik propilaksis
        $('.ab-propilaksis-lap-operasi').change(function () {
            let id = $(this).val();
            if (id === '1978') {
                $('#ket-ab-lap-operasi').removeClass('d-none');
            } else {
                $('#ket-ab-lap-operasi').addClass('d-none');
                $('#jenis-ab-lap-operasi').val(null);
            }
        });
        if ($('.ab-propilaksis-lap-operasi:checked').val() === '1978') {
            $('#ket-ab-lap-operasi').removeClass('d-none');
        }
        // Akhir antibiotik propilaksis

        // Mulai teknik anestesi lokal
        $('.teknik-anestesi-lokal-lap-operasi').change(function () {
            let id = $(this).val();
            if (id === '2149') {
                $('#form-teknik-lap-operasi').removeClass('d-none');
            } else {
                $('#form-teknik-lap-operasi').addClass('d-none');
                $('#ket-teknik-lap-operasi, #waktu-ab-lap-operasi').val(null);
            }
        });
        if ($('.teknik-anestesi-lokal-lap-operasi:checked').val() === '2149') {
            $('#form-teknik-lap-operasi').removeClass('d-none');
        }
        // Akhir teknik anestesi lokal

        // Mulai respon hipersensitivitas
        $('.respon-hipersensitivitas-lap-operasi').change(function () {
            let id = $(this).val();
            if (id === '2151') {
                $('#form-respon-lap-operasi').removeClass('d-none');
            } else {
                $('#form-respon-lap-operasi').addClass('d-none');
                $('#ket-respon-lap-operasi').val(null);
            }
        });
        if ($('.respon-hipersensitivitas-lap-operasi:checked').val() === '2151') {
            $('#form-respon-lap-operasi').removeClass('d-none');
        }
        // Akhir respon hipersensitivitas

        // Mulai kejadian toksikasi
        $('.kejadian-toksikasi-lap-operasi').change(function () {
            let id = $(this).val();
            if (id === '2153') {
                $('#form-toksikasi-lap-operasi').removeClass('d-none');
            } else {
                $('#form-toksikasi-lap-operasi').addClass('d-none');
                $('#sebutkan-toksikasi-lap-operasi').val(null);
            }
        });
        if ($('.kejadian-toksikasi-lap-operasi:checked').val() === '2153') {
            $('#form-toksikasi-lap-operasi').removeClass('d-none');
        }
        // Akhir kejadian toksikasi

        // Mulai komplikasi
        $('.komplikasi-lap-operasi').change(function () {
            let id = $(this).val();
            if (id === '2155') {
                $('#form-komplikasi-lap-operasi').removeClass('d-none');
            } else {
                $('#form-komplikasi-lap-operasi').addClass('d-none');
                $('#ket-komplikasi-lap-operasi').val(null);
            }
        });
        if ($('.komplikasi-lap-operasi:checked').val() === '2155') {
            $('#form-komplikasi-lap-operasi').removeClass('d-none');
        }
        // Akhir komplikasi

        // Mulai tambah transfusi darah
        $('#tambah-transfusi-lap-operasi').click(function () {
            let jenis = $('#jenis-transfusi-lap-operasi');
            let volume = $('#volume-transfusi-lap-operasi');
            let isi =
                "<tr>" +
                "<td><input type='checkbox' id='pilih-transfusi-lap-operasi' name='pilih_transfusi'></td>" +
                "<td><input type='text' class='form-control isi-jenis-transfusi-lap-operasi' name='jenis_transfusi[ ]' value='" + jenis.val() + "' readonly></td>" +
                "<td><input type='number' class='form-control isi-volume-transfusi-lap-operasi' name='volume_transfusi[ ]' value='" + volume.val() + "' step='0.01' readonly></td>" +
                "</tr>";
            if (jenis.val() && volume.val()) {
                $(isi).hide().appendTo('table tbody#list-transfusi-lap-operasi').fadeIn(1000);
            }

            // Bersihkan Form
            jenis.val(null);
            volume.val(null);
        });
        // Akhir tambah transfusi darah

        // Mulai hapus transfusi darah
        $('#hapus-transfusi-lap-operasi').click(function () {
            $('table tbody#list-transfusi-lap-operasi').find("input[name='pilih_transfusi']").each(function () {
                if ($(this).is(':checked')) {
                    $(this).parents('tr').fadeOut(500, function () {
                        $(this).remove();
                    });
                }
            });
        });
        // Akhir hapus transfusi darah

        // Mulai spesimen
        $('.spesimen-lap-operasi').change(function () {
            let id = $(this).val();
            if (id === '2161') {
                $('#form-spesimen-lap-operasi').removeClass('d-none');
            } else {
                $('#form-spesimen-lap-operasi').addClass('d-none');
                $('#sebutkan-spesimen-lap-operasi').val(null);
            }
        });
        if ($('.spesimen-lap-operasi:checked').val() === '2161') {
            $('#form-spesimen-lap-operasi').removeClass('d-none');
        }
        // Akhir spesimen

        // Mulai pemasangan implan
        $('.pemasangan-implan-lap-operasi').change(function () {
            let id = $(this).val();
            if (id === '2163') {
                $('#form-pemasangan-implan-lap-operasi').removeClass('d-none');
            } else {
                $('#form-pemasangan-implan-lap-operasi').addClass('d-none');
                $('#nama-implan-lap-operasi, #seri-implan-lap-operasi').val(null);
            }
        });
        if ($('.pemasangan-implan-lap-operasi:checked').val() === '2163') {
            $('#form-pemasangan-implan-lap-operasi').removeClass('d-none');
        }
        // Akhir pemasangan implan

        // Mulai riwayat penyakit sekarang
        let checkRiwayatPenyakitSekarang = $('.riwayat-penyakit-sekarang-lap-operasi');
        checkRiwayatPenyakitSekarang.click(function () {
            let id = $(this).val();
            if (id === '4661') {
                $('#info-riwayat-penyakit-sekarang-lap-operasi').addClass('d-none');
                $('#form-riwayat-penyakit-sekarang-lap-operasi').removeClass('d-none');
                $('#ket-riwayat-penyakit-sekarang-lap-operasi').attr('required', null).removeAttr('readonly', null);
            } else if (id === '4662') {
                riwayatPenyakitSekarang('<?= $nokun ?>');
                $('#form-riwayat-penyakit-sekarang-lap-operasi').removeClass('d-none');
                $('#ket-riwayat-penyakit-sekarang-lap-operasi').attr('readonly', 'required');
            } else if (id === '4663') {
                $('#info-riwayat-penyakit-sekarang-lap-operasi, #form-riwayat-penyakit-sekarang-lap-operasi').addClass('d-none');
                $('#ket-riwayat-penyakit-sekarang-lap-operasi').removeAttr('required', null).val(null);
            }
        });

        function riwayatPenyakitSekarang(id, status = 0) {
            let riwayat_penyakit_sekarang;
            if (status === 1) {
                riwayat_penyakit_sekarang = getJSON("<?= base_url('rekam_medis/Medis/ambilRiwayatPenyakitSekarang') ?>", {
                    id: id
                });
            } else {
                riwayat_penyakit_sekarang = getJSON("<?= base_url('rekam_medis/Medis/ambilRiwayatPenyakitSekarang') ?>", {
                    nokun: id
                });
            }

            if (riwayat_penyakit_sekarang.data !== null) {
                $('#info-riwayat-penyakit-sekarang-lap-operasi').html('Dibuat oleh <b>' + riwayat_penyakit_sekarang.data['oleh_desc'] + '</b> pada ' + moment(riwayat_penyakit_sekarang.data['created_at']).format('LLL') + null).removeClass('d-none');
                $('#ket-riwayat-penyakit-sekarang-lap-operasi').val(riwayat_penyakit_sekarang.data['riwayat_sakit_sekarang']);
            } else {
                toastr.warning('Data riwayat penyakit sekarang tidak tersedia');
            }
        }
        // Akhir riwayat penyakit sekarang

        // Mulai simpan
        $('#btn-simpan-lap-operasi').click(function (event) {
            // Mulai proses simpan
            let form = $('#form-lap-operasi').serializeArray();
            $.ajax({
                url: "<?= base_url('operasi/FormLaporanOperasi/aksi/simpan') ?>",
                data: form,
                method: 'POST',
                dataType: 'json',
                success: function (data) {
                    if (data.status === 'success') {
                        toastr.success('Berhasil disimpan');
                        $('#view-lap-operasi').load("<?= base_url('operasi/FormLaporanOperasi/index/' . $nomr . '/' . $nopen . '/' . $nokun) ?>");
                    } else {
                        $.each(data.errors, function (index, element) {
                            toastr.warning(element);
                        });
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    toastr.error('Internal Server Error!');
                }
            });
            event.preventDefault();
            // Akhir proses simpan
        });
        // Akhir simpan

        // Mulai history
        $('#tbl-history-lap-operasi').click(function () {
            let nomr = $(this).attr('data-id');
            $.ajax({
                method: 'POST',
                url: "<?= base_url('operasi/FormLaporanOperasi/history') ?>",
                data: {
                    nomr: nomr,
                },
                success: function (data) {
                    $('#lihat-history-modal-lap-operasi').html(data);
                }
            });
        });
        // Akhir history

        // Batal data
        $(document).on('click', '.tbl-batal-lap-operasi', function () {
            let id = $(this).data('id');
            $('#id-lap-operasi').val(id);
        });

        // Tutup pertanyaan batal data
        $('#tutup-batal-lap-operasi').click(function () {
            $('#modal-batal-lap-operasi').modal('toggle');
        });

        // Yakin batal data
        $('#yakin-batal-lap-operasi').click(function () {
            const id = $('#id-lap-operasi').val();
            $.ajax({
                type: 'POST',
                url: "<?= base_url('operasi/FormLaporanOperasi/batal') ?>",
                data: {
                    id: id,
                },
                dataType: 'json',
                success: function (data) {
                    if (data.status === 'success') {
                        alertify.success('Data dibatalkan');
                        location.reload();
                    } else {
                        alertify.warning('Internal Server Error');
                    }
                }
            })
        });

        // Ambil data yang akan diubah
        $(document).on('click', '.tbl-detail-lap-operasi', function () {
            let id = $(this).data('id');
            $.ajax({
                method: 'POST',
                url: "<?= base_url('operasi/FormLaporanOperasi/index') ?>",
                data: {
                    id: id,
                    jenis: 'detail',
                },
                success: function (data) {
                    $('#lihat-detail-lap-operasi').html(data);
                }
            })
        });
    });
</script>

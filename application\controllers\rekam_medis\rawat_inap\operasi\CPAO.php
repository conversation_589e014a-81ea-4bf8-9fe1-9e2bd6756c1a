<?php
defined('BASEPATH') or exit('No direct script access allowed');

class CPAO extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'rekam_medis/rawat_inap/operasi/CPAOModel'
      )
    );
  }

  public function index()
  {
    $post = $this->input->post();
    $id = isset($post['id']) ? $post['id'] : null;
    $data = array(
      'alat' => $this->masterModel->referensi(1562),
      'kassa' => $this->masterModel->referensi(1563),
      'xray' => $this->masterModel->referensi(1564),
      'instrumen' => $this->CPAOModel->instrumen(),
      'listDr' => $this->masterModel->listDrUmum(),
      'listPerawat' => $this->masterModel->listPerawat(),
    );
    if (isset($id)) {
      // Mulai form detail
      $data['id'] = $id;
      $data['detail'] = $this->CPAOModel->detail($post['id']);
      // echo '<pre>';print_r($data);exit();
      $this->load->view('rekam_medis/rawat_inap/operasi/CPAO/detail/index', $data);
      // Akhir form detail
    } else {
      $pasien = $this->pengkajianAwalModel->getNomr($this->uri->segment(2));
      $data['pasien'] = $pasien;
      $data['nokun'] = $pasien['NOKUN'];
      $data['jumlah'] = $this->CPAOModel->history($pasien['NOKUN'], 'jumlah');
      // echo '<pre>';print_r($data);exit();
      $this->load->view('rekam_medis/rawat_inap/operasi/CPAO/index', $data);
    }
  }

  public function instrumenAlatOperasi()
  {
    $post = $this->input->post();
    $id = isset($post['id']) ? $post['id'] : null;
    $data = array('instrumenAlatOperasi' => $this->CPAOModel->instrumenAlatOperasi($id));
    if (isset($post['param'])) {
      if ($post['param'] == 'index') {
        // echo '<pre>';print_r($data);exit();
        $this->load->view('rekam_medis/rawat_inap/operasi/CPAO/instrumen', $data);
      } elseif ($post['param'] == 'detail') {
        $data['detailAlat'] = $this->CPAOModel->detailAlat($post['idCPAO']);
        // echo '<pre>';print_r($data);exit();
        $this->load->view('rekam_medis/rawat_inap/operasi/CPAO/detail/instrumen', $data);
      }
    }
  }

  public function aksi($param)
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'simpan') {
        $rules = $this->CPAOModel->rules;
        $this->form_validation->set_rules($rules);
        if ($this->form_validation->run() == true) {
          $post = $this->input->post();
          // echo '<pre>';print_r($post);exit();
          $id = isset($post['id']) ? $post['id'] : null;
          $idCPAOAlat = isset($post['id_cpao_alat']) ? $post['id_cpao_alat'] : null;
          $idCPAOTambahan = isset($post['id_cpao_tambahan']) ? $post['id_cpao_tambahan'] : null;
          $idCPAOKassa = isset($post['id_cpao_kassa']) ? $post['id_cpao_kassa'] : null;
          $oleh = $this->session->userdata['id'];
          $createdAt = date('Y-m-d H:i:s');

          // Mulai data CPAO
          $data = array(
            'id_instrumen' => isset($post['set_instrumen']) ? $post['set_instrumen'] : null,
            'nokun' => isset($post['nokun']) ? $post['nokun'] : null,
            'tanggal' => isset($post['tanggal']) ? $post['tanggal'] : null,
            'jam' => isset($post['jam']) ? $post['jam'] : null,
            'tindakan_operasi' => isset($post['tindakan_operasi']) ? $post['tindakan_operasi'] : null,
            'alat' => isset($post['alat']) ? $post['alat'] : null,
            'ket_alat' => isset($post['ket_alat']) ? $post['ket_alat'] : null,
            'kassa' => isset($post['kassa']) ? $post['kassa'] : null,
            'ket_kassa' => isset($post['ket_kassa']) ? $post['ket_kassa'] : null,
            'xray' => isset($post['xray']) ? $post['xray'] : null,
            'ket_xray' => isset($post['ket_xray']) ? $post['ket_xray'] : null,
            'dok_operator' => isset($post['dok_operator']) ? $post['dok_operator'] : null,
            'instrumentator' => isset($post['instrumentator']) ? json_encode($post['instrumentator']) : null,
            'instrumentator_2' => isset($post['instrumentator_2']) ? $post['instrumentator_2'] : null,
            'onloop' => isset($post['onloop']) ? json_encode($post['onloop']) : null,
            'oleh' => $oleh,
            'created_at' => $createdAt,
            'status' => 1,
          );
          // echo '<pre>';print_r($data);exit();
          if (isset($id)) {
            $this->CPAOModel->ubah($data, $id);
          } else {
            $id = $this->CPAOModel->simpan($data);
          }
          // Akhir data CPAO

          // Mulai data alat
          $indexAlat = 0;
          $dataAlat = array();
          if (isset($post['no_alat'])) {
            foreach ($post['no_alat'] as $n) {
              $dataAlat[$indexAlat] = array(
                'id_cpao' => $id,
                'id_alat' => isset($post['id_alat'][$indexAlat]) ? $post['id_alat'][$indexAlat] : null,
                'nama' => isset($post['nama_alat'][$indexAlat]) ? $post['nama_alat'][$indexAlat] : null,
                'pendek_pre_op' => isset($post['pendek_pre_op'][$indexAlat]) ? $post['pendek_pre_op'][$indexAlat] : null,
                'pendek_tambahan' => isset($post['pendek_tambahan'][$indexAlat]) ? $post['pendek_tambahan'][$indexAlat] : null,
                'sedang_pre_op' => isset($post['sedang_pre_op'][$indexAlat]) ? $post['sedang_pre_op'][$indexAlat] : null,
                'sedang_tambahan' => isset($post['sedang_tambahan'][$indexAlat]) ? $post['sedang_tambahan'][$indexAlat] : null,
                'panjang_pre_op' => isset($post['panjang_pre_op'][$indexAlat]) ? $post['panjang_pre_op'][$indexAlat] : null,
                'panjang_tambahan' => isset($post['panjang_tambahan'][$indexAlat]) ? $post['panjang_tambahan'][$indexAlat] : null,
                'oleh' => $oleh,
                'created_at' => $createdAt,
                'status' => 1,
              );
              $indexAlat++;
            }
          }
          // echo '<pre>';print_r($dataAlat);exit();
          if (isset($idCPAOAlat)) {
            $this->CPAOModel->ubahAlat($dataAlat, $idCPAOAlat);
          } else {
            $this->CPAOModel->simpanAlat($dataAlat);
          }
          // Akhir data alat

          // Mulai data tambahan
          $dataTambahan = array(
            'id_cpao' => $id,
            'suction_pre_op' => isset($post['suction_pre_op']) ? $post['suction_pre_op'] : null,
            'suction_tambahan' => isset($post['suction_tambahan']) ? $post['suction_tambahan'] : null,
            'k_medium_pre_op' => isset($post['k_medium_pre_op']) ? $post['k_medium_pre_op'] : null,
            'k_medium_tambahan' => isset($post['k_medium_tambahan']) ? $post['k_medium_tambahan'] : null,
            'retraktor_pre_op' => isset($post['retraktor_pre_op']) ? $post['retraktor_pre_op'] : null,
            'retraktor_tambahan' => isset($post['retraktor_tambahan']) ? $post['retraktor_tambahan'] : null,
            'l_hak_pre_op' => isset($post['l_hak_pre_op']) ? $post['l_hak_pre_op'] : null,
            'l_hak_tambahan' => isset($post['l_hak_tambahan']) ? $post['l_hak_tambahan'] : null,
            'jarum_lepas_pre_op' => isset($post['jarum_lepas_pre_op']) ? $post['jarum_lepas_pre_op'] : null,
            'jarum_lepas_tambahan' => isset($post['jarum_lepas_tambahan']) ? $post['jarum_lepas_tambahan'] : null,
            'nm_tambahan_6' => isset($post['nm_tambahan_6']) ? $post['nm_tambahan_6'] : null,
            'tambahan_6_pre_op' => isset($post['tambahan_6_pre_op']) ? $post['tambahan_6_pre_op'] : null,
            'tambahan_6_tambahan' => isset($post['tambahan_6_tambahan']) ? $post['tambahan_6_tambahan'] : null,
            'nm_tambahan_7' => isset($post['nm_tambahan_7']) ? $post['nm_tambahan_7'] : null,
            'tambahan_7_pre_op' => isset($post['tambahan_7_pre_op']) ? $post['tambahan_7_pre_op'] : null,
            'tambahan_7_tambahan' => isset($post['tambahan_7_tambahan']) ? $post['tambahan_7_tambahan'] : null,
            'nm_tambahan_8' => isset($post['nm_tambahan_8']) ? $post['nm_tambahan_8'] : null,
            'tambahan_8_pre_op' => isset($post['tambahan_8_pre_op']) ? $post['tambahan_8_pre_op'] : null,
            'tambahan_8_tambahan' => isset($post['tambahan_8_tambahan']) ? $post['tambahan_8_tambahan'] : null,
            'oleh' => $oleh,
            'created_at' => $createdAt,
            'status' => 1,
          );
          // echo '<pre>';print_r($dataTambahan);exit();
          if (isset($idCPAOTambahan)) {
            $this->CPAOModel->ubahTambahan($dataTambahan, $idCPAOTambahan);
          } else {
            $this->CPAOModel->simpanTambahan($dataTambahan);
          }
          // Akhir data tambahan

          // Mulai data kassa
          $dataKassa = array(
            'id_cpao' => $id,
            'kassa_besar_masuk_1' => isset($post['kassa_besar_masuk_1']) ? $post['kassa_besar_masuk_1'] : null,
            'kassa_besar_masuk_2' => isset($post['kassa_besar_masuk_2']) ? $post['kassa_besar_masuk_2'] : null,
            'kassa_besar_masuk_3' => isset($post['kassa_besar_masuk_3']) ? $post['kassa_besar_masuk_3'] : null,
            'kassa_besar_masuk_4' => isset($post['kassa_besar_masuk_4']) ? $post['kassa_besar_masuk_4'] : null,
            'kassa_besar_masuk_5' => isset($post['kassa_besar_masuk_5']) ? $post['kassa_besar_masuk_5'] : null,
            'kassa_besar_masuk_6' => isset($post['kassa_besar_masuk_6']) ? $post['kassa_besar_masuk_6'] : null,
            'kassa_besar_masuk_7' => isset($post['kassa_besar_masuk_7']) ? $post['kassa_besar_masuk_7'] : null,
            'kassa_besar_masuk_8' => isset($post['kassa_besar_masuk_8']) ? $post['kassa_besar_masuk_8'] : null,
            'kassa_besar_masuk_9' => isset($post['kassa_besar_masuk_9']) ? $post['kassa_besar_masuk_9'] : null,
            'kassa_besar_masuk_10' => isset($post['kassa_besar_masuk_10']) ? $post['kassa_besar_masuk_10'] : null,
            'kassa_besar_keluar_1' => isset($post['kassa_besar_keluar_1']) ? $post['kassa_besar_keluar_1'] : null,
            'kassa_besar_keluar_2' => isset($post['kassa_besar_keluar_2']) ? $post['kassa_besar_keluar_2'] : null,
            'kassa_besar_keluar_3' => isset($post['kassa_besar_keluar_3']) ? $post['kassa_besar_keluar_3'] : null,
            'kassa_besar_keluar_4' => isset($post['kassa_besar_keluar_4']) ? $post['kassa_besar_keluar_4'] : null,
            'kassa_besar_keluar_5' => isset($post['kassa_besar_keluar_5']) ? $post['kassa_besar_keluar_5'] : null,
            'kassa_besar_keluar_6' => isset($post['kassa_besar_keluar_6']) ? $post['kassa_besar_keluar_6'] : null,
            'kassa_besar_keluar_7' => isset($post['kassa_besar_keluar_7']) ? $post['kassa_besar_keluar_7'] : null,
            'kassa_besar_keluar_8' => isset($post['kassa_besar_keluar_8']) ? $post['kassa_besar_keluar_8'] : null,
            'kassa_besar_keluar_9' => isset($post['kassa_besar_keluar_9']) ? $post['kassa_besar_keluar_9'] : null,
            'kassa_besar_keluar_10' => isset($post['kassa_besar_keluar_10']) ? $post['kassa_besar_keluar_10'] : null,
            'deskripsi_kassa_besar' => isset($post['deskripsi_kassa_besar']) ? $post['deskripsi_kassa_besar'] : null,
            'kassa_kecil_masuk_1' => isset($post['kassa_kecil_masuk_1']) ? $post['kassa_kecil_masuk_1'] : null,
            'kassa_kecil_masuk_2' => isset($post['kassa_kecil_masuk_2']) ? $post['kassa_kecil_masuk_2'] : null,
            'kassa_kecil_masuk_3' => isset($post['kassa_kecil_masuk_3']) ? $post['kassa_kecil_masuk_3'] : null,
            'kassa_kecil_masuk_4' => isset($post['kassa_kecil_masuk_4']) ? $post['kassa_kecil_masuk_4'] : null,
            'kassa_kecil_masuk_5' => isset($post['kassa_kecil_masuk_5']) ? $post['kassa_kecil_masuk_5'] : null,
            'kassa_kecil_masuk_6' => isset($post['kassa_kecil_masuk_6']) ? $post['kassa_kecil_masuk_6'] : null,
            'kassa_kecil_masuk_7' => isset($post['kassa_kecil_masuk_7']) ? $post['kassa_kecil_masuk_7'] : null,
            'kassa_kecil_masuk_8' => isset($post['kassa_kecil_masuk_8']) ? $post['kassa_kecil_masuk_8'] : null,
            'kassa_kecil_masuk_9' => isset($post['kassa_kecil_masuk_9']) ? $post['kassa_kecil_masuk_9'] : null,
            'kassa_kecil_masuk_10' => isset($post['kassa_kecil_masuk_10']) ? $post['kassa_kecil_masuk_10'] : null,
            'kassa_kecil_keluar_1' => isset($post['kassa_kecil_keluar_1']) ? $post['kassa_kecil_keluar_1'] : null,
            'kassa_kecil_keluar_2' => isset($post['kassa_kecil_keluar_2']) ? $post['kassa_kecil_keluar_2'] : null,
            'kassa_kecil_keluar_3' => isset($post['kassa_kecil_keluar_3']) ? $post['kassa_kecil_keluar_3'] : null,
            'kassa_kecil_keluar_4' => isset($post['kassa_kecil_keluar_4']) ? $post['kassa_kecil_keluar_4'] : null,
            'kassa_kecil_keluar_5' => isset($post['kassa_kecil_keluar_5']) ? $post['kassa_kecil_keluar_5'] : null,
            'kassa_kecil_keluar_6' => isset($post['kassa_kecil_keluar_6']) ? $post['kassa_kecil_keluar_6'] : null,
            'kassa_kecil_keluar_7' => isset($post['kassa_kecil_keluar_7']) ? $post['kassa_kecil_keluar_7'] : null,
            'kassa_kecil_keluar_8' => isset($post['kassa_kecil_keluar_8']) ? $post['kassa_kecil_keluar_8'] : null,
            'kassa_kecil_keluar_9' => isset($post['kassa_kecil_keluar_9']) ? $post['kassa_kecil_keluar_9'] : null,
            'kassa_kecil_keluar_10' => isset($post['kassa_kecil_keluar_10']) ? $post['kassa_kecil_keluar_10'] : null,
            'deskripsi_kassa_kecil' => isset($post['deskripsi_kassa_kecil']) ? $post['deskripsi_kassa_kecil'] : null,
            'depper_besar_masuk_1' => isset($post['depper_besar_masuk_1']) ? $post['depper_besar_masuk_1'] : null,
            'depper_besar_masuk_2' => isset($post['depper_besar_masuk_2']) ? $post['depper_besar_masuk_2'] : null,
            'depper_besar_masuk_3' => isset($post['depper_besar_masuk_3']) ? $post['depper_besar_masuk_3'] : null,
            'depper_besar_masuk_4' => isset($post['depper_besar_masuk_4']) ? $post['depper_besar_masuk_4'] : null,
            'depper_besar_masuk_5' => isset($post['depper_besar_masuk_5']) ? $post['depper_besar_masuk_5'] : null,
            'depper_besar_masuk_6' => isset($post['depper_besar_masuk_6']) ? $post['depper_besar_masuk_6'] : null,
            'depper_besar_masuk_7' => isset($post['depper_besar_masuk_7']) ? $post['depper_besar_masuk_7'] : null,
            'depper_besar_masuk_8' => isset($post['depper_besar_masuk_8']) ? $post['depper_besar_masuk_8'] : null,
            'depper_besar_masuk_9' => isset($post['depper_besar_masuk_9']) ? $post['depper_besar_masuk_9'] : null,
            'depper_besar_masuk_10' => isset($post['depper_besar_masuk_10']) ? $post['depper_besar_masuk_10'] : null,
            'deskripsi_depper_besar' => isset($post['deskripsi_depper_besar']) ? $post['deskripsi_depper_besar'] : null,
            'depper_besar_keluar_1' => isset($post['depper_besar_keluar_1']) ? $post['depper_besar_keluar_1'] : null,
            'depper_besar_keluar_2' => isset($post['depper_besar_keluar_2']) ? $post['depper_besar_keluar_2'] : null,
            'depper_besar_keluar_3' => isset($post['depper_besar_keluar_3']) ? $post['depper_besar_keluar_3'] : null,
            'depper_besar_keluar_4' => isset($post['depper_besar_keluar_4']) ? $post['depper_besar_keluar_4'] : null,
            'depper_besar_keluar_5' => isset($post['depper_besar_keluar_5']) ? $post['depper_besar_keluar_5'] : null,
            'depper_besar_keluar_6' => isset($post['depper_besar_keluar_6']) ? $post['depper_besar_keluar_6'] : null,
            'depper_besar_keluar_7' => isset($post['depper_besar_keluar_7']) ? $post['depper_besar_keluar_7'] : null,
            'depper_besar_keluar_8' => isset($post['depper_besar_keluar_8']) ? $post['depper_besar_keluar_8'] : null,
            'depper_besar_keluar_9' => isset($post['depper_besar_keluar_9']) ? $post['depper_besar_keluar_9'] : null,
            'depper_besar_keluar_10' => isset($post['depper_besar_keluar_10']) ? $post['depper_besar_keluar_10'] : null,
            'deskripsi_depper_besar' => isset($post['deskripsi_depper_besar']) ? $post['deskripsi_depper_besar'] : null,
            'depper_kecil_masuk_1' => isset($post['depper_kecil_masuk_1']) ? $post['depper_kecil_masuk_1'] : null,
            'depper_kecil_masuk_2' => isset($post['depper_kecil_masuk_2']) ? $post['depper_kecil_masuk_2'] : null,
            'depper_kecil_masuk_3' => isset($post['depper_kecil_masuk_3']) ? $post['depper_kecil_masuk_3'] : null,
            'depper_kecil_masuk_4' => isset($post['depper_kecil_masuk_4']) ? $post['depper_kecil_masuk_4'] : null,
            'depper_kecil_masuk_5' => isset($post['depper_kecil_masuk_5']) ? $post['depper_kecil_masuk_5'] : null,
            'depper_kecil_masuk_6' => isset($post['depper_kecil_masuk_6']) ? $post['depper_kecil_masuk_6'] : null,
            'depper_kecil_masuk_7' => isset($post['depper_kecil_masuk_7']) ? $post['depper_kecil_masuk_7'] : null,
            'depper_kecil_masuk_8' => isset($post['depper_kecil_masuk_8']) ? $post['depper_kecil_masuk_8'] : null,
            'depper_kecil_masuk_9' => isset($post['depper_kecil_masuk_9']) ? $post['depper_kecil_masuk_9'] : null,
            'depper_kecil_masuk_10' => isset($post['depper_kecil_masuk_10']) ? $post['depper_kecil_masuk_10'] : null,
            'deskripsi_depper_kecil' => isset($post['deskripsi_depper_kecil']) ? $post['deskripsi_depper_kecil'] : null,
            'depper_kecil_keluar_1' => isset($post['depper_kecil_keluar_1']) ? $post['depper_kecil_keluar_1'] : null,
            'depper_kecil_keluar_2' => isset($post['depper_kecil_keluar_2']) ? $post['depper_kecil_keluar_2'] : null,
            'depper_kecil_keluar_3' => isset($post['depper_kecil_keluar_3']) ? $post['depper_kecil_keluar_3'] : null,
            'depper_kecil_keluar_4' => isset($post['depper_kecil_keluar_4']) ? $post['depper_kecil_keluar_4'] : null,
            'depper_kecil_keluar_5' => isset($post['depper_kecil_keluar_5']) ? $post['depper_kecil_keluar_5'] : null,
            'depper_kecil_keluar_6' => isset($post['depper_kecil_keluar_6']) ? $post['depper_kecil_keluar_6'] : null,
            'depper_kecil_keluar_7' => isset($post['depper_kecil_keluar_7']) ? $post['depper_kecil_keluar_7'] : null,
            'depper_kecil_keluar_8' => isset($post['depper_kecil_keluar_8']) ? $post['depper_kecil_keluar_8'] : null,
            'depper_kecil_keluar_9' => isset($post['depper_kecil_keluar_9']) ? $post['depper_kecil_keluar_9'] : null,
            'depper_kecil_keluar_10' => isset($post['depper_kecil_keluar_10']) ? $post['depper_kecil_keluar_10'] : null,
            'deskripsi_depper_kecil' => isset($post['deskripsi_depper_kecil']) ? $post['deskripsi_depper_kecil'] : null,
            'nm_kassa_5' => isset($post['nm_kassa_5']) ? $post['nm_kassa_5'] : null,
            'kassa_5_masuk_1' => isset($post['kassa_5_masuk_1']) ? $post['kassa_5_masuk_1'] : null,
            'kassa_5_masuk_2' => isset($post['kassa_5_masuk_2']) ? $post['kassa_5_masuk_2'] : null,
            'kassa_5_masuk_3' => isset($post['kassa_5_masuk_3']) ? $post['kassa_5_masuk_3'] : null,
            'kassa_5_masuk_4' => isset($post['kassa_5_masuk_4']) ? $post['kassa_5_masuk_4'] : null,
            'kassa_5_masuk_5' => isset($post['kassa_5_masuk_5']) ? $post['kassa_5_masuk_5'] : null,
            'kassa_5_masuk_6' => isset($post['kassa_5_masuk_6']) ? $post['kassa_5_masuk_6'] : null,
            'kassa_5_masuk_7' => isset($post['kassa_5_masuk_7']) ? $post['kassa_5_masuk_7'] : null,
            'kassa_5_masuk_8' => isset($post['kassa_5_masuk_8']) ? $post['kassa_5_masuk_8'] : null,
            'kassa_5_masuk_9' => isset($post['kassa_5_masuk_9']) ? $post['kassa_5_masuk_9'] : null,
            'kassa_5_masuk_10' => isset($post['kassa_5_masuk_10']) ? $post['kassa_5_masuk_10'] : null,
            'kassa_5_keluar_1' => isset($post['kassa_5_keluar_1']) ? $post['kassa_5_keluar_1'] : null,
            'kassa_5_keluar_2' => isset($post['kassa_5_keluar_2']) ? $post['kassa_5_keluar_2'] : null,
            'kassa_5_keluar_3' => isset($post['kassa_5_keluar_3']) ? $post['kassa_5_keluar_3'] : null,
            'kassa_5_keluar_4' => isset($post['kassa_5_keluar_4']) ? $post['kassa_5_keluar_4'] : null,
            'kassa_5_keluar_5' => isset($post['kassa_5_keluar_5']) ? $post['kassa_5_keluar_5'] : null,
            'kassa_5_keluar_6' => isset($post['kassa_5_keluar_6']) ? $post['kassa_5_keluar_6'] : null,
            'kassa_5_keluar_7' => isset($post['kassa_5_keluar_7']) ? $post['kassa_5_keluar_7'] : null,
            'kassa_5_keluar_8' => isset($post['kassa_5_keluar_8']) ? $post['kassa_5_keluar_8'] : null,
            'kassa_5_keluar_9' => isset($post['kassa_5_keluar_9']) ? $post['kassa_5_keluar_9'] : null,
            'kassa_5_keluar_10' => isset($post['kassa_5_keluar_10']) ? $post['kassa_5_keluar_10'] : null,
            'deskripsi_kassa_5' => isset($post['deskripsi_kassa_5']) ? $post['deskripsi_kassa_5'] : null,
            'nm_kassa_6' => isset($post['nm_kassa_6']) ? $post['nm_kassa_6'] : null,
            'kassa_6_masuk_1' => isset($post['kassa_6_masuk_1']) ? $post['kassa_6_masuk_1'] : null,
            'kassa_6_masuk_2' => isset($post['kassa_6_masuk_2']) ? $post['kassa_6_masuk_2'] : null,
            'kassa_6_masuk_3' => isset($post['kassa_6_masuk_3']) ? $post['kassa_6_masuk_3'] : null,
            'kassa_6_masuk_4' => isset($post['kassa_6_masuk_4']) ? $post['kassa_6_masuk_4'] : null,
            'kassa_6_masuk_5' => isset($post['kassa_6_masuk_5']) ? $post['kassa_6_masuk_5'] : null,
            'kassa_6_masuk_6' => isset($post['kassa_6_masuk_6']) ? $post['kassa_6_masuk_6'] : null,
            'kassa_6_masuk_7' => isset($post['kassa_6_masuk_7']) ? $post['kassa_6_masuk_7'] : null,
            'kassa_6_masuk_8' => isset($post['kassa_6_masuk_8']) ? $post['kassa_6_masuk_8'] : null,
            'kassa_6_masuk_9' => isset($post['kassa_6_masuk_9']) ? $post['kassa_6_masuk_9'] : null,
            'kassa_6_masuk_10' => isset($post['kassa_6_masuk_10']) ? $post['kassa_6_masuk_10'] : null,
            'kassa_6_keluar_1' => isset($post['kassa_6_keluar_1']) ? $post['kassa_6_keluar_1'] : null,
            'kassa_6_keluar_2' => isset($post['kassa_6_keluar_2']) ? $post['kassa_6_keluar_2'] : null,
            'kassa_6_keluar_3' => isset($post['kassa_6_keluar_3']) ? $post['kassa_6_keluar_3'] : null,
            'kassa_6_keluar_4' => isset($post['kassa_6_keluar_4']) ? $post['kassa_6_keluar_4'] : null,
            'kassa_6_keluar_5' => isset($post['kassa_6_keluar_5']) ? $post['kassa_6_keluar_5'] : null,
            'kassa_6_keluar_6' => isset($post['kassa_6_keluar_6']) ? $post['kassa_6_keluar_6'] : null,
            'kassa_6_keluar_7' => isset($post['kassa_6_keluar_7']) ? $post['kassa_6_keluar_7'] : null,
            'kassa_6_keluar_8' => isset($post['kassa_6_keluar_8']) ? $post['kassa_6_keluar_8'] : null,
            'kassa_6_keluar_9' => isset($post['kassa_6_keluar_9']) ? $post['kassa_6_keluar_9'] : null,
            'kassa_6_keluar_10' => isset($post['kassa_6_keluar_10']) ? $post['kassa_6_keluar_10'] : null,
            'deskripsi_kassa_6' => isset($post['deskripsi_kassa_6']) ? $post['deskripsi_kassa_6'] : null,
            'oleh' => $oleh,
            'created_at' => $createdAt,
            'status' => 1,
          );
          // echo '<pre>';print_r($dataKassa);exit();
          if (isset($idCPAOKassa)) {
            $this->CPAOModel->ubahKassa($dataKassa, $idCPAOKassa);
          } else {
            $this->CPAOModel->simpanKassa($dataKassa);
          }
          // Akhir data kassa

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
          }
        } else {
          $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
        }
        echo json_encode($result);
      }
    }
  }

  public function tabel()
  {
    $draw = intval($this->input->post('draw'));
    $nokun = $this->input->post('nokun');
    $history = $this->CPAOModel->history($nokun, 'tabel', null);
    $data = array();
    $no = 1;
    $disabled = null;
    $status = null;
    // echo '<pre>';print_r($nokun);exit();

    foreach ($history->result() as $h) {
      if ($h->status == 0) {
        $disabled = 'disabled';
        $status = '<p class="text-danger">Dibatalkan</p>';
      } elseif ($h->status == 1) {
        $disabled = null;
        $status = '<p class="text-success">Diterima</p>';
      }

      $data[] = array(
        $no,
        date('d-m-Y', strtotime($h->tanggal)),
        date('H.i', strtotime($h->jam)),
        $h->instrumen,
        $h->pengisi,
        date('d-m-Y, H:i:s', strtotime($h->created_at)),
        $status,
        "<div class='btn-group' role='group'>
          <button type='button' href='#modal-batal-cpao' class='btn btn-sm btn-danger waves-effect tbl-batal-cpao' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-window-close'></i> Batal
          </button>
          <button type='button' href='#modal-detail-cpao' class='btn btn-sm btn-primary waves-effect tbl-detail-cpao' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-eye'></i> Lihat
          </button>
        </div>",
      );
      $no++;
    }

    $output = array(
      'draw' => $draw,
      'recordsTotal' => $history->num_rows(),
      'recordsFiltered' => $history->num_rows(),
      'data' => $data
    );
    echo json_encode($output);
  }

  public function history()
  {
    $post = $this->input->post();
    $data = array('nokun' => $post['nokun']);
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/operasi/CPAO/history', $data);
  }

  public function batal()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id = isset($post['id']) ? $post['id'] : null;

    $data = array('status' => 0);
    $this->CPAOModel->ubah($data, $id);
    $this->CPAOModel->ubahAlat($data, $id);
    $this->CPAOModel->ubahTambahan($data, $id);
    $this->CPAOModel->ubahKassa($data, $id);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }
}

// End of file CPAO.php
// Location: ./application/controlerrs/rekam_medis/rawat_inap/operasi/CPAO.php
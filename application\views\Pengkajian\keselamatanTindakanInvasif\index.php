<ul class="nav nav-tabs nav-justified" style="color:white;">
  <li class="nav-item">
    <a href="#keselamatanTindakanInvasif" data-toggle="tab" aria-expanded="false" class="nav-link active" data-target="#tabformPengkajianTindakanInvasif">
      Form Pengkajian Keselamatan Tindakan Invasif
    </a>
  </li>
  <li class="nav-item">
    <a href="#keselamatanTindakanInvasif" data-toggle="tab" aria-expanded="true" class="nav-link" data-target="#tabhistoryKeselamatanTindakanInvasif">
      History Pengkajian Keselamatan Tindakan Invasif
    </a>
  </li>
</ul>

<div class="tab-content">
  <div role="tabpanel" class="tab-pane fade show active" id="tabformPengkajianTindakanInvasif">
    <div class="row">
      <div class="col-lg-12">
        <form id="formKeselamatanTindakanInvasif">
          <input type="hidden" name="nokun" value="<?= $getNomr['NOKUN']?>">
          <input type="hidden" name="id_kti" value="<?= isset($getKTI['id_kti']) ? $getKTI['id_kti'] : "" ?>">
          <input type="hidden" name="pengisi" value="<?= $this->session->userdata('id'); ?>">
          <div class="col-xl-12 mt-md-3">
            <!-- START DATA UMUM PASIEN -->
            <div id="accordion">
              <div class="card new-card">

                <div class="card-header new-card-header" id="headingOne">
                  <H6 class="m-0">
                    <span>
                      <b>A. DATA UMUM PASIEN</b>
                    </span>
                  </H6>
                </div>

                <div id="dataUmumPasien">
                  <div class="card-body">
                    <!-- START 1. TANDA VITAL -->
                    <div class="row jarak">
                      <div class="col-md-1">
                        <span>1</span>
                      </div>
                      <div class="col-md-5">
                        Tanda vital
                      </div>
                    </div>

                    <!-- START PUKUL -->
                    <div class="row jarak">
                      <div class="offset-md-3 col-md-3">
                        Pukul
                      </div>
                      <div class="col-md-6">
                        <input type="text" class="form-control pukulKesInv" id="pukulKesInv" name="pukulKesInv" required value="<?= isset($getKTI['pukul']) ? $getKTI['pukul'] : "" ?>">
                      </div>
                    </div>

                    <!-- START TEKANAN DARAH -->
                    <div class="row jarak">
                      <div class="offset-md-3 col-md-3">
                        Tekanan darah
                      </div>
                      <div class="col-md-3">
                        <input type="text" name="tekananDarSistolikKesInv" id="tekananDarSistolikKesInv" class="form-control jarak3 tekananDarSistolikKesInv" placeholder="[ Sistolik ]" value="<?= isset($getKTI['tekanan_darah_1']) ? $getKTI['tekanan_darah_1'] : "" ?>" onkeypress="if ( isNaN(this.value + String.fromCharCode(event.keyCode) )) return false;" required>
                      </div>

                      <div class="col-md-3">
                        <input type="text" name="tekananDarDiastolikKesInv" id="tekananDarDiastolikKesInv" class="form-control jarak3 tekananDarDiastolikKesInv" placeholder="[ Diastolik ]" value="<?= isset($getKTI['tekanan_darah_2']) ? $getKTI['tekanan_darah_2'] : "" ?>" onkeypress="if ( isNaN(this.value + String.fromCharCode(event.keyCode) )) return false;" required>
                      </div>
                    </div>

                    <!-- START NADI -->
                    <div class="row jarak">
                      <div class="offset-md-3 col-md-3">
                        Nadi
                      </div>
                      <div class="col-md-6">
                        <input type="text" name="NadiKesInvasif" id="NadiKesInvasif" class="form-control jarak3 NadiKesInvasif" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['nadi']) ? $getKTI['nadi'] : "" ?>" onkeypress="if ( isNaN(this.value + String.fromCharCode(event.keyCode) )) return false;" required>
                      </div>
                    </div>

                    <!-- START PERNAFASAN -->
                    <div class="row jarak">
                      <div class="offset-md-3 col-md-3">
                        Pernafasan
                      </div>
                      <div class="col-md-6">
                        <input type="text" name="pernafasanKesInvasif" id="pernafasanKesInvasif" class="form-control jarak3 pernafasanKesInvasif" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['napas']) ? $getKTI['napas'] : "" ?>" onkeypress="if ( isNaN(this.value + String.fromCharCode(event.keyCode) )) return false;" required>
                      </div>
                    </div>

                    <!-- START SUHU -->
                    <div class="row jarak">
                      <div class="offset-md-3 col-md-3">
                        Suhu
                      </div>
                      <div class="col-md-6">
                        <input type="text" name="suhuKesInvasif" id="suhuKesInvasif" class="form-control jarak3 suhuKesInvasif" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['suhu']) ? $getKTI['suhu'] : "" ?>" onkeypress="if ( isNaN(this.value + String.fromCharCode(event.keyCode) )) return false;" required>
                      </div>
                    </div>

                    <!-- END 1. TANDA VITAL -->

                    <!-- START 2. DIAGNOSIS -->
                    <div class="row jarak">
                      <div class="col-md-1">
                        <span>2</span>
                      </div>
                      <div class="col-md-5">
                        Diagnosis
                      </div>
                      <div class="col-md-6">
                        <input type="text" name="diagnosisKeselamatanInvasif" id="diagnosisKeselamatanInvasif" class="form-control jarak3 diagnosisKeselamatanInvasif" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['diagnosis']) ? $getKTI['diagnosis'] : "" ?>" required>
                      </div>
                    </div>
                    <!-- END 2. DIAGNOSIS -->
                  </div>
                </div>

              </div>
            </div>

            <!-- START VERIFIKASI PRA TINDAKAN -->
            <div id="accordion">
              <div class="card new-card">
               <div class="card-header new-card-header" id="headingTwo">
                <H6 class="m-0">
                  <span>
                    <b>B. PERENCANAAN TINDAKAN</b>
                  </span>
                </H6>
              </div>
              <div id="perencanaanTindakaniNV">
                <div class="card-body">

                  <!-- START 1. IDENTITAS PASIEN -->
                  <div class="row jarak">
                    <div class="col-md-1">
                      <span>1</span>
                    </div>
                    <div class="col-md-7">
                      Identitas Pasien
                    </div>
                    <div class="col-md-4">
                      <div class="checkbox checkbox-primary form-check-inline jarak2">
                        <input type="checkbox" name="identitasPasien" id="identitasPasien" value="" class="identitasPasien" <?php
                        if(isset($getKTI['identitas_pasien'])) {
                          if($getKTI['identitas_pasien'] == 1) {
                            echo "checked";
                          } else{
                            echo "";
                          }
                        }?>>
                        <label for="identitasPasien">Sesuai</label>
                      </div>
                    </div>
                  </div>
                  <!-- END 1. IDENTITAS PASIEN -->

                  <!-- START 2. RENCANA TINDAKAN -->
                  <div class="row jarak">
                    <div class="col-md-1">
                      <span>2</span>
                    </div>
                    <div class="col-md-3">
                      Rencana tindakan
                    </div>
                    <div class="col-md-4">
                      <input type="text" name="deskRencanaTindakanKti" id="deskRencanaTindakanKti" class="form-control jarak3 deskRencanaTindakanKti" placeholder="[ Rencana Tindakan ]" value="<?= isset($getKTI['desk_rencana_tindakan']) ? $getKTI['desk_rencana_tindakan'] : "" ?>" required>
                    </div>
                    <div class="col-md-4">
                      <div class="checkbox checkbox-primary form-check-inline jarak2">
                        <input type="checkbox" name="rencanaTindakan" id="rencanaTindakan" value="" class="rencanaTindakan" <?php
                        if(isset($getKTI['rencana_tindakan'])) {
                          if($getKTI['rencana_tindakan'] == 1) {
                            echo "checked";
                          } else{
                            echo "";
                          }
                        }?>>
                        <label for="rencanaTindakan">Sesuai</label>
                      </div>
                    </div>
                  </div>
                  <!-- END 2. RENCANA TINDAKAN -->

                  <!-- START 3. PENANDAAN SISI TINDAKAN -->
                  <div class="row jarak">
                    <div class="col-md-1">
                      <span>3</span>
                    </div>
                    <div class="col-md-3">
                      Penandaan sisi tindakan
                    </div>
                    <div class="col-md-4">
                      <input type="text" name="deskPenandaanSisiTindakanKti" id="deskPenandaanSisiTindakanKti" class="form-control jarak3 deskPenandaanSisiTindakanKti" placeholder="[ Penandaan Sisi Tindakan ]" value="<?= isset($getKTI['desk_penandaan_sisi_tindakan']) ? $getKTI['desk_penandaan_sisi_tindakan'] : "" ?>" required>
                    </div>
                    <div class="col-md-4">
                      <?php foreach ($penandaanSisiLokasi as $penandaSisLok): ?>
                        <div class="radio radio-primary form-check-inline jarak2">
                          <input type="radio" name="penandaSisLok" id="penandaSisLok<?=$penandaSisLok['id_variabel']?>" value="<?=$penandaSisLok['id_variabel']?>" class="penandaSisLok" required <?php
                          if(isset($getKTI['penandaan_sisi_tindakan'])) {
                            if($getKTI['penandaan_sisi_tindakan'] == $penandaSisLok['id_variabel']) {
                              echo "checked";
                            } else{
                              echo "";
                            }
                          }?>>
                          <label for="penandaSisLok<?=$penandaSisLok['id_variabel']?>"><?= $penandaSisLok['variabel'] ?></label>
                        </div>
                      <?php endforeach; ?>
                    </div>
                  </div>
                  <!-- END 3. PENANDAAN SISI TINDAKAN -->

                  <!-- START 4. INFORMED CONSENT TINDAKAN -->
                  <div class="row jarak">
                    <div class="col-md-1">
                      <span>4</span>
                    </div>
                    <div class="col-md-5">
                      Informed consent tindakan
                    </div>
                    <div class="col-md-6">
                      <?php foreach ($inforConTin as $inforCoTn): ?>
                        <div class="radio radio-primary form-check-inline jarak2">
                          <input type="radio" name="informedConTin" id="informedConTin<?=$inforCoTn['id_variabel']?>" value="<?=$inforCoTn['id_variabel']?>" class="informedConTin" required <?php
                          if(isset($getKTI['informed_consent_tindakan'])) {
                            if($getKTI['informed_consent_tindakan'] == $inforCoTn['id_variabel']) {
                              echo "checked";
                            } else{
                              echo "";
                            }
                          }?>>
                          <label for="informedConTin<?=$inforCoTn['id_variabel']?>"><?= $inforCoTn['variabel'] ?></label>
                        </div>
                      <?php endforeach; ?>
                    </div>
                  </div>
                  <!-- END 4. INFORMED CONSENT TINDAKAN -->

                  <!-- START 5. INFORMED CONSENT TINDAKAN ANESTESI/SEDASI -->
                  <div class="row jarak">
                    <div class="col-md-1">
                      <span>5</span>
                    </div>
                    <div class="col-md-5">
                      Informed consent tindakan anestesi / sedasi
                    </div>
                    <div class="col-md-6">
                      <?php foreach ($inforConTinAnesSed as $inConTinAS): ?>
                        <div class="radio radio-primary form-check-inline jarak2">
                          <input type="radio" name="informedConTinAS" id="informedConTinAS<?=$inConTinAS['id_variabel']?>" value="<?=$inConTinAS['id_variabel']?>" class="informedConTinAS" required <?php
                          if(isset($getKTI['informed_consent_anatesi_sedasi'])) {
                            if($getKTI['informed_consent_anatesi_sedasi'] == $inConTinAS['id_variabel']) {
                              echo "checked";
                            } else{
                              echo "";
                            }
                          }?>>
                          <label for="informedConTinAS<?=$inConTinAS['id_variabel']?>"><?= $inConTinAS['variabel'] ?></label>
                        </div>
                      <?php endforeach; ?>
                    </div>
                  </div>
                  <!-- END 5. INFORMED CONSENT TINDAKAN ANESTESI/SEDASI -->

                  <!-- START 6. PEMERIKSAAN PENUNJANG -->
                  <div class="row jarak">
                    <div class="col-md-1">
                      <span>6</span>
                    </div>
                    <div class="col-md-5">
                      Pemeriksaan penunjang :
                    </div>
                  </div>

                  <div class="row jarak">
                    <div class="offset-md-3 col-md-3">
                     - Panoramic
                   </div>
                   <div class="col-md-6">
                    <?php foreach ($pemPenunjangPanoramic as $pemPenPano): ?>
                      <div class="radio radio-primary form-check-inline jarak2">
                        <input type="radio" name="pemPenPano" id="pemPenPano<?=$pemPenPano['id_variabel']?>" value="<?=$pemPenPano['id_variabel']?>" class="pemPenPano" required <?php
                        if(isset($getKTI['panoramic'])) {
                          if($getKTI['panoramic'] == $pemPenPano['id_variabel']) {
                            echo "checked";
                          } else{
                            echo "";
                          }
                        }?>>
                        <label for="pemPenPano<?=$pemPenPano['id_variabel']?>"><?= $pemPenPano['variabel'] ?></label>
                      </div>
                    <?php endforeach; ?>
                  </div>
                </div>
                <div id="showPanoramic" style="display: none;">
                  <div class="row jarak">
                    <div class="offset-md-6 col-md-6">
                      <input type="text" name="panoramic_desk" id="panoramic_desk" class="form-control jarak3 panoramic_desk" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['panoramic_desk']) ? $getKTI['panoramic_desk'] : "" ?>">
                    </div>
                  </div>
                </div>

                  <div class="row jarak">
                    <div class="offset-md-3 col-md-3">
                     - Foto Thoraks
                   </div>
                   <div class="col-md-6">
                    <?php foreach ($pemPenunjangFoto as $pemPenFot): ?>
                      <div class="radio radio-primary form-check-inline jarak2">
                        <input type="radio" name="pemPenFoto" id="pemPenFoto<?=$pemPenFot['id_variabel']?>" value="<?=$pemPenFot['id_variabel']?>" class="pemPenFoto" required <?php
                        if(isset($getKTI['foto_thoraks'])) {
                          if($getKTI['foto_thoraks'] == $pemPenFot['id_variabel']) {
                            echo "checked";
                          } else{
                            echo "";
                          }
                        }?>>
                        <label for="pemPenFoto<?=$pemPenFot['id_variabel']?>"><?= $pemPenFot['variabel'] ?></label>
                      </div>
                    <?php endforeach; ?>
                  </div>
                </div>

                <div class="row jarak">
                  <div class="offset-md-3 col-md-3">
                   - CT Scan
                 </div>
                 <div class="col-md-6">
                  <?php foreach ($pemPenunjangCtScan as $pemPenCS): ?>
                    <div class="radio radio-primary form-check-inline jarak2">
                      <input type="radio" name="pemPenCtScan" id="pemPenCtScan<?=$pemPenCS['id_variabel']?>" value="<?=$pemPenCS['id_variabel']?>" class="pemPenCtScan" required <?php
                      if(isset($getKTI['ctscan'])) {
                        if($getKTI['ctscan'] == $pemPenCS['id_variabel']) {
                          echo "checked";
                        } else{
                          echo "";
                        }
                      }?>>
                      <label for="pemPenCtScan<?=$pemPenCS['id_variabel']?>"><?= $pemPenCS['variabel'] ?></label>
                    </div>
                  <?php endforeach; ?>
                </div>
              </div>

              <div class="row jarak">
                <div class="offset-md-3 col-md-3">
                 - USG
               </div>
               <div class="col-md-6">
                <?php foreach ($pemPenunjangUSG as $pemPenUSG): ?>
                  <div class="radio radio-primary form-check-inline jarak2">
                    <input type="radio" name="pemPenunjangUSG" id="pemPenunjangUSG<?=$pemPenUSG['id_variabel']?>" value="<?=$pemPenUSG['id_variabel']?>" class="pemPenunjangUSG" required <?php
                    if(isset($getKTI['usg'])) {
                      if($getKTI['usg'] == $pemPenUSG['id_variabel']) {
                        echo "checked";
                      } else{
                        echo "";
                      }
                    }?>>
                    <label for="pemPenunjangUSG<?=$pemPenUSG['id_variabel']?>"><?= $pemPenUSG['variabel'] ?></label>
                  </div>
                <?php endforeach; ?>
              </div>
            </div>

            <div class="row jarak">
              <div class="offset-md-3 col-md-3">
               - MRI
             </div>
             <div class="col-md-6">
              <?php foreach ($pemPenunjangMRI as $pemPenMRI): ?>
                <div class="radio radio-primary form-check-inline jarak2">
                  <input type="radio" name="pemPenunjangMRI" id="pemPenunjangMRI<?=$pemPenMRI['id_variabel']?>" value="<?=$pemPenMRI['id_variabel']?>" class="pemPenunjangMRI" required <?php
                  if(isset($getKTI['mri'])) {
                    if($getKTI['mri'] == $pemPenMRI['id_variabel']) {
                      echo "checked";
                    } else{
                      echo "";
                    }
                  }?>>
                  <label for="pemPenunjangMRI<?=$pemPenMRI['id_variabel']?>"><?= $pemPenMRI['variabel'] ?></label>
                </div>
              <?php endforeach; ?>
            </div>
          </div>

          <div class="row jarak">
            <div class="offset-md-3 col-md-3">
             - Laboratorium
           </div>
           <div class="col-md-6">
            <?php foreach ($pemPenunjangLab as $pemPenLab): ?>
              <div class="radio radio-primary form-check-inline jarak2">
                <input type="radio" name="pemPenunjangLaboratorium" id="pemPenunjangLaboratorium<?=$pemPenLab['id_variabel']?>" value="<?=$pemPenLab['id_variabel']?>" class="pemPenunjangLaboratorium" required <?php
                if(isset($getKTI['lab'])) {
                  if($getKTI['lab'] == $pemPenLab['id_variabel']) {
                    echo "checked";
                  } else{
                    echo "";
                  }
                }?>>
                <label for="pemPenunjangLaboratorium<?=$pemPenLab['id_variabel']?>"><?= $pemPenLab['variabel'] ?></label>
              </div>
            <?php endforeach; ?>
          </div>
        </div>

        <div class="row jarak">
          <div class="offset-md-3 col-md-3">
            - Lainnya
          </div>
          <div class="col-md-6">
            <input type="text" name="pemeriksaanPenunjangLainnya" id="pemeriksaanPenunjangLainnya" class="form-control jarak3 pemeriksaanPenunjangLainnya" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['pemeriksaaan_penunjang_lainnya']) ? $getKTI['pemeriksaaan_penunjang_lainnya'] : "" ?>">
          </div>
        </div>
        <!-- END 6. PEMERIKSAAN PENUNJANG -->

        <!-- START 7. PERSEDIAAN DARAH -->
        <div class="row jarak">
          <div class="col-md-1">
            <span>7</span>
          </div>
          <div class="col-md-5">
            Persediaan darah
          </div>
          <div class="col-md-6">
            <?php foreach ($persediaanDarah as $persDar): ?>
              <div class="radio radio-primary form-check-inline jarak2">
                <input type="radio" name="persediDarh" id="persediDarh<?=$persDar['id_variabel']?>" value="<?=$persDar['id_variabel']?>" class="persediDarh" required <?php
                if(isset($getKTI['persediaan_darah'])) {
                  if($getKTI['persediaan_darah'] == $persDar['id_variabel']) {
                    echo "checked";
                  } else{
                    echo "";
                  }
                }?>>
                <label for="persediDarh<?=$persDar['id_variabel']?>"><?= $persDar['variabel'] ?></label>
              </div>
            <?php endforeach; ?>
          </div>
        </div>

        <div id="showPersediaanDarah" style="display: none;">
          <div class="row jarak">
            <div class="offset-md-3 col-md-3">
              Golongan darah
            </div>
            <div class="col-md-3">
              <input type="text" name="deskGolonganDarah" id="deskGolonganDarah" class="form-control jarak3 deskGolonganDarah" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['golongan_darah']) ? $getKTI['golongan_darah'] : "" ?>">
            </div>
          </div>
          <div class="row jarak">
            <div class="offset-md-3 col-md-3">
              PRC (cc)
            </div>
            <div class="col-md-3">
              <input type="text" name="deskPRC" id="deskPRC" class="form-control jarak3 deskPRC" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['prc']) ? $getKTI['prc'] : "" ?>">
            </div>
          </div>
          <div class="row jarak">
            <div class="offset-md-3 col-md-3">
              FFP (cc/unit)
            </div>
            <div class="col-md-3">
              <input type="text" name="deskFFP" id="deskFFP" class="form-control jarak3 deskFFP" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['ffp']) ? $getKTI['ffp'] : "" ?>">
            </div>
          </div>
          <div class="row jarak">
            <div class="offset-md-3 col-md-3">
              Lainnya
            </div>
            <div class="col-md-3">
              <input type="text" name="deskLainnyaPersediaanDarah" id="deskLainnyaPersediaanDarah" class="form-control jarak3 deskLainnyaPersediaanDarah" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['lainnya_persediaan_darah']) ? $getKTI['lainnya_persediaan_darah'] : "" ?>">
            </div>
          </div>
        </div>

        <!-- END 7. PERSEDIAAN DARAH -->

        <!-- START 8. KETERSEDIAAN IMPLANT / APLIKATOR / OBAT -->
        <div class="row jarak">
          <div class="col-md-1">
            <span>8</span>
          </div>
          <div class="col-md-5">
            Ketersediaan Implant / aplikator / obat
          </div>
          <div class="col-md-6">
            <?php foreach ($ketersediaanImplant as $keterImp): ?>
              <div class="radio radio-primary form-check-inline jarak2">
                <input type="radio" name="ketersediaanImp" id="ketersediaanImp<?=$keterImp['id_variabel']?>" value="<?=$keterImp['id_variabel']?>" class="ketersediaanImp" required <?php
                if(isset($getKTI['ketersediaan_implant'])) {
                  if($getKTI['ketersediaan_implant'] == $keterImp['id_variabel']) {
                    echo "checked";
                  } else{
                    echo "";
                  }
                }?>>
                <label for="ketersediaanImp<?=$keterImp['id_variabel']?>"><?= $keterImp['variabel'] ?></label>
              </div>
            <?php endforeach; ?>
          </div>
        </div>

        <div id="showKetersediaanImplant" style="display: none;">
          <div class="row jarak">
            <div class="offset-md-6 col-md-6">
              <input type="text" name="deskKetersediaanImplant" id="deskKetersediaanImplant" class="form-control jarak3 deskKetersediaanImplant" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['isi_ketersediaan_implant']) ? $getKTI['isi_ketersediaan_implant'] : "" ?>">
            </div>
          </div>
        </div>
        <!-- END 8. KETERSEDIAAN IMPLANT / APLIKATOR / OBAT -->

      </div>
    </div>

  </div>
</div>

<!-- START CEKLIS KESELAMATAN TINDAKAN INVASIF -->
<div id="accordion">
  <div class="card new-card showTimeSignOutKesInv">
   <div class="card-header new-card-header" id="headingThree">
    <H6 class="m-0">
      <span>
        <b>C. CEKLIS KESELAMATAN TINDAKAN INVASIF</b>
      </span>
    </H6>
  </div>
  <div id="ceklisKeselamatanTindakanInvasif">
    <div class="card-body">

      <!-- START TIME OUT -->
      <div class="row">
        <div class="offset-md-3 col-md-6">
          <div class="text-center header-sub-pengkajian">
            TIME OUT
          </div>
        </div>
      </div>

      <div class="row jarak">
        <div class="offset-md-2 col-md-3">
          Pukul
        </div>
        <div class="col-md-4">
          <input type="text" class="form-control pukulTimeOutInv" id="pukulTimeOutInv" name="pukulTimeOutInv" value="<?= isset($getKTI['pukul_timeout_inv']) ? $getKTI['pukul_timeout_inv'] : "" ?>">
        </div>
      </div>

      <div class="row jarak">
        <div class="col-md-1">
          <span>1</span>
        </div>
        <div class="col-md-5">
          Seluruh anggota tim menyebutkan nama dan perannya
        </div>
        <div class="col-md-6">
          <div class="checkbox checkbox-primary form-check-inline jarak2">
            <input type="checkbox" name="sebutNamaPeranTimeOut" id="sebutNamaPeranTimeOut" value="" class="sebutNamaPeranTimeOut" <?php
            if(isset($getKTI['menyebutkan_nama'])) {
              if($getKTI['menyebutkan_nama'] == 1) {
                echo "checked";
              } else{
                echo "";
              }
            }?>>
            <label for="sebutNamaPeranTimeOut">Ya</label>
          </div>
        </div>
      </div>

      <div class="row jarak">
        <div class="col-md-1">
          <span>2</span>
        </div>
        <div class="col-md-5">
          Perawat konfirmasi secara verbal :
        </div>
      </div>

      <div class="row jarak">
        <div class="offset-md-2 col-md-4">
          - Benar identitas pasien
        </div>
        <div class="col-md-6">
          <div class="checkbox checkbox-primary form-check-inline jarak2">
            <input type="checkbox" name="benarIdentitasTimeOut" id="benarIdentitasTimeOut" value="" class="benarIdentitasTimeOut" <?php
            if(isset($getKTI['benar_identitas_pasien'])) {
              if($getKTI['benar_identitas_pasien'] == 1) {
                echo "checked";
              } else{
                echo "";
              }
            }?>>
            <label for="benarIdentitasTimeOut">Ya</label>
          </div>
        </div>
      </div>

      <div class="row jarak">
        <div class="offset-md-2 col-md-4">
          - Benar tindakan
        </div>
        <div class="col-md-6">
          <div class="checkbox checkbox-primary form-check-inline jarak2">
            <input type="checkbox" name="benarTindakanTimeOut" id="benarTindakanTimeOut" value="" class="benarTindakanTimeOut" <?php
            if(isset($getKTI['benar_tindakan'])) {
              if($getKTI['benar_tindakan'] == 1) {
                echo "checked";
              } else{
                echo "";
              }
            }?>>
            <label for="benarTindakanTimeOut">Ya</label>
          </div>
        </div>
      </div>

      <div class="row jarak">
        <div class="offset-md-2 col-md-4">
          - Benar sisi lokasi tindakan
        </div>
        <div class="col-md-6">
          <?php foreach ($benarSisiLokasiTin as $benSisLokTin): ?>
            <div class="radio radio-primary form-check-inline jarak2">
              <input type="radio" name="benarSisiLokasiTin" id="benarSisiLokasiTin<?=$benSisLokTin['id_variabel']?>" value="<?=$benSisLokTin['id_variabel']?>" class="benarSisiLokasiTin" <?php
              if(isset($getKTI['benar_sisi_lokasi_tindakan'])) {
                if($getKTI['benar_sisi_lokasi_tindakan'] == $benSisLokTin['id_variabel']) {
                  echo "checked";
                } else{
                  echo "";
                }
              }?>>
              <label for="benarSisiLokasiTin<?=$benSisLokTin['id_variabel']?>"><?= $benSisLokTin['variabel'] ?></label>
            </div>
          <?php endforeach; ?>
        </div>
      </div>

      <div class="row jarak">
        <div class="col-md-1">
          <span>3</span>
        </div>
        <div class="col-md-5">
          Adakah hal khusus yang perlu diperhatikan ?
        </div>
        <div class="col-md-6">
          <?php foreach ($khususPerhatikan as $khusPerh): ?>
            <div class="radio radio-primary form-check-inline jarak2">
              <input type="radio" name="khususPerhatikan" id="khususPerhatikan<?=$khusPerh['id_variabel']?>" value="<?=$khusPerh['id_variabel']?>" class="khususPerhatikan" <?php
              if(isset($getKTI['hal_khusus'])) {
                if($getKTI['hal_khusus'] == $khusPerh['id_variabel']) {
                  echo "checked";
                } else{
                  echo "";
                }
              }?>>
              <label for="khususPerhatikan<?=$khusPerh['id_variabel']?>"><?= $khusPerh['variabel'] ?></label>
            </div>
          <?php endforeach; ?>
        </div>
      </div>

      <div id="showKhususPerhatikan" style="display: none;">
        <div class="row jarak">
          <div class="offset-md-6 col-md-6">
            <input type="text" name="deskKhususPerhatikan" id="deskKhususPerhatikan" class="form-control jarak3 deskKhususPerhatikan" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['isi_hal_khusus']) ? $getKTI['isi_hal_khusus'] : "" ?>">
          </div>
        </div>
      </div>

      <div class="row jarak">
        <div class="col-md-1">
          <span>4</span>
        </div>
        <div class="col-md-5">
          Jika ada, langkah apa yang akan dilakukan
        </div>
        <div class="col-md-6">
          <input type="text" name="jikaAdaLangkahApaTimeOut" id="jikaAdaLangkahApaTimeOut" class="form-control jarak3 jikaAdaLangkahApaTimeOut" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['langkah_yang_dilakukan']) ? $getKTI['langkah_yang_dilakukan'] : "" ?>">
        </div>
      </div>

      <div class="row jarak">
        <div class="col-md-1">
          <span>5</span>
        </div>
        <div class="col-md-5">
          Apakah instrumen sudah benar ?
        </div>
        <div class="col-md-6">
          <?php foreach ($instrumenSudahBenar as $instruSudBen): ?>
            <div class="radio radio-primary form-check-inline jarak2">
              <input type="radio" name="instrusudahBenar" id="instrusudahBenar<?=$instruSudBen['id_variabel']?>" value="<?=$instruSudBen['id_variabel']?>" class="instrusudahBenar" <?php
              if(isset($getKTI['instrumen'])) {
                if($getKTI['instrumen'] == $instruSudBen['id_variabel']) {
                  echo "checked";
                } else{
                  echo "";
                }
              }?>>
              <label for="instrusudahBenar<?=$instruSudBen['id_variabel']?>"><?= $instruSudBen['variabel'] ?></label>
            </div>
          <?php endforeach; ?>
        </div>
      </div>

      <div class="row jarak">
        <div class="col-md-1">
          <span>6</span>
        </div>
        <div class="col-md-5">
          Apakah foto radiologi sudah sesuai dan ditayangkan ?
        </div>
        <div class="col-md-6">
          <?php foreach ($fotoRadiologiSesuai as $fotRadSes): ?>
            <div class="radio radio-primary form-check-inline jarak2">
              <input type="radio" name="fotRadioSesuai" id="fotRadioSesuai<?=$fotRadSes['id_variabel']?>" value="<?=$fotRadSes['id_variabel']?>" class="fotRadioSesuai" <?php
              if(isset($getKTI['foto_radiologi'])) {
                if($getKTI['foto_radiologi'] == $fotRadSes['id_variabel']) {
                  echo "checked";
                } else{
                  echo "";
                }
              }?>>
              <label for="fotRadioSesuai<?=$fotRadSes['id_variabel']?>"><?= $fotRadSes['variabel'] ?></label>
            </div>
          <?php endforeach; ?>
        </div>
      </div>
      <!-- END TIME OUT -->

      <!-- START SIGN OUT -->
      <div class="row">
        <div class="offset-md-3 col-md-6">
          <div class="text-center header-sub-pengkajian">
            SIGN OUT
          </div>
        </div>
      </div>

      <div class="row jarak">
        <div class="offset-md-2 col-md-3">
          Pukul
        </div>
        <div class="col-md-4">
          <input type="text" class="form-control pukulSignOutInv" id="pukulSignOutInv" name="pukulSignOutInv" value="<?= isset($getKTI['pukul_signout_inv']) ? $getKTI['pukul_signout_inv'] : "" ?>">
        </div>
      </div>

      <div class="row jarak">
        <div class="col-md-1">
          <span>1</span>
        </div>
        <div class="col-md-5">
          Perawat melakukan konfirmasi verbal, tentang :
        </div>
      </div>

      <div class="row jarak">
        <div class="offset-md-1 col-md-1">
          <span>A</span>
        </div>
        <div class="col-md-4">
          Nama tindakan
        </div>
        <div class="col-md-6">
          <div class="checkbox checkbox-primary form-check-inline jarak2">
            <input type="checkbox" name="namaTindakanSignOut" id="namaTindakanSignOut" value="" class="namaTindakanSignOut" <?php
            if(isset($getKTI['nama_tindakan'])) {
              if($getKTI['nama_tindakan'] == 1) {
                echo "checked";
              } else{
                echo "";
              }
            }?>>
            <label for="namaTindakanSignOut">Ya</label>
          </div>
        </div>
      </div>

      <div class="row jarak">
        <div class="offset-md-1 col-md-1">
          <span>B</span>
        </div>
        <div class="col-md-4">
         Kelengkapan kasa jarum / instrumen
       </div>
       <div class="col-md-6">
        <?php foreach ($kelengkapanKasaJarum as $keleKapKasJar): ?>
          <div class="radio radio-primary form-check-inline jarak2">
            <input type="radio" name="kelengkapanKapKasaJarum" id="kelengkapanKapKasaJarum<?=$keleKapKasJar['id_variabel']?>" value="<?=$keleKapKasJar['id_variabel']?>" class="kelengkapanKapKasaJarum" <?php
            if(isset($getKTI['kelengkapan_kasa'])) {
              if($getKTI['kelengkapan_kasa'] == $keleKapKasJar['id_variabel']) {
                echo "checked";
              } else{
                echo "";
              }
            }?>>
            <label for="kelengkapanKapKasaJarum<?=$keleKapKasJar['id_variabel']?>"><?= $keleKapKasJar['variabel'] ?></label>
          </div>
        <?php endforeach; ?>
      </div>
    </div>

    <div class="row jarak">
      <div class="offset-md-1 col-md-1">
        <span>C</span>
      </div>
      <div class="col-md-4">
       Spesimen di beri label
     </div>
     <div class="col-md-6">
      <?php foreach ($spesimenBeriLabel as $spesBerLab): ?>
        <div class="radio radio-primary form-check-inline jarak2">
          <input type="radio" name="spesimenBeriLab" id="spesimenBeriLab<?=$spesBerLab['id_variabel']?>" value="<?=$spesBerLab['id_variabel']?>" class="spesimenBeriLab" <?php
          if(isset($getKTI['spesimen'])) {
            if($getKTI['spesimen'] == $spesBerLab['id_variabel']) {
              echo "checked";
            } else{
              echo "";
            }
          }?>>
          <label for="spesimenBeriLab<?=$spesBerLab['id_variabel']?>"><?= $spesBerLab['variabel'] ?></label>
        </div>
      <?php endforeach; ?>
    </div>
  </div>

  <div class="row jarak">
    <div class="offset-md-1 col-md-1">
      <span>D</span>
    </div>
    <div class="col-md-4">
     Nama implan dan lokasi pemasangan
   </div>
   <div class="col-md-6">
    <?php foreach ($namaImplanDanLokasi as $namImplanDnLok): ?>
      <div class="radio radio-primary form-check-inline jarak2">
        <input type="radio" name="namaImplanLok" id="namaImplanLok<?=$namImplanDnLok['id_variabel']?>" value="<?=$namImplanDnLok['id_variabel']?>" class="namaImplanLok" <?php
        if(isset($getKTI['nama_implant'])) {
          if($getKTI['nama_implant'] == $namImplanDnLok['id_variabel']) {
            echo "checked";
          } else{
            echo "";
          }
        }?>>
        <label for="namaImplanLok<?=$namImplanDnLok['id_variabel']?>"><?= $namImplanDnLok['variabel'] ?></label>
      </div>
    <?php endforeach; ?>
  </div>
</div>

<div id="showNamaImplanDanLokasi" style="display: none;">
  <div class="row jarak">
    <div class="offset-md-6 col-md-6">
      <input type="text" name="deskNamaImplanDanLokasi" id="deskNamaImplanDanLokasi" class="form-control jarak3 deskNamaImplanDanLokasi" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['isi_implant']) ? $getKTI['isi_implant'] : "" ?>">
    </div>
  </div>
</div>

<div class="row jarak">
  <div class="offset-md-1 col-md-1">
    <span>E</span>
  </div>
  <div class="col-md-4">
   Apakah ada masalah peralatan yang perlu diperhatikan
 </div>
 <div class="col-md-6">
  <?php foreach ($peralatanYangPerlu as $perYgPerlu): ?>
    <div class="radio radio-primary form-check-inline jarak2">
      <input type="radio" name="peralatanygPrl" id="peralatanygPrl<?=$perYgPerlu['id_variabel']?>" value="<?=$perYgPerlu['id_variabel']?>" class="peralatanygPrl" <?php
      if(isset($getKTI['masalah_peralatan'])) {
        if($getKTI['masalah_peralatan'] == $perYgPerlu['id_variabel']) {
          echo "checked";
        } else{
          echo "";
        }
      }?>>
      <label for="peralatanygPrl<?=$perYgPerlu['id_variabel']?>"><?= $perYgPerlu['variabel'] ?></label>
    </div>
  <?php endforeach; ?>
</div>
</div>

<div id="showperalatanYangPerlu" style="display: none;">
  <div class="row jarak">
    <div class="offset-md-6 col-md-6">
      <input type="text" name="deskperalatanYangPerlu" id="deskperalatanYangPerlu" class="form-control jarak3 deskperalatanYangPerlu" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['isi_peralatan_yang_perlu']) ? $getKTI['isi_peralatan_yang_perlu'] : "" ?>">
    </div>
  </div>
</div>

<div class="row jarak">
  <div class="col-md-1">
    <span>2</span>
  </div>
  <div class="col-md-5">
    Adakah masalah yang harus diperhatikan
  </div>
  <div class="col-md-6">
    <?php foreach ($masalahHarusPerhatikan as $mslhHrsPerh): ?>
      <div class="radio radio-primary form-check-inline jarak2">
        <input type="radio" name="mslhHrsPerhatikan" id="mslhHrsPerhatikan<?=$mslhHrsPerh['id_variabel']?>" value="<?=$mslhHrsPerh['id_variabel']?>" class="mslhHrsPerhatikan" <?php
        if(isset($getKTI['masalah_yang_harus_diperhatikan'])) {
          if($getKTI['masalah_yang_harus_diperhatikan'] == $mslhHrsPerh['id_variabel']) {
            echo "checked";
          } else{
            echo "";
          }
        }?>>
        <label for="mslhHrsPerhatikan<?=$mslhHrsPerh['id_variabel']?>"><?= $mslhHrsPerh['variabel'] ?></label>
      </div>
    <?php endforeach; ?>
  </div>
</div>

<div id="showMasalahHarusPerhatikan" style="display: none;">
  <div class="row jarak">
    <div class="offset-md-6 col-md-6">
      <input type="text" name="deskMasalahHarusPerhatikan" id="deskMasalahHarusPerhatikan" class="form-control jarak3 deskMasalahHarusPerhatikan" placeholder="[ Jelaskan ]" value="<?= isset($getKTI['isi_masalah_yang_harus_diperhatikan']) ? $getKTI['isi_masalah_yang_harus_diperhatikan'] : "" ?>">
    </div>
  </div>
</div>

</div>
</div>

</div>
</div>


<div class="row">
 <div class="col-lg-2 offset-lg-10 mt-3">
  <div class="form-group">
    <input type="submit" class="btn btn-block btn-primary" id="btn-keselamatan-tindakan-invasif" value="Simpan">
  </div>
</div>
</div>

</div>

</form>
</div>

</div>
</div>
<div role="tabpanel" class="tab-pane fade" id="tabhistoryKeselamatanTindakanInvasif">
  <div class="row">
    <div class="col-12">
      <div class="table-responsive">
        <table id="tblhistoryKeselamatanTindakanInvasif" class="table table-bordered table-hover dt-responsive  table-custom" cellspacing="0" width="100%">
          <thead>
            <tr class="table-tr-custom">
              <th>No</th>
              <th>Tanggal</th>
              <th>Ruangan</th>
              <th>DPJP</th>
              <th>OLEH</th>
              <th>#</th>
            </tr>
          </thead>
          <tbody>
            <?php $no = 1; foreach($historyKTI as $hkti):?>
            <tr>
              <td><?=$no?></td>
              <td><?=$hkti['TANGGAL_SERAHTERIMA']?></td>
              <td><?=$hkti['RUANGAN_KUNJUNGAN']?></td>
              <td><?=$hkti['DPJP']?></td>
              <td><?=$hkti['USER']?></td>
              <td>
                <?php if($ruangan == 2){ ?>
                  <button type="button" class="btn btn-outline-success btn-sm  btn-block tombolEditTimeSignOutKesInvRi" data-idkti="<?=$hkti['id_kti']?>" data-norm="<?=$hkti['NORM']?>" data-nopen="<?=$hkti['NOPEN']?>" data-nokun="<?=$hkti['NOKUN']?>"><i class="fas fa-edit"></i> Edit Pra Tindakan</button>
                <?php }else{ ?>
                  <a href="/simrskd/pengkajianAwal/index/<?= $this->uri->segment(4)."/".$this->uri->segment(5)."/".$this->uri->segment(6)."/ews/".$hkti['id_kti'] ?>" class="btn btn-sm btn-primary btn-block"><i class="fas fa-edit"></i> Edit</a>
                <?php } ?>
                <a href="#viewEditTimeSignOutKesInv" class="btn btn-outline-warning btn-sm btn-block tombolEditTimeSignOutKesInv" data-idkes="<?=$hkti['id_kti']?>" data-nokun="<?=$hkti['NOKUN']?>" data-toggle="modal" data-backdrop="static">
                    <i class="fas fa-edit"></i> Time & Sign Out
                  </a>

                <a href="/reports/simrskd/invasif/KeselamatanInvasif/keselamatanTindakanInvasif.php?format=pdf&id=<?=$hkti['id_kti']?>" class="btn btn-success btn-block btn-sm" target="_blank"><i class="fa fa-print"></i> Cetak</a></td>
              </tr>
              <?php $no++; endforeach;?>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- START MODAL VIEW TIME SIGN OUT -->
<div id="viewEditTimeSignOutKesInv" class="modal fade" role="dialog">
  <div class="modal-dialog modal-xl">
    <div class="modal-content">
      <div id="hasilEditTimeSignOutKesInv">

      </div>
    </div>
  </div>
</div>
  <!-- END MODAL VIEW TIME SIGN OUT -->

<script>
  $(document).ready(function(){
    $('.showTimeSignOutKesInv').hide();

    $('#tblhistoryKeselamatanTindakanInvasif').on('click','.tombolEditTimeSignOutKesInvRi', function() {
        var id = $(this).attr('data-idkti');
        var norm = $(this).attr('data-norm');
        var nopen = $(this).attr('data-nopen');
        var nokun = $(this).attr('data-nokun');
        var link = norm + "/" + nopen + "/" + nokun + "/" + id;
        // alert(link);

        $('#view_kesInvRi').load('<?php echo base_url("rekam_medis/rawat_inap/invasifAnestesi/KesInvRi/viewIndexRi/") ?>' + link);
      });

    $('#tblhistoryKeselamatanTindakanInvasif').on('click', '.tombolEditTimeSignOutKesInv', function () {
      var idkes = $(this).data('idkes');
      var nokun = $(this).data('nokun');
      $.ajax({
        type  : 'POST',
        url   : '<?php echo base_url() ?>pengkajianprorad/FormKeselamatanTindakanInvasif/viewInputTimeSignOutKesInv',
        data  : {
          nokun:nokun,
          idkes:idkes
        },
        success : function(data){
          $('#hasilEditTimeSignOutKesInv').html(data);
        }
      });
    });

    if($('.pemPenPano').val() == 6292 && $('.pemPenPano').is(':checked')){
       $('#showPanoramic').css("display", "block");
       $('.panoramic_desk').attr("required", "");
     }else{
       $('#showPanoramic').css("display", "none");
       $('.panoramic_desk').removeAttr("required", "");
     }

    // Panoramic
   $('.pemPenPano').on('click',function(){
     var id = $(this).val();
     if(id == 6292){
       $('#showPanoramic').css("display", "block");
       $('.panoramic_desk').attr("required", "");
     }else{
       $('#showPanoramic').css("display", "none");
       $('.panoramic_desk').removeAttr("required", "");
     }
   });

    // Khusus perhatikan
    $('.khususPerhatikan').on('click',function(){
     var id = $(this).val();
     if(id == 1421){
       $('#showKhususPerhatikan').css("display", "block");
       $('.deskKhususPerhatikan').attr("required", "");
     }else{
       $('#showKhususPerhatikan').css("display", "none");
       $('.deskKhususPerhatikan').removeAttr("required", "");
     }
   });

    if($(".khususPerhatikan:checked").val() == 1421){
     $('#showKhususPerhatikan').css('display','block');
   }

    // Nama implan dan lokasi
    $('.namaImplanLok').on('click',function(){
     var id = $(this).val();
     if(id == 1448){
       $('#showNamaImplanDanLokasi').css("display", "block");
       $('.deskNamaImplanDanLokasi').attr("required", "");
     }else{
       $('#showNamaImplanDanLokasi').css("display", "none");
       $('.deskNamaImplanDanLokasi').removeAttr("required", "");
     }
   });

    if($(".namaImplanLok:checked").val() == 1448){
     $('#showNamaImplanDanLokasi').css('display','block');
   }

   // Pukul tanda vital
   $('#pukulKesInv').timepicker({
    defaultTIme : false,
    showMeridian: false,
    icons: {
      up: 'fas fa-chevron-up',
      down: 'fas fa-chevron-down'
    }
  });

   // Pukul time out
   $('#pukulTimeOutInv').timepicker({
    defaultTIme : false,
    showMeridian: false,
    icons: {
      up: 'fas fa-chevron-up',
      down: 'fas fa-chevron-down'
    }
  });

   // Pukul sign out
   $('#pukulSignOutInv').timepicker({
    defaultTIme : false,
    showMeridian: false,
    icons: {
      up: 'fas fa-chevron-up',
      down: 'fas fa-chevron-down'
    }
  });

   // Masalah harus perhatikan
   $('.mslhHrsPerhatikan').on('click',function(){
     var id = $(this).val();
     if(id == 1452){
       $('#showMasalahHarusPerhatikan').css("display", "block");
       $('.deskMasalahHarusPerhatikan').attr("required", "");
     }else{
       $('#showMasalahHarusPerhatikan').css("display", "none");
       $('.deskMasalahHarusPerhatikan').removeAttr("required", "");
     }
   });

   if($(".mslhHrsPerhatikan:checked").val() == 1452){
     $('#showMasalahHarusPerhatikan').css('display','block');
   }

   // Ketersediaan Implant
   $('.ketersediaanImp').on('click',function(){
     var id = $(this).val();
     if(id == 1417){
       $('#showKetersediaanImplant').css("display", "block");
       $('.deskKetersediaanImplant').attr("required", "");
     }else{
       $('#showKetersediaanImplant').css("display", "none");
       $('.deskKetersediaanImplant').removeAttr("required", "");
     }
   });

   if($(".ketersediaanImp:checked").val() == 1417){
     $('#showKetersediaanImplant').css('display','block');
   }

   $('.peralatanygPrl').on('click',function(){
     var id = $(this).val();
     if(id == 1450){
       $('#showperalatanYangPerlu').css("display", "block");
       $('.deskperalatanYangPerlu').attr("required", "");
     }else{
       $('#showperalatanYangPerlu').css("display", "none");
       $('.deskperalatanYangPerlu').removeAttr("required", "");
     }
   });

   if($(".peralatanygPrl:checked").val() == 1450){
     $('#showperalatanYangPerlu').css('display','block');
   }

   // Persediaan darah
   $('.persediDarh').on('click',function(){
     var id = $(this).val();
     if(id == 1415){
       $('#showPersediaanDarah').css("display", "block");
       $('.deskGolonganDarah').attr("required", "");
       $('.deskPRC').attr("required", "");
       $('.deskFFP').attr("required", "");
       $('.deskLainnyaPersediaanDarah').attr("required", "");
     }else{
       $('#showPersediaanDarah').css("display", "none");
       $('.deskGolonganDarah').removeAttr("required", "");
       $('.deskPRC').removeAttr("required", "");
       $('.deskFFP').removeAttr("required", "");
       $('.deskLainnyaPersediaanDarah').removeAttr("required", "");
     }
   });

   if($(".persediDarh:checked").val() == 1415){
     $('#showPersediaanDarah').css('display','block');
   }

   // Simpan Form Keselamatan Tindakan Invasif
   $("#formKeselamatanTindakanInvasif").submit(function( event ) {
    dataKESELAMATANTINDAKANINVASIF = $("#formKeselamatanTindakanInvasif").serializeArray();
    $.ajax({
      url:"<?= base_url('pengkajianprorad/FormKeselamatanTindakanInvasif/action_keselamatantindakaninvasif/tambah') ?>",
      method:"POST",
      data:dataKESELAMATANTINDAKANINVASIF,
      success:function(data)
      {
        alertify.success('Data Tersimpan');
        location.reload();
      }
    });
    event.preventDefault();
  });
   $('#tblhistoryKeselamatanTindakanInvasif').DataTable({
    "responsive": true,
    "order":[],
    "language":{
      "sEmptyTable":"Maaf, tidak ada data yang bisa ditampilkan",
      "sInfo":"Menampilkan _START_ to _END_ of _TOTAL_ data",
      "sInfoEmpty":"Menampilkan 0 to 0 of 0 data",
      "sInfoFiltered":"(filtered from _MAX_ total entries)",
      "sInfoPostFix":"",
      "sInfoThousands":",",
      "sLengthMenu":"Menampilkan _MENU_ data",
      "sLoadingRecords":"Loading...",
      "sProcessing":"Processing...",
      "sSearch":"Pencarian:",
      "sZeroRecords":"Data tidak ditemukan",
      "oPaginate":{"sFirst":"Pertama",
      "sLast":"Terakhir",
      "sNext":"Selanjutnya",
      "sPrevious":"Sebelumnya"
    }
  }
});

 });
</script>

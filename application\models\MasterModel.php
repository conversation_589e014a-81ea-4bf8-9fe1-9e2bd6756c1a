<?php
defined('BASEPATH') or exit('No direct script access allowed');

class MasterModel extends CI_Model
{
    public function __construct()
    {
        parent::__construct();
    }

    public function referensi($id)
    {
        $query = $this->db->query("SELECT *
                               FROM db_master.variabel dv
                               WHERE dv.id_referensi = '$id' AND dv.status='1' ORDER BY dv.seq,dv.id_variabel");

        return $query->result_array();
    }

    public function referensiSimpel($jenis, $id = 0)
    {
        if ($this->input->get('q')) {
            $this->db->like('r.DESKRIPSI', $this->input->get('q'));
        }
        if ($id != 0) {
            $this->db->where("r.ID = $id");
        }
        $this->db->select('*');
        $this->db->from('`master`.referensi r');
        $this->db->where("r.JENIS= $jenis AND r.STATUS=1");

        $query = $this->db->get();
        if ($id != 0) {
            return $query->row_array();
        }
        return $query->result_array();
    }

    public function masterTpnIng()
    {
        $query = "
        SELECT ing.id id, ing.ingredients ingredients
        , IF(ing.solution_bags=1, 'for aminoacids, glocose, electrolytes', 'for lipids') solution_bags
        , ing.status STATUS, IF(ing.status=1, 'table-success', 'table-danger') warna_table
        , IF(ing.status=1, 'checked', '' ) ceklis
        , ing.status status
        FROM medis.tb_ingredients ing
        ";
        $bind = $this->db->query($query);
        return $bind->result_array();
    }

    function datatableChildIngredients($id)
    {
        $this->db->select("ingc.preparation_used, ingc.present_solution, ingc.konsentrasi
 , ingc.osmolarity, ingc.kalori
 , IF(ingc.status=1, 'checked', '') cek_status
 , ingc.id, ingc.dalam");
        $this->db->from('medis.tb_ingredients_child ingc');
        $this->db->where('ingc.id_ingredients', $id);
    }

    function getChildIngredients($id)
    {
        $this->datatableChildIngredients($id);
        if ($_POST["length"] != -1) {
            $this->db->limit($_POST["length"], $_POST["start"]);
        }
        $query = $this->db->get();
        return $query->result();
    }

    public function referensiNuklir($id)
    {
        $query = $this->db->query(
            "SELECT *
            FROM db_master.variabel dv
            WHERE dv.id_referensi = '$id' AND dv.status='1' AND dv.nilai != '9999'
            ORDER BY dv.nilai ASC"
        );

        return $query->result_array();
    }

    public function jenisPengkajian()
    {
        $query = $this->db->query("SELECT * FROM db_master.tb_jenis_pengkajian jp");

        return $query->result_array();
    }

    public function jenisBerkasRM()
    {
        $query = $this->db->query(
            "SELECT *
            FROM master.tb_jenis_rekammedis tjr
            WHERE tjr.STATUS = 1
            ORDER BY tjr.ID"
        );

        return $query->result_array();
    }

    public function jenisBerkasRM2($layanan)
    {
        $query = $this->db->query(
            "SELECT  a.id id_kategori, a.nama_kategori, a.jenis_layanan,b.id, b.nama_berkas
            FROM db_layanan.kategori_berkas a
            LEFT JOIN db_layanan.jenis_berkas b ON a.id=b.id_kategori
            WHERE a.status = 1
            AND b.`status`=1
            AND a.jenis_layanan='" . $layanan . "'
            ORDER BY a.id,b.id"
        );

        return $query->result_array();
    }

    public function cekVerifPengkajian($id)
    {
        $query = $this->db->query(
            "SELECT *
            FROM db_master.tb_verif_pengkajian vr
            WHERE vr.id_user='$id' AND vr.status='1'"
        );

        return $query->result_array();
    }

    public function parameterEws()
    {
        $query = $this->db->query("SELECT *
                               FROM db_master.referensi mr
                               WHERE mr.id_referensi in (124,125,126,127,128,129,130) AND mr.`status` = 1");

        return $query->result_array();
    }

    public function historyLokalis($nomr)
    {
        $query = $this->db->query("SELECT *
                               FROM db_master.tb_status_lokalis dtsl
                               WHERE dtsl.`STATUS` = 1 AND dtsl.NOMR = '$nomr'
                               ORDER BY dtsl.TGL_INPUT DESC");

        return $query->result();
    }

    public function historyCpptSoap($nomr)
    {
        $query = $this->db->query("SELECT *
                               FROM medis.tb_cppt_soap tcs
                               WHERE tcs.`STATUS` = 1 AND tcs.NOMR = '$nomr'
                               ORDER BY tcs.tglinput DESC");

        return $query->result();
    }

    public function hasilFotoLokalisNorm($norm)
    {
        $query = $this->db->query("SELECT *
                               FROM db_master.tb_status_lokalis dts
                               WHERE dts.NOMR = '$norm' AND dts.`STATUS` = 1");

        return $query->row_array();
    }

    public function getDeskRuangan($id)
    {
        $query = $this->db->query("SELECT *
            FROM master.ruangan ru
            WHERE ru.STATUS=1 AND ru.ID='$id'");

        return $query->row_array();
    }

    public function hasilFotoLokalis($id)
    {
        $query = $this->db->query("SELECT *
                               FROM db_master.tb_status_lokalis dts
                               WHERE dts.ID = '$id' AND dts.`STATUS` = 1");

        return $query->row_array();
    }

    public function hasilFotoCpptSoap($id)
    {
        $query = $this->db->query("SELECT *
                               FROM medis.tb_cppt_soap tcs
                               WHERE tcs.ID = '$id' AND tcs.`STATUS` = 1");

        return $query->row_array();
    }

    public function formMasalahKesehatan()
    {
        $query = $this->db->query(
            "SELECT *
            FROM db_master.tb_masalah_kesehatan mk
            WHERE mk.`STATUS`='1'"
        );
        return $query->result_array();
    }

    // List Seluruh Pegawai
    public function listAllPegawai()
    {
        if ($this->input->get('q')) {
            $this->db->like('master.getNamaLengkapPegawai(peg.NIP)', $this->input->get('q'));
        }
        $this->db->select('pengguna.ID, peg.NIP, master.getNamaLengkapPegawai(peg.NIP) NAMA_LENGKAP');
        $this->db->from('aplikasi.pengguna pengguna');
        $this->db->join('master.pegawai peg', 'pengguna.NIP = peg.NIP', 'left');
        $this->db->where('peg.NIP is NOT NULL', null, false);

        $query = $this->db->get();
        return $query->result_array();
    }

    // List terapis
    public function listTerapis()
    {
        $this->db->select(
            'ap.ID ID_PENGGUNA, master.getNamaLengkapPegawai(p.NIP) TERAPIS, p.PROFESI ID_PROFESI, ref.DESKRIPSI JENIS'
        );
        $this->db->from('master.pegawai p');
        $this->db->join('master.referensi ref', 'ref.ID = p.PROFESI AND ref.JENIS = 36', 'left');
        $this->db->join('aplikasi.pengguna ap', 'ap.NIP = p.NIP', 'left');
        $this->db->where_in('p.PROFESI', [9, 15]);
        $this->db->group_by('ap.ID');

        $query = $this->db->get();
        return $query->result_array();
    }

    // List Dokter
    public function listDr()
    {
        if ($this->input->get('q')) {
            $this->db->like('p.NAMA', $this->input->get('q'));
        }

        $this->db->select(
            'dok.ID ID_DOKTER, master.getNamaLengkapPegawai(dok.NIP) DOKTER, p.SMF ID_SMF, smf.DESKRIPSI SMF'
        );
        $this->db->from('master.dokter dok');
        $this->db->join('master.pegawai p', 'p.NIP = dok.NIP', 'left');
        $this->db->join('master.referensi smf', 'smf.ID = p.SMF AND smf.JENIS = 26', 'left');
        if (!$this->input->get('all')) {
            $this->db->where('dok.STATUS', 1);
        }
        $this->db->where('dok.ID !=', 230);
        $this->db->order_by('smf.DESKRIPSI asc', 'p.NAMA asc');

        $query = $this->db->get();
        return $query->result_array();
    }

    // List Dokter Umum
    public function listDrUmum($banDokter = null, $banSMF = null, $khususSMF = null)
    {
        $this->db->select(
            'dok.ID ID_DOKTER, master.getNamaLengkapPegawai(dok.NIP) DOKTER, p.SMF ID_SMF, smf.DESKRIPSI SMF'
        );
        $this->db->from('master.dokter dok');
        $this->db->join('master.pegawai p', 'p.NIP = dok.NIP', 'left');
        $this->db->join('master.referensi smf', 'smf.ID = p.SMF AND smf.JENIS = 26', 'left');
        $this->db->where('dok.STATUS', 1);
        if (isset($banDokter)) {
            foreach ($banDokter as $bd) {
                $this->db->where('dok.ID !=', $bd); // Array ID dokter yang di-ban
            }
        }
        if (isset($banSMF)) {
            foreach ($banSMF as $bs) {
                $this->db->where('smf.ID !=', $bs); // Array ID SMF yang di-ban
            }
        }
        if (isset($khususSMF)) {
            foreach ($khususSMF as $ks) {
                $this->db->where('smf.ID', $ks); // Array ID SMF yang dikhususkan
            }
        }
        $this->db->order_by('smf.DESKRIPSI asc', 'p.NAMA asc');

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listUserDr()
    {
        $this->db->select(
            'ap.ID ID_PENGGUNA, dok.ID ID_DOKTER, master.getNamaLengkapPegawai(dok.NIP) DOKTER, p.SMF ID_SMF,
            smf.DESKRIPSI SMF'
        );
        $this->db->from('master.dokter dok');
        $this->db->join('master.pegawai p', 'p.NIP = dok.NIP', 'left');
        $this->db->join('master.referensi smf', 'smf.ID = p.SMF AND smf.JENIS = 26', 'left');
        $this->db->join('aplikasi.pengguna ap', 'ap.NIP = dok.NIP', 'left');
        $this->db->where('dok.STATUS', 1);
        $this->db->order_by('smf.DESKRIPSI asc', 'p.NAMA asc');

        $query = $this->db->get();
        return $query->result_array();
    }

    // List Dokter Anastersi
    public function listDrAnastesi()
    {
        $this->db->select(
            'dok.ID ID_DOKTER, master.getNamaLengkapPegawai(dok.NIP) DOKTER, p.SMF ID_SMF, smf.DESKRIPSI SMF'
        );
        $this->db->from('master.dokter dok');
        $this->db->join('master.pegawai p', 'p.NIP = dok.NIP', 'left');
        $this->db->join('master.referensi smf', 'smf.ID = p.SMF AND smf.JENIS = 26', 'left');
        $this->db->where('dok.STATUS', 1);
        $this->db->where('dok.ID !=', 230);
        $this->db->where_in('p.SMF', [6, 55, 46]);
        $this->db->order_by('smf.DESKRIPSI asc', 'p.NAMA asc');

        $query = $this->db->get();
        return $query->result_array();
    }

    // List Dokter Paliatif
    public function listDrPaliatif()
    {
        $this->db->select(
            'dok.ID ID_DOKTER, master.getNamaLengkapPegawai(dok.NIP) DOKTER, p.SMF ID_SMF, smf.DESKRIPSI SMF'
        );
        $this->db->from('master.dokter dok');
        $this->db->join('master.pegawai p', 'p.NIP = dok.NIP', 'left');
        $this->db->join('master.referensi smf', 'smf.ID = p.SMF AND smf.JENIS = 26', 'left');
        $this->db->where('dok.STATUS', 1);
        $this->db->where_in('dok.ID', [9, 10, 17, 134, 159, 251]);
        $this->db->order_by('smf.DESKRIPSI asc', 'p.NAMA asc');

        $query = $this->db->get();
        return $query->result_array();
    }

    // Nomor Dokter
    public function nomorDokter($id)
    {
        $this->db->select('md.ID, master.getNamaLengkapPegawai(md.NIP) DOKTER, kp.NOMOR');
        $this->db->from('master.dokter md');
        $this->db->join('master.kontak_pegawai kp', 'md.NIP = kp.NIP');
        $this->db->where("md.STATUS = 1 AND kp.JENIS = 3 AND kp.NOMOR != '' AND md.ID = $id");
        $this->db->order_by('DOKTER asc');

        $query = $this->db->get();
        return $query->row();
    }

    // Nomor SMF
    public function nomorSMF($id)
    {
        $this->db->select('md.ID, master.getNamaLengkapPegawai(md.NIP) DOKTER, smf.DESKRIPSI, kp.NOMOR');
        $this->db->from('master.dokter md');
        $this->db->join('master.kontak_pegawai kp', 'md.NIP = kp.NIP', 'left');
        $this->db->join('master.pegawai p', 'p.NIP = md.NIP', 'left');
        $this->db->join('master.referensi smf', 'smf.ID = p.SMF AND smf.JENIS = 26', 'left');
        $this->db->where('md.STATUS', 1);
        $this->db->where('kp.JENIS', 3);
        $this->db->where('kp.NOMOR !=', '');
        $this->db->where('smf.ID', $id);
        $this->db->order_by('md.ID asc');

        $query = $this->db->get();
        return $query->result_array();
    }

    // Nomor Pasien
    public function nomorPasien($norm)
    {
        $this->db->select('kp.NOMOR');
        $this->db->from('`master`.kontak_pasien kp');
        $this->db->where("kp.NORM = $norm AND kp.JENIS = 3");

        $query = $this->db->get();
        return $query->row();
    }

    // List Pasien
    public function pasien()
    {
        if ($this->input->get('q')) {
            $this->db->where('mp.NORM', $this->input->get('q'));
            // $this->db->like('mp.NORM', $this->input->get('q'));
            // $this->db->or_like('NAMA', $this->input->get('q'));
        }
        $this->db->select('mp.NORM, `master`.getNamaLengkap(mp.NORM) NAMA');
        $this->db->from('`master`.pasien mp');
        $this->db->order_by('NAMA asc');
        $this->db->limit('20');

        $query = $this->db->get();
        return $query->result_array();
    }

    // Get Nama Pasien
    public function getPasien($norm)
    {
        $this->db->select("`master`.getNamaLengkap($norm) PASIEN");

        $query = $this->db->get();
        return $query->row()->PASIEN;
    }

    // Get Dokter
    public function getDokter($id)
    {
        $this->db->select("`master`.getNamaLengkapPegawai(md.NIP) DOKTER")->from('`master`.dokter md')->where('md.ID', $id);

        $query = $this->db->get();
        return $query->row()->DOKTER;
    }

    // Get SMF
    public function getSMF($id)
    {
        $this->db->select("mr.DESKRIPSI SMF")->from('`master`.referensi mr ')->where(array('mr.ID' => $id, 'mr.JENIS' => 26));

        $query = $this->db->get();
        return $query->row()->SMF;
    }

    // Get Penguna
    public function getPenguna($id)
    {
        $this->db->select("`master`.getNamaLengkapPegawai(ap.NIP) NAMALENGKAP")->from('aplikasi.pengguna ap ')->where('ap.ID', $id);

        $query = $this->db->get();
        return $query->row()->NAMALENGKAP;
    }

    // List Perawat
    public function listPerawat($idRuang = null, $jenisRuang = null)
    {
        $this->db->select('mapeg.NIP, peng.ID, master.getNamaLengkapPegawai(mapeg.NIP) NAMA');
        $this->db->from('master.pegawai mapeg');
        $this->db->join('aplikasi.pengguna peng', 'mapeg.NIP = peng.NIP', 'left');
        if (isset($idRuang) && isset($jenisRuang)) {
            $this->db->join('master.perawat p', 'mapeg.NIP = p.NIP', 'left');
            $this->db->join('master.perawat_ruangan pr', 'p.ID = pr.PERAWAT', 'left');
            $this->db->join('master.ruangan r', 'pr.RUANGAN = r.ID', 'left');
            $this->db->where('r.ID', $idRuang);
            $this->db->where('r.JENIS', $jenisRuang);
        }
        $this->db->where('mapeg.PROFESI', 6);
        $this->db->group_by('peng.ID');
        $this->db->order_by('NAMA asc');

        $query = $this->db->get();
        return $query->result_array();
    }

    // List Perawat
    public function listPerawatPenataAnestesi($idRuang = null, $jenisRuang = null)
    {
        $this->db->select('mapeg.NIP, peng.ID, master.getNamaLengkapPegawai(mapeg.NIP) NAMA');
        $this->db->from('master.pegawai mapeg');
        $this->db->join('aplikasi.pengguna peng', 'mapeg.NIP = peng.NIP', 'left');
        if (isset($idRuang) && isset($jenisRuang)) {
            $this->db->join('master.perawat p', 'mapeg.NIP = p.NIP', 'left');
            $this->db->join('master.perawat_ruangan pr', 'p.ID = pr.PERAWAT', 'left');
            $this->db->join('master.ruangan r', 'pr.RUANGAN = r.ID', 'left');
            $this->db->where('r.ID', $idRuang);
            $this->db->where('r.JENIS', $jenisRuang);
        }
        $this->db->where_in('mapeg.PROFESI', [19, 6]);
        $this->db->where('peng.STATUS', 1);
        $this->db->group_by('peng.ID');
        $this->db->order_by('NAMA asc');

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listPetugasSerahTerima($idRuang = null, $jenisRuang = null)
    {
        $this->db->select('mapeg.NIP, peng.ID, master.getNamaLengkapPegawai(mapeg.NIP) NAMA');
        $this->db->from('master.pegawai mapeg');
        $this->db->join('aplikasi.pengguna peng', 'mapeg.NIP = peng.NIP', 'left');
        if (isset($idRuang) && isset($jenisRuang)) {
            $this->db->join('master.perawat p', 'mapeg.NIP = p.NIP', 'left');
            $this->db->join('master.perawat_ruangan pr', 'p.ID = pr.PERAWAT', 'left');
            $this->db->join('master.ruangan r', 'pr.RUANGAN = r.ID', 'left');
            $this->db->where('r.ID', $idRuang);
            $this->db->where('r.JENIS', $jenisRuang);
        }
        $this->db->where_in('mapeg.PROFESI', [19, 6, 16, 15, 9]);
        $this->db->group_by('peng.ID');
        $this->db->order_by('NAMA asc');

        $query = $this->db->get();
        return $query->result_array();
    }

    // List Tindakan Keperawatan
    public function listTindakanKeperawatan()
    {
        $this->db->select('tk.ID, tk.TINDAKAN_KEPERAWATAN TINDAKAN');
        $this->db->from('db_master.tb_tindakan_keperawatan tk');

        $query = $this->db->get();
        return $query->result_array();
    }

    public function pilihPerawatPrtklKemo()
    {
        $q = $this->input->get('q');
        $query = $this->db->query("SELECT mapeg.NIP, master.getNamaLengkapPegawai(mapeg.NIP) NAMA
            FROM master.pegawai mapeg
            WHERE NAMA LIKE '%" . $q . "%'
            #GROUP BY NAMA
            LIMIT 10");

        return $query->result();
    }

    public function hasilPerawatProtokolKemo($id)
    {
        $query = $this->db->query("SELECT *, master.getNamaLengkapPegawai(pg.NIP) NAMA_PERAWAT_2
            FROM keperawatan.tb_prtkl_kemo_pemberian tpkm
            LEFT JOIN master.pegawai pg ON pg.NIP = tpkm.PERAWAT_2
            WHERE tpkm.ID_PRTKL_DETAIL='$id'");

        return $query->row_array();
    }

    public function listRadiografer2()
    {
        $query = $this->db->query("SELECT peg.NIP NIP, peng.ID ID_PENGGUNA, master.getNamaLengkapPegawai(peg.NIP) NAMA, ref.DESKRIPSI PROFESI
            FROM master.pegawai peg
            #LEFT JOIN master.dokter dok ON dok.NIP = peg.NIP
            LEFT JOIN master.referensi ref ON ref.ID = peg.PROFESI AND ref.JENIS=36
            LEFT JOIN aplikasi.pengguna peng ON peng.NIP = peg.NIP
            WHERE peg.PROFESI=8
            ORDER BY peg.NAMA ASC
            ");

        return $query->result_array();
    }

    public function listFisikawanMedis()
    {
        $query = $this->db->query("SELECT peg.NIP NIP, peng.ID ID_PENGGUNA, master.getNamaLengkapPegawai(peg.NIP) NAMA, ref.DESKRIPSI PROFESI
            FROM master.pegawai peg
            #LEFT JOIN master.dokter dok ON dok.NIP = peg.NIP
            LEFT JOIN master.referensi ref ON ref.ID = peg.PROFESI AND ref.JENIS=36
            LEFT JOIN aplikasi.pengguna peng ON peng.NIP = peg.NIP
            WHERE #dok.`STATUS`=1 AND
            peg.PROFESI=17
            ORDER BY peg.NAMA ASC
            ");

        return $query->result_array();
    }

    // List Pegawai
    public function listPegawai()
    {
        $this->db->select('mp.NIP, `master`.getNamaLengkapPegawai(mp.NIP) NAMA');
        $this->db->from('`master`.pegawai mp');
        $this->db->where('mp.`STATUS` !=', 0);
        $this->db->order_by('NAMA asc');

        $query = $this->db->get();
        return $query->result_array();
    }

    //List Golongan Darah
    public function listGolonganDarah()
    {
        $this->db->select('goldar.ID, goldar.DESKRIPSI golongan');
        $this->db->from('master.referensi goldar');
        $this->db->where('goldar.JENIS', 6);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listDrAnestesi()
    {
        $query = $this->db->query(
            "SELECT dok.ID ID_DOKTER, master.getNamaLengkapPegawai(dok.NIP) DOKTER, ref.DESKRIPSI SMF
            FROM master.pegawai peg
            LEFT JOIN master.dokter dok ON dok.NIP = peg.NIP
            LEFT JOIN master.referensi ref ON ref.ID = peg.SMF AND ref.JENIS = 26
            WHERE dok.`STATUS` = 1 AND ref.ID IN (6, 46, 55)
            ORDER BY peg.NAMA ASC"
        );

        return $query->result_array();
    }

    public function listDrPelaksana()
    {
        $query = $this->db->query("SELECT dok.ID ID_DOKTER, master.getNamaLengkapPegawai(dok.NIP) DOKTER, ref.DESKRIPSI SMF
            FROM master.pegawai peg
            LEFT JOIN master.dokter dok ON dok.NIP = peg.NIP
            LEFT JOIN master.referensi ref ON ref.ID = peg.SMF AND ref.JENIS=26
            WHERE dok.`STATUS`=1 #AND ref.ID=6
            ORDER BY peg.NAMA ASC
            ");

        return $query->result_array();
    }

    public function listKajianSistem()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 632);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listKajianSistem1()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 633);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listKajianSistem2()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 664);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listKajianSistem3()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 665);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listKajianSistem4()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 666);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listKajianSistem5()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 667);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listKajianSistem6()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 668);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listKajianSistem7()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 669);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listKajianSistem8()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 670);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listKajianSistem9()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 671);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listKajianSistem10()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 672);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listKajianSistem11()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 673);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listKajianSistem12()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 674);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listKajianSistem13()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 675);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listKajianSistem14()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 676);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listKajianSistem15()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 677);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listPemriksaanFisik()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 651);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listPemriksaanFisik1()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 652);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listPemriksaanFisik2()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 694);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listPemriksaanFisik3()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 695);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listPemriksaanFisik4()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 697);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listPemriksaanFisik5()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 698);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listPemriksaanFisik6()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 699);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listPemriksaanPenunjang()
    {
        $this->db->select('rp.id_variabel, rp.variabel, rp.nilai');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 653);
        $this->db->order_by('rp.nilai');

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listKlasifikasiAsa()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 655);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listMonitoring()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 659);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listPerawatanPascaSedasi()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 662);

        $query = $this->db->get();
        return $query->result_array();
    }

    // FORM CATATAN PASIEN DI KAMAR PEMULIHAN SEDASI

    public function listKesadaranSedasi()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 727);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listPernapasanSedasi()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 728);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listNyeriSedasi()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 729);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listRisikoJatuhSedasi()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 730);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listFrekuensiNapasSedasi()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 731);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listFrekuensiNadiSedasi()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 732);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listTekananDarahSedasi()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 733);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listSkalaNyeriSedasi()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 734);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listRuangPemulihan()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 736);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listScorePadss()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 737);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listKeluarNyeri()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 738);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listPemulihanRisiko()
    {
        $this->db->select('rp.id_variabel, rp.variabel');
        $this->db->from('db_master.variabel rp');
        $this->db->where('rp.id_referensi', 739);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function pilihProtokolKemo()
    {
        $this->db->select('*');
        $this->db->from('db_master.tb_protokol_kemo');

        $query = $this->db->get();
        return $query->result_array();
    }

    // List SMF
    public function listSMF()
    {
        $this->db->select('p.SMF ID_SMF, smf.DESKRIPSI SMF');
        $this->db->from('master.dokter dok');
        $this->db->join('master.pegawai p', 'p.NIP = dok.NIP', 'left');
        $this->db->join('master.referensi smf', 'smf.ID = p.SMF AND smf.JENIS = 26', 'left');
        $this->db->where('dok.STATUS', 1);
        // $this->db->where('smf.ID !=', 5);// Gigi
        // $this->db->where('smf.ID !=', 31);// Dokter Umum
        $this->db->where('smf.ID !=', 14); // THT
        $this->db->where('smf.ID !=', 36); // Non SMF
        $this->db->where('smf.ID !=', 48); // Gigi Penyakit Mulut
        $this->db->where('smf.ID !=', 54); // Gigi Bedah Mulut
        $this->db->group_by('p.SMF');
        $this->db->order_by('smf.DESKRIPSI asc');

        $query = $this->db->get();
        return $query->result_array();
    }

    //STADIUM
    public function stadium()
    {
        $query = $this->db->query("SELECT * FROM master.stadium");

        return $query->result_array();
    }

    public function tbBbCpptAkhirPrtklKemo($nopen)
    {
        $query = $this->db->query("SELECT * FROM
            (SELECT 'TABLE TB_BB', tbbb.bb BB, tbbb.tb TB, tbbb.created_at TANGGAL

            FROM db_pasien.tb_tb_bb tbbb

            LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tbbb.nokun

            WHERE tbbb.`status`=1 AND pk.NOPEN='$nopen'

            UNION

            SELECT 'CPPT' JENIS, IF(cp.tb_bb IS NULL,ctv.bb,tv.bb) BB
            , IF(cp.tb_bb IS NULL,ctv.tb,tv.tb) TB
            , cp.tanggal TANGGAL

            FROM keperawatan.tb_cppt cp
            LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = cp.nokun
            LEFT JOIN keperawatan.tb_cppt_tanda_vital ctv ON ctv.id_cppt = cp.id
            LEFT JOIN db_pasien.tb_tb_bb tv ON tv.id = cp.tb_bb
            WHERE cp.`status`=1 AND cp.pemberi_cppt=2 AND pk.NOPEN='$nopen') a

            ORDER BY a.TANGGAL DESC

            LIMIT 1");

        return $query->row_array();
    }

    public function diagnosisCpptAkhirPrtklKemo($nopen)
    {
        $query = $this->db->query("SELECT c.analisis ANALISIS
            FROM keperawatan.tb_cppt c
            LEFT JOIN pendaftaran.kunjungan k ON k.NOMOR = c.nokun
            LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = k.NOPEN
            WHERE c.pemberi_cppt='1' AND p.NOMOR='$nopen' AND c.status='1'
            ORDER BY c.tanggal DESC
            LIMIT 1");

        return $query->row_array();
    }

    //ASUHAN KEPERAWATAN
    public function asuhanKeperawatan($id)
    {
        $query = $this->db->query("SELECT * FROM db_master.tb_asuhan_keperawatan ak WHERE find_in_set($id,ak.ID_VARIABEL)");
        return $query->row();
    }

    public function masalahKesehatan($id)
    {
        $query = $this->db->query("SELECT * FROM db_master.tb_masalah_kesehatan mk WHERE find_in_set($id,mk.ID)");
        return $query->row();
    }

    //ASUHAN KEPERAWATAN DETIL
    public function asuhanKeperawatanDetil($id)
    {
        $this->db->select('*');
        $this->db->from('db_master.tb_asuhan_keperawatan_detil akdt');
        $this->db->where('akdt.ID_ASUHAN', $id);
        $this->db->where('akdt.STATUS', '1');
        $query = $this->db->get();
        return $query->result_array();
    }

    public function masalahKesehatanDetil($id)
    {
        $this->db->select('*');
        $this->db->from('db_master.tb_masalah_kesehatan_detail mkd');
        $this->db->where('mkd.ID_MASALAH', $id);
        $this->db->where('mkd.STATUS', '1');
        $query = $this->db->get();
        return $query->result_array();
    }

    public function tindakanPenunjang($jenis, $parent = 0) 
    {
        $this->db->select('*');
        $this->db->from('master.referensi_tindakan_penunjang mf');
        $this->db->where('mf.jenis_penunjang', $jenis)->where('mf.parent', $parent)->where('mf.status != 0');
        $this->db->order_by('mf.urutan', 'ASC');

        $query = $this->db->get();
        return $query;
    }

    public function tindakanPenunjangIGD($jenis, $igd)
    {
        $this->db->select('*');
        $this->db->from('master.referensi_tindakan_penunjang mf');
        $this->db->where('mf.jenis_penunjang', $jenis)->where('mf.igd', $igd)->where('mf.status != 0');
        $this->db->order_by('mf.urutan', 'ASC');

        $query = $this->db->get();
        return $query;
    }

    //PEGAWAI
    public function pegawai()
    {
        $query = $this->db->query("SELECT mp.`*`,mra.DESKRIPSI AGAMA_PEGAWAI, mrj.DESKRIPSI JENIS_KELAMIN_PEGAWAI, mrp.DESKRIPSI PROFESI_PEGAWAI, mrs.DESKRIPSI SMF_PEGAWAI,CONCAT(IF(mp.TEMPAT_LAHIR='','',CONCAT(mp.TEMPAT_LAHIR, ', ')), DATE_FORMAT(mp.TANGGAL_LAHIR,'%d-%m-%Y')) TTL
                               FROM master.pegawai mp
                               LEFT JOIN master.referensi mra ON mra.ID = mp.AGAMA AND mra.JENIS = 1
                               LEFT JOIN master.referensi mrj ON mrj.ID = mp.JENIS_KELAMIN AND mrj.JENIS = 2
                               LEFT JOIN master.referensi mrp ON mrp.ID = mp.PROFESI AND mrp.JENIS = 4
                               LEFT JOIN master.referensi mrs ON mrs.ID = mp.SMF AND mrs.JENIS = 26  ");

        return $query;
    }

    ////////////////////////////////////////////////////////AWAL TIMJA/////////////////////////////////////////////
    public function timja()
    {
        $query = $this->db->query("SELECT
                                        mt.ID,
                                        peg.NIP,
                                        master.getNamaLengkapPegawai ( dok.NIP ) NAMADOKTER,
                                        timja.DESKRIPSI TIMJA,
                                        jabatan.DESKRIPSI JABATAN_TIMJA,
                                        ap.NAMA OLEH,
                                        mt.STATUS_TIMJA
                                FROM master.timja mt
                                        LEFT JOIN master.dokter dok ON dok.ID = mt.ID_DOKTER
                                        LEFT JOIN master.pegawai peg ON peg.NIP = dok.NIP
                                        LEFT JOIN master.referensi timja ON timja.ID = mt.ID_TIMJA AND timja.JENIS = 102
                                        LEFT JOIN master.referensi jabatan ON jabatan.ID = mt.ID_JABATAN_TIMJA AND jabatan.JENIS = 103
                                        LEFT JOIN aplikasi.pengguna ap ON ap.ID = mt.OLEH");

        return $query;
    }

    public function dokter_timja()
    {
        $query = $this->db->query(
            "SELECT
                dok.ID ID_DOKTER,
                peg.NIP NIP,
                master.getNamaLengkapPegawai ( dok.NIP ) NAMADOKTER,
                ref.DESKRIPSI SMF
            FROM
                master.dokter dok
                LEFT JOIN master.pegawai peg ON peg.NIP = dok.NIP
                LEFT JOIN master.referensi ref ON ref.ID = peg.SMF
                    AND ref.JENIS = 26
            WHERE
                dok.STATUS = 1
            ORDER BY
                ref.DESKRIPSI ASC,
                peg.NAMA ASC"
        );

        return $query->result_array();
    }

    public function referensiTimja($jenis, $id)
    {
        $this->db->select('r.JENIS, r.ID, r.DESKRIPSI, r.STATUS');
        $this->db->from('master.referensi r');
        if (isset($id)) {
            $this->db->join('master.timja t', 't.ID_TIMJA = r.ID', 'left');
            $this->db->join('master.dokter d', 'd.ID = t.ID_DOKTER', 'left');
            $this->db->join('aplikasi.pengguna p', 'p.NIP = d.NIP', 'left');
            $this->db->where('p.ID', $id);
        }
        $this->db->where('r.JENIS', $jenis);
        $this->db->where('r.STATUS', 1);
        $query = $this->db->get();
        return $query->result_array();
    }

    public function simpanTimja($data)
    {
        $this->db->insert('master.timja', $data);
    }

    public function updateTimja($id_timja, $data)
    {
        $this->db->where('ID', $id_timja);
        $this->db->update('master.timja', $data);
    }

    public function listTimja()
    {
        $this->db->select('*');
        $this->db->from('master.referensi r');
        $this->db->where('r.JENIS', 102);
        $this->db->where('r.STATUS', 1);
        $query = $this->db->get();
        return $query->result_array();
    }
    ////////////////////////////////////////////////////////AKHIR TIMJA/////////////////////////////////////////////

    // public function SPegawaiAktiv($nip, $data)
    // {
    //   $this->db->where('NIP', $nip);
    //   $this->db->update('master.pegawai', $data);
    // }

    // public function SPegawaiNonAktiv($nip, $data)
    // {
    //   $this->db->where('NIP', $nip);
    //   $this->db->update('master.pegawai', $data);
    // }

    //E-Resep
    public function farmasi()
    {
        $this->db->select('*');
        $this->db->from('master.ruangan');
        $this->db->where(array('STATUS' => 1, 'JENIS' => 5, 'JENIS_KUNJUNGAN' => 11));

        $query = $this->db->get();
        return $query->result_array();
    }

    public function ruanganRskd()
    {
        $this->db->select('ID ID_RUANGAN, JENIS, JENIS_KUNJUNGAN, DESKRIPSI, STATUS');
        $this->db->from('master.ruangan r');
        $this->db->where('r.JENIS', '5');
        $this->db->where('r.STATUS', '1');
        $this->db->where_in('r.JENIS_KUNJUNGAN', array('1', '2', '3', '5', '6', '14', '13', '15'));
        $this->db->order_by('DESKRIPSI ASC');

        $query = $this->db->get();
        return $query->result_array();
    }

    // Ruangan Bedah
    public function ruanganBedah()
    {
        $this->db->select('ID ID_RUANGAN, JENIS, JENIS_KUNJUNGAN, DESKRIPSI, STATUS');
        $this->db->from('master.ruangan r');
        $this->db->where('r.JENIS', '5');
        $this->db->where('r.STATUS', '1');
        $this->db->where_in('r.JENIS_KUNJUNGAN', '6');
        $this->db->order_by('DESKRIPSI ASC');

        $query = $this->db->get();
        return $query->result_array();
    }

    // Ruangan Rawat Jalan
    public function ruanganRawatJalan()
    {
        $this->db->select('ID ID_RUANGAN, JENIS, JENIS_KUNJUNGAN, DESKRIPSI, STATUS');
        $this->db->from('master.ruangan');
        $this->db->where('JENIS', '5');
        $this->db->where('STATUS', '1');
        $this->db->where_in('JENIS_KUNJUNGAN', array(1, 2, 4, 5, 14, 13, 15));
        $this->db->order_by('DESKRIPSI ASC');

        $query = $this->db->get();
        return $query->result_array();
    }

    // Ruangan Rawat Inap
    public function ruanganRawatInap()
    {
        $this->db->select('ID ID_RUANGAN, JENIS, JENIS_KUNJUNGAN, DESKRIPSI, STATUS');
        $this->db->from('master.ruangan r');
        $this->db->where('r.JENIS', '5');
        $this->db->where('r.STATUS', '1');
        $this->db->where_in('r.JENIS_KUNJUNGAN', array(3, 6));
        $this->db->order_by('DESKRIPSI ASC');

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listRawatInapPrtklKemo()
    {
        $query = $this->db->query("SELECT ID ID_RUANGAN, JENIS, JENIS_KUNJUNGAN, DESKRIPSI, STATUS
            FROM master.ruangan r
            WHERE r.JENIS='5' AND r.STATUS='1'
            AND r.JENIS_KUNJUNGAN NOT IN (11,4)
            ORDER BY DESKRIPSI ASC");

        return $query->result_array();
    }

    //PENGGUNA
    public function pengguna()
    {
        $query = $this->db->query("SELECT ap.`*`,mra.DESKRIPSI AGAMA_PEGAWAI, mrj.DESKRIPSI JENIS_KELAMIN_PEGAWAI, mrp.DESKRIPSI PROFESI_PEGAWAI, mrs.DESKRIPSI SMF_PEGAWAI,CONCAT(IF(mp.TEMPAT_LAHIR='','',CONCAT(mp.TEMPAT_LAHIR, ', ')), DATE_FORMAT(mp.TANGGAL_LAHIR,'%d-%m-%Y')) TTL
                               FROM aplikasi.pengguna ap
                               LEFT JOIN master.pegawai mp ON mp.NIP = ap.NIP
                               LEFT JOIN master.referensi mra ON mra.ID = mp.AGAMA AND mra.JENIS = 1
                               LEFT JOIN master.referensi mrj ON mrj.ID = mp.JENIS_KELAMIN AND mrj.JENIS = 2
                               LEFT JOIN master.referensi mrp ON mrp.ID = mp.PROFESI AND mrp.JENIS = 4
                               LEFT JOIN master.referensi mrs ON mrs.ID = mp.SMF AND mrs.JENIS = 26
                               WHERE ap.ID != 1 ORDER BY ap.NAMA");

        return $query;
    }

    // public function SPenggunaAktiv($id_pengguna, $data)
    // {
    //   $this->db->where('ID', $id_pengguna);
    //   $this->db->update('aplikasi.pengguna', $data);
    // }

    // public function SPenggunaNonAktiv($id_pengguna, $data)
    // {
    //   $this->db->where('ID', $id_pengguna);
    //   $this->db->update('aplikasi.pengguna', $data);
    // }

    // Mapping Tindakan
    public function referensiTindakanSimpelPK()
    {
        $this->db->select('rtp.ID ID_REFERENSI, rtp.DESKRIPSI, rtp.TINDAKAN, r.DESKRIPSI RUANGAN, IF(rtp.TINDAKAN = 0, "-", t.NAMA) AS NAMA');
        $this->db->from('master.referensi_tindakan_penunjang rtp');
        $this->db->join('master.ruangan r', 'r.ID = rtp.JENIS_PENUNJANG');
        $this->db->join('master.tindakan t', 't.ID = rtp.TINDAKAN', 'left');
        $this->db->where('rtp.JENIS_PENUNJANG', '105070101');
        $this->db->order_by('rtp.ID');

        $query = $this->db->get();
        if ($query->num_rows() > 0) {
            foreach ($query->result() as $data) {
                $referensi[] = $data;
            }
            return $query;
        }
    }

    // Ruang PK
    public function ruangPK()
    {
        $this->db->select('ID, DESKRIPSI');
        $this->db->from('master.ruangan');
        $this->db->where('JENIS', 5);
        $this->db->where('JENIS_KUNJUNGAN', 4);
        $this->db->where('STATUS', 1);
        $this->db->where('ID !=', 105080101);
        $this->db->where('ID !=', 105080102);
        $query = $this->db->get();
        return $query->result_array();
    }

    public function referensiTindakanSimpelRadiologi()
    {
        $this->db->select('rtp.ID ID_REFERENSI, rtp.DESKRIPSI, rtp.TINDAKAN, r.DESKRIPSI RUANGAN, IF(rtp.TINDAKAN = 0, "-", t.NAMA) AS NAMA');
        $this->db->from('master.referensi_tindakan_penunjang rtp');
        $this->db->join('master.ruangan r', 'r.ID = rtp.JENIS_PENUNJANG');
        $this->db->join('master.tindakan t', 't.ID = rtp.TINDAKAN', 'left');
        $this->db->where('rtp.JENIS_PENUNJANG', '105100101');
        $this->db->order_by('rtp.ID');

        $query = $this->db->get();
        if ($query->num_rows() > 0) {
            foreach ($query->result() as $data) {
                $referensi[] = $data;
            }
            return $query;
        }
    }

    public function referensiTindakanSimpelProsedur()
    {
        $this->db->select('rtp.ID ID_REFERENSI, rtp.DESKRIPSI, rtp.TINDAKAN, r.DESKRIPSI RUANGAN, IF(rtp.TINDAKAN = 0, "-", t.NAMA) AS NAMA');
        $this->db->from('master.referensi_tindakan_penunjang rtp');
        $this->db->join('master.ruangan r', 'r.ID = rtp.JENIS_PENUNJANG');
        $this->db->join('master.tindakan t', 't.ID = rtp.TINDAKAN', 'left');
        $this->db->where('rtp.JENIS_PENUNJANG', '105060101');
        $this->db->order_by('rtp.ID');

        $query = $this->db->get();
        if ($query->num_rows() > 0) {
            foreach ($query->result() as $data) {
                $referensi[] = $data;
            }
            return $query;
        }
    }

    public function SReferensiAktif($data, $id)
    {
        $update = $this->db->update('master.referensi_tindakan_penunjang', $data, array('id' => $id));

        //return the status
        return $update ? true : false;
    }

    public function SReferensiNonAktif($data, $id)
    {
        $update = $this->db->update('master.referensi_tindakan_penunjang', $data, array('id' => $id));

        //return the status
        return $update ? true : false;
    }

    //Menu SIMRSKD
    public function menuSimrsKd($id, $parent = 0)
    {
        $query = $this->db->query("SELECT am.ID,
                               am.LABEL,am.LINK
                               , IF(amu.ID IS NULL, 0, 1) STATUS
                               , am.PARENT
                               FROM akses_simrskd.MENU_USER amu
                               RIGHT JOIN akses_simrskd.MENU am ON amu.ID_MENU = am.ID AND amu.ID_USER = '$id'
                               WHERE am.PARENT = $parent AND am.ID != 5");

        return $query;
    }

    //Menu Master Ruangan
    public function RuanganSimrsKd($id)
    {
        $query = $this->db->query("SELECT mr.ID
                             , mr.DESKRIPSI
                             , IF(asur.ID IS NULL, 0, 1) STATUS
                             FROM akses_simrskd.USER_RUANGAN asur
                             RIGHT JOIN master.ruangan mr ON asur.ID_RUANGAN = mr.ID AND asur.ID_USER = '$id'
                             ORDER BY mr.ID ASC");
        return $query;
    }

    public function menuAktiv()
    {
        $data = array(
            'ID_MENU' => $this->input->post('id'),
            'ID_USER' => $this->input->post('id_user'),
        );

        return $this->db->insert("akses_simrskd.MENU_USER", $data);
    }

    public function menuNonAkativ()
    {
        $data = array(
            'ID_MENU' => $this->input->post('id'),
            'ID_USER' => $this->input->post('id_user'),
        );
        $this->db->select('t.ID, t.NAMA, t.STATUS');
        $this->db->from('master.tindakan t');
        $this->db->order_by('t.ID');
        return $this->db->delete("akses_simrskd.MENU_USER", $data);
    }

    public function ruanganAktiv()
    {
        $data = array(
            'ID_RUANGAN' => $this->input->post('id'),
            'ID_USER' => $this->input->post('id_user'),
        );

        return $this->db->insert("akses_simrskd.USER_RUANGAN", $data);
    }

    public function ruanganNonAkativ()
    {
        $data = array(
            'ID_RUANGAN' => $this->input->post('id'),
            'ID_USER' => $this->input->post('id_user'),
        );
        $this->db->select('t.ID, t.NAMA, t.STATUS');
        $this->db->from('master.tindakan t');
        $this->db->order_by('t.ID');
        return $this->db->delete("akses_simrskd.USER_RUANGAN", $data);
    }

    public function menu($parent = 0)
    {
        $this->db->select('m.*');
        $this->db->from('akses_simrskd.MENU_USER mu');
        $this->db->join('akses_simrskd.MENU m', 'mu.ID_MENU = m.ID');
        $this->db->where(array('mu.ID_USER' => $_SESSION['id'], 'm.PARENT' => $parent, 'mu.STATUS !=' => 0));
        $this->db->order_by('m.SEQ');

        $query = $this->db->get();
        return $query;
    }

    // Ruangan SIMRSKD
    public function tindakanSimpelPK()
    {
        $this->db->select(
            't.ID ID_TINDAKAN_SIMPEL, t.NAMA TINDAKAN_SIMPEL, t.STATUS STATUS_TINDAKAN_SIMPEL,
            tr.STATUS STATUS_TINDAKAN_RUANGAN, r.DESKRIPSI RUANGAN, tt.TARIF'
        );
        $this->db->from('master.tindakan_ruangan tr');
        $this->db->join('master.tindakan t', 't.ID = tr.TINDAKAN', 'left');
        $this->db->join('master.tarif_tindakan tt', 'tt.TINDAKAN = t.ID', 'left');
        $this->db->join('master.ruangan r', 'r.ID = tr.RUANGAN');
        $this->db->where('tr.RUANGAN', '105070101');
        $this->db->where('tr.STATUS', '1');
        $this->db->group_by('t.ID');
        $this->db->order_by('tr.STATUS DESC', 't.ID ASC');

        $query = $this->db->get();
        return $query->result_array();
    }

    public function tindakanSimpelRadiologi()
    {
        $this->db->select('t.ID ID_TINDAKAN_SIMPEL, t.NAMA TINDAKAN_SIMPEL, t.STATUS STATUS_TINDAKAN_SIMPEL, tr.STATUS STATUS_TINDAKAN_RUANGAN, r.DESKRIPSI RUANGAN, tt.TARIF');
        $this->db->from('master.tindakan_ruangan tr');
        $this->db->join('master.tindakan t', 't.ID = tr.TINDAKAN', 'left');
        $this->db->join('master.tarif_tindakan tt', 'tt.TINDAKAN = t.ID', 'left');
        $this->db->join('master.ruangan r', 'r.ID = tr.RUANGAN');
        $this->db->where('tr.RUANGAN', '105100101');
        $this->db->where('tr.STATUS', '1');
        $this->db->group_by('t.ID');
        $this->db->order_by('tr.STATUS DESC', 't.ID ASC');

        $query = $this->db->get();
        return $query->result_array();
    }

    public function tindakanSimpelProsedur()
    {
        $this->db->select('t.ID ID_TINDAKAN_SIMPEL, t.NAMA TINDAKAN_SIMPEL, t.STATUS STATUS_TINDAKAN_SIMPEL, tr.STATUS STATUS_TINDAKAN_RUANGAN, r.DESKRIPSI RUANGAN, tt.TARIF');
        $this->db->from('master.tindakan_ruangan tr');
        $this->db->join('master.tindakan t', 't.ID = tr.TINDAKAN', 'left');
        $this->db->join('master.tarif_tindakan tt', 'tt.TINDAKAN = t.ID', 'left');
        $this->db->join('master.ruangan r', 'r.ID = tr.RUANGAN');
        $this->db->where('tr.RUANGAN', '105060101');
        $this->db->where('tr.STATUS', '1');
        $this->db->group_by('t.ID');
        $this->db->order_by('tr.STATUS DESC', 't.ID ASC');

        $query = $this->db->get();
        return $query->result_array();
    }

    // List Kasus
    public function listKasus()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('ref.id_referensi=', 257);

        $query = $this->db->get();
        return $query->result_array();
    }

    // List Rencana
    public function listRencana()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('ref.id_referensi=', 260);

        $query = $this->db->get();
        return $query->result_array();
    }

    // List Perbaikan keadaan umum
    public function listPerbaikanKeadaanUmum()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('ref.id_referensi=', 261);

        $query = $this->db->get();
        return $query->result_array();
    }

    // List Tindakan
    public function listTindakan()
    {
        $this->db->select('masTi.ID, masTi.NAMA');
        $this->db->from('master.tindakan masTi');
        $this->db->where('masTi.status=', 1);

        $query = $this->db->get();
        return $query->result_array();
    }

    // List Diet
    public function listDiet()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('ref.id_referensi=', 56);

        $query = $this->db->get();
        return $query->result_array();
    }

    // List Jenis Diet
    public function listJenisDiet()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('ref.id_referensi=', 57);

        $query = $this->db->get();
        return $query->result_array();
    }

    // List ODC
    public function listOdc()
    {
        $this->db->select('ID, DESKRIPSI');
        $this->db->from('master.ruangan r');
        $this->db->where('r.JENIS=', 5);
        $this->db->where('r.STATUS=', 1);
        $this->db->where_in('r.ID', ['105020101', '105020102', '105090102']);

        $query = $this->db->get();
        return $query->result_array();
    }

    //   List Rawat Inap
    public function listRawatInap()
    {
        $this->db->select('ID, DESKRIPSI');
        $this->db->from('master.ruangan r');
        $this->db->where('r.JENIS=', 5);
        $this->db->where('r.`STATUS`=', 1);
        $this->db->where_in('r.ID', ['105010101', '105010201', '105010301', '105010401', '105010501', '105010601', '105010801', '105010901', '105011001', '105011201']);

        $query = $this->db->get();
        return $query->result_array();
    }

    //   List Rawat Khusus
    public function listRawatKhusus()
    {
        $this->db->select('ID, DESKRIPSI');
        $this->db->from('master.ruangan r');
        $this->db->where('r.JENIS=', 5);
        $this->db->where('r.`STATUS`=', 1);
        $this->db->where_in('r.ID', ['105030101', '105030201', '105030301']);

        $query = $this->db->get();
        return $query->result_array();
    }

    // List Intensive Care
    public function listIntensiveCare()
    {
        $this->db->select('ID, DESKRIPSI');
        $this->db->from('master.ruangan r');
        $this->db->where('r.JENIS=', 5);
        $this->db->where('r.`STATUS`=', 1);
        $this->db->where_in('r.ID', ['105010701', '105011101']);

        $query = $this->db->get();
        return $query->result_array();
    }

    // List Ruangan Khusus
    public function listRuangKhusus()
    {
        $this->db->select('ID, DESKRIPSI');
        $this->db->from('master.ruangan r');
        $this->db->where('r.JENIS=', 5);
        $this->db->where('r.`STATUS`=', 1);
        $this->db->where_in('r.ID', ['105011001', '105011301']);

        $query = $this->db->get();
        return $query->result_array();
    }

    // List Transfusi
    public function listTransfusi()
    {
        $this->db->select('d.id ID, d.nama NAMA');
        $this->db->from('db_master.tb_darah d');
        $this->db->limit(20);
        if ($this->input->get('q')) {
            $this->db->like(' d.NAMA', $this->input->get('q'));
        }

        $query = $this->db->get();
        return $query->result_array();
    }

    // List Semua Ruangan
    public function listRuangan()
    {
        $this->db->select('ID, DESKRIPSI');
        $this->db->from('master.ruangan r');
        $this->db->where('r.JENIS=', 5);
        $this->db->where('r.STATUS=', 1);
        $this->db->where_in('r.ID', ['105020101', '105020102', '105090102', '105010701', '105011101', '105030101', '105030201', '105030301', '105030101', '105030201', '105030301', '105010701', '105011101']);

        $query = $this->db->get();
        return $query->result_array();
    }

    // List Obat
    public function listObat()
    {
        $this->db->select('ID, NAMA');
        $this->db->from('inventory.barang ib');
        $this->db->where('ib.`STATUS`=', 1);
        $this->db->limit(20);
        if ($this->input->get('q')) {
            $this->db->like(' ib.NAMA', $this->input->get('q'));
        }
        $query = $this->db->get();
        return $query->result_array();
    }

    // List Palliative
    public function listPalliative()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where_in('var.id_variabel', ['1569', '1570', '1571', '1573', '1574']);

        $query = $this->db->get();
        return $query->result_array();
    }

    //List Riwayat Kesehatan
    public function listRiwayatKesehatan()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 487);

        $query = $this->db->get();
        return $query->result_array();
    }

    //List Serah Terima Data Pasien IGD
    public function listserahTerimaDataPasienIGD()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 648);

        $query = $this->db->get();
        return $query->result_array();
    }

    //List Alat Terpasang IGD
    public function listAlatTerpasangIGD()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 649);

        $query = $this->db->get();
        return $query->result_array();
    }

    //List Masalah Asuhan IGD
    public function listMasalahAsuhanIGD()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 650);

        $query = $this->db->get();
        return $query->result_array();
    }

    //List Shift IGD
    public function listShiftIGD()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 654);

        $query = $this->db->get();
        return $query->result_array();
    }

    // persetujuan transfusi darah
    public function dasarDiagnosis()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 988);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function indikasiTindakan()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 989);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function tujuanTindakan()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 990);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function tujuanPengobatan()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 991);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function risikoTTD()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 992);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function komplikasiTTD()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 993);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function prognosisTTD()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 994);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function alternatifRisikoTTD()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 995);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function transfusiDarahTTD()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 996);

        $query = $this->db->get();
        return $query->result_array();
    }

    //List Kesadaran IGD
    public function listKesadaranIGD()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 656);

        $query = $this->db->get();
        return $query->result_array();
    }

    //List Resiko Jatuh IGD
    public function listResikoJatuhIGD()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 660);

        $query = $this->db->get();
        return $query->result_array();
    }

    //List Oksigen IGD
    public function listOksigenIGD()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 663);

        $query = $this->db->get();
        return $query->result_array();
    }

    //List Status Pasien [rekonsiliasi obat]
    public function listStatusPasienRekonsiliasi()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 827);

        $query = $this->db->get();
        return $query->result_array();
    }

    //List Riwayat Alergi Obat/Makanan [rekonsiliasi obat]
    public function listRiwayatAlergiObat()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 828);

        $query = $this->db->get();
        return $query->result_array();
    }

    //List Perencanaan Asuhan
    public function listPerencanaanAsuhan()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 512);

        $query = $this->db->get();
        return $query->result_array();
    }

    //List Kausatif
    public function listKausatif()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 508);

        $query = $this->db->get();
        return $query->result_array();
    }

    //List Intervensi
    public function listPenghentianIntervensi()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 509);

        $query = $this->db->get();
        return $query->result_array();
    }

    //List Kondisi Psikologis
    public function listKondisiPsikologis()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 488);

        $query = $this->db->get();
        return $query->result_array();
    }

    //List Mengerti Kondisi Akhir Hidup
    public function listKondisi()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 489);

        $query = $this->db->get();
        return $query->result_array();
    }

    //List Mengerti Kondisi Akhir Hidup Pasien
    public function listKondisiPasien()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 490);

        $query = $this->db->get();
        return $query->result_array();
    }

    //List Mengerti Kondisi Akhir Hidup Pasien
    public function listKondisiKeluarga()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 491);

        $query = $this->db->get();
        return $query->result_array();
    }

    //List Berduka
    public function listBerduka()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 492);
        $this->db->order_by('var.id_variabel', 'DESC');

        $query = $this->db->get();
        return $query->result_array();
    }

    //List Berduka
    public function listBerdukaPasien()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 493);
        $this->db->order_by('var.id_variabel', 'ASC');

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listBerdukaKeluarga()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 494);
        $this->db->order_by('var.id_variabel', 'ASC');

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listPotensiReaksi()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 496);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listPasienPsikolog()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 497);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listKeluargaPsikolog()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 498);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listKebutuhanSpiritual()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 499);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listKebutuhanSpiritualPasien()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 500);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listKebutuhanSpiritualKeluarga()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 501);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listKebutuhanPendukung()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 502);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listTerapiKomplementer()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 503);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listMembutuhkanCaregiver()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 504);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listPerawatanDirumah()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 505);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listIntervensi()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 506);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listMasalahKeperawatan()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.id_referensi', 507);

        $query = $this->db->get();
        return $query->result_array();
    }

    // List Jenis Diet
    public function listJenisObat()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('ref.id_referensi=', 270);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listPengenceranObat()
    {
        $this->db->select('ID, NAMA');
        $this->db->from('inventory.barang ib');
        $this->db->where('ib.`STATUS`=', 1);
        $this->db->where('ib.`NAMA` like', '%NACL%');
        $query = $this->db->get();
        return $query->result_array();
    }

    public function refPemberianObatProtokol()
    {
        $this->db->select('var.id_variabel, var.id_referensi, var.variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('ref.id_referensi=', 279);

        $query = $this->db->get();
        return $query->result_array();
    }

    // List Jam
    public function listJam()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('ref.id_referensi=', 277);

        $query = $this->db->get();
        return $query->result_array();
    }

    // List History Dirawat
    public function listHistoryDirawat()
    {
        $this->db->select('pr.created_at TANGGAL, pp.NORM, mp.NAMA, pr.tanggal TANGGAL_DIRAWAT');
        $this->db->from('medis.tb_permintaan_rawat pr');
        $this->db->join('pendaftaran.kunjungan pk', 'pr.kunjungan = pk.NOMOR', 'left');
        $this->db->join('pendaftaran.pendaftaran pp', 'pk.NOPEN = pp.NOMOR', 'left');
        $this->db->join('master.pasien mp', 'pp.NORM = mp.NORM', 'left');
        $query = $this->db->get();
        return $query->result_array();
    }

    // List Persiapan Bronkoskopi
    public function listPersiapanBronkoskopi1()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel, var.status_checked');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('ref.id_referensi=', 339);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listPersiapanBronkoskopi2()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel, var.status_checked');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('ref.id_referensi=', 340);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listPersiapanBronkoskopi3()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel, var.status_checked');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('ref.id_referensi=', 341);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listPersiapanBronkoskopi4()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel, var.status_checked');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('ref.id_referensi=', 342);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listPersiapanBronkoskopi5()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel, var.status_checked');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('ref.id_referensi=', 343);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listPersiapanBronkoskopi6()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel, var.status_checked');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('ref.id_referensi=', 344);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listPersiapanBronkoskopi7()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel, var.status_checked');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('ref.id_referensi=', 345);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listBronkoskopi()
    {
        $query = $this->db->query("select ref.id_referensi id, ref.referensi bronkoskopi, ya.variabel ya
        , ya.id_variabel id_ya, tidak.variabel tidak, tidak.id_variabel id_tidak
        from db_master.referensi ref
        left join db_master.variabel ya on ref.id_referensi = ya.id_referensi AND ya.status_checked=0
        left join db_master.variabel tidak on ref.id_referensi = tidak.id_referensi AND tidak.status_checked=1
        where ref.id_referensi in(339,340,341,342,343,344,345)");

        return $query->result_array();
    }

    public function listComboBronkoskopiYa()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel, var.status_checked');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('var.status_checked=', 0);
        $this->db->where_in('ref.id_referensi', [339, 340, 341, 342, 343, 344, 345]);

        $query = $this->db->get();
        return $query->result_array();
    }

    // List Persiapan Gastroskopi
    public function listPersiapanGastroskopi1()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel, var.status_checked');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('ref.id_referensi=', 353);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listPersiapanGastroskopi2()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel, var.status_checked');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('ref.id_referensi=', 354);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listPersiapanGastroskopi3()
    {
        $this->db->select('var.id_referensi, var.variabel, var.id_variabel, var.status_checked');
        $this->db->from('db_master.referensi ref');
        $this->db->join('db_master.variabel var', 'ref.id_referensi = var.id_referensi', 'left');
        $this->db->where('ref.id_referensi=', 355);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listKesan()
    {
        $this->db->select('var2.id_variabel, var2.variabel');
        $this->db->from('db_master.referensi ref2');
        $this->db->join('db_master.variabel var2', 'ref2.id_referensi = var2.id_referensi', 'left');
        $this->db->where('ref2.id_referensi=', 389);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listRestriksi()
    {
        $this->db->select('var2.id_variabel, var2.variabel');
        $this->db->from('db_master.referensi ref2');
        $this->db->join('db_master.variabel var2', 'ref2.id_referensi = var2.id_referensi', 'left');
        $this->db->where('ref2.id_referensi=', 390);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listObstruksi()
    {
        $this->db->select('var2.id_variabel, var2.variabel');
        $this->db->from('db_master.referensi ref2');
        $this->db->join('db_master.variabel var2', 'ref2.id_referensi = var2.id_referensi', 'left');
        $this->db->where('ref2.id_referensi=', 391);

        $query = $this->db->get();
        return $query->result_array();
    }

    // LTBG
    public function listRadiografer()
    {
        $this->db->select('peg.NIP, master.getNamaLengkapPegawai(peg.NIP) NAMA');
        $this->db->from('master.pegawai peg');
        $this->db->where('peg.PROFESI =', 8);
        $this->db->where('peg.STATUS =', 1);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function listFisikaMedis()
    {
        $this->db->select('peg.NIP, master.getNamaLengkapPegawai(peg.NIP) NAMA');
        $this->db->from('master.pegawai peg');
        $this->db->where('peg.PROFESI =', 17);
        $this->db->where('peg.STATUS =', 1);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function soap()
    {
        $query = $this->db->query("SELECT * FROM master.tb_gambar_soap");

        return $query->result_array();
    }

    public function getPictSoap($idGambar)
    {
        $query = $this->db->query("SELECT file FROM master.tb_gambar_soap WHERE id= '$idGambar'");
        return $query->row_array();
    }

    public function protokolKemo()
    {
        $this->db->select("*");
        $this->db->from("db_master.tb_protokol_kemo");
        $query = $this->db->get();
        if ($query->num_rows() > 0) {
            foreach ($query->result() as $data) {
                $referensi[] = $data;
            }
            return $query;
        }
    }

    // Detil protokol kemoterapi model ( Hasan )
    public function detilPKemoMod($id)
    {
        $query = $this->db->query("SELECT pk.ID ID_PROTOKOL, pk.NAMA NAMA_PROTOKOL, pkd.ID ID_DETAIL, pkd.JENIS
        , IF(pkd.JENIS=1,'Default',IF(pkd.JENIS=2,'Premedikasi','Bilas')) JENIS_DESK
        , pkd.OBAT_PROTOKOL ID_OBAT_PROTOKOL, op.NAMA_OBAT,pkd.DOSIS_PROTOKOL DOSIS, op.SEDIAAN MG_ML
        , pkd.PENGENCERAN ID_OBAT_PENGENCERAN, opp.NAMA_OBAT OBAT_PENGENCERAN, pkd.DOSIS_PENGENCERAN, opp.SEDIAAN
        , pkd.AKSES_PEMBERIAN ID_AKSES_PEMBERIAN, aks.variabel AKSES_PEMBERIAN, pkd.KECEPATAN, pkd.CHECKED_STATUS

        FROM db_master.tb_protokol_kemo pk

        LEFT JOIN db_master.tb_protokol_kemo_detil pkd ON pkd.PAKET = pk.ID
        LEFT JOIN db_master.tb_obat_protokol op ON op.ID = pkd.OBAT_PROTOKOL
        LEFT JOIN db_master.tb_obat_protokol opp ON opp.ID = pkd.PENGENCERAN
        LEFT JOIN db_master.variabel aks ON aks.id_variabel = pkd.AKSES_PEMBERIAN

        WHERE pk.`STATUS`=1 AND pkd.`STATUS`=1 AND op.`STATUS`=1
        AND pk.ID='$id'
        ORDER BY pkd.ID ASC
        ");
        // $query = $this->db->get();
        if ($query->num_rows() > 0) {
            foreach ($query->result() as $data) {
                $referensi[] = $data;
            }
            return $query->result_array();
        }
    }

    public function detilPKemoModInfo($id)
    {
        $query = $this->db->query("SELECT pk.ID ID_PROTOKOL, pk.NAMA NAMA_PROTOKOL, pkd.ID ID_DETAIL, pkd.JENIS
        , IF(pkd.JENIS=1,'Default',IF(pkd.JENIS=2,'Premedikasi','Bilas')) JENIS_DESK
        , pkd.OBAT_PROTOKOL ID_OBAT_PROTOKOL, op.NAMA_OBAT,pkd.DOSIS_PROTOKOL DOSIS, op.SEDIAAN MG_ML
        , pkd.PENGENCERAN ID_OBAT_PENGENCERAN, opp.NAMA_OBAT OBAT_PENGENCERAN, pkd.DOSIS_PENGENCERAN, opp.SEDIAAN
        , pkd.AKSES_PEMBERIAN ID_AKSES_PEMBERIAN, aks.variabel AKSES_PEMBERIAN, pkd.KECEPATAN, pkd.CHECKED_STATUS

        FROM db_master.tb_protokol_kemo pk

        LEFT JOIN db_master.tb_protokol_kemo_detil pkd ON pkd.PAKET = pk.ID
        LEFT JOIN db_master.tb_obat_protokol op ON op.ID = pkd.OBAT_PROTOKOL
        LEFT JOIN db_master.tb_obat_protokol opp ON opp.ID = pkd.PENGENCERAN
        LEFT JOIN db_master.variabel aks ON aks.id_variabel = pkd.AKSES_PEMBERIAN

        WHERE pk.`STATUS`=1 AND pkd.`STATUS`=1 AND op.`STATUS`=1
        AND pk.ID='$id'
        ORDER BY pkd.ID ASC
        ");
        // $query = $this->db->get();
        if ($query->num_rows() > 0) {
            foreach ($query->result() as $data) {
                $referensi[] = $data;
            }
            // return $query->result_array();
            return $result = $query->row_array();
        }
    }

    public function detailObatProtokol($id)
    {
        $this->db->select("pkd.ID ID_PROTOKOL_KEMO_DETIL
        , pk.ID ID_PAKET, pk.NAMA NAMA_PAKET
        , pkd.JENIS, IF(pkd.JENIS=1, 'Default'
        , IF(pkd.JENIS=2, 'Premedikasi','Bilas')) JENIS_DESKRIPSI
        , pkd.FARMASI ID_FARMASI_OBAT , ib.NAMA NAMA_OBAT
        , ibp.NAMA PENGENCERAN, pkd.AKSES_PEMBERIAN ID_AKSES_PEMBERIAN, akpem.variabel AKSES_PEMBERIAN
        , pkd.KECEPATAN
        , pkd.KETERANGAN, pkd.`STATUS` STATUS_PROTOKOL_KEMO_DETIL ");
        $this->db->from("db_master.tb_protokol_kemo_detil pkd");
        $this->db->join('db_master.tb_protokol_kemo pk', 'pk.ID = pkd.PAKET', 'left');
        $this->db->join('db_master.variabel akpem', 'akpem.id_variabel = pkd.AKSES_PEMBERIAN', 'left');
        $this->db->join('inventory.barang ib', 'ib.ID = pkd.FARMASI', 'left');
        $this->db->join('inventory.barang ibp', 'ibp.ID = pkd.PENGENCERAN', 'left');
        // $this->db->join('inventory.barang_ruangan br', 'br.ID = pkd.BARANG_RUANGAN', 'left');
        // $this->db->join('master.ruangan ruang', 'ruang.ID = br.RUANGAN', 'left');
        $this->db->where('pkd.`STATUS`=', 1);
        $this->db->where('pk.ID=', $id);

        $query = $this->db->get();
        return $query->result_array();
    }

    public function tindakan($id)
    {
        $this->db->select('t.ID,t.NAMA');
        $this->db->from('master.tindakan t');
        $this->db->join('master.tindakan_ruangan tr', 't.ID = tr.TINDAKAN', 'left');
        $this->db->where('tr.RUANGAN', $id);
        $this->db->where('t.`STATUS`!=', 0);
        $query = $this->db->get();
        return $query->result_array();
    }

    public function siteMarking()
    {
        $query = $this->db->query("SELECT * FROM master.tb_site_marking WHERE status = 1 ");

        return $query->result_array();
    }

    public function getPictSiteMarking($idGambar)
    {
        $query = $this->db->query("SELECT file FROM master.tb_site_marking WHERE id= '$idGambar'");
        return $query->row_array();
    }

    public function gambar_simulator()
    {
        $query = $this->db->query("SELECT * FROM master.tb_gambar_simulator WHERE status = 1 ");

        return $query->result_array();
    }

    public function getPictSimulatorInforation($idGambar)
    {
        $query = $this->db->query("SELECT file FROM master.tb_gambar_simulator WHERE id= '$idGambar'");
        return $query->row_array();
    }

    public function dataDiriPasien($nomr)
    {
        $query = $this->db->query(
            "SELECT mp.*, master.getNamaLengkap(mp.NORM) NAMAPASIEN, pp.NOMOR NOPEN, pk.NOMOR NOKUN,
            CONCAT(master.getCariUmurTahun(pp.TANGGAL, mp.TANGGAL_LAHIR), ' Tahun') UMUR, mk.NOMOR tlpn
            FROM master.pasien mp
            LEFT JOIN pendaftaran.pendaftaran pp ON pp.NORM = mp.NORM
            LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = pp.NOMOR
            LEFT JOIN master.kontak_pasien mk ON mk.NORM = mp.NORM
            WHERE mp.NORM = '$nomr'
            ORDER BY pp.NOMOR DESC
            LIMIT 1"
        );
        return $query->row_array();
    }

    public function parameterPews()
    {
        $query = $this->db->query("SELECT *
                               FROM db_master.referensi mr
                               WHERE mr.id_referensi in (808,809,810) AND mr.`status` = 1");

        return $query->result_array();
    }

    public function listCairan()
    {
        $this->db->select('*');
        $this->db->from('inventory.barang b');
        $this->db->where_in('b.KATEGORI', array('10103', '101002', '10102', '10115'));
        $this->db->where('b.`STATUS` = 1');
        $this->db->limit(20);
        if ($this->input->get('q')) {
            $this->db->like(' b.NAMA', $this->input->get('q'));
        }

        $query = $this->db->get();
        return $query->result_array();
    }

    public function ruanganIntensif()
    {
        $query = $this->db->query("SELECT * FROM master.ruangan r WHERE r.JENIS = 5 AND r.JENIS_KUNJUNGAN = 3 AND r.`STATUS` = 1
        ");

        return $query->result_array();
    }

    public function listDarahLengkap($nomr)
    {
        $query = $this->db->query("SELECT hlo.ID ID_HASIL, orla.TANGGAL TANGGAL_ORDER
        , pendK.MASUK TGL_MASUK_LAB
        , hlo.HIS_NO_LAB NOKUN_LAB, p.NOMOR NOPENS, p.NORM
        , master.getNamaLengkap(p.NORM) NAMA_PASIEN
        , mt.ID ID_TINDAKAN_SIMPEL, mt.NAMA TINDAKAN_SIMPEL
        , hlo.LIS_NAMA_TEST PARAMETER, hlo.LIS_NILAI_NORMAL NILAI_RUJUKAN, hlo.LIS_SATUAN
        , hlo.LIS_HASIL, hlo.LIS_CATATAN, hlo.LIS_TANGGAL TGL_HASIL

            FROM lis.hasil_log hlo
                LEFT JOIN pendaftaran.kunjungan pendK ON pendK.NOMOR = hlo.HIS_NO_LAB
                LEFT JOIN master.tindakan mt ON mt.ID = hlo.HIS_KODE_TEST
                LEFT JOIN layanan.order_lab orla ON orla.NOMOR=pendK.REF
                LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pendK.NOPEN
            WHERE p.NORM ='$nomr' AND mt.ID IN (1439,1435,1430)
            ORDER BY hlo.LIS_TANGGAL desc LIMIT 100
            #GROUP BY mt.NAMA, hlo.LIS_NAMA_TEST");

        return $query->result_array();
    }

    public function listFungsiHati($nomr)
    {
        $query = $this->db->query("SELECT hlo.ID ID_HASIL, orla.TANGGAL TANGGAL_ORDER
        , pendK.MASUK TGL_MASUK_LAB
        , hlo.HIS_NO_LAB NOKUN_LAB, p.NOMOR NOPENS, p.NORM
        , master.getNamaLengkap(p.NORM) NAMA_PASIEN
        , mt.ID ID_TINDAKAN_SIMPEL, mt.NAMA TINDAKAN_SIMPEL
        , hlo.LIS_NAMA_TEST PARAMETER, hlo.LIS_NILAI_NORMAL NILAI_RUJUKAN, hlo.LIS_SATUAN
        , hlo.LIS_HASIL, hlo.LIS_CATATAN, hlo.LIS_TANGGAL TGL_HASIL

            FROM lis.hasil_log hlo
                LEFT JOIN pendaftaran.kunjungan pendK ON pendK.NOMOR = hlo.HIS_NO_LAB
                LEFT JOIN master.tindakan mt ON mt.ID = hlo.HIS_KODE_TEST
                LEFT JOIN layanan.order_lab orla ON orla.NOMOR=pendK.REF
                LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pendK.NOPEN
            WHERE p.NORM ='$nomr' AND mt.ID IN (1482,1481,1484,1493,1485,1487,1488,1490,5309,169)
            ORDER BY hlo.LIS_TANGGAL desc LIMIT 100
            #GROUP BY mt.NAMA, hlo.LIS_NAMA_TEST");

        return $query->result_array();
    }

    public function listFungsiGinjal($nomr)
    {
        $query = $this->db->query("SELECT hlo.ID ID_HASIL, orla.TANGGAL TANGGAL_ORDER
        , pendK.MASUK TGL_MASUK_LAB
        , hlo.HIS_NO_LAB NOKUN_LAB, p.NOMOR NOPENS, p.NORM
        , master.getNamaLengkap(p.NORM) NAMA_PASIEN
        , mt.ID ID_TINDAKAN_SIMPEL, mt.NAMA TINDAKAN_SIMPEL
        , hlo.LIS_NAMA_TEST PARAMETER, hlo.LIS_NILAI_NORMAL NILAI_RUJUKAN, hlo.LIS_SATUAN
        , hlo.LIS_HASIL, hlo.LIS_CATATAN, hlo.LIS_TANGGAL TGL_HASIL

            FROM lis.hasil_log hlo
                LEFT JOIN pendaftaran.kunjungan pendK ON pendK.NOMOR = hlo.HIS_NO_LAB
                LEFT JOIN master.tindakan mt ON mt.ID = hlo.HIS_KODE_TEST
                LEFT JOIN layanan.order_lab orla ON orla.NOMOR=pendK.REF
                LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pendK.NOPEN
            WHERE p.NORM ='$nomr' AND mt.ID IN (1511,1515,1517,1521,1549)
            ORDER BY hlo.LIS_TANGGAL desc LIMIT 100
            #GROUP BY mt.NAMA, hlo.LIS_NAMA_TEST");

        return $query->result_array();
    }

    public function listMarkerInfeksi($nomr)
    {
        $query = $this->db->query("SELECT hlo.ID ID_HASIL, orla.TANGGAL TANGGAL_ORDER
        , pendK.MASUK TGL_MASUK_LAB
        , hlo.HIS_NO_LAB NOKUN_LAB, p.NOMOR NOPENS, p.NORM
        , master.getNamaLengkap(p.NORM) NAMA_PASIEN
        , mt.ID ID_TINDAKAN_SIMPEL, mt.NAMA TINDAKAN_SIMPEL
        , hlo.LIS_NAMA_TEST PARAMETER, hlo.LIS_NILAI_NORMAL NILAI_RUJUKAN, hlo.LIS_SATUAN
        , hlo.LIS_HASIL, hlo.LIS_FLAG, hlo.LIS_CATATAN, hlo.LIS_TANGGAL TGL_HASIL

            FROM lis.hasil_log hlo
                LEFT JOIN pendaftaran.kunjungan pendK ON pendK.NOMOR = hlo.HIS_NO_LAB
                LEFT JOIN master.tindakan mt ON mt.ID = hlo.HIS_KODE_TEST
                LEFT JOIN layanan.order_lab orla ON orla.NOMOR=pendK.REF
                LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pendK.NOPEN
            WHERE p.NORM ='$nomr' AND mt.ID IN (5232,5233,5279,5300,5301,5302
                    ,5242,1183,447,1181,1390,1375
                    ,1581,449,127,1405,5317,1179
                    ,131,560,76,55,63,2102,1606,5368)
            ORDER BY hlo.LIS_TANGGAL desc LIMIT 100");

        return $query->result_array();
    }

    public function listKultur($nomr)
    {
        $query = $this->db->query("SELECT hlo.ID ID_HASIL, orla.TANGGAL TANGGAL_ORDER
        , pendK.MASUK TGL_MASUK_LAB
        , hlo.HIS_NO_LAB NOKUN_LAB, p.NOMOR NOPENS, p.NORM
        , master.getNamaLengkap(p.NORM) NAMA_PASIEN
        , mt.ID ID_TINDAKAN_SIMPEL, mt.NAMA TINDAKAN_SIMPEL
        , hlo.LIS_NAMA_TEST PARAMETER, hlo.LIS_NILAI_NORMAL NILAI_RUJUKAN, hlo.LIS_SATUAN
        , hlo.LIS_HASIL, hlo.LIS_CATATAN, hlo.LIS_TANGGAL TGL_HASIL

            FROM lis.hasil_log hlo
                LEFT JOIN pendaftaran.kunjungan pendK ON pendK.NOMOR = hlo.HIS_NO_LAB
                LEFT JOIN master.tindakan mt ON mt.ID = hlo.HIS_KODE_TEST
                LEFT JOIN layanan.order_lab orla ON orla.NOMOR=pendK.REF
                LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pendK.NOPEN
            WHERE p.NORM ='$nomr' AND mt.ID IN (1541,1560,1563,1569)
            ORDER BY hlo.LIS_TANGGAL desc
            LIMIT 100
            #GROUP BY mt.NAMA, hlo.LIS_NAMA_TEST");

        return $query->result_array();
    }

    public function jalurPemberian()
    {
        if ($this->input->get('q')) {
            $this->db->like('dv.variabel', $this->input->get('q'));
        }
        $this->db->select('*');
        $this->db->from('db_master.variabel dv');
        $this->db->where('dv.id_referensi', 901);
        $this->db->where('dv.status', 1);
        if ($this->input->get('id_variabel')) {
            $this->db->where('dv.id_variabel', $this->input->get('id_variabel'));
        }


        $query = $this->db->get();
        return $query->result_array();
    }



    public function radiografer()
    {
        $squery = "SELECT ap.ID idpengguna, master.getNamaLengkapPegawai(peg.NIP) nama
        FROM master.perawat_ruangan pr
        LEFT JOIN master.perawat p ON p.ID = pr.PERAWAT
        LEFT JOIN master.pegawai peg ON peg.NIP = p.NIP
        LEFT JOIN aplikasi.pengguna ap ON ap.NIP=peg.NIP AND ap.status = 1
        WHERE pr.RUANGAN ='105120101' #RUANG RADIOTERAPI
        AND pr.STATUS=1 AND p.STATUS=1
        AND peg.PROFESI = 8 #PERAWAT 6 #RADIOGRAFER 8
        AND ap.ID IS NOT NULL
        GROUP BY p.ID
        ORDER BY peg.NAMA ASC";

        $query = $this->db->query($squery);
        return $query->result_array();
    }


    public function fismed()
    {
        $squery = "SELECT ap.ID idpengguna, master.getNamaLengkapPegawai(peg.NIP) nama
        FROM master.staff_ruangan sr
        LEFT JOIN master.staff st ON st.ID = sr.STAFF
        LEFT JOIN master.pegawai peg ON peg.NIP = st.NIP
        LEFT JOIN aplikasi.pengguna ap ON ap.NIP = st.NIP
        WHERE sr.RUANGAN ='105120101' #RUANG RADIOTERAPI
        AND sr.STATUS=1 AND st.STATUS=1
        AND peg.PROFESI = 17 #PROFESI FISIKAWAN MEDIS
        GROUP BY st.ID
        ORDER BY peg.NAMA ASC";

        $query = $this->db->query($squery);
        return $query->result_array();
    }

    public function getKamarbyNokun($nokun) {
        $this->db->select('mref.ID, mref.DESKRIPSI');
        $this->db->from('pendaftaran.kunjungan pk');
        $this->db->join('master.ruang_kamar_tidur rkt', 'pk.RUANG_KAMAR_TIDUR = rkt.ID', 'left');
        $this->db->join('master.ruang_kamar rk', 'rkt.RUANG_KAMAR = rk.ID', 'left');
        $this->db->join('master.referensi mref', 'rk.KELAS = mref.ID AND mref.JENIS = 19', 'left');
        $this->db->where('pk.NOMOR', $nokun);
        $query = $this->db->get();
        // if (!$query) {
        //   log_message('error', 'Query error: ' . $this->db->last_query());
        //   return [];
        // }
        
        return $query->result();
      }
}

/* End of file MasterModel.php */
/* Location: ./application/models/MasterModel.php */
<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class FormulirSkriningVisual extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'formulirSkriningVisualModel', 'EresepModel', 'pengkajianAwalModel'));
  }

  public function index()
  {
    $rawat = "inap";
    $pinereIgd = $this->masterModel->referensi(468);
    $tuberkolosisIgd = $this->masterModel->referensi(469);
    $mobilisasiIgd = $this->masterModel->referensi(470);
    $resikoJatuhIgd = $this->masterModel->referensi(471);
    $labIgd = $this->masterModel->referensi(472);
    $radIgd = $this->masterModel->referensi(473);
    $paIgd = $this->masterModel->referensi(474);
    $ditujukanIgd = $this->masterModel->referensi(475);
    $kebutuhanPelayananIgd = $this->masterModel->referensi(476);
    $dirujukIgd = $this->masterModel->referensi(477);

    $nokun = $this->uri->segment(5);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $nomr =  $getNomr['NORM'];
    $listHistorySv = $this->formulirSkriningVisualModel->tblHistorySvInap($nomr);

    $data = array(
      'pinereIgd' => $pinereIgd,
      'rawat' => $rawat,
      'tuberkolosisIgd' => $tuberkolosisIgd,
      'mobilisasiIgd' => $mobilisasiIgd,
      'resikoJatuhIgd' => $resikoJatuhIgd,
      'labIgd' => $labIgd,
      'radIgd' => $radIgd,
      'paIgd' => $paIgd,
      'ditujukanIgd' => $ditujukanIgd,
      'kebutuhanPelayananIgd' => $kebutuhanPelayananIgd,
      'dirujukIgd' => $dirujukIgd,
      'listHistorySv' => $listHistorySv,
    );

    $this->load->view('Pengkajian/igd/formulirSkriningVisual/index', $data);
  }

  public function ditujukanIgd()
  {
    $id = $this->input->post("id");
    $rn = $this->input->post("ruangan");

    if($id == 1558){
      $ruangan = $this->masterModel->ruanganRawatJalan();
    }else{
      $ruangan = $this->masterModel->ruanganRawatInap();
    }

    echo "<option disabled selected>Pilih Ruangan</option>";
    foreach ($ruangan as $ruangan ) {
      $rr = isset($rn) ? $ruangan['ID_RUANGAN'] == $rn ? "selected" : "" :"" ;
      echo "<option value='".$ruangan['ID_RUANGAN']."' ".$rr.">".$ruangan['DESKRIPSI']."<option>";
    }
    // echo "<pre>";print_r($ruangan);exit();
  }

  public function simpanSkriningVisual()
  {
    $post = $this->input->post();

    $dataSkriningVisual = array(
      'nokun'                  => $post['nokun'],
      'catatan_pinere'         => $post['hasilPinere'],
      'catatan_tuberkulosis'   => $post['hasilKulosis'],
      'jenis_pasien_baru'      => isset($post['pasienBaru']) ? $post['pasienBaru'] : "",
      'mobilisasi'             => isset($post['mobilisasi']) ? $post['mobilisasi'] : "",
      'resiko_jatuh'           => isset($post['risikoJatuh']) ? $post['risikoJatuh'] : "",
      'lab'                    => isset($post['laboratorium']) ? $post['laboratorium'] : "",
      'lab_desk'               => isset($post['deskPkIgd']) ? $post['deskPkIgd'] : "",
      'radiodiagnostik'        => isset($post['radiodiagnostik']) ? $post['radiodiagnostik'] : "",
      'radiodiagnostik_desk'   => isset($post['deskRadIgd']) ? $post['deskRadIgd'] : "",
      'pa'                     => isset($post['patologiAnatomi']) ? $post['patologiAnatomi'] : "",
      'pa_desk'                => isset($post['deskPaIgd']) ? $post['deskPaIgd'] : "",
      'ditujukan'              => isset($post['ditujukanIgd']) ? $post['ditujukanIgd'] : "",
      'ruangan_ditujukan'      => isset($post['deskDitujukanIgd']) ? $post['deskDitujukanIgd'] : "",
      'kebutuhan_pelayanan'    => isset($post['kebutuhanPelayanan']) ? $post['kebutuhanPelayanan'] : "",
      'dirujuk_keluar'         => isset($post['diRujukKe']) ? $post['diRujukKe'] : "",
      'dirujuk_keluar_lainnya' => isset($post['deskRirujukIgd']) ? $post['deskRirujukIgd'] : "",
      'oleh'                   => $this->session->userdata('id'),
    );
    $getIdSkriningVisual = $this->formulirSkriningVisualModel->simpanSv($dataSkriningVisual);

    $pinere = array();
    $indexPinere = 0;
    if (isset($post['pinere'])) {
      foreach ($post['pinere'] as $input) {
        if ($post['pinere'][$indexPinere] != "") {
          array_push(
            $pinere, array(
              'id_skrining_visual'   => $getIdSkriningVisual,
              'id_variabel_skrining' => $post['pinere'][$indexPinere],
            )
          );
        }
        $indexPinere++;
      }
      $this->db->insert_batch('keperawatan.tb_skrining_visual_pinere', $pinere);
    }

    $tuberkulosis = array();
    $indexTuberkulosis = 0;
    if (isset($post['tuberkulosis'])) {
      foreach ($post['tuberkulosis'] as $input) {
        if ($post['tuberkulosis'][$indexTuberkulosis] != "") {
          array_push(
            $tuberkulosis, array(
              'id_skrining_visual'   => $getIdSkriningVisual,
              'id_variabel_skrining' => $post['tuberkulosis'][$indexTuberkulosis],
            )
          );
        }
        $indexTuberkulosis++;
      }
      $this->db->insert_batch('keperawatan.tb_skrining_visual_tuberkulosis', $tuberkulosis);
    }
  }

  public function tblHistorySv()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->post('nomr');
    $listHistorySv = $this->formulirSkriningVisualModel->tblHistorySv($nomr);

    $data = array();
    $no = 1;
    foreach ($listHistorySv->result() as $historySv) {
      $data[] = array(
        $no,
        $historySv->NORM,
        $historySv->NAMAPASIEN,
        $historySv->NOKUN,
        $historySv->USER,
        date("d-m-Y H:i:s",strtotime($historySv->TANGGAL)),
        '<a href="#modalHistorySv" class="btn btn-sm btn-block btn-primary" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="'.$historySv->id.'"><i class="fas fa-edit"></i> Edit</a>'
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listHistorySv->num_rows(),
      "recordsFiltered" => $listHistorySv->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function detailHistorySv()
  {
    $id = $this->input->post('id');
    $dHistorySv = $this->formulirSkriningVisualModel->detilHistorySv($id);
    // echo "<pre>";print_r($dHistorySv);exit();

    //IGD Skrining Visual
    $pinereIgd             = $this->masterModel->referensi(468);
    $tuberkolosisIgd       = $this->masterModel->referensi(469);
    $mobilisasiIgd         = $this->masterModel->referensi(470);
    $resikoJatuhIgd        = $this->masterModel->referensi(471);
    $labIgd                = $this->masterModel->referensi(472);
    $radIgd                = $this->masterModel->referensi(473);
    $paIgd                 = $this->masterModel->referensi(474);
    $ditujukanIgd          = $this->masterModel->referensi(475);
    $kebutuhanPelayananIgd = $this->masterModel->referensi(476);
    $dirujukIgd            = $this->masterModel->referensi(477);

    $data = array(
      'id'                    => $id,
      'dHistorySv'            => $dHistorySv,
      'pinereIgd'             => $pinereIgd,
      'tuberkolosisIgd'       => $tuberkolosisIgd,
      'mobilisasiIgd'         => $mobilisasiIgd,
      'resikoJatuhIgd'        => $resikoJatuhIgd,
      'labIgd'                => $labIgd,
      'radIgd'                => $radIgd,
      'paIgd'                 => $paIgd,
      'ditujukanIgd'          => $ditujukanIgd,
      'kebutuhanPelayananIgd' => $kebutuhanPelayananIgd,
      'dirujukIgd'            => $dirujukIgd,
    );

    $this->load->view('Pengkajian/igd/formulirSkriningVisual/edit', $data);
  }

  public function updateSkriningVisual()
  {
    $post = $this->input->post();
    $idSkriningVisual = $post['idSkriningVisual'];

    $dataSkriningVisualEdit = array(
      'catatan_pinere'         => $post['hasilPinereEdit'],
      'catatan_tuberkulosis'   => $post['hasilKulosisEdit'],
      'jenis_pasien_baru'      => isset($post['pasienBaruEdit']) ? $post['pasienBaruEdit'] : "",
      'mobilisasi'             => isset($post['mobilisasiEdit']) ? $post['mobilisasiEdit'] : "",
      'resiko_jatuh'           => isset($post['risikoJatuhEdit']) ? $post['risikoJatuhEdit'] : "",
      'lab'                    => isset($post['laboratoriumEdit']) ? $post['laboratoriumEdit'] : "",
      'lab_desk'               => isset($post['deskPkIgdEdit']) ? $post['deskPkIgdEdit'] : "",
      'radiodiagnostik'        => isset($post['radiodiagnostikEdit']) ? $post['radiodiagnostikEdit'] : "",
      'radiodiagnostik_desk'   => isset($post['deskRadIgdEdit']) ? $post['deskRadIgdEdit'] : "",
      'pa'                     => isset($post['patologiAnatomiEdit']) ? $post['patologiAnatomiEdit'] : "",
      'pa_desk'                => isset($post['deskPaIgdEdit']) ? $post['deskPaIgdEdit'] : "",
      'ditujukan'              => isset($post['ditujukanIgdEdit']) ? $post['ditujukanIgdEdit'] : "",
      'ruangan_ditujukan'      => isset($post['deskDitujukanIgdEdit']) ? $post['deskDitujukanIgdEdit'] : "",
      'kebutuhan_pelayanan'    => isset($post['kebutuhanPelayananEdit']) ? $post['kebutuhanPelayananEdit'] : "",
      'dirujuk_keluar'         => isset($post['diRujukKeEdit']) ? $post['diRujukKeEdit'] : "",
      'dirujuk_keluar_lainnya' => isset($post['deskRirujukIgdEdit']) ? $post['deskRirujukIgdEdit'] : "",
      'oleh'                   => $this->session->userdata('id'),
    );

    $this->formulirSkriningVisualModel->updateSv($dataSkriningVisualEdit,$idSkriningVisual);

    $this->db->delete('keperawatan.tb_skrining_visual_pinere', array('id_skrining_visual' => $idSkriningVisual));
    $pinere = array();
    $indexPinere = 0;
    if (isset($post['pinereEdit'])) {
      foreach ($post['pinereEdit'] as $input) {
        if ($post['pinereEdit'][$indexPinere] != "") {
          array_push(
            $pinere, array(
              'id_skrining_visual'   => $idSkriningVisual,
              'id_variabel_skrining' => $post['pinereEdit'][$indexPinere],
            )
          );
        }
        $indexPinere++;
      }
      $this->db->insert_batch('keperawatan.tb_skrining_visual_pinere', $pinere);
    }

    $this->db->delete('keperawatan.tb_skrining_visual_tuberkulosis', array('id_skrining_visual' => $idSkriningVisual));

    $tuberkulosis = array();
    $indexTuberkulosis = 0;
    if (isset($post['tuberkulosisEdit'])) {
      foreach ($post['tuberkulosisEdit'] as $input) {
        if ($post['tuberkulosisEdit'][$indexTuberkulosis] != "") {
          array_push(
            $tuberkulosis, array(
              'id_skrining_visual'   => $idSkriningVisual,
              'id_variabel_skrining' => $post['tuberkulosisEdit'][$indexTuberkulosis],
            )
          );
        }
        $indexTuberkulosis++;
      }
      $this->db->insert_batch('keperawatan.tb_skrining_visual_tuberkulosis', $tuberkulosis);
    }
  }

}

/* End of file FormulirSkriningVisual.php */
/* Location: ./application/controllers/igd/FormulirSkriningVisual.php */

<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Stockart extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }
        $this->load->model('Farmasi/StockartModel', 'StockartModel');
        $this->load->library('form_validation');
        
        // Set timezone to Asia/Jakarta
        date_default_timezone_set('Asia/Jakarta');
    }

    public function index($nomr = null, $nopen = null, $nokun = null)
    {
        $data['nomr'] = $nomr;
        $data['nopen'] = $nopen;
        $data['nokun'] = $nokun;
        $data['title'] = 'Permintaan Stockart';
        $this->load->view('Farmasi/Stockart', $data);
    }

    public function simpan()
    {
        $this->form_validation->set_rules('nokun', 'No. Kunjungan', 'required');
        $items = $this->input->post('items');
        
        if (empty($items) || !is_array($items)) {
            echo json_encode(['status' => 'error', 'message' => 'Minimal harus ada 1 item']);
            return;
        }

        if ($this->form_validation->run() == FALSE) {
            echo json_encode(['status' => 'error', 'message' => validation_errors()]);
        } else {
            // Generate unique id_pengajuan
            $id_pengajuan = $this->generateIdPengajuan();
            
            $nokun = $this->input->post('nokun');
            $created_by = $this->session->userdata('id');
            $created_at = date('Y-m-d H:i:s');
            
            // Start transaction
            $this->db->trans_start();
            
            // Insert each item with the same id_pengajuan
            foreach ($items as $item) {
                $data = [
                    'id_pengajuan' => $id_pengajuan,
                    'nokun' => $nokun,
                    'id_item' => $item['id_item'],
                    'quantity' => $item['quantity'],
                    'note' => $item['note_item'],
                    'status' => 1, // 1 = diajukan, 2 = diterima, 3 = ditolak, 0 = deleted
                    'category' => 2, // Set category = 2 for stockart
                    'created_by' => $created_by,
                    'created_at' => $created_at,
                    'updated_by' => $created_by,
                    'updated_at' => $created_at
                ];
                
                $this->StockartModel->simpan($data);
            }
            
            // Complete transaction
            $this->db->trans_complete();
            
            if ($this->db->trans_status() === FALSE) {
                echo json_encode(['status' => 'error', 'message' => 'Gagal menyimpan pengajuan']);
            } else {
                echo json_encode(['status' => 'success', 'message' => 'Pengajuan berhasil disimpan', 'id_pengajuan' => $id_pengajuan]);
            }
        }
    }
    
    private function generateIdPengajuan()
    {
        // Generate unique ID with format: PGJ + YYYYMMDD + sequential number
        $date = date('Ymd');
        $prefix = 'PST' . $date;
        
        // Get the last sequence for today
        $this->db->select('id_pengajuan');
        $this->db->from('inventory.request_item');
        $this->db->like('id_pengajuan', $prefix, 'after');
        $this->db->where('category', 2); // Filter by category = 2
        $this->db->order_by('id_pengajuan', 'DESC');
        $this->db->limit(1);
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            $last_id = $query->row()->id_pengajuan;
            $last_sequence = intval(substr($last_id, -4));
            $new_sequence = $last_sequence + 1;
        } else {
            $new_sequence = 1;
        }
        
        return $prefix . str_pad($new_sequence, 4, '0', STR_PAD_LEFT);
    }

    public function getData()
    {
        try {
            $nomr = $this->input->post('nomr');
            
            // DataTables parameters
            $draw = intval($this->input->post('draw'));
            $start = intval($this->input->post('start'));
            $length = intval($this->input->post('length'));
            $search_value = $this->input->post('search')['value'];
            
            // Get data with search and pagination
            $result = $this->StockartModel->getDataTableWithSearch($nomr, $start, $length, $search_value);
            
            // Ensure data is an array
            if (!is_array($result['data'])) {
                $result['data'] = [];
            }
            
            // Format response for DataTables
            $response = [
                'draw' => $draw,
                'recordsTotal' => $result['total'],
                'recordsFiltered' => $result['filtered'],
                'data' => $result['data']
            ];
            
            header('Content-Type: application/json');
            echo json_encode($response);
        } catch (Exception $e) {
            $response = [
                'draw' => intval($this->input->post('draw')),
                'recordsTotal' => 0,
                'recordsFiltered' => 0,
                'data' => [],
                'error' => 'Terjadi kesalahan saat memuat data'
            ];
            header('Content-Type: application/json');
            echo json_encode($response);
        }
    }

    public function hapus()
    {
        $id = $this->input->post('id');
        $data = [
            'status' => 0,
            'updated_by' => $this->session->userdata('id'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if ($this->StockartModel->softDelete($id, $data)) {
            echo json_encode(['status' => 'success', 'message' => 'Data berhasil dihapus']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Gagal menghapus data']);
        }
    }
    
    public function hapusPengajuan()
    {
        $id_pengajuan = $this->input->post('id_pengajuan');
        $data = [
            'status' => 0,
            'updated_by' => $this->session->userdata('id'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if ($this->StockartModel->softDeleteByPengajuan($id_pengajuan, $data)) {
            echo json_encode(['status' => 'success', 'message' => 'Pengajuan berhasil dihapus']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Gagal menghapus pengajuan']);
        }
    }
    
    public function getDetailPengajuan()
    {
        $id_pengajuan = $this->input->get('id_pengajuan');
        $data = $this->StockartModel->getDetailPengajuan($id_pengajuan);
        
        if ($data && count($data) > 0) {
            $response = [
                'id_pengajuan' => $data[0]->id_pengajuan,
                'nokun' => $data[0]->nokun,
                'status' => $data[0]->status,
                'created_at' => date('d/m/Y H:i:s', strtotime($data[0]->created_at)),
                'updated_at' => date('d/m/Y H:i:s', strtotime($data[0]->updated_at)),
                'created_by_name' => $data[0]->created_by_name ?: '-',
                'updated_by_name' => $data[0]->updated_by_name ?: '-',
                'items' => []
            ];
            
            foreach ($data as $item) {
                $response['items'][] = [
                    'nama_barang' => $item->nama_barang,
                    'quantity' => $item->quantity,
                    'note_item' => $item->note
                ];
            }
            
            echo json_encode(['status' => 'success', 'data' => $response]);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Data tidak ditemukan']);
        }
    }

    public function update()
    {
        $id = $this->input->post('id');
        $data = [
            'id_item' => $this->input->post('id_item'),
            'quantity' => $this->input->post('quantity'),
            'note' => $this->input->post('note'),
            'updated_by' => $this->session->userdata('id'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        if ($this->StockartModel->update($id, $data)) {
            echo json_encode(['status' => 'success', 'message' => 'Data berhasil diupdate']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Gagal mengupdate data']);
        }
    }

    public function getById()
    {
        $id = $this->input->post('id');
        $data = $this->StockartModel->getById($id);
        if ($data) {
            // Get item name
            $this->db->select('NAMA');
            $this->db->from('inventory.barang');
            $this->db->where('ID', $data->id_item);
            $item = $this->db->get()->row();
            $data->nama_barang = $item ? $item->NAMA : '';
            echo json_encode(['status' => 'success', 'data' => $data]);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Data tidak ditemukan']);
        }
    }

    public function getBarang()
    {
        $search = $this->input->get('q');
        $data = $this->StockartModel->getBarang($search);
        echo json_encode($data); // Return direct array, not wrapped in 'results'
    }

    public function isRoomRegistered($id_room)
    {
        return $this->StockartModel->isRoomRegistered($id_room);
    }
}
<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PengkajianDafOpe extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      [
        'masterModel',
        'pengkajianAwalModel',
        'operasi/AsistenBedahModel',
        'operasi/PengkajianDafOpeModel',
        'operasi/PengkajianPraOperasiModel',
        'operasi/WaitingListModel'
      ]
    );
  }

  public function index()
  {
    $nokun = $this->uri->segment(5);
    $norm = $this->uri->segment(3);

    $data = [
      'norm' => $norm,
      'getNomr' => $this->pengkajianAwalModel->getNomr($nokun),
      'listDr' => $this->masterModel->listDr(),
      'tujuanOperasi' => $this->masterModel->referensi(1456),
      'sifatOperasi' => $this->masterModel->referensi(621),
      'rencanaJenisPembiusan' => $this->masterModel->referensi(622),
      'historydaftaroperasi' => $this->PengkajianDafOpeModel->history($norm),
      'potongBeku' => $this->masterModel->referensi(1802),
      'joinOperasi' => $this->masterModel->referensi(1851),
      'dataSebelumnya' => $this->PengkajianDafOpeModel->ambil($nokun),
      'dataPengkajianPraOperasi' => $this->PengkajianPraOperasiModel->ambil($nokun)
    ];

    // echo'<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/operasi/pendaftaranPasienOperasi', $data);
  }

  public function alasan()
  {
    $post = $this->input->post();
    // echo'<pre>';print_r($post);exit();
    $result = $this->PengkajianDafOpeModel->alasanSifatOperasi($post['sifatOperasi'], $post['smf']);
    $data = [];
    foreach ($result as $row) {
      $sub_array = [];
      $sub_array['id'] = $row['id'];
      $sub_array['text'] = $row['deskripsi'];
      $data[] = $sub_array;
    }
    // echo '<pre>';print_r($data);exit();
    echo json_encode($data);
  }

  public function action_dafoperasi($param)
  {
    // echo'<pre>';print_r($param);exit();
    $this->db->trans_begin();
    $post = $this->input->post();
    // echo'<pre>';print_r($post);exit();
    $idWl = $post['id_waiting_list'] ?? 0;
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        // Mulai rules
        $this->form_validation->set_rules($this->AsistenBedahModel->rules());
        $this->form_validation->set_rules($this->PengkajianDafOpeModel->rules());

        // Mulai sifat operasi
        if ($param == 'tambah') {
          if (isset($post['sifat_operasi'])) {
            if ($post['sifat_operasi'] == 2131) { // CITO
              $this->form_validation->set_rules($this->PengkajianDafOpeModel->rulesCITO());
            } elseif ($post['sifat_operasi'] == 6080) { // Urgent
              $this->form_validation->set_rules($this->PengkajianDafOpeModel->rulesUrgent());
            } elseif ($post['sifat_operasi'] == 6125) { // Prioritas
              $this->form_validation->set_rules($this->PengkajianDafOpeModel->rulesPrioritas());
            }
          }
        }
        // Akhir sifat operasi

        if (isset($post['rencana_jenis_pembiusan']) && $post['rencana_jenis_pembiusan'] == 2138) { // Rencana jenis pembiusan
          $this->form_validation->set_rules($this->PengkajianDafOpeModel->rulesRencanaJenisPembiusan());
        }
        // Akhir rules

        if ($this->form_validation->run() == true) {
          // Mulai data
          $nokun = $post['nokun'];
          $dokterBedah = $post['dokter_bedah'];
          $diagnosis = $post['diagnosa_medis'];
          $tindakan = $post['rencana_tindakan_operasi'];
          $tanggalOperasi = $post['tanggal_operasi'];
          $tujuanOperasi = $post['tujuan_operasi'];
          $sifatOperasi = $post['sifat_operasi'];
          $created_at = date('Y-m-d H:i:s');
          $oleh = $this->session->userdata('id');
          // Akhir data

          // Mulai data daftar operasi
          $dataDaftarOperasi = [
            'diagnosa_medis' => $diagnosis,
            'rencana_tindakan_operasi' => $tindakan[0],
            'ruang_tujuan' => $post['ruang_tujuan'],
            'ruang_operasi' => $post['ruang_operasi'],
            'perkiraan_lama_operasi' => $post['perkiraan_lama_operasi'],
            'tanggal_operasi' => $tanggalOperasi,
            'jam_operasi' => $post['jam_operasi'],
            'tujuan_operasi' => $tujuanOperasi,
            'sifat_operasi' => $sifatOperasi,
            'rencana_jenis_pembiusan' => $post['rencana_jenis_pembiusan'],
            'rencana_jenis_pembiusan_lain' => $post['rencana_jenis_pembiusan_lain'] ?? null,
            'dokter_bedah' => $dokterBedah[0],
            'potong_beku' => $post['potong_beku'],
            'join_operasi' => $post['join_operasi'],
            'catatan_khusus' => $post['catatan_khusus'] ?? null,
            'kelas' => $post['kelas'] ?? null,
          ];
          // echo'<pre>';print_r($dataDaftarOperasi);exit();
          // Akhir data daftar operasi

          // Mulai data waiting list
          $dataWl = [
            'id_dokter' => $dokterBedah[0],
            'diagnosis' => $diagnosis,
            'tindakan' => $tindakan[0],
            'tujuan_operasi' => $tujuanOperasi,
            'sifat_operasi' => $sifatOperasi,
            'tanggal' => date('Y-m-d'),
          ];
          // Akhir data waiting list

          // Mulai aksi
          if (isset($param)) {
            if ($param == 'ubah') {
              $id = $post['id'];

              // Mulai periksa alasan
              if (isset($post['sifat_operasi_lain'])) {
                $dataDaftarOperasi['sifat_operasi_lain'] = $post['sifat_operasi_lain'];
              }
              if (isset($post['alasan_urgent'])) {
                $dataDaftarOperasi['alasan_urgent'] = $post['alasan_urgent'];
              }
              if (isset($post['alasan_prioritas'])) {
                $dataDaftarOperasi['alasan_prioritas'] = $post['alasan_prioritas'];
              }
              if (isset($post['ruang_operasi'])) {
                $dataDaftarOperasi['ruang_operasi'] = $post['ruang_operasi'];
              }
              // Akhir periksa alasan
              // echo'<pre>';print_r($dataDaftarOperasi);exit();

              $this->PengkajianDafOpeModel->ubah($id, $dataDaftarOperasi);
              $this->WaitingListModel->ubah($idWl, $dataWl);
              $this->WaitingListModel->ubahRencana($idWl, ['status' => 0]);
              $this->AsistenBedahModel->ubah($id, ['status' => 0]);
            } elseif ($param == 'tambah') {
              // Mulai simpan daftar operasi
              $dataDaftarOperasi['nokun'] = $nokun;
              $dataDaftarOperasi['sifat_operasi_lain'] = $post['sifat_operasi_lain'] ?? 0;
              $dataDaftarOperasi['alasan_urgent'] = $post['alasan_urgent'] ?? 0;
              $dataDaftarOperasi['alasan_prioritas'] = $post['alasan_prioritas'] ?? 0;
              $dataDaftarOperasi['oleh'] = $oleh;
              $dataDaftarOperasi['created_at'] = $created_at;
              $dataDaftarOperasi['status'] = 1;
              // echo'<pre>';print_r($dataDaftarOperasi);exit();
              $id = $this->PengkajianDafOpeModel->simpan($dataDaftarOperasi);
              // Akhir simpan daftar operasi

              // Mulai simpan waiting list
              $dataWl['norm'] = $post['norm'];
              $dataWl['nokun'] = $nokun;
              $dataWl['id_pendaftaran_operasi'] = $id;
              $dataWl['created_at'] = $created_at;
              $dataWl['status'] = 1;
              $dataWl['oleh'] = $oleh;
              $idWl = $this->WaitingListModel->simpan($dataWl);
              // Akhir simpan waiting list
            }

            // Mulai simpan waiting list rencana
            $dataWlr = [
              'id_wlo' => $idWl,
              'tanggal_rencana' => $tanggalOperasi,
              'keterangan' => $post['keterangan'] ?? null,
              'created_at' => $created_at,
              'status' => 1,
              'oleh' => $oleh
            ];
            $this->WaitingListModel->simpanRencana($dataWlr);
            // Akhir simpan waiting list rencana

            // Mulai simpan dokter bedah lain
            if (count($dokterBedah) > 1) { // Periksa apakah dokter bedah lebih dari 1
              $i = 0;
              $dataDokter = [];
              foreach ($dokterBedah as $db) {
                if ($i > 0) {
                  $dataDokter[$i] = [
                    'id_pendaftaran' => $id,
                    'asisten_bedah' => $dokterBedah[$i],
                    'rencana_tindakan' => $tindakan[$i],
                    'oleh' => $oleh,
                    'status' => 1
                  ];
                }
                $i++;
              }
              // echo'<pre>';print_r($dataDokter);exit();
              $this->AsistenBedahModel->simpan($dataDokter);
            }
            // Akhir simpan dokter bedah lain
          }
          // Akhir aksi

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = ['status' => 'failed'];
          } else {
            $this->db->trans_commit();
            $result = ['status' => 'success'];
          }
        } else {
          $result = [
            'status' => 'failed',
            'errors' => $this->form_validation->error_array()
          ];
        }

        echo json_encode($result);
      }
    }
  }

  public function viewDaftarPraOperasi()
  {
    $id = $this->input->post('id');

    $data = [
      'id' => $id,
      'listDr' => $this->masterModel->listDr(),
      'tujuanOperasi' => $this->masterModel->referensi(1456),
      'sifatOperasi' => $this->masterModel->referensi(621),
      'rencanaJenisPembiusan' => $this->masterModel->referensi(622),
      'potongBeku' => $this->masterModel->referensi(1802),
      'joinOperasi' => $this->masterModel->referensi(1851),
      'kelasOperasi' => $this->PengkajianDafOpeModel->getKelasOperasi(),
      'getDaftarOperasi' => $this->PengkajianDafOpeModel->detail($id),
      'tindakanDokterLain' => $this->AsistenBedahModel->ambilTindakan($id)
    ];

    // echo'<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/operasi/editDaftarPraOperasi', $data);
  }


  /**
   * Fungsi untuk memeriksa ruangan pasien dan menentukan penjamin/pembiayaan operasi yang sesuai
   * Digunakan untuk auto-select penjamin/pembiayaan operasi berdasarkan GEDUNG
   * Jika GEDUNG IS NULL, pilih Operasi Reguler (ID=2)
   * Jika GEDUNG=1, pilih Operasi Swasta (Gedung C) (ID=16)
   */
  public function cekRuangan()
  {
    $id_ruangan = $this->input->post('id_ruangan');

    if ($id_ruangan) {
      // Ambil data ruangan dari masterModel
      $ruangan = $this->masterModel->getDeskRuangan($id_ruangan);

      // Kembalikan data dalam format JSON
      echo json_encode($ruangan);
    } else {
      echo json_encode(null);
    }
  }
}

/* End of file PendaftaranDafOpe.php */
/* Location: ./application/controllers/operasi/PendaftaranDafOpe.php */
